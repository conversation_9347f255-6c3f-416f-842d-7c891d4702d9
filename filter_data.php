<?php
session_start();
include 'db.php'; 

if (!isset($_POST['date'])) {
    echo "No date received.";
    exit;
}

$date = $_POST['date'];
$formattedDate = date('Y-m-d', strtotime($date));

// Add debug information
error_log("Selected date: $formattedDate");

$health_center = $_SESSION['health_center'];
$sql = ($health_center == 'CITY HEALTH OFFICE') ?
    "SELECT * FROM nip_table WHERE DateOfBirth = '$formattedDate'" :
    "SELECT * FROM nip_table WHERE Barangay = '".$_SESSION['health_center']."' AND DateOfBirth = '$formattedDate'";

$result = $conn->query($sql);

if ($result === false) {
    error_log("SQL Error: " . $conn->error);
    echo "Database error.";
    exit;
}

$output = '';
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $output .= '<tr>
            <td>' . $row['id'] . '</td>
            <td>' . date('M d,Y', strtotime($row['DateOfBirth'])) . '</td>
            <td>' . date('M d,Y', strtotime($row['DateOfRegistration'])) . '</td>
            <td>' . $row['ChildNumber'] . '</td>
            <td>' . $row['SurnameChild'] . '</td>
            <td>' . $row['FirstNameChild'] . '</td>
            <td>' . $row['NameOfChild'] . '</td>
            <td>' . $row['Sex'] . '</td>
            <td>' . $row['NameofMother'] . '</td>
            <td>' . $row['Age'] . '</td>
            <td>' . $row['Barangay'] . '</td>
            <td>' . $row['PurokStreetSitio'] . '</td>
            <td>
                <button type="button" class="btn btn-small teal darken-4 modal-trigger" href="#modal1' . $row['id'] . '"><i class="material-icons center">remove_red_eye</i></button>
                <button type="button" class="btn btn-small blue darken-4"><i class="material-icons center">edit</i></button>
                <button type="button" class="btn btn-small red darken-4"><i class="material-icons center">delete</i></button>
            </td>
        </tr>';
    }
} else {
    $output .= '<tr><td colspan="13">No records found.</td></tr>';
}

echo $output;
?>
