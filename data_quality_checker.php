<?php
session_start();
include 'db.php';
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Function to verify health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

// Verify access
if (!verifyHealthCenterAccess($conn, $_SESSION['health_center'])) {
    echo json_encode([
        'error' => 'Wrong password - Data quality check blocked.',
        'blocked_reason' => 'health_center_mismatch'
    ]);
    exit;
}

// Function to perform comprehensive data quality checks
function performDataQualityCheck($conn, $health_center) {
    $quality_report = [
        'overall_score' => 0,
        'total_records' => 0,
        'checks' => [],
        'issues' => [],
        'recommendations' => []
    ];
    
    // Get total records
    $total_stmt = $conn->prepare("SELECT COUNT(*) as count FROM nip_table WHERE deleted = 0 AND Barangay = ?");
    $total_stmt->bind_param("s", $health_center);
    $total_stmt->execute();
    $total_result = $total_stmt->get_result();
    $quality_report['total_records'] = $total_result->fetch_assoc()['count'];
    $total_stmt->close();
    
    if ($quality_report['total_records'] == 0) {
        return $quality_report;
    }
    
    $checks = [];
    $total_score = 0;
    $max_score = 0;
    
    // Check 1: Missing Child Names
    $missing_names_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (NameOfChild IS NULL OR NameOfChild = '' OR lastnameOfChild IS NULL OR lastnameOfChild = '')
    ");
    $missing_names_stmt->bind_param("s", $health_center);
    $missing_names_stmt->execute();
    $missing_names = $missing_names_stmt->get_result()->fetch_assoc()['count'];
    $missing_names_stmt->close();
    
    $names_score = max(0, 100 - ($missing_names / $quality_report['total_records'] * 100));
    $checks['missing_names'] = [
        'name' => 'Complete Child Names',
        'score' => round($names_score, 1),
        'issues_count' => $missing_names,
        'description' => 'Records with complete first and last names',
        'status' => $names_score >= 90 ? 'excellent' : ($names_score >= 70 ? 'good' : 'needs_improvement')
    ];
    
    // Check 2: Missing Mother Names
    $missing_mothers_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (NameofMother IS NULL OR NameofMother = '')
    ");
    $missing_mothers_stmt->bind_param("s", $health_center);
    $missing_mothers_stmt->execute();
    $missing_mothers = $missing_mothers_stmt->get_result()->fetch_assoc()['count'];
    $missing_mothers_stmt->close();
    
    $mothers_score = max(0, 100 - ($missing_mothers / $quality_report['total_records'] * 100));
    $checks['missing_mothers'] = [
        'name' => 'Complete Mother Names',
        'score' => round($mothers_score, 1),
        'issues_count' => $missing_mothers,
        'description' => 'Records with mother names filled',
        'status' => $mothers_score >= 90 ? 'excellent' : ($mothers_score >= 70 ? 'good' : 'needs_improvement')
    ];
    
    // Check 3: Valid Birth Dates
    $invalid_birth_dates_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (DateOfBirth IS NULL OR DateOfBirth = '0000-00-00' OR DateOfBirth > CURDATE())
    ");
    $invalid_birth_dates_stmt->bind_param("s", $health_center);
    $invalid_birth_dates_stmt->execute();
    $invalid_birth_dates = $invalid_birth_dates_stmt->get_result()->fetch_assoc()['count'];
    $invalid_birth_dates_stmt->close();
    
    $birth_dates_score = max(0, 100 - ($invalid_birth_dates / $quality_report['total_records'] * 100));
    $checks['valid_birth_dates'] = [
        'name' => 'Valid Birth Dates',
        'score' => round($birth_dates_score, 1),
        'issues_count' => $invalid_birth_dates,
        'description' => 'Records with valid birth dates',
        'status' => $birth_dates_score >= 95 ? 'excellent' : ($birth_dates_score >= 80 ? 'good' : 'needs_improvement')
    ];
    
    // Check 4: Reasonable Birth Weights
    $invalid_weights_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (BirthWeightInGrams IS NULL OR BirthWeightInGrams <= 0 OR BirthWeightInGrams > 6000 OR BirthWeightInGrams < 500)
    ");
    $invalid_weights_stmt->bind_param("s", $health_center);
    $invalid_weights_stmt->execute();
    $invalid_weights = $invalid_weights_stmt->get_result()->fetch_assoc()['count'];
    $invalid_weights_stmt->close();
    
    $weights_score = max(0, 100 - ($invalid_weights / $quality_report['total_records'] * 100));
    $checks['reasonable_weights'] = [
        'name' => 'Reasonable Birth Weights',
        'score' => round($weights_score, 1),
        'issues_count' => $invalid_weights,
        'description' => 'Birth weights between 500g and 6000g',
        'status' => $weights_score >= 90 ? 'excellent' : ($weights_score >= 75 ? 'good' : 'needs_improvement')
    ];
    
    // Check 5: Complete Addresses
    $missing_addresses_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (Address IS NULL OR Address = '' OR LENGTH(Address) < 10)
    ");
    $missing_addresses_stmt->bind_param("s", $health_center);
    $missing_addresses_stmt->execute();
    $missing_addresses = $missing_addresses_stmt->get_result()->fetch_assoc()['count'];
    $missing_addresses_stmt->close();
    
    $addresses_score = max(0, 100 - ($missing_addresses / $quality_report['total_records'] * 100));
    $checks['complete_addresses'] = [
        'name' => 'Complete Addresses',
        'score' => round($addresses_score, 1),
        'issues_count' => $missing_addresses,
        'description' => 'Records with detailed addresses',
        'status' => $addresses_score >= 85 ? 'excellent' : ($addresses_score >= 65 ? 'good' : 'needs_improvement')
    ];
    
    // Check 6: Valid Gender Information
    $invalid_gender_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (Sex IS NULL OR Sex = '' OR Sex NOT IN ('Male', 'Female', 'M', 'F'))
    ");
    $invalid_gender_stmt->bind_param("s", $health_center);
    $invalid_gender_stmt->execute();
    $invalid_gender = $invalid_gender_stmt->get_result()->fetch_assoc()['count'];
    $invalid_gender_stmt->close();
    
    $gender_score = max(0, 100 - ($invalid_gender / $quality_report['total_records'] * 100));
    $checks['valid_gender'] = [
        'name' => 'Valid Gender Information',
        'score' => round($gender_score, 1),
        'issues_count' => $invalid_gender,
        'description' => 'Records with proper gender designation',
        'status' => $gender_score >= 95 ? 'excellent' : ($gender_score >= 85 ? 'good' : 'needs_improvement')
    ];
    
    // Check 7: Immunization Status Completeness
    $missing_immunization_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (IMMUNIZATIONSTATUS IS NULL OR IMMUNIZATIONSTATUS = '')
    ");
    $missing_immunization_stmt->bind_param("s", $health_center);
    $missing_immunization_stmt->execute();
    $missing_immunization = $missing_immunization_stmt->get_result()->fetch_assoc()['count'];
    $missing_immunization_stmt->close();
    
    $immunization_score = max(0, 100 - ($missing_immunization / $quality_report['total_records'] * 100));
    $checks['immunization_status'] = [
        'name' => 'Immunization Status',
        'score' => round($immunization_score, 1),
        'issues_count' => $missing_immunization,
        'description' => 'Records with immunization status filled',
        'status' => $immunization_score >= 90 ? 'excellent' : ($immunization_score >= 70 ? 'good' : 'needs_improvement')
    ];
    
    // Check 8: Recent Registration Dates
    $old_registrations_stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (DateOfRegistration IS NULL OR DateOfRegistration < DATE_SUB(CURDATE(), INTERVAL 2 YEAR))
    ");
    $old_registrations_stmt->bind_param("s", $health_center);
    $old_registrations_stmt->execute();
    $old_registrations = $old_registrations_stmt->get_result()->fetch_assoc()['count'];
    $old_registrations_stmt->close();
    
    $registration_score = max(0, 100 - ($old_registrations / $quality_report['total_records'] * 100));
    $checks['recent_registrations'] = [
        'name' => 'Recent Registrations',
        'score' => round($registration_score, 1),
        'issues_count' => $old_registrations,
        'description' => 'Records registered within last 2 years',
        'status' => $registration_score >= 80 ? 'excellent' : ($registration_score >= 60 ? 'good' : 'needs_improvement')
    ];
    
    // Calculate overall score
    $total_score = array_sum(array_column($checks, 'score'));
    $max_score = count($checks) * 100;
    $quality_report['overall_score'] = round($total_score / count($checks), 1);
    $quality_report['checks'] = $checks;
    
    // Generate issues and recommendations
    $issues = [];
    $recommendations = [];
    
    foreach ($checks as $key => $check) {
        if ($check['score'] < 70) {
            $issues[] = [
                'type' => $key,
                'severity' => $check['score'] < 50 ? 'high' : 'medium',
                'message' => $check['name'] . ' needs improvement (' . $check['issues_count'] . ' issues)',
                'count' => $check['issues_count']
            ];
            
            // Add specific recommendations
            switch ($key) {
                case 'missing_names':
                    $recommendations[] = 'Review and complete missing child names in registration forms';
                    break;
                case 'missing_mothers':
                    $recommendations[] = 'Ensure mother names are collected during registration';
                    break;
                case 'valid_birth_dates':
                    $recommendations[] = 'Verify and correct invalid birth dates';
                    break;
                case 'reasonable_weights':
                    $recommendations[] = 'Check birth weight entries for accuracy (500g-6000g range)';
                    break;
                case 'complete_addresses':
                    $recommendations[] = 'Collect more detailed address information';
                    break;
                case 'valid_gender':
                    $recommendations[] = 'Standardize gender field entries (Male/Female)';
                    break;
                case 'immunization_status':
                    $recommendations[] = 'Update immunization status for all records';
                    break;
                case 'recent_registrations':
                    $recommendations[] = 'Archive old records and focus on recent registrations';
                    break;
            }
        }
    }
    
    $quality_report['issues'] = $issues;
    $quality_report['recommendations'] = array_unique($recommendations);
    
    // Add quality grade
    if ($quality_report['overall_score'] >= 90) {
        $quality_report['grade'] = 'A';
        $quality_report['grade_description'] = 'Excellent data quality';
    } elseif ($quality_report['overall_score'] >= 80) {
        $quality_report['grade'] = 'B';
        $quality_report['grade_description'] = 'Good data quality';
    } elseif ($quality_report['overall_score'] >= 70) {
        $quality_report['grade'] = 'C';
        $quality_report['grade_description'] = 'Fair data quality';
    } elseif ($quality_report['overall_score'] >= 60) {
        $quality_report['grade'] = 'D';
        $quality_report['grade_description'] = 'Poor data quality';
    } else {
        $quality_report['grade'] = 'F';
        $quality_report['grade_description'] = 'Critical data quality issues';
    }
    
    return $quality_report;
}

// Function to get detailed issues for a specific check
function getDetailedIssues($conn, $health_center, $check_type, $limit = 20) {
    $issues = [];
    
    switch ($check_type) {
        case 'missing_names':
            $stmt = $conn->prepare("
                SELECT id, NameOfChild, lastnameOfChild, NameofMother, DateOfRegistration 
                FROM nip_table 
                WHERE deleted = 0 AND Barangay = ? 
                AND (NameOfChild IS NULL OR NameOfChild = '' OR lastnameOfChild IS NULL OR lastnameOfChild = '')
                ORDER BY DateOfRegistration DESC 
                LIMIT ?
            ");
            break;
            
        case 'missing_mothers':
            $stmt = $conn->prepare("
                SELECT id, NameOfChild, lastnameOfChild, NameofMother, DateOfRegistration 
                FROM nip_table 
                WHERE deleted = 0 AND Barangay = ? 
                AND (NameofMother IS NULL OR NameofMother = '')
                ORDER BY DateOfRegistration DESC 
                LIMIT ?
            ");
            break;
            
        case 'valid_birth_dates':
            $stmt = $conn->prepare("
                SELECT id, NameOfChild, lastnameOfChild, DateOfBirth, DateOfRegistration 
                FROM nip_table 
                WHERE deleted = 0 AND Barangay = ? 
                AND (DateOfBirth IS NULL OR DateOfBirth = '0000-00-00' OR DateOfBirth > CURDATE())
                ORDER BY DateOfRegistration DESC 
                LIMIT ?
            ");
            break;
            
        case 'reasonable_weights':
            $stmt = $conn->prepare("
                SELECT id, NameOfChild, lastnameOfChild, BirthWeightInGrams, DateOfRegistration 
                FROM nip_table 
                WHERE deleted = 0 AND Barangay = ? 
                AND (BirthWeightInGrams IS NULL OR BirthWeightInGrams <= 0 OR BirthWeightInGrams > 6000 OR BirthWeightInGrams < 500)
                ORDER BY DateOfRegistration DESC 
                LIMIT ?
            ");
            break;
            
        default:
            return [];
    }
    
    $stmt->bind_param("si", $health_center, $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $issues[] = $row;
    }
    
    $stmt->close();
    return $issues;
}

try {
    $health_center = $_SESSION['health_center'];
    $action = $_GET['action'] ?? 'check';
    
    switch ($action) {
        case 'check':
            $quality_report = performDataQualityCheck($conn, $health_center);
            
            echo json_encode([
                'success' => true,
                'quality_report' => $quality_report,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        case 'detailed_issues':
            $check_type = $_GET['check_type'] ?? '';
            $limit = intval($_GET['limit'] ?? 20);
            
            if (empty($check_type)) {
                echo json_encode(['error' => 'Check type is required']);
                break;
            }
            
            $issues = getDetailedIssues($conn, $health_center, $check_type, $limit);
            
            echo json_encode([
                'success' => true,
                'check_type' => $check_type,
                'issues' => $issues,
                'count' => count($issues)
            ]);
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Data quality check error: ' . $e->getMessage()
    ]);
}
?>
