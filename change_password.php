<?php
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode(['success' => false, 'message' => 'User not authenticated']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Validate required fields
if (!isset($_POST['currentPassword']) || !isset($_POST['newPassword'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

$currentPassword = trim($_POST['currentPassword']);
$newPassword = trim($_POST['newPassword']);
$health_center = $_SESSION['health_center'];

// Basic validation
if (empty($currentPassword) || empty($newPassword)) {
    echo json_encode(['success' => false, 'message' => 'All fields are required']);
    exit;
}

if (strlen($newPassword) < 6) {
    echo json_encode(['success' => false, 'message' => 'New password must be at least 6 characters long']);
    exit;
}

if ($currentPassword === $newPassword) {
    echo json_encode(['success' => false, 'message' => 'New password must be different from current password']);
    exit;
}

// Include database connection
include 'db.php';

try {
    // First, verify the current password
    $stmt = $conn->prepare("SELECT id, password FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    $user = $result->fetch_assoc();
    
    // Verify current password
    if ($user['password'] !== $currentPassword) {
        echo json_encode(['success' => false, 'message' => 'Current password is incorrect']);
        exit;
    }
    
    // Update password
    $updateStmt = $conn->prepare("UPDATE health_facility SET password = ? WHERE id = ?");
    $updateStmt->bind_param("si", $newPassword, $user['id']);
    
    if ($updateStmt->execute()) {
        // Log the password change (optional - for security audit)
        $logStmt = $conn->prepare("INSERT INTO user_action_logs (user_id, table_name, action_type, performed_by, reason) VALUES (?, 'health_facility', 'PASSWORD_CHANGE', ?, 'Password changed by user')");
        $performed_by = $_SESSION['health_center'] ?? 'Unknown';
        $logStmt->bind_param("is", $user['id'], $performed_by);
        $logStmt->execute();
        
        echo json_encode([
            'success' => true, 
            'message' => 'Password changed successfully!'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update password. Please try again.']);
    }
    
    $stmt->close();
    $updateStmt->close();
    if (isset($logStmt)) {
        $logStmt->close();
    }
    
} catch (Exception $e) {
    error_log("Password change error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
