<?php
// Simple installation helper for PhpSpreadsheet
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Dependencies - NIP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <nav class="teal">
        <div class="nav-wrapper">
            <a href="#" class="brand-logo center">Install Dependencies</a>
        </div>
    </nav>

    <div class="container" style="margin-top: 20px;">
        <div class="card">
            <div class="card-content">
                <span class="card-title">
                    <i class="material-icons left">get_app</i>
                    Install PhpSpreadsheet for Excel Import
                </span>
                
                <div class="card-panel blue lighten-5 blue-text text-darken-2">
                    <h6><i class="material-icons left">info</i>Installation Instructions:</h6>
                    <ol>
                        <li>Make sure you have Composer installed on your system</li>
                        <li>Open command prompt/terminal in your project directory: <code>c:\xampp\htdocs\nip</code></li>
                        <li>Run the following command:</li>
                    </ol>
                    <div class="card-panel grey lighten-4">
                        <code>composer install</code>
                    </div>
                    <p>This will install PhpSpreadsheet library which enables Excel file import functionality.</p>
                </div>

                <div class="card-panel orange lighten-5 orange-text text-darken-2">
                    <h6><i class="material-icons left">warning</i>Alternative: Manual Installation</h6>
                    <p>If you don't have Composer, you can:</p>
                    <ol>
                        <li>Download PhpSpreadsheet from GitHub</li>
                        <li>Extract it to a 'vendor' folder in your project</li>
                        <li>Or use the basic CSV import functionality which doesn't require additional libraries</li>
                    </ol>
                </div>

                <div class="card-panel green lighten-5 green-text text-darken-2">
                    <h6><i class="material-icons left">check_circle</i>Current Status:</h6>
                    <p>
                        PhpSpreadsheet: 
                        <?php if (file_exists('vendor/autoload.php')): ?>
                            <span class="green-text"><strong>Installed ✓</strong></span>
                        <?php else: ?>
                            <span class="red-text"><strong>Not Installed ✗</strong></span>
                        <?php endif; ?>
                    </p>
                </div>

                <div class="center-align">
                    <a href="import_excel.php" class="btn waves-effect waves-light teal">
                        <i class="material-icons left">arrow_forward</i>Go to Import Page
                    </a>
                    <a href="import_excel_advanced.php" class="btn waves-effect waves-light blue">
                        <i class="material-icons left">arrow_forward</i>Advanced Import Page
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
</body>
</html>