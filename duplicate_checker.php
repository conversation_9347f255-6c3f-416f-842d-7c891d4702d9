<?php
session_start();
include 'db.php';

// Admin authentication check
if (!isset($_SESSION['admin_authenticated'])) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_password'])) {
        $admin_pass = $_POST['admin_password'];
        if ($admin_pass === 'admin2024') {
            $_SESSION['admin_authenticated'] = true;
        } else {
            $error_message = 'Invalid admin password!';
        }
    }
}

$duplicates = [];
$search_performed = false;

// Handle duplicate search
if (isset($_POST['find_duplicates']) && isset($_SESSION['admin_authenticated'])) {
    $search_performed = true;
    
    // Find exact duplicates (same name, DOB, and mother)
    $exact_sql = "SELECT 
        GROUP_CONCAT(id) as ids,
        COUNT(*) as count,
        NameOfChild,
        LastNameOfChild,
        DateOfBirth,
        NameofMother,
        Barangay,
        GROUP_CONCAT(FamilySerialNumber) as family_serials,
        GROUP_CONCAT(DateOfRegistration) as reg_dates
        FROM nip_table 
        WHERE deleted = 0
        GROUP BY NameOfChild, LastNameOfChild, DateOfBirth, NameofMother
        HAVING COUNT(*) > 1
        ORDER BY count DESC, NameOfChild";
    
    $exact_result = $conn->query($exact_sql);
    if ($exact_result) {
        while ($row = $exact_result->fetch_assoc()) {
            $duplicates['exact'][] = $row;
        }
    }
    
    // Find similar names (SOUNDEX)
    $similar_sql = "SELECT 
        a.id as id1, a.NameOfChild as name1, a.LastNameOfChild as lastname1, a.DateOfBirth as dob1, a.NameofMother as mother1,
        b.id as id2, b.NameOfChild as name2, b.LastNameOfChild as lastname2, b.DateOfBirth as dob2, b.NameofMother as mother2,
        a.FamilySerialNumber as serial1, b.FamilySerialNumber as serial2
        FROM nip_table a
        JOIN nip_table b ON (
            (SOUNDEX(a.NameOfChild) = SOUNDEX(b.NameOfChild) OR SOUNDEX(a.LastNameOfChild) = SOUNDEX(b.LastNameOfChild))
            AND a.DateOfBirth = b.DateOfBirth
            AND a.id < b.id
            AND a.deleted = 0 AND b.deleted = 0
        )
        ORDER BY a.NameOfChild";
    
    $similar_result = $conn->query($similar_sql);
    if ($similar_result) {
        while ($row = $similar_result->fetch_assoc()) {
            $duplicates['similar'][] = $row;
        }
    }
    
    // Find duplicate phone numbers
    $phone_sql = "SELECT 
        GROUP_CONCAT(id) as ids,
        COUNT(*) as count,
        PhoneNumber,
        GROUP_CONCAT(CONCAT(NameOfChild, ' ', LastNameOfChild)) as names
        FROM nip_table 
        WHERE PhoneNumber IS NOT NULL AND PhoneNumber != '' AND deleted = 0
        GROUP BY PhoneNumber
        HAVING COUNT(*) > 1
        ORDER BY count DESC";
    
    $phone_result = $conn->query($phone_sql);
    if ($phone_result) {
        while ($row = $phone_result->fetch_assoc()) {
            $duplicates['phone'][] = $row;
        }
    }
    
    // Find duplicate family serial numbers
    $serial_sql = "SELECT 
        GROUP_CONCAT(id) as ids,
        COUNT(*) as count,
        FamilySerialNumber,
        GROUP_CONCAT(CONCAT(NameOfChild, ' ', LastNameOfChild)) as names
        FROM nip_table 
        WHERE FamilySerialNumber IS NOT NULL AND FamilySerialNumber != '' AND deleted = 0
        GROUP BY FamilySerialNumber
        HAVING COUNT(*) > 1
        ORDER BY count DESC";
    
    $serial_result = $conn->query($serial_sql);
    if ($serial_result) {
        while ($row = $serial_result->fetch_assoc()) {
            $duplicates['serial'][] = $row;
        }
    }
}

// Handle duplicate removal
if (isset($_POST['remove_duplicate']) && isset($_SESSION['admin_authenticated'])) {
    $record_id = $_POST['record_id'];
    $reason = $_POST['reason'];
    
    if (!empty($record_id) && !empty($reason)) {
        // Soft delete the record
        $delete_stmt = $conn->prepare("UPDATE nip_table SET deleted = 1, deleted_reason = ?, deleted_date = NOW(), deleted_by = ? WHERE id = ?");
        $admin_user = 'ADMIN_DUPLICATE_REMOVAL';
        $delete_stmt->bind_param("ssi", $reason, $admin_user, $record_id);
        
        if ($delete_stmt->execute()) {
            $success_message = "Record ID $record_id has been marked as duplicate and removed.";
            
            // Log the action
            $log_stmt = $conn->prepare("INSERT INTO user_action_logs (user_id, table_name, action_type, performed_by, reason) VALUES (?, 'nip_table', 'DUPLICATE_REMOVAL', 'ADMIN', ?)");
            $log_stmt->bind_param("is", $record_id, $reason);
            $log_stmt->execute();
            $log_stmt->close();
        } else {
            $error_message = "Failed to remove duplicate: " . $delete_stmt->error;
        }
        $delete_stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Checker - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .duplicate-container {
            margin-top: 30px;
            max-width: 1400px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .duplicate-card {
            margin-bottom: 15px;
            border-left: 4px solid #f44336;
        }
        .similar-card {
            border-left: 4px solid #ff9800;
        }
        .phone-card {
            border-left: 4px solid #2196f3;
        }
        .serial-card {
            border-left: 4px solid #9c27b0;
        }
        .record-details {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .admin-section {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container duplicate-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align">
                            <i class="material-icons left">find_in_page</i>
                            Duplicate Record Checker
                        </span>

                        <?php if (isset($error_message)): ?>
                            <div class="card red lighten-4">
                                <div class="card-content red-text">
                                    <i class="material-icons left">error</i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($success_message)): ?>
                            <div class="card green lighten-4">
                                <div class="card-content green-text">
                                    <i class="material-icons left">check_circle</i>
                                    <?php echo htmlspecialchars($success_message); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!isset($_SESSION['admin_authenticated'])): ?>
                            <!-- Admin Authentication -->
                            <div class="card admin-section">
                                <div class="card-content">
                                    <span class="card-title white-text">
                                        <i class="material-icons left">admin_panel_settings</i>
                                        Admin Authentication Required
                                    </span>
                                    <form method="POST">
                                        <div class="input-field">
                                            <i class="material-icons prefix white-text">lock</i>
                                            <input id="admin_password" name="admin_password" type="password" class="white-text" required>
                                            <label for="admin_password" class="white-text">Admin Password</label>
                                        </div>
                                        <button type="submit" class="btn white blue-text waves-effect">
                                            <i class="material-icons left">login</i>Authenticate
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Search Controls -->
                            <div class="row">
                                <div class="col s12 center-align">
                                    <form method="POST">
                                        <button type="submit" name="find_duplicates" class="btn large blue waves-effect">
                                            <i class="material-icons left">search</i>Find All Duplicates
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <?php if ($search_performed): ?>
                                <!-- Results Summary -->
                                <div class="row">
                                    <div class="col s12 m3">
                                        <div class="card red lighten-4">
                                            <div class="card-content center-align">
                                                <h4><?php echo count($duplicates['exact'] ?? []); ?></h4>
                                                <p>Exact Duplicates</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m3">
                                        <div class="card orange lighten-4">
                                            <div class="card-content center-align">
                                                <h4><?php echo count($duplicates['similar'] ?? []); ?></h4>
                                                <p>Similar Names</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m3">
                                        <div class="card blue lighten-4">
                                            <div class="card-content center-align">
                                                <h4><?php echo count($duplicates['phone'] ?? []); ?></h4>
                                                <p>Phone Duplicates</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m3">
                                        <div class="card purple lighten-4">
                                            <div class="card-content center-align">
                                                <h4><?php echo count($duplicates['serial'] ?? []); ?></h4>
                                                <p>Serial Duplicates</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Exact Duplicates -->
                                <?php if (!empty($duplicates['exact'])): ?>
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title red-text">
                                                <i class="material-icons left">error</i>
                                                Exact Duplicates (<?php echo count($duplicates['exact']); ?>)
                                            </span>
                                            
                                            <?php foreach ($duplicates['exact'] as $duplicate): ?>
                                                <div class="card duplicate-card">
                                                    <div class="card-content">
                                                        <h6><strong><?php echo htmlspecialchars($duplicate['NameOfChild'] . ' ' . $duplicate['LastNameOfChild']); ?></strong></h6>
                                                        <p><strong>Birth Date:</strong> <?php echo $duplicate['DateOfBirth']; ?></p>
                                                        <p><strong>Mother:</strong> <?php echo htmlspecialchars($duplicate['NameofMother']); ?></p>
                                                        <p><strong>Barangay:</strong> <?php echo htmlspecialchars($duplicate['Barangay']); ?></p>
                                                        <p><strong>Duplicate Count:</strong> <?php echo $duplicate['count']; ?></p>
                                                        
                                                        <div class="record-details">
                                                            <strong>Record IDs:</strong> <?php echo $duplicate['ids']; ?><br>
                                                            <strong>Family Serials:</strong> <?php echo $duplicate['family_serials']; ?><br>
                                                            <strong>Registration Dates:</strong> <?php echo $duplicate['reg_dates']; ?>
                                                        </div>
                                                        
                                                        <div style="margin-top: 15px;">
                                                            <button class="btn red waves-effect remove-duplicate-btn" 
                                                                    data-ids="<?php echo $duplicate['ids']; ?>"
                                                                    data-name="<?php echo htmlspecialchars($duplicate['NameOfChild'] . ' ' . $duplicate['LastNameOfChild']); ?>">
                                                                <i class="material-icons left">delete</i>Remove Duplicates
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Similar Names -->
                                <?php if (!empty($duplicates['similar'])): ?>
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title orange-text">
                                                <i class="material-icons left">warning</i>
                                                Similar Names (<?php echo count($duplicates['similar']); ?>)
                                            </span>
                                            
                                            <?php foreach ($duplicates['similar'] as $similar): ?>
                                                <div class="card similar-card">
                                                    <div class="card-content">
                                                        <div class="row">
                                                            <div class="col s12 m6">
                                                                <h6>Record 1 (ID: <?php echo $similar['id1']; ?>)</h6>
                                                                <p><strong>Name:</strong> <?php echo htmlspecialchars($similar['name1'] . ' ' . $similar['lastname1']); ?></p>
                                                                <p><strong>DOB:</strong> <?php echo $similar['dob1']; ?></p>
                                                                <p><strong>Mother:</strong> <?php echo htmlspecialchars($similar['mother1']); ?></p>
                                                                <p><strong>Serial:</strong> <?php echo $similar['serial1']; ?></p>
                                                            </div>
                                                            <div class="col s12 m6">
                                                                <h6>Record 2 (ID: <?php echo $similar['id2']; ?>)</h6>
                                                                <p><strong>Name:</strong> <?php echo htmlspecialchars($similar['name2'] . ' ' . $similar['lastname2']); ?></p>
                                                                <p><strong>DOB:</strong> <?php echo $similar['dob2']; ?></p>
                                                                <p><strong>Mother:</strong> <?php echo htmlspecialchars($similar['mother2']); ?></p>
                                                                <p><strong>Serial:</strong> <?php echo $similar['serial2']; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Phone Number Duplicates -->
                                <?php if (!empty($duplicates['phone'])): ?>
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title blue-text">
                                                <i class="material-icons left">phone</i>
                                                Phone Number Duplicates (<?php echo count($duplicates['phone']); ?>)
                                            </span>
                                            
                                            <?php foreach ($duplicates['phone'] as $phone): ?>
                                                <div class="card phone-card">
                                                    <div class="card-content">
                                                        <h6><strong>Phone:</strong> <?php echo htmlspecialchars($phone['PhoneNumber']); ?></h6>
                                                        <p><strong>Used by <?php echo $phone['count']; ?> records:</strong></p>
                                                        <div class="record-details">
                                                            <strong>Record IDs:</strong> <?php echo $phone['ids']; ?><br>
                                                            <strong>Names:</strong> <?php echo htmlspecialchars($phone['names']); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Family Serial Duplicates -->
                                <?php if (!empty($duplicates['serial'])): ?>
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title purple-text">
                                                <i class="material-icons left">confirmation_number</i>
                                                Family Serial Duplicates (<?php echo count($duplicates['serial']); ?>)
                                            </span>
                                            
                                            <?php foreach ($duplicates['serial'] as $serial): ?>
                                                <div class="card serial-card">
                                                    <div class="card-content">
                                                        <h6><strong>Serial:</strong> <?php echo htmlspecialchars($serial['FamilySerialNumber']); ?></h6>
                                                        <p><strong>Used by <?php echo $serial['count']; ?> records:</strong></p>
                                                        <div class="record-details">
                                                            <strong>Record IDs:</strong> <?php echo $serial['ids']; ?><br>
                                                            <strong>Names:</strong> <?php echo htmlspecialchars($serial['names']); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if (empty($duplicates['exact']) && empty($duplicates['similar']) && empty($duplicates['phone']) && empty($duplicates['serial'])): ?>
                                    <div class="card green lighten-4">
                                        <div class="card-content green-text center-align">
                                            <i class="material-icons large">check_circle</i>
                                            <h5>No Duplicates Found!</h5>
                                            <p>Your database is clean - no duplicate records detected.</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>

                            <div class="center-align" style="margin-top: 30px;">
                                <a href="password_recovery.php" class="btn green waves-effect">
                                    <i class="material-icons left">build</i>Password Recovery
                                </a>
                                <a href="user_settings.php" class="btn blue waves-effect">
                                    <i class="material-icons left">people</i>User Management
                                </a>
                                <a href="?logout=1" class="btn red waves-effect">
                                    <i class="material-icons left">logout</i>Logout
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Remove Duplicate Modal -->
    <div id="removeDuplicateModal" class="modal">
        <div class="modal-content">
            <h4 class="red-text">
                <i class="material-icons left">warning</i>
                Remove Duplicate Record
            </h4>
            <p>Select which record to remove and provide a reason:</p>
            
            <form method="POST" id="removeDuplicateForm">
                <div class="input-field">
                    <select id="record_id" name="record_id" required>
                        <option value="" disabled selected>Choose record to remove</option>
                    </select>
                    <label>Record to Remove</label>
                </div>
                
                <div class="input-field">
                    <textarea id="reason" name="reason" class="materialize-textarea" required></textarea>
                    <label for="reason">Reason for Removal</label>
                </div>
                
                <div class="modal-footer">
                    <a href="#!" class="modal-close waves-effect waves-grey btn-flat">Cancel</a>
                    <button type="submit" name="remove_duplicate" class="btn red waves-effect waves-light">
                        <i class="material-icons left">delete</i>Remove Record
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Handle remove duplicate button clicks
            document.querySelectorAll('.remove-duplicate-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const ids = this.dataset.ids.split(',');
                    const name = this.dataset.name;
                    
                    // Populate the select dropdown
                    const select = document.getElementById('record_id');
                    select.innerHTML = '<option value="" disabled selected>Choose record to remove</option>';
                    
                    ids.forEach(function(id) {
                        const option = document.createElement('option');
                        option.value = id.trim();
                        option.textContent = `Record ID: ${id.trim()} (${name})`;
                        select.appendChild(option);
                    });
                    
                    // Reinitialize the select
                    M.FormSelect.init(select);
                    
                    // Open modal
                    const modal = M.Modal.getInstance(document.getElementById('removeDuplicateModal'));
                    modal.open();
                });
            });
        });
    </script>

    <?php
    // Handle logout
    if (isset($_GET['logout'])) {
        unset($_SESSION['admin_authenticated']);
        header('Location: duplicate_checker.php');
        exit;
    }
    ?>
</body>
</html>
