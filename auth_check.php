<?php
/**
 * Authentication Check with Remember Me Functionality
 * Include this file at the top of pages that require authentication
 */

function checkAuthentication() {
    // Check if session exists, if not check for remember me cookie
    if (!isset($_SESSION['health_center'])) {
        // Check if remember me cookie exists
        if (isset($_CO<PERSON>IE['remember_user']) && isset($_COOKIE['remember_token'])) {
            // Include database connection
            include_once 'db.php';
            
            $remember_token = $_COOKIE['remember_token'];
            $health_center = $_COOKIE['remember_user'];
            
            // Verify the remember token in database
            $stmt = $conn->prepare("SELECT * FROM health_facility WHERE health_center = ? AND remember_token = ?");
            $stmt->bind_param("ss", $health_center, $remember_token);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                // Valid remember token, restore session
                $user = $result->fetch_assoc();
                $_SESSION['health_center'] = $user['health_center'];
                $_SESSION['fullname'] = $user['fullname'];
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['AddressofFacility'] = $user['AddressofFacility'];
                $_SESSION['mobile_number'] = $user['mobile_number'];
                
                // Generate new remember token for security (token rotation)
                $new_token = bin2hex(random_bytes(32));
                $update_stmt = $conn->prepare("UPDATE health_facility SET remember_token = ? WHERE id = ?");
                $update_stmt->bind_param("si", $new_token, $user['id']);
                $update_stmt->execute();
                
                // Update cookie with new token
                setcookie('remember_token', $new_token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 days, httponly
                
                $stmt->close();
                $update_stmt->close();
                $conn->close();
                
                return true; // Authentication successful via remember token
            } else {
                // Invalid or expired token, clear cookies and redirect
                setcookie('remember_user', '', time() - 3600, '/');
                setcookie('remember_token', '', time() - 3600, '/');
                header('Location: login.php');
                exit;
            }
        } else {
            // No session and no valid remember me cookie
            header('Location: login.php');
            exit;
        }
    }
    
    return true; // Authentication successful via existing session
}

// Auto-run authentication check when this file is included
if (!function_exists('session_status') || session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

checkAuthentication();
?>
