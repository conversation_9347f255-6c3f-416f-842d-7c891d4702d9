<?php
// Debug script to count columns in the INSERT statement

// From db.php - the actual INSERT statement columns
$db_columns = [
    'DateOfRegistration', 'DateOfBirth', 'AgeofChild', 'FamilySerialNumber', 'ChildNumber',
    'LastNameOfChild', 'NameOfChild', 'middlename_of_child', 'Sex', 'NameofMother', 'BirthdateofMother', 'Age',
    'Barangay', 'PurokStreetSitio', 'HouseNo', 'Address', 'PlaceofDelivery', 'NameofFacility', 'Attendant', 'TypeofDelivery',
    'BirthWeightInGrams', 'BirthWeightClassification', 'WastheChildReferredforNewbornScreening', 'DateReferredforNewbornScreening',
    'DateofConsultationNewbornScreening', 'TTStatusofMother', 'Dateassessed', 'WastheChildProtectedatBirth', 'DateofConsultationCPAB',
    'BCG', 'DateBCGwasgiven', 'DateofConsultationBCG', 'PENTAHIB1', 'DatePenta1wasgiven', 'DateofConsultationPENTAHIB1',
    'PENTAHIB2', 'DatePentahib2wasgiven', 'DateofConsultationPENTAHIB2', 'PENTAHIB3', 'DatePentahib3wasgiven', 'DateofConsultationPENTAHIB3',
    'OPV1', 'DateOPV1wasgiven', 'DateofConsultationOPV1v', 'OPV2', 'DateOPV2wasgiven', 'DateofConsultationOPV2',
    'OPV3', 'dateOPV3wasgiven', 'DateofConsultationOPV3', 'HEPAatBirth', 'TimingHEPAatBirth', 'DateHEPAatBirthwasgiven',
    'DateofConsultationHEPAatBirth', 'HEPAB1', 'dateHEPA1wasgiven', 'DateofConsultationHEPAB1',
    'HEPAB2', 'dateHEPA2wasgiven', 'DateofConsultationHEPAB2', 'HEPAB3', 'dateHEPA3WASGIVEN', 'DateofConsultationHEPAB3',
    'HEPAB4', 'dateHEPA4WASGIVEN', 'DateofConsultationHEPAB4', 'AMV19monthstobelow12months', 'DateAMV1WASGIVEN', 'DateofConsultationAMV1',
    'MMR12MOSTO15MOS', 'dateMMRWASGIVEN', 'DateofConsultationMMR',
    'FIC', 'dateFICWASGIVEN', 'DateofConsultationFIC', 'CIC', 'dateCICWASGIVEN', 'DateofConsultationCIC',
    'AMV2_16MOSTO5YRSOLD', 'dateAMV2WASGIVEN', 'DateofConsultationAMV2',
    'IMMUNIZATIONSTATUS', 'DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED',
    'FIRSTMONTH', 'SECONDMONTH', 'THIRDMONTH', 'FOURTHDMONTH', 'FIFTMONTH', 'SIXTHMONTH', 'date6MONTHS',
    'WASTHECHILDEXCLUSIVELY', 'DATEOFCONSULTATIONEXCLUSIVEBF',
    'TT1', 'DATEOFCONSULTTT1', 'TT2', 'DATEOFCONSULTTT2', 'TT3', 'DATEOFCONSULTTT3', 'TT4', 'DATEOFCONSULTTT4', 'TT5', 'DATEOFCONSULTT5',
    'PhoneNumber', 'approvedBy', 'dateOfExpiration'
];

echo "Total columns in db.php INSERT: " . count($db_columns) . "\n";
echo "Columns:\n";
foreach ($db_columns as $i => $col) {
    echo ($i + 1) . ". $col\n";
}

// Generate the correct bind_param string
$bind_types = str_repeat('s', count($db_columns));
echo "\nCorrect bind_param type string: \"$bind_types\"\n";
echo "Length: " . strlen($bind_types) . "\n";

// Generate the correct VALUES placeholder
$placeholders = str_repeat('?, ', count($db_columns) - 1) . '?';
echo "\nCorrect VALUES placeholders: ($placeholders)\n";
?>