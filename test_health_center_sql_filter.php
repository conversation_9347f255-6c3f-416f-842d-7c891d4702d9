<?php
// Test Health Center SQL Filter
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🏥 Health Center SQL Filter Test</h2>";

// Set up test session if needed
if (!isset($_SESSION['health_center'])) {
    $_SESSION['health_center'] = 'TEST_BARANGAY';
    $_SESSION['fullname'] = 'Test User';
    echo "<p style='color: orange;'>⚠️ Set test health center: " . $_SESSION['health_center'] . "</p>";
} else {
    echo "<p style='color: green;'>✅ Health center set: " . $_SESSION['health_center'] . "</p>";
}

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('<p style="color: red;">Connection failed: ' . mysqli_connect_error() . '</p>');
}

echo "<p style='color: green;'>✅ Database connected successfully</p>";

// Test 1: Check all barangays in database
echo "<h3>Test 1: Available Barangays in Database</h3>";
$barangay_query = "SELECT DISTINCT Barangay, COUNT(*) as record_count 
                   FROM nip_table 
                   WHERE (deleted IS NULL OR deleted = 0) 
                   GROUP BY Barangay 
                   ORDER BY record_count DESC";

$barangay_result = mysqli_query($conn, $barangay_query);

if ($barangay_result && mysqli_num_rows($barangay_result) > 0) {
    echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #2196f3; color: white;'>";
    echo "<th>Barangay</th><th>Record Count</th><th>Access Status</th>";
    echo "</tr>";
    
    $user_health_center = $_SESSION['health_center'];
    $total_accessible = 0;
    $total_restricted = 0;
    
    while ($row = mysqli_fetch_assoc($barangay_result)) {
        $is_accessible = ($row['Barangay'] === $user_health_center);
        $access_status = $is_accessible ? 
            "<span style='color: green; font-weight: bold;'>✅ ACCESSIBLE</span>" : 
            "<span style='color: red; font-weight: bold;'>❌ RESTRICTED</span>";
        
        $row_style = $is_accessible ? "background: #e8f5e8;" : "background: #ffebee;";
        
        echo "<tr style='$row_style'>";
        echo "<td><strong>" . htmlspecialchars($row['Barangay']) . "</strong></td>";
        echo "<td>" . $row['record_count'] . "</td>";
        echo "<td>" . $access_status . "</td>";
        echo "</tr>";
        
        if ($is_accessible) {
            $total_accessible += $row['record_count'];
        } else {
            $total_restricted += $row['record_count'];
        }
    }
    
    echo "<tr style='background: #f8f9fa; font-weight: bold;'>";
    echo "<td>TOTALS</td>";
    echo "<td>Accessible: $total_accessible | Restricted: $total_restricted</td>";
    echo "<td>Total: " . ($total_accessible + $total_restricted) . "</td>";
    echo "</tr>";
    echo "</table>";
    
} else {
    echo "<p style='color: orange;'>⚠️ No records found in database</p>";
}

// Test 2: Test the exact SQL query from filter_records.php
echo "<h3>Test 2: Testing Filter SQL Query</h3>";

$user_health_center = $_SESSION['health_center'];

// Test the exact query structure from filter_records.php
$sql = "SELECT id, DateOfRegistration, NameOfChild, middlename_of_child, LastNameOfChild, 
               DateOfBirth, Sex, NameofMother, Barangay, Address, PhoneNumber, FamilySerialNumber 
        FROM nip_table 
        WHERE (deleted IS NULL OR deleted = 0) AND Barangay = ? 
        LIMIT 100";

$params = [$user_health_center];
$types = "s";

echo "<p><strong>SQL Query:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($sql) . "</pre>";
echo "<p><strong>Parameters:</strong></p>";
echo "<ul>";
echo "<li>Health Center: " . htmlspecialchars($user_health_center) . "</li>";
echo "<li>Parameter Types: " . htmlspecialchars($types) . "</li>";
echo "</ul>";

$stmt = mysqli_prepare($conn, $sql);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $accessible_records = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Double-check that the record belongs to user's health center
        if ($row['Barangay'] === $user_health_center) {
            $accessible_records[] = $row;
        }
    }
    mysqli_stmt_close($stmt);
    
    echo "<p style='color: green;'>✅ Query executed successfully</p>";
    echo "<p><strong>Records returned:</strong> " . count($accessible_records) . "</p>";
    
    if (count($accessible_records) > 0) {
        echo "<h4>Sample Accessible Records:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #4caf50; color: white;'>";
        echo "<th>ID</th><th>Full Name</th><th>Birth Date</th><th>Barangay</th><th>Registration Date</th>";
        echo "</tr>";
        
        $sample_count = min(5, count($accessible_records));
        for ($i = 0; $i < $sample_count; $i++) {
            $record = $accessible_records[$i];
            $full_name = trim($record['NameOfChild'] . ' ' . $record['middlename_of_child'] . ' ' . $record['LastNameOfChild']);
            
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td><strong>" . htmlspecialchars($full_name) . "</strong></td>";
            echo "<td>" . $record['DateOfBirth'] . "</td>";
            echo "<td>" . htmlspecialchars($record['Barangay']) . "</td>";
            echo "<td>" . $record['DateOfRegistration'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (count($accessible_records) > 5) {
            echo "<p><em>... and " . (count($accessible_records) - 5) . " more records</em></p>";
        }
    } else {
        echo "<div style='background: #fff3e0; padding: 15px; border-radius: 5px; border-left: 4px solid #ff9800;'>";
        echo "<h4>⚠️ No Records Found</h4>";
        echo "<p>No records found for your health center: <strong>" . htmlspecialchars($user_health_center) . "</strong></p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No children are registered for this barangay</li>";
        echo "<li>All records for this barangay have been deleted</li>";
        echo "<li>The health center name doesn't match any barangay names</li>";
        echo "</ul>";
        echo "</div>";
    }
} else {
    echo "<p style='color: red;'>❌ Query preparation failed: " . mysqli_error($conn) . "</p>";
}

// Test 3: Test with additional search parameters
echo "<h3>Test 3: Testing with Search Parameters</h3>";

$test_first_name = "Juan";
$test_last_name = "Dela Cruz";

echo "<p>Testing search for: <strong>$test_first_name $test_last_name</strong> in health center: <strong>$user_health_center</strong></p>";

// Build query with additional parameters (like in the actual filter)
$sql_with_search = "SELECT id, DateOfRegistration, NameOfChild, middlename_of_child, LastNameOfChild, 
                           DateOfBirth, Sex, NameofMother, Barangay, Address, PhoneNumber, FamilySerialNumber 
                    FROM nip_table 
                    WHERE (deleted IS NULL OR deleted = 0) AND Barangay = ?";

$search_params = [$user_health_center];
$search_types = "s";

// Add search conditions
$sql_with_search .= " AND NameOfChild LIKE ?";
$search_params[] = "%" . $test_first_name . "%";
$search_types .= "s";

$sql_with_search .= " AND LastNameOfChild LIKE ?";
$search_params[] = "%" . $test_last_name . "%";
$search_types .= "s";

$sql_with_search .= " LIMIT 100";

echo "<p><strong>Search Query:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($sql_with_search) . "</pre>";

echo "<p><strong>Search Parameters:</strong></p>";
echo "<ul>";
foreach ($search_params as $index => $param) {
    echo "<li>Parameter " . ($index + 1) . ": " . htmlspecialchars($param) . "</li>";
}
echo "</ul>";

$search_stmt = mysqli_prepare($conn, $sql_with_search);
if ($search_stmt) {
    mysqli_stmt_bind_param($search_stmt, $search_types, ...$search_params);
    mysqli_stmt_execute($search_stmt);
    $search_result = mysqli_stmt_get_result($search_stmt);
    $search_count = mysqli_num_rows($search_result);
    
    echo "<p style='color: green;'>✅ Search query executed successfully</p>";
    echo "<p><strong>Search results:</strong> $search_count records found</p>";
    
    if ($search_count > 0) {
        echo "<h4>Search Results:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Full Name</th><th>Barangay</th><th>Birth Date</th></tr>";
        
        while ($row = mysqli_fetch_assoc($search_result)) {
            $full_name = trim($row['NameOfChild'] . ' ' . $row['middlename_of_child'] . ' ' . $row['LastNameOfChild']);
            
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($full_name) . "</td>";
            echo "<td>" . htmlspecialchars($row['Barangay']) . "</td>";
            echo "<td>" . $row['DateOfBirth'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    mysqli_stmt_close($search_stmt);
} else {
    echo "<p style='color: red;'>❌ Search query preparation failed: " . mysqli_error($conn) . "</p>";
}

// Summary
echo "<h3>📋 SQL Filter Test Summary</h3>";
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
echo "<h4>✅ Health Center SQL Filter Status</h4>";

echo "<h5>Implementation Details:</h5>";
echo "<ul>";
echo "<li><strong>SQL Filter:</strong> ✅ WHERE Barangay = ? condition added</li>";
echo "<li><strong>Parameter Binding:</strong> ✅ Health center as first parameter</li>";
echo "<li><strong>Session Check:</strong> ✅ Validates \$_SESSION['health_center']</li>";
echo "<li><strong>Double Validation:</strong> ✅ PHP-level check on returned records</li>";
echo "<li><strong>Search Integration:</strong> ✅ Works with additional search criteria</li>";
echo "</ul>";

echo "<h5>Security Features:</h5>";
echo "<ul>";
echo "<li>🔒 <strong>Database-Level Filtering:</strong> SQL WHERE clause prevents unauthorized data</li>";
echo "<li>🔒 <strong>Prepared Statements:</strong> Secure parameter binding</li>";
echo "<li>🔒 <strong>Session Validation:</strong> Access control at application level</li>";
echo "<li>🔒 <strong>Double-Check:</strong> Additional PHP validation</li>";
echo "</ul>";

echo "<h5>Current Configuration:</h5>";
echo "<ul>";
echo "<li><strong>Your Health Center:</strong> " . htmlspecialchars($user_health_center) . "</li>";
echo "<li><strong>SQL Condition:</strong> WHERE ... AND Barangay = '" . htmlspecialchars($user_health_center) . "'</li>";
echo "<li><strong>Access Level:</strong> Health Center Specific</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Test the Filter System</h3>";
echo "<p>";
echo "<a href='filter_records.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Test Filter Page</a>";
echo "<a href='nip.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📝 Registration Form</a>";
echo "</p>";

mysqli_close($conn);

echo "<hr>";
echo "<p><em>Health center SQL filter test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4, h5 {
    color: #343a40;
}

table {
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

th, td {
    padding: 8px 12px;
    text-align: left;
}

th {
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: rgba(0,0,0,0.02);
}

pre {
    font-size: 14px;
    line-height: 1.4;
    overflow-x: auto;
}

ul, ol {
    line-height: 1.6;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}
</style>
