<?php
// Database Error Fix Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Database Error Fix Tool</h2>";

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'national_immunization_program';

try {
    $conn = new mysqli($host, $username, $password, $database);
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    $conn->set_charset("utf8");
    echo "<p style='color: green;'>✅ Connected to database successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    exit;
}

// Fix 1: Ensure nip_table has proper structure
echo "<h3>1. 🔧 Fixing Main Table Structure</h3>";

$table_fixes = [
    "ALTER TABLE nip_table ADD COLUMN IF NOT EXISTS deleted TINYINT(1) DEFAULT 0",
    "ALTER TABLE nip_table ADD COLUMN IF NOT EXISTS deleted_reason TEXT",
    "ALTER TABLE nip_table ADD COLUMN IF NOT EXISTS deleted_date TIMESTAMP NULL",
    "ALTER TABLE nip_table ADD COLUMN IF NOT EXISTS deleted_by VARCHAR(255)",
    "ALTER TABLE nip_table MODIFY COLUMN middlename_of_child VARCHAR(255) DEFAULT ''",
    "ALTER TABLE nip_table MODIFY COLUMN PhoneNumber VARCHAR(20) DEFAULT ''",
    "CREATE INDEX IF NOT EXISTS idx_deleted ON nip_table(deleted)",
    "CREATE INDEX IF NOT EXISTS idx_name_dob ON nip_table(NameOfChild, LastNameOfChild, DateOfBirth)",
    "CREATE INDEX IF NOT EXISTS idx_family_serial ON nip_table(FamilySerialNumber)"
];

foreach ($table_fixes as $fix) {
    try {
        $conn->query($fix);
        echo "<p style='color: green;'>✅ Applied: " . substr($fix, 0, 50) . "...</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Skipped: " . substr($fix, 0, 50) . "... (may already exist)</p>";
    }
}

// Fix 2: Create support tables
echo "<h3>2. 🔧 Creating Support Tables</h3>";

$support_tables = [
    "duplicate_block_log" => "CREATE TABLE IF NOT EXISTS duplicate_block_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        child_name VARCHAR(255) NOT NULL,
        last_name VARCHAR(255) NOT NULL,
        middle_name VARCHAR(255),
        dob DATE NOT NULL,
        mother_name VARCHAR(255),
        blocked_reason TEXT NOT NULL,
        blocked_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        blocked_by VARCHAR(255),
        health_center VARCHAR(255),
        full_name VARCHAR(500),
        ip_address VARCHAR(45),
        user_agent TEXT,
        INDEX idx_blocked_date (blocked_date),
        INDEX idx_full_name (full_name),
        INDEX idx_dob (dob),
        INDEX idx_health_center (health_center)
    )",
    
    "user_action_logs" => "CREATE TABLE IF NOT EXISTS user_action_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        table_name VARCHAR(100),
        action_type VARCHAR(50),
        record_id INT,
        old_values JSON,
        new_values JSON,
        performed_by VARCHAR(255),
        performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        reason TEXT,
        INDEX idx_performed_at (performed_at),
        INDEX idx_action_type (action_type),
        INDEX idx_table_name (table_name),
        INDEX idx_user_id (user_id)
    )"
];

foreach ($support_tables as $table_name => $sql) {
    try {
        $conn->query($sql);
        echo "<p style='color: green;'>✅ Created/verified table: $table_name</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Failed to create $table_name: " . $e->getMessage() . "</p>";
    }
}

// Fix 3: Clean up data inconsistencies
echo "<h3>3. 🔧 Cleaning Data Inconsistencies</h3>";

$cleanup_queries = [
    "UPDATE nip_table SET deleted = 0 WHERE deleted IS NULL",
    "UPDATE nip_table SET middlename_of_child = '' WHERE middlename_of_child IS NULL",
    "UPDATE nip_table SET PhoneNumber = '' WHERE PhoneNumber IS NULL",
    "UPDATE nip_table SET Address = '' WHERE Address IS NULL",
    "UPDATE nip_table SET Barangay = '' WHERE Barangay IS NULL"
];

foreach ($cleanup_queries as $query) {
    try {
        $result = $conn->query($query);
        $affected = $conn->affected_rows;
        echo "<p style='color: green;'>✅ Cleaned data: $affected rows affected</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Cleanup failed: " . $e->getMessage() . "</p>";
    }
}

// Fix 4: Test duplicate checking functions
echo "<h3>4. 🔧 Testing Duplicate Checking Functions</h3>";

// Include the functions from db.php
include_once 'db.php';

try {
    // Test generateUniqueFamilySerial function
    if (function_exists('generateUniqueFamilySerial')) {
        $test_serial = generateUniqueFamilySerial($conn);
        echo "<p style='color: green;'>✅ generateUniqueFamilySerial() works: $test_serial</p>";
    } else {
        echo "<p style='color: red;'>❌ generateUniqueFamilySerial() function not found</p>";
    }
    
    // Test checkForDuplicates function
    if (function_exists('checkForDuplicates')) {
        $test_check = checkForDuplicates($conn, 'Test', 'Child', 'Middle', '2020-01-01', 'Test Mother', '', '', '');
        echo "<p style='color: green;'>✅ checkForDuplicates() works - Found: " . ($test_check['found'] ? 'Yes' : 'No') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ checkForDuplicates() function not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Function testing failed: " . $e->getMessage() . "</p>";
}

// Fix 5: Verify database permissions
echo "<h3>5. 🔧 Verifying Database Permissions</h3>";

$permission_tests = [
    "SELECT" => "SELECT COUNT(*) FROM nip_table LIMIT 1",
    "INSERT" => "INSERT INTO user_action_logs (action_type, performed_by, reason) VALUES ('TEST', 'SYSTEM', 'Permission test')",
    "UPDATE" => "UPDATE user_action_logs SET reason = 'Permission test completed' WHERE action_type = 'TEST' AND performed_by = 'SYSTEM'",
    "DELETE" => "DELETE FROM user_action_logs WHERE action_type = 'TEST' AND performed_by = 'SYSTEM'"
];

foreach ($permission_tests as $operation => $query) {
    try {
        $conn->query($query);
        echo "<p style='color: green;'>✅ $operation permission works</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ $operation permission failed: " . $e->getMessage() . "</p>";
    }
}

// Fix 6: Database optimization
echo "<h3>6. 🔧 Database Optimization</h3>";

$optimization_queries = [
    "OPTIMIZE TABLE nip_table",
    "ANALYZE TABLE nip_table"
];

foreach ($optimization_queries as $query) {
    try {
        $conn->query($query);
        echo "<p style='color: green;'>✅ Executed: $query</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Optimization skipped: " . $e->getMessage() . "</p>";
    }
}

// Fix 7: Create a test record to verify everything works
echo "<h3>7. 🔧 Testing Complete Registration Process</h3>";

try {
    // Test the complete registration process
    $test_data = [
        'DateOfRegistration' => date('Y-m-d'),
        'NameOfChild' => 'TEST_CHILD_' . time(),
        'LastNameOfChild' => 'TEST_LASTNAME',
        'middlename_of_child' => 'TEST_MIDDLE',
        'DateOfBirth' => '2020-01-01',
        'Sex' => 'MALE',
        'NameofMother' => 'TEST_MOTHER',
        'Barangay' => 'TEST_BARANGAY',
        'Address' => 'TEST_ADDRESS',
        'FamilySerialNumber' => 'TEST-' . time(),
        'PhoneNumber' => '09123456789'
    ];
    
    $columns = implode(', ', array_keys($test_data));
    $placeholders = str_repeat('?,', count($test_data) - 1) . '?';
    $sql = "INSERT INTO nip_table ($columns) VALUES ($placeholders)";
    
    $stmt = $conn->prepare($sql);
    if ($stmt) {
        $stmt->bind_param(str_repeat('s', count($test_data)), ...array_values($test_data));
        if ($stmt->execute()) {
            $test_id = $conn->insert_id;
            echo "<p style='color: green;'>✅ Test record created successfully (ID: $test_id)</p>";
            
            // Clean up test record
            $conn->query("DELETE FROM nip_table WHERE id = $test_id");
            echo "<p style='color: green;'>✅ Test record cleaned up</p>";
        } else {
            throw new Exception($stmt->error);
        }
        $stmt->close();
    } else {
        throw new Exception($conn->error);
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Registration test failed: " . $e->getMessage() . "</p>";
}

// Summary
echo "<hr><h3>8. 📋 Fix Summary</h3>";

// Get final statistics
try {
    $stats = [];
    
    $result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE deleted = 0");
    $stats['total_records'] = $result->fetch_assoc()['total'];
    
    $result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE middlename_of_child IS NOT NULL AND middlename_of_child != ''");
    $stats['records_with_middle_name'] = $result->fetch_assoc()['total'];
    
    $result = $conn->query("SELECT COUNT(*) as total FROM duplicate_block_log");
    $stats['blocked_attempts'] = $result->fetch_assoc()['total'];
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
    echo "<h4 style='color: #2e7d32;'>✅ Database Fixes Applied Successfully!</h4>";
    echo "<ul>";
    echo "<li><strong>Total Active Records:</strong> {$stats['total_records']}</li>";
    echo "<li><strong>Records with Middle Names:</strong> {$stats['records_with_middle_name']}</li>";
    echo "<li><strong>Blocked Duplicate Attempts:</strong> {$stats['blocked_attempts']}</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Could not get final statistics: " . $e->getMessage() . "</p>";
}

echo "<h3>🎯 Next Steps</h3>";
echo "<ol>";
echo "<li><strong>Test Registration:</strong> Try registering a child through the normal form</li>";
echo "<li><strong>Test Duplicates:</strong> Try registering the same child again to verify blocking works</li>";
echo "<li><strong>Monitor System:</strong> Check the duplicate_block_log table for any issues</li>";
echo "<li><strong>Regular Maintenance:</strong> Run this fix script periodically to maintain database health</li>";
echo "</ol>";

echo "<h3>🔗 Quick Links</h3>";
echo "<p>";
echo "<a href='database_health_check.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Health Check</a>";
echo "<a href='nip.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='test_middle_name_duplicate_check.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Test System</a>";
echo "<a href='duplicate_checker.php' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔧 Duplicate Manager</a>";
echo "</p>";

$conn->close();
echo "<hr>";
echo "<p><em>Database fixes completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}

ol, ul {
    line-height: 1.6;
}
</style>
