<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
  
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>Edit</title>
    <?php include 'style.php';?>
    <style>
       input {
    text-transform:uppercase;
   }
        label {
      font-weight:500 !important;
      font-size:17.7px !important;
    }
    .dropdown-content li>a, .dropdown-content li>span {
    font-size: 15.5px !important;
    color: black !important;
    display: block;
    line-height: 22px;
    padding: 10px 12px;
}
    .tab a.active {
    background-color: transparent;
    color: #1976d2   !important;
    font-weight: bold;
    
    background-color: #e3f2fd  !important; 
}
.tabs .indicator {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #1976d2 ;
    will-change: left, right;
}
  .tab a {
    color:black !important;
  }
    /* label color */
   .input-field label {
     color: #000 !important;
   }
   /* label focus color */
   .input-field input[type=text]:focus + label {
     color: #000;
   }
   /* label underline focus color */
   .input-field input[type=text]:focus {
     border-bottom: 1px solid #039be5  !important;
     box-shadow: 0 1px 0 0 #039be5 !important;
   }
   /* valid color */
   .input-field input[type=text]:focus {
    border-bottom: 1px solid #039be5  !important;
     box-shadow: 0 1px 0 0 #039be5 !important;
   }
   /* invalid color */
   .input-field input[type=text]:focus {
    border-bottom: 1px solid #039be5  !important;
    box-shadow: 0 1px 0 0 #039be5 !important;
   }
   .input-field input[type=date]:focus {
    border-bottom: 1px solid #039be5  !important;
    box-shadow: 0 1px 0 0 #039be5 !important;
   }
   .input-field input[type=number]:focus {
    border-bottom: 1px solid #039be5  !important;
    box-shadow: 0 1px 0 0 #039be5 !important;
   }
   
   /* icon prefix focus color */
   .input-field .prefix.active {
     color: #000;
   }
   input {
    border-radius: 6px !important;
    border:1px solid #ddd !important;
   }
   [type="radio"]:checked + span:after,
[type="radio"].with-gap:checked + span:before,
[type="radio"].with-gap:checked + span:after {
  border: 2px solid #212121  ;
}
[type="radio"]:checked + span:after,
[type="radio"].with-gap:checked + span:after {
  background-color: #212121 !important ;
}
    </style>
</head>
<body>
<?php include 'nav.php'; ?>

<?php 
include 'db.php'; // Ensure the DB connection is correct

// Retrieve the 'id', 'from', and 'to' parameters from the URL
$id = isset($_GET['id']) ? $_GET['id'] : '';
 
// Check if the 'id', 'from', and 'to' parameters are valid
if ($id) {
    // Prepare the SQL query to filter records by ID and date range
    $sql = "SELECT * FROM nip_table WHERE id = ? ";
    $stmt = $conn->prepare($sql);
    
    // Bind the parameters to the prepared statement
    // 'i' for integer (ID), 'ss' for strings (dates)
    $stmt->bind_param("s", $id); 
    
    // Execute the statement
    $stmt->execute();
    
    // Fetch results
    $result = $stmt->get_result();
    
    // Loop through the results and output them
    $row = $result->fetch_assoc();
    
    // Close the statement and connection
    $stmt->close();
} else {
    echo "Invalid parameters. Please provide valid 'id', 'from', and 'to' values.";
}
?>
<script>
  function updateRecord() {
  const id = document.getElementById('id').value;
  const DateOfRegistration = document.getElementById('DateOfRegistration').value;
  const DateOfBirth = document.getElementById('DateOfBirth').value;
  const AgeofChild = document.getElementById('AgeofChild').value;
  const FamilySerialNumber = document.getElementById('FamilySerialNumber').value;
  const ChildNumber = document.getElementById('ChildNumber').value;
  const lastnameOfChild = document.getElementById('lastnameOfChild').value;
  const NameOfChild = document.getElementById('NameOfChild').value;
  const middlename_of_child = document.getElementById('middlename_of_child').value;
  const Sex = document.getElementById('Sex').value;
  const NameofMother = document.getElementById('NameofMother').value;
  const BirthdateofMother = document.getElementById('BirthdateofMother').value;
  const AgeofMother = document.getElementById('AgeofMother').value;
  const Barangay = document.getElementById('Barangay').value;
  const PurokStreetSitio = document.getElementById('PurokStreetSitio').value;
  const HouseNo = document.getElementById('HouseNo').value;
  const Address = document.getElementById('Address').value;
  
  // Get selected Place of Delivery using querySelector
  const PlaceofDelivery = document.querySelector('input[name="PlaceofDelivery"]:checked').value;
  const NameofFacility = document.getElementById('NameofFacility').value;
  const Attendant = document.querySelector('input[name="Attendant"]:checked').value;
  const TypeofDelivery = document.querySelector('input[name="TypeofDelivery"]:checked').value;
  const BirthWeightInGrams = document.getElementById('BirthWeightInGrams').value;
  const BirthWeightClassification = document.getElementById('BirthWeightClassification').value;
  const WastheChildReferredforNewbornScreening = document.querySelector('input[name="WastheChildReferredforNewbornScreening"]:checked').value;
  const DateReferredforNewbornScreening = document.getElementById('DateReferredforNewbornScreening').value;
  const DateofConsultationNewbornScreening = document.getElementById('DateofConsultationNewbornScreening').value;
  const TTStatusofMother = document.getElementById('TTStatusofMother').value;
  const Dateassessed = document.getElementById('Dateassessed').value;
  const WastheChildProtectedatBirth = document.getElementById('WastheChildProtectedatBirth').value;
  const DateofConsultationCPAB = document.getElementById('DateofConsultationCPAB').value;
  const BCG = document.querySelector('input[name="BCG"]:checked').value;
  
  const DateBCGwasgiven = document.getElementById('DateBCGwasgiven').value;
  const DateofConsultationBCG = document.getElementById('DateofConsultationBCG').value;
  const PENTAHIB1 = document.getElementById('PENTAHIB1').value;
  const DatePenta1wasgiven = document.getElementById('DatePenta1wasgiven').value;
  const DateofConsultationPENTAHIB1 = document.getElementById('DateofConsultationPENTAHIB1').value;
  const PENTAHIB2 = document.getElementById('PENTAHIB2').value;
  const DatePentahib2wasgiven = document.getElementById('DatePentahib2wasgiven').value;
  const DateofConsultationPENTAHIB2 = document.getElementById('DateofConsultationPENTAHIB2').value;
  const PENTAHIB3 = document.getElementById('PENTAHIB3').value;
  const DatePentahib3wasgiven = document.getElementById('DatePentahib3wasgiven').value;
  const DateofConsultationPENTAHIB3 = document.getElementById('DateofConsultationPENTAHIB3').value;
  const OPV1 = document.getElementById('OPV1').value;
  const DateOPV1wasgiven = document.getElementById('DateOPV1wasgiven').value;
  const DateofConsultationOPV1v = document.getElementById('DateofConsultationOPV1v').value;
  const OPV2 = document.getElementById('OPV2').value;
  const DateOPV2wasgiven = document.getElementById('DateOPV2wasgiven').value;
  const DateofConsultationOPV2 = document.getElementById('DateofConsultationOPV2').value;
  const OPV3 = document.getElementById('OPV3').value;
  const dateOPV3wasgiven = document.getElementById('dateOPV3wasgiven').value;
  const DateofConsultationOPV3 = document.getElementById('DateofConsultationOPV3').value;
  const HEPAatBirth = document.getElementById('HEPAatBirth').value;
  const TimingHEPAatBirth = document.getElementById('TimingHEPAatBirth').value;
  const DateHEPAatBirthwasgiven = document.getElementById('DateHEPAatBirthwasgiven').value;
  const DateofConsultationHEPAatBirth = document.getElementById('DateofConsultationHEPAatBirth').value;
  const HEPAB1 = document.getElementById('HEPAB1').value;
  const dateHEPA1wasgiven = document.getElementById('dateHEPA1wasgiven').value;
  const DateofConsultationHEPAB1 = document.getElementById('DateofConsultationHEPAB1').value;
  const AMV19monthstobelow12months = document.getElementById('AMV19monthstobelow12months').value;
  const DateAMV1WASGIVEN = document.getElementById('DateAMV1WASGIVEN').value;
  const DateofConsultationAMV1 = document.getElementById('DateofConsultationAMV1').value;
  const MMR12MOSTO15MOS = document.getElementById('MMR12MOSTO15MOS').value;
  const dateMMRWASGIVEN = document.getElementById('dateMMRWASGIVEN').value;
  const DateofConsultationMMR = document.getElementById('DateofConsultationMMR').value;
  const AMV2_16MOSTO5YRSOLD = document.getElementById('AMV2_16MOSTO5YRSOLD').value;
  const dateAMV2WASGIVEN = document.getElementById('dateAMV2WASGIVEN').value;
  const DateofConsultationAMV2 = document.getElementById('DateofConsultationAMV2').value;
  const IMMUNIZATIONSTATUS = document.getElementById('IMMUNIZATIONSTATUS').value;
  const DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = document.getElementById('DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED').value;
  const FIRSTMONTH = document.querySelector('input[name="FIRSTMONTH"]:checked') ? document.querySelector('input[name="FIRSTMONTH"]:checked').value : '';
  const SECONDMONTH = document.querySelector('input[name="SECONDMONTH"]:checked') ? document.querySelector('input[name="SECONDMONTH"]:checked').value : '';
  const THIRDMONTH = document.querySelector('input[name="THIRDMONTH"]:checked') ? document.querySelector('input[name="THIRDMONTH"]:checked').value : '';
  const FOURTHMONTH = document.querySelector('input[name="FOURTHMONTH"]:checked') ? document.querySelector('input[name="FOURTHMONTH"]:checked').value : '';
  const FIFTHMONTH = document.querySelector('input[name="FIFTHMONTH"]:checked') ? document.querySelector('input[name="FIFTHMONTH"]:checked').value : '';
  const SIXTHMONTH = document.querySelector('input[name="SIXTHMONTH"]:checked') ? document.querySelector('input[name="SIXTHMONTH"]:checked').value : '';
  const date6MONTHS = document.getElementById('date6MONTHS').value;
  const WASTHECHILDEXCLUSIVELY = document.getElementById('WASTHECHILDEXCLUSIVELY').value;
  const DATEOFCONSULTATIONEXCLUSIVEBF = document.getElementById('DATEOFCONSULTATIONEXCLUSIVEBF').value;
  const TT1 = document.getElementById('TT1').value;
  const DATEOFCONSULTTT1 = document.getElementById('DATEOFCONSULTTT1').value;
  const TT2 = document.getElementById('TT2').value;
  const DATEOFCONSULTTT2 = document.getElementById('DATEOFCONSULTTT2').value;
  const TT3 = document.getElementById('TT3').value;
  const DATEOFCONSULTTT3 = document.getElementById('DATEOFCONSULTTT3').value;
  const TT4 = document.getElementById('TT4').value;
  const DATEOFCONSULTTT4 = document.getElementById('DATEOFCONSULTTT4').value;
  const TT5 = document.getElementById('TT5').value;
  const DATEOFCONSULTT5 = document.getElementById('DATEOFCONSULTT5').value;
  const PhoneNumber = document.getElementById('PhoneNumber').value;
      

  const formData = new FormData();
  formData.append('id', id);
  formData.append('DateOfRegistration', DateOfRegistration);
  formData.append('DateOfBirth', DateOfBirth);
  formData.append('AgeofChild', AgeofChild);
  formData.append('FamilySerialNumber', FamilySerialNumber);
  formData.append('ChildNumber', ChildNumber);
  formData.append('lastnameOfChild', lastnameOfChild);
  formData.append('NameOfChild', NameOfChild);
  formData.append('middlename_of_child', middlename_of_child);
  formData.append('Sex', Sex);
  formData.append('NameofMother', NameofMother);
  formData.append('BirthdateofMother', BirthdateofMother);
  formData.append('AgeofMother', AgeofMother);
  formData.append('Barangay', Barangay);
  formData.append('PurokStreetSitio', PurokStreetSitio);
  formData.append('HouseNo', HouseNo);
  formData.append('Address', Address);
  formData.append('PlaceofDelivery', PlaceofDelivery);
  formData.append('NameofFacility', NameofFacility);
  formData.append('Attendant', Attendant);
  formData.append('TypeofDelivery', TypeofDelivery);
  formData.append('BirthWeightInGrams', BirthWeightInGrams);
  formData.append('BirthWeightClassification', BirthWeightClassification);
  formData.append('WastheChildReferredforNewbornScreening', WastheChildReferredforNewbornScreening);
  formData.append('DateReferredforNewbornScreening', DateReferredforNewbornScreening);
  formData.append('DateofConsultationNewbornScreening', DateofConsultationNewbornScreening);
  formData.append('TTStatusofMother', TTStatusofMother);
  formData.append('Dateassessed', Dateassessed);
  formData.append('WastheChildProtectedatBirth', WastheChildProtectedatBirth);
  formData.append('DateofConsultationCPAB', DateofConsultationCPAB);
  formData.append('BCG', BCG);
  formData.append('DateBCGwasgiven', DateBCGwasgiven);
  formData.append('DateofConsultationBCG', DateofConsultationBCG);
  formData.append('PENTAHIB1', PENTAHIB1);
  formData.append('DatePenta1wasgiven', DatePenta1wasgiven);
  formData.append('DateofConsultationPENTAHIB1', DateofConsultationPENTAHIB1);
  formData.append('PENTAHIB2', PENTAHIB2);
  formData.append('DatePentahib2wasgiven', DatePentahib2wasgiven);
  formData.append('DateofConsultationPENTAHIB2', DateofConsultationPENTAHIB2);
  formData.append('PENTAHIB3', PENTAHIB3);
  formData.append('DatePentahib3wasgiven', DatePentahib3wasgiven);
  formData.append('DateofConsultationPENTAHIB3', DateofConsultationPENTAHIB3);
  formData.append('OPV1', OPV1);
  formData.append('DateOPV1wasgiven', DateOPV1wasgiven);
  formData.append('DateofConsultationOPV1v', DateofConsultationOPV1v);
  formData.append('OPV2', OPV2);
  formData.append('DateOPV2wasgiven', DateOPV2wasgiven);
  formData.append('DateofConsultationOPV2', DateofConsultationOPV2);
  formData.append('OPV3', OPV3);
  formData.append('dateOPV3wasgiven', dateOPV3wasgiven);
  formData.append('DateofConsultationOPV3', DateofConsultationOPV3);
  formData.append('HEPAatBirth', HEPAatBirth);
  formData.append('TimingHEPAatBirth', TimingHEPAatBirth);
  formData.append('DateHEPAatBirthwasgiven', DateHEPAatBirthwasgiven);
  formData.append('DateofConsultationHEPAatBirth', DateofConsultationHEPAatBirth);
  formData.append('HEPAB1', HEPAB1);
  formData.append('dateHEPA1wasgiven', dateHEPA1wasgiven);
  formData.append('DateofConsultationHEPAB1', DateofConsultationHEPAB1);
  formData.append('AMV19monthstobelow12months', AMV19monthstobelow12months);
  formData.append('DateAMV1WASGIVEN', DateAMV1WASGIVEN);
  formData.append('DateofConsultationAMV1', DateofConsultationAMV1);
  formData.append('MMR12MOSTO15MOS', MMR12MOSTO15MOS);
  formData.append('dateMMRWASGIVEN', dateMMRWASGIVEN);
  formData.append('DateofConsultationMMR', DateofConsultationMMR);
  formData.append('AMV2_16MOSTO5YRSOLD', AMV2_16MOSTO5YRSOLD);
  formData.append('dateAMV2WASGIVEN', dateAMV2WASGIVEN);
  formData.append('DateofConsultationAMV2', DateofConsultationAMV2);
  formData.append('IMMUNIZATIONSTATUS', IMMUNIZATIONSTATUS);
  formData.append('DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED', DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED);
  formData.append('FIRSTMONTH', FIRSTMONTH);
  formData.append('SECONDMONTH', SECONDMONTH);
  formData.append('THIRDMONTH', THIRDMONTH);
  formData.append('FOURTHMONTH', FOURTHMONTH);
  formData.append('FIFTHMONTH', FIFTHMONTH);
  formData.append('SIXTHMONTH', SIXTHMONTH);
  formData.append('date6MONTHS', date6MONTHS);
  formData.append('WASTHECHILDEXCLUSIVELY', WASTHECHILDEXCLUSIVELY);
  formData.append('DATEOFCONSULTATIONEXCLUSIVEBF', DATEOFCONSULTATIONEXCLUSIVEBF);
  formData.append('TT1', TT1);
  formData.append('DATEOFCONSULTTT1', DATEOFCONSULTTT1);
  formData.append('TT2', TT2);
  formData.append('DATEOFCONSULTTT2', DATEOFCONSULTTT2);
  formData.append('TT3', TT3);
  formData.append('DATEOFCONSULTTT3', DATEOFCONSULTTT3);
  formData.append('TT4', TT4);
  formData.append('DATEOFCONSULTTT4', DATEOFCONSULTTT4);
  formData.append('TT5', TT5);
  formData.append('DATEOFCONSULTT5', DATEOFCONSULTT5);
  formData.append('PhoneNumber', PhoneNumber);
   

  // Send AJAX request
  const xhr = new XMLHttpRequest();
  xhr.open('POST', 'update_record.php', true);

  xhr.onload = function () {
    if (xhr.status === 200) {
      M.toast({ html: xhr.responseText, classes: 'green' });
    } else {
      M.toast({ html: 'Error: ' + xhr.statusText, classes: 'red' });
    }
  };

  xhr.onerror = function () {
    M.toast({ html: 'Request failed', classes: 'red' });
  };

  xhr.send(formData);
}

</script>
 
<div class="row " >
  <form id="updateForm" class="col s12 m8 offset-m1 z-depth-0" method="POST" action="update_record.php">
    <h5 class="center " style="padding-top:0.5em;padding-bottom:0.5em;font-weight:500;font-size:20.5px;">Edit Record</h5>
    <hr>
    
    <div id="test2" class="col s12 m12">
      <h6 class="black-text" style="font-size:19.2px;font-weight:500;">A. Information of Child</h6>
  
      <div class="row">
        <!-- Hidden Field for Record ID -->
        <input type="hidden" name="id" id="id" value="<?php echo htmlspecialchars($_GET['id']); ?>" />
  
        <!-- Date of Registration -->
        <div class="input-field col s12 m4">
          <input name="DateOfRegistration" id="DateOfRegistration" type="date" value="<?php echo htmlspecialchars($row['DateOfRegistration']); ?>" readonly />
          <label for="DateOfRegistration">Date Of Registration</label>
        </div>
  
        <!-- Date of Birth -->
        <div class="input-field col s12 m4">
          <input name="DateOfBirth" id="DateOfBirth" type="date" value="<?php echo htmlspecialchars($row['DateOfBirth']); ?>" required />
          <label for="DateOfBirth">Date of Birth</label>
        </div>
  
        <!-- Age of Child -->
        <div class="input-field col s12 m4">
          <input name="AgeofChild" id="AgeofChild" type="text" placeholder="" value="<?php echo htmlspecialchars($row['AgeofChild']); ?>" readonly />
          <label for="AgeofChild">Age of Child</label>
        </div>
  
        <!-- Family Serial Number -->
        <div class="input-field col s12 m5">
          <input name="FamilySerialNumber" id="FamilySerialNumber" type="text" value="<?php echo htmlspecialchars($row['FamilySerialNumber']); ?>" readonly>
          <label for="FamilySerialNumber">Family Serial Number</label>
        </div>
  
        <!-- Child Number -->
        <div class="input-field col s12 m5">
          <input name="ChildNumber" id="ChildNumber" type="text" value="<?php echo htmlspecialchars($row['ChildNumber']); ?>" readonly />
          <label for="ChildNumber">Child Number</label>
        </div>
  
        <!-- Name of Child -->
        <div class="input-field col s12 m4">
          <input name="lastnameOfChild" id="lastnameOfChild" type="text" value="<?php echo htmlspecialchars($row['lastnameOfChild']); ?>" required />
          <label for="lastnameOfChild">Last Name Of Child</label>
        </div>
        <div class="input-field col s12 m4">
          <input name="NameOfChild" id="NameOfChild" type="text" value="<?php echo htmlspecialchars($row['NameOfChild']); ?>" required />
          <label for="NameOfChild">Name of Child</label>
        </div>
        <div class="input-field col s12 m4">
          <input name="middlename_of_child" id="middlename_of_child" type="text" value="<?php echo htmlspecialchars($row['middlename_of_child']); ?>" required />
          <label for="middlename_of_child">Middlename of Child</label>
        </div>
  
        <!-- Sex -->
        <div class="input-field col s12 m4">
          <select name="Sex" id="Sex" required>
            <option value="<?php echo htmlspecialchars($row['Sex']); ?>" selected disabled>Child is <?php echo htmlspecialchars($row['Sex']); ?></option>
            <option value="MALE">MALE</option>
            <option value="FEMALE">FEMALE</option>
          </select>
          <label for="Sex" style="font-size:14px !important;">Sex</label>
        </div>
      </div>
  
      <hr>
      <h6 class="black-text" style="font-size:19.2px;font-weight:500;">B. Information of Mother</h6>
  
      <div class="row">
        <!-- Name of Mother -->
        <div class="input-field col s12 m5">
          <input name="NameofMother" id="NameofMother" type="text" value="<?php echo htmlspecialchars($row['NameofMother']); ?>" required />
          <label for="NameofMother">Name of Mother</label>
        </div>
  
        <!-- Birthdate of Mother -->
        <div class="input-field col s12 m4">
          <input name="BirthdateofMother" id="BirthdateofMother" type="date" value="<?php echo htmlspecialchars($row['BirthdateofMother']); ?>" required />
 
          <label for="BirthdateofMother">Birthdate of Mother</label>
        </div>
        
        <div class="input-field col s12 m3">
          <input placeholder="" id="AgeofMother" type="text" value="<?php echo htmlspecialchars($row['Age']); ?>"  class="  ">
          <label for="AgeofMother">Age of Mother</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="Barangay" type="text"  value="<?php echo htmlspecialchars($row['Barangay']); ?>"  class="  " readonly disabled>
          <label for="Barangay">Barangay</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" id="PurokStreetSitio" type="text"  value="<?php echo htmlspecialchars($row['PurokStreetSitio']); ?>"   class="  ">
          <label for="PurokStreetSitio">Purok/Street/Sitio</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" id="HouseNo" type="text" value="<?php echo htmlspecialchars($row['HouseNo']); ?>"   class="  ">
          <label for="HouseNo">House No.</label>
        </div>
        <div class="input-field col s12 m12">
          <input placeholder="" id="Address" type="text" value="<?php echo htmlspecialchars($row['Address']); ?>"  class="  ">
          <label for="Address">Address</label>
        </div>
        </div>
        <hr>
        <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">Post Partum Information</h6>
    
        <div class="row">
        <div class="input-field  col s12 m12 z-depth-1 ">
          
          <label for="" class="" style="font-weight: 500 !important;">Place of Delivery</label><br>
         
          <p>
            <label>
              <input id="PlaceofDelivery_HOSPITAL"  name="PlaceofDelivery" type="radio" value="HOSPITAL" 
                     <?php echo ($row['PlaceofDelivery'] == 'HOSPITAL') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">HOSPITAL</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="PlaceofDelivery_BIRTHING_HOME" name="PlaceofDelivery" type="radio" value="BIRTHING-HOME" 
                     <?php echo ($row['PlaceofDelivery'] == 'BIRTHING-HOME') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">BIRTHING-HOME</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="PlaceofDelivery_HOME" name="PlaceofDelivery" type="radio" value="HOME" 
                     <?php echo ($row['PlaceofDelivery'] == 'HOME') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">HOME</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="PlaceofDelivery_OTHER" name="PlaceofDelivery" type="radio" value="OTHER" 
                     <?php echo ($row['PlaceofDelivery'] == 'OTHER') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">OTHER</span>
            </label>
          </p>
          
          
          
   

        </div>
        <div class="input-field col s12 m12">
          <input placeholder="" id="NameofFacility" name="NameofFacility" value="<?php echo htmlspecialchars($row['NameofFacility']); ?>" type="text"  class="  ">
          <label for="NameofFacility">Name of Facility</label>
        </div>
        <div class="input-field  col s12 m12 z-depth-1 ">
          
          <label for="" class="" style="font-weight: 500 !important;">Attendant</label><br>
         
          <p>
            <label>
              <input id="Attendant_DOCTOR" name="Attendant" value="DOCTOR" type="radio" 
                     <?php echo ($row['Attendant'] == 'DOCTOR') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">DOCTOR</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="Attendant_NURSE" name="Attendant" value="NURSE" type="radio" 
                     <?php echo ($row['Attendant'] == 'NURSE') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">NURSE</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="Attendant_MIDWIFE" name="Attendant" value="MIDWIFE" type="radio" 
                     <?php echo ($row['Attendant'] == 'MIDWIFE') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">MIDWIFE</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="Attendant_HILOT" name="Attendant" value="HILOT" type="radio" 
                     <?php echo ($row['Attendant'] == 'HILOT') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">HILOT</span>
            </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          
            <label>
              <input id="Attendant_OTHER" name="Attendant" value="OTHER" type="radio" 
                     <?php echo ($row['Attendant'] == 'OTHER') ? 'checked' : ''; ?> />
              <span style="font-weight: 500;">OTHER</span>
            </label>
          </p>
          
   

        </div>
       

        <div class="input-field  col s12 m12 z-depth-1 ">
          
          <label for="" class="" style="font-weight: 500 !important;">Type of Delivery</label><br>
         
          <p >
      <label>
        <input id="TypeofDelivery_NSD" name="TypeofDelivery" value="NSD" type="radio"  <?php echo ($row['TypeofDelivery'] == 'NSD') ? 'checked' : ''; ?>  />
        <span style="font-weight: 500;">NSD</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input id="TypeofDelivery_CS" name="TypeofDelivery" value="CS" type="radio"  <?php echo ($row['TypeofDelivery'] == 'CS') ? 'checked' : ''; ?>  />
        <span style="font-weight: 500;">CS</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input id="TypeofDelivery_OTHERS" name="TypeofDelivery" value="OTHERS" type="radio"  <?php echo ($row['TypeofDelivery'] == 'OTHERS') ? 'checked' : ''; ?>  />
        <span style="font-weight: 500;">OTHERS</span>
      </label>
       
     
    </p>
   

        </div>
         
   
      <div class="row"  >
        <div class="input-field col s12 m6">
          <input placeholder="" name="BirthWeightInGrams" id="BirthWeightInGrams" type="text" value="<?php echo htmlspecialchars($row['BirthWeightInGrams']); ?>"   class="  ">
          <label for="BirthWeightInGrams">Birth Weight(In Grams)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="BirthWeightClassification" type="text" value="<?php echo htmlspecialchars($row['BirthWeightClassification']); ?>"   class="  ">
          <label for="BirthWeightClassification">BirthWeight Classification</label>
        </div>
        </div>
        <hr>
        <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">NB Screening</h6>

      <div class="input-field  col s12 m12 z-depth-1 ">
          <label for="" class="" style="font-weight: 500 !important;">Was the Child Referred for Newborn Screening?</label><br>
         
          <p >
      <label>
        <input id="WastheChildReferredforNewbornScreeningYES" name="WastheChildReferredforNewbornScreening" type="radio" value="YES" <?php echo ($row['WastheChildReferredforNewbornScreening'] == 'YES') ? 'checked' : ''; ?> />
        <span style="font-weight: 500;">YES</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input id="WastheChildReferredforNewbornScreeningNO" name="WastheChildReferredforNewbornScreening" type="radio" value="NO" <?php echo ($row['WastheChildReferredforNewbornScreening'] == 'NO') ? 'checked' : ''; ?> />
        <span style="font-weight: 500;">NO</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
    </p>
        </div>


        <div class="row"  >
        <div class="input-field col s12 m6">
          <input   id="DateReferredforNewbornScreening" type="date" value="<?php echo htmlspecialchars($row['DateReferredforNewbornScreening']); ?>"   class="  ">
          <label for="DateReferredforNewbornScreening">Date Referred for Newborn Screening</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DateofConsultationNewbornScreening" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationNewbornScreening']); ?>"   class="  ">
          <label for="DateofConsultationNewbornScreening">Date of Consultation(Newborn Screening)</label>
        </div>
        </div>



        <hr>
        <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">Child Protected at Birth?</h6>

      <div class="input-field  col s12 m6  ">
        
        <select name="TTStatusofMother" id="TTStatusofMother" >
          <option value="<?php echo htmlspecialchars($row['TTStatusofMother']); ?>" disabled selected><?php echo htmlspecialchars($row['TTStatusofMother']); ?></option>
          <option value="TT1">TT1</option>
          <option value="TT2 LESS THAN 1 MONTH">TT2 LESS THAN 1 MONTH</option>
          <option value="TT2 MORE THAN 1 MONTH">TT2 MORE THAN 1 MONTH</option>
          <option value="TT3">TT3</option>
          <option value="TT4">TT4</option>
          <option value="TT5">TT5</option>
          <option value="TT5 PLUS">TT5 PLUS</option>
          
        </select>
        <label for="TTStatusofMother" style="font-size:14px !important;">TT Status of Mother</label>
        </div>


        <div class="row"  >
        <div class="input-field col s12 m6">
          <input placeholder="" id="Dateassessed" type="date" value="<?php echo htmlspecialchars($row['Dateassessed']); ?>"   class="  ">
          <label for="Dateassessed">Date Assessed</label>
        </div>
        <div class="input-field col s12 m6">
        
          <select name="WastheChildProtectedatBirth" id="WastheChildProtectedatBirth" >
            <option disabled selected><?php echo htmlspecialchars($row['WastheChildProtectedatBirth']); ?></option>
            <option value="YES">YES</option>
            <option value="NO">NO</option>
            
            
          </select>
          <label for="WastheChildProtectedatBirth" style="font-size:14px !important;">Was the Child Protected at Birth?</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DateofConsultationCPAB" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationCPAB']); ?>"   class="  ">
          <label for="DateofConsultationCPAB">Date of Consultation(CPAB)</label>
        </div>
        </div>
        </div>



        </div>


        <div id="test1" class="col s12"> 
          
          <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">Antigens</h6>
          <div class="row " >
            
            <div class="input-field col s12 m12">
              
          <div class="input-field  col s12 m12 z-depth-1 ">
          <label for="" class="" style="font-weight: 500 !important;">BCG</label><br>
          <input type="hidden" name="BCG" value="NO"> 

          <p >
      <label>
        <input name="BCG" id="BCGyes" value="YES" type="radio" <?php echo ($row['BCG'] == 'YES') ? 'checked' : ''; ?> />
        <span style="font-weight: 500;">YES</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="BCG" id="BCGno" value="NO" type="radio" <?php echo ($row['BCG'] == 'NO') ? 'checked' : ''; ?> />
        <span style="font-weight: 500;">NO</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
    </p>
        </div>
        </div>

        <div class="input-field col s12 m6">
          <input placeholder="" id="DateBCGwasgiven" type="date" value="<?php echo htmlspecialchars($row['DateBCGwasgiven']); ?>"   class="  ">
          <label for="DateBCGwasgiven">Date BCG was given</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" name="DateofConsultationBCG" id="DateofConsultationBCG" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationBCG']); ?>"   class="  ">
          <label for="DateofConsultationBCG">Date of Consultation(BCG)</label>
        </div>

        </div>
        <hr>


         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">PENTA-HIB Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" id="PENTAHIB1" type="text" value="<?php echo htmlspecialchars($row['PENTAHIB1']); ?>"   class="  ">
          <label for="PENTAHIB1">PENTA-HIB1</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DatePenta1wasgiven" type="date" value="<?php echo htmlspecialchars($row['DatePenta1wasgiven']); ?>"   class="  ">
          <label for="DatePenta1wasgiven">Date PENTA-HIB1 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input   placeholder="" id="DateofConsultationPENTAHIB1" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPENTAHIB1']); ?>"   class="   ">
          <label for="DateofConsultationPENTAHIB1">Date of Consultation (PENTA-HIB1)</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" id="PENTAHIB2" type="text" value="<?php echo htmlspecialchars($row['PENTAHIB2']); ?>"   class="  ">
          <label for="PENTAHIB2">PENTA-HIB2</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DatePentahib2wasgiven" type="date" value="<?php echo htmlspecialchars($row['DatePentahib2wasgiven']); ?>"   class="  ">
          <label for="DatePentahib2wasgiven">Date Penta-HIB2 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateofConsultationPENTAHIB2" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPENTAHIB2']); ?>"   class="  ">
          <label for="DateofConsultationPENTAHIB2">Date of Consultation (PENTA-HIB2)</label>
        </div>

        
        <div class="input-field col s12 m2">
          <input placeholder="" id="PENTAHIB3" type="text" value="<?php echo htmlspecialchars($row['PENTAHIB3']); ?>"   class="  ">
          <label for="PENTAHIB3">PENTA-HIB3</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DatePentahib3wasgiven" type="date" value="<?php echo htmlspecialchars($row['DatePentahib3wasgiven']); ?>"   class="  ">
          <label for="DatePentahib3wasgiven">Date PENTA-HIB3 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateofConsultationPENTAHIB3" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPENTAHIB3']); ?>"   class="  ">
          <label for="DateofConsultationPENTAHIB3">Date of Consultation (PENTA-HIB3)</label>
        </div>












         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">OPV Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" id="OPV1" type="text" value="<?php echo htmlspecialchars($row['OPV1']); ?>"   class="  ">
          <label for="OPV1">OPV1</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateOPV1wasgiven" type="date" value="<?php echo htmlspecialchars($row['DateOPV1wasgiven']); ?>"   class="  ">
          <label for="DateOPV1wasgiven">Date OPV1 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateofConsultationOPV1v" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationOPV1v']); ?>"   class="  ">
          <label for="DateofConsultationOPV1v">Date of Consultation (OPV1)</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" id="OPV2" type="text" value="<?php echo htmlspecialchars($row['OPV2']); ?>"   class="  ">
          <label for="OPV2">OPV2</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateOPV2wasgiven" type="date" value="<?php echo htmlspecialchars($row['DateOPV2wasgiven']); ?>"   class="  ">
          <label for="DateOPV2wasgiven">Date OPV2 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateofConsultationOPV2" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationOPV2']); ?>"   class="  ">
          <label for="DateofConsultationOPV2">Date of Consultation (OPV2)</label>
        </div>

        
        <div class="input-field col s12 m2">
          <input placeholder="" id="OPV3" type="text" value="<?php echo htmlspecialchars($row['OPV3']); ?>"   class="  ">
          <label for="OPV3">OPV3</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="dateOPV3wasgiven" type="date" value="<?php echo htmlspecialchars($row['dateOPV3wasgiven']); ?>"   class="  ">
          <label for="dateOPV3wasgiven">Date OPV3 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="DateofConsultationOPV3" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationOPV3']); ?>"   class="  ">
          <label for="DateofConsultationOPV3">Date of Consultation (OPV3)</label>
        </div>
      
<!-- ----------------------------------- -->
         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">HEPATITIS Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" id="HEPAatBirth" type="text" value="<?php echo htmlspecialchars($row['HEPAatBirth']); ?>"   class="  ">
          <label for="HEPAatBirth">HEPA at Birth</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" id="TimingHEPAatBirth" type="text" value="<?php echo htmlspecialchars($row['TimingHEPAatBirth']); ?>"   class="  ">
          <label for="TimingHEPAatBirth">Timing HEPA at Birth</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" id="DateHEPAatBirthwasgiven" type="date" value="<?php echo htmlspecialchars($row['DateHEPAatBirthwasgiven']); ?>"   class="  ">
          <label for="DateHEPAatBirthwasgiven">Date HEPA at Birth was given</label>
        </div>


        <div class="input-field col s12 m4">
          <input placeholder="" id="DateofConsultationHEPAatBirth" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationHEPAatBirth']); ?>"   class="  ">
          <label for="DateofConsultationHEPAatBirth">Date of Consultation(HEPA at Birth)</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" id="HEPAB1" type="text" value="<?php echo htmlspecialchars($row['HEPAB1']); ?>"   class="  ">
          <label for="HEPAB1">HEPA B1</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" id="dateHEPA1wasgiven" type="date" value="<?php echo htmlspecialchars($row['dateHEPA1wasgiven']); ?>"   class="  ">
          <label for="dateHEPA1wasgiven">Date HEPA1 was given</label>
        </div>

        
        <div class="input-field col s12 m4">
          <input placeholder="" id="DateofConsultationHEPAB1" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationHEPAB1']); ?>"   class="  ">
          <label for="DateofConsultationHEPAB1">Date of Consultation (HEPA B1)</label>
        </div>
        
       
          <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">MCV Antigen</h6>
          
          <div class="input-field col s12 m4">
          <input placeholder="" id="AMV19monthstobelow12months" type="text" value="<?php echo htmlspecialchars($row['AMV19monthstobelow12months']); ?>"   class="  ">
          <label for="AMV19monthstobelow12months">MCV 1 (9months to below 12 months)</label>
        </div>

        <div class="input-field col s12 m4">
          <input   id="DateAMV1WASGIVEN" name="DateAMV1WASGIVEN" value="<?php echo htmlspecialchars($row['DateAMV1WASGIVEN']); ?>" type="date"  >
          <label for="DateAMV1WASGIVEN">DATE MCV 1 WAS GIVEN</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="DateofConsultationAMV1" name="DateofConsultationAMV1" value="<?php echo htmlspecialchars($row['DateofConsultationAMV1']); ?>" type="date"  >
          <label for="DateofConsultationAMV1">DATE OF CONSULTATON (MCV 1)</label>
        </div> 
        <div class="input-field col s12 m4">
          <input placeholder=""   id="MMR12MOSTO15MOS" name="MMR12MOSTO15MOS" value="<?php echo htmlspecialchars($row['MMR12MOSTO15MOS']); ?>" type="text"  >
          <label for="MMR12MOSTO15MOS">MCV2(12 MOS. TO 15 MOS.)</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="dateMMRWASGIVEN" name="dateMMRWASGIVEN" value="<?php echo htmlspecialchars($row['dateMMRWASGIVEN']); ?>" type="date"  >
          <label for="dateMMRWASGIVEN">DATE MCV2 WAS GIVEN</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="DateofConsultationMMR" name="DateofConsultationMMR" value="<?php echo htmlspecialchars($row['DateofConsultationMMR']); ?>" type="date"  >
          <label for="DateofConsultationMMR">DATE OF CONSULTATION (MCV2)</label>
        </div> 
        
        <div class="input-field col s12 m4">
          <input  placeholder="" id="AMV2_16MOSTO5YRSOLD" name="AMV2_16MOSTO5YRSOLD" value="<?php echo htmlspecialchars($row['AMV2_16MOSTO5YRSOLD']); ?>" type="text"  >
          <label for="AMV2_16MOSTO5YRSOLD">AMV 2(16 MOS. TO 5 YRS. OLD)</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="dateAMV2WASGIVEN" name="dateAMV2WASGIVEN" value="<?php echo htmlspecialchars($row['dateAMV2WASGIVEN']); ?>" type="date"  >
          <label for="dateAMV2WASGIVEN">DATE AMV2 WAS GIVEN</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="DateofConsultationAMV2" name="DateofConsultationAMV2" value="<?php echo htmlspecialchars($row['DateofConsultationAMV2']); ?>" type="date"  >
          <label for="DateofConsultationAMV2">DATE OF CONSULTATION (AMV2)</label>
        </div> 
       
        <div class="input-field col s12 m4">
          <input placeholder=""  id="IMMUNIZATIONSTATUS" name="IMMUNIZATIONSTATUS" value="<?php echo htmlspecialchars($row['IMMUNIZATIONSTATUS']); ?>" type="text"  >
          <label for="IMMUNIZATIONSTATUS">IMMUNIZATION STATUS</label>
        </div> 
        <div class="input-field col s12 m8">
          <input   id="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" name="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" value="<?php echo htmlspecialchars($row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED']); ?>" type="date"  >
          <label  for="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED">DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED</label>
        </div> 

      </div>
      <div >

      </div>



 
        <div id="test3" class="col s12"> 
          
          <div class="row " >
            
            <div class="input-field col s12 m12">
              
              <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">Exclusive Breastfeeding</h6>
          <div class="input-field  col s12 m12 z-depth-1 ">
            <label for="" class="" style="font-weight: 500 !important;">CHILD WAS EXCLUSIVELY BREASTFED (PUT A CHECK)</label><br>
            <p>
              <label>
                <input id="CHILD1" name="FIRSTMONTH" value="FIRST MONTH" type="checkbox" class="filled-in" 
                <?php echo ($row['FIRSTMONTH'] == 'FIRST MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">FIRST MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD2" name="SECONDMONTH" value="SECOND MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['SECONDMONTH'] == 'SECOND MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">SECOND MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD3" name="THIRDMONTH" value="THIRD MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['THIRDMONTH'] == 'THIRD MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">THIRD MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD4" name="FOURTHMONTH" value="FOURTH MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['FOURTHDMONTH'] == 'FOURTH MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">FOURTH MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD5" name="FIFTHMONTH" value="FIFTH MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['FIFTMONTH'] == 'FIFTH MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">FIFTH MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD6" name="SIXTHMONTH" value="SIXTH MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['SIXTHMONTH'] == 'SIXTH MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">SIXTH MONTH</span>
              </label>
            </p>
            
        </div>
        </div>
        
        <div class="input-field col s12 m5">
          <input placeholder="" id="date6MONTHS" type="date" value="<?php echo htmlspecialchars($row['date6MONTHS']); ?>"   class="  ">
          <label for="date6MONTHS">DATE 6 MONTHS</label>
        </div>
        <div class="input-field col s12 m7">
          <input placeholder="" id="WASTHECHILDEXCLUSIVELY" type="text" value="<?php echo htmlspecialchars($row['WASTHECHILDEXCLUSIVELY']); ?>"   class="  ">
          <label for="WASTHECHILDEXCLUSIVELY">WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?</label>
        </div>
        <div class="input-field col s12 m12">
          <input placeholder="" id="DATEOFCONSULTATIONEXCLUSIVEBF" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTATIONEXCLUSIVEBF']); ?>"   class="  ">
          <label for="DATEOFCONSULTATIONEXCLUSIVEBF">DATE OF CONSULTATION (EXCLUSIVE)</label>
        </div>

        </div>
        <hr>
        
         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">NON-PREGNANT GIVEN TETANUS TOXOID IMMUNIZATION</h6>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT1" type="text" value="<?php echo htmlspecialchars($row['TT1']); ?>"   class="  ">
          <label for="TT1">TD1</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT1" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT1']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT1">DATE OF CONSULT (TT1)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT2" type="text" value="<?php echo htmlspecialchars($row['TT2']); ?>"   class="  ">
          <label for="TT2">TT2</label>
        </div>


        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT2" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT2']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT2">DATE OF CONSULT (TT2)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT3" type="text" value="<?php echo htmlspecialchars($row['TT3']); ?>"   class="  ">
          <label for="TT3">TT3</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT3" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT3']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT3">DATE OF CONSULT (TT3)</label>
        </div>

        
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT4" type="text" value="<?php echo htmlspecialchars($row['TT4']); ?>"   class="  ">
          <label for="TT4">TT4</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT4" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT4']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT4">DATE OF CONSULT (TT4)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT5" type="text" value="<?php echo htmlspecialchars($row['TT5']); ?>"   class="  ">
          <label for="TT5">TT5</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTT5" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTT5']); ?>"   class="  ">
          <label for="DATEOFCONSULTT5">DATE OF CONSULT (TT5)</label>
        </div>
        <div class="input-field col s12 m12">
          <input placeholder="" id="PhoneNumber" type="text" value="<?php echo htmlspecialchars($row['PhoneNumber']); ?>"   class="  ">
          <label for="PhoneNumber">Phone Number</label>
        </div>


 
   
  
        
        

      </div>
      <div >

      </div>



 



      <div class="col s12 ">
        <button type="button" style="font-weight:500;" onclick="updateRecord()" class="  btn blue  darken-3">Update Record</button>
      </div>
           <div class="row">
       <div class="col s12 m12">
         <ul class="tabs   lighten-4">
           <li class="tab col s5"><a class="blue-text" href="#test2"><div class="chip blue lighten-2 white-text">1</div>Personal Information</a></li>
           <li class="tab col s3"><a class="blue-text" href="#test1"><div class="chip blue lighten-2 white-text">2</div>Antigens</a></li>
           <li class="tab col s4"><a class="blue-text" href="#test3"><div class="chip blue lighten-2 white-text">3</div>Breastfeeding</a></li>
        
         </ul>
       </div>
     
   
   
     
  </div>

    

    </div>
       
      
       
      </div>
    </form>
  </div>
        <script>
         $(document).ready(function(){
    $('.tabs').tabs();
  });
        </script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
      var elems = document.querySelectorAll('select');
      M.FormSelect.init(elems);
  });
</script>

 <script>
  document.getElementById('DateOfBirth').addEventListener('change', function () {
    var dob = new Date(this.value);
    var today = new Date();
    var age = today.getFullYear() - dob.getFullYear();
    var m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
    }
    document.getElementById('AgeofChild').value = age;
});

 </script>
 <script>
  document.getElementById('BirthdateofMother').addEventListener('change', function () {
    var dob = new Date(this.value);
    var today = new Date();
    var age = today.getFullYear() - dob.getFullYear();
    var m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
    }
    document.getElementById('AgeofMother').value = age;
});

 </script>
</body>
</html>


 
  