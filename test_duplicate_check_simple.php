<?php
// Simple Duplicate Check Test - First Name, Last Name, Birth Date
session_start();
include 'db.php';

// Set up session for testing
if (!isset($_SESSION['health_center'])) {
    $_SESSION['health_center'] = 'TEST_CENTER';
    $_SESSION['fullname'] = 'Test User';
}

echo "<h2>🧪 Simple Duplicate Check Test</h2>";
echo "<p>Testing duplicate detection using <strong>First Name + Last Name + Birth Date</strong></p>";

// Test data
$testCases = [
    [
        'name' => 'Original Registration',
        'firstName' => 'Juan',
        'lastName' => '<PERSON>a Cruz',
        'birthDate' => '2020-01-15',
        'expected' => 'Should Pass'
    ],
    [
        'name' => 'Exact Duplicate',
        'firstName' => 'Juan',
        'lastName' => 'Dela Cruz',
        'birthDate' => '2020-01-15',
        'expected' => 'Should Be Blocked'
    ],
    [
        'name' => 'Different First Name',
        'firstName' => 'Maria',
        'lastName' => '<PERSON><PERSON>',
        'birthDate' => '2020-01-15',
        'expected' => 'Should Pass'
    ],
    [
        'name' => 'Different Last Name',
        'firstName' => 'Juan',
        'lastName' => '<PERSON>',
        'birthDate' => '2020-01-15',
        'expected' => 'Should Pass'
    ],
    [
        'name' => 'Different Birth Date',
        'firstName' => 'Juan',
        'lastName' => 'Dela Cruz',
        'birthDate' => '2020-02-15',
        'expected' => 'Should Pass'
    ]
];

// Function to test duplicate check
function testDuplicateCheck($conn, $testCase) {
    echo "<h3>🎯 Test: {$testCase['name']}</h3>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Input Data:</h4>";
    echo "<ul>";
    echo "<li><strong>First Name:</strong> {$testCase['firstName']}</li>";
    echo "<li><strong>Last Name:</strong> {$testCase['lastName']}</li>";
    echo "<li><strong>Birth Date:</strong> {$testCase['birthDate']}</li>";
    echo "<li><strong>Expected Result:</strong> {$testCase['expected']}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test the duplicate check function
    $duplicateCheck = checkDuplicateChild($conn, $testCase['firstName'], $testCase['lastName'], $testCase['birthDate']);
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Test Results:</h4>";
    echo "<p><strong>Duplicate Found:</strong> " . ($duplicateCheck['found'] ? 'YES' : 'NO') . "</p>";
    
    if ($duplicateCheck['found']) {
        echo "<p><strong>Message:</strong> " . htmlspecialchars($duplicateCheck['message']) . "</p>";
    }
    
    if (!empty($duplicateCheck['message']) && !$duplicateCheck['found']) {
        echo "<p><strong>Warning:</strong> " . htmlspecialchars($duplicateCheck['message']) . "</p>";
    }
    echo "</div>";
    
    // Determine if test passed
    $shouldBeBlocked = strpos($testCase['expected'], 'Blocked') !== false;
    $wasBlocked = $duplicateCheck['found'];
    $testPassed = ($shouldBeBlocked && $wasBlocked) || (!$shouldBeBlocked && !$wasBlocked);
    
    if ($testPassed) {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #4caf50;'>";
        echo "<h4 style='color: #2e7d32;'>✅ TEST PASSED</h4>";
        echo "<p>Result matches expectation</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #f44336;'>";
        echo "<h4 style='color: #c62828;'>❌ TEST FAILED</h4>";
        echo "<p>Result does not match expectation</p>";
        echo "</div>";
    }
    
    return $testPassed;
}

// Run tests
$passedTests = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testCase) {
    echo "<hr>";
    if (testDuplicateCheck($conn, $testCase)) {
        $passedTests++;
    }
}

// Summary
echo "<hr>";
echo "<h3>📊 Test Summary</h3>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; background: white;'>";
echo "<tr style='background: #2196f3; color: white;'>";
echo "<th>Test Case</th><th>First Name</th><th>Last Name</th><th>Birth Date</th><th>Expected</th><th>Status</th>";
echo "</tr>";

foreach ($testCases as $testCase) {
    $duplicateCheck = checkDuplicateChild($conn, $testCase['firstName'], $testCase['lastName'], $testCase['birthDate']);
    $shouldBeBlocked = strpos($testCase['expected'], 'Blocked') !== false;
    $wasBlocked = $duplicateCheck['found'];
    $testPassed = ($shouldBeBlocked && $wasBlocked) || (!$shouldBeBlocked && !$wasBlocked);
    
    $statusColor = $testPassed ? 'green' : 'red';
    $statusIcon = $testPassed ? '✅ PASS' : '❌ FAIL';
    
    echo "<tr>";
    echo "<td>{$testCase['name']}</td>";
    echo "<td>{$testCase['firstName']}</td>";
    echo "<td>{$testCase['lastName']}</td>";
    echo "<td>{$testCase['birthDate']}</td>";
    echo "<td>{$testCase['expected']}</td>";
    echo "<td style='color: {$statusColor};'>{$statusIcon}</td>";
    echo "</tr>";
}

echo "</table>";

echo "<div style='margin-top: 20px;'>";
echo "<h4>Overall Results:</h4>";
echo "<p><strong>Tests Passed:</strong> $passedTests / $totalTests</p>";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>";

if ($passedTests == $totalTests) {
    echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Duplicate check is working correctly.</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please review the duplicate check logic.</p>";
}
echo "</div>";
echo "</div>";

// Database Statistics
echo "<hr>";
echo "<h3>📈 Database Statistics</h3>";
try {
    // Total records
    $total_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE (deleted IS NULL OR deleted = 0)");
    $total_row = $total_result->fetch_assoc();
    echo "<p><strong>Total Active Records:</strong> {$total_row['total']}</p>";
    
    // Check if duplicate_block_log table exists and get blocked attempts
    $table_check = $conn->query("SHOW TABLES LIKE 'duplicate_block_log'");
    if ($table_check->num_rows > 0) {
        $blocked_result = $conn->query("SELECT COUNT(*) as total FROM duplicate_block_log");
        $blocked_row = $blocked_result->fetch_assoc();
        echo "<p><strong>Blocked Duplicate Attempts:</strong> {$blocked_row['total']}</p>";
        
        // Show recent blocked attempts
        $recent_blocked = $conn->query("SELECT * FROM duplicate_block_log ORDER BY blocked_date DESC LIMIT 5");
        if ($recent_blocked->num_rows > 0) {
            echo "<h4>Recent Blocked Attempts:</h4>";
            echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%; background: white;'>";
            echo "<tr style='background: #f44336; color: white;'>";
            echo "<th>Child Name</th><th>Birth Date</th><th>Blocked Date</th><th>Reason</th></tr>";
            
            while ($row = $recent_blocked->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['child_name']} {$row['last_name']}</td>";
                echo "<td>{$row['dob']}</td>";
                echo "<td>{$row['blocked_date']}</td>";
                echo "<td>{$row['blocked_reason']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p><strong>Blocked Attempts Log:</strong> Table not created yet</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error retrieving statistics: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🎯 How the Duplicate Check Works</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
echo "<h4>✅ Duplicate Detection Logic:</h4>";
echo "<ol>";
echo "<li><strong>Input Validation:</strong> Checks if first name, last name, and birth date are provided</li>";
echo "<li><strong>Database Query:</strong> Searches for existing records with exact match on all three fields</li>";
echo "<li><strong>Result Processing:</strong> If match found, registration is blocked and attempt is logged</li>";
echo "<li><strong>Error Handling:</strong> If database error occurs, allows registration but shows warning</li>";
echo "</ol>";

echo "<h4>🔍 What Gets Checked:</h4>";
echo "<ul>";
echo "<li><strong>First Name:</strong> Exact match (case-sensitive)</li>";
echo "<li><strong>Last Name:</strong> Exact match (case-sensitive)</li>";
echo "<li><strong>Birth Date:</strong> Exact match (YYYY-MM-DD format)</li>";
echo "<li><strong>Active Records Only:</strong> Ignores deleted records</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Quick Links</h3>";
echo "<p>";
echo "<a href='nip.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='duplicate_checker.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Duplicate Manager</a>";
echo "<a href='database_health_check.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔧 Health Check</a>";
echo "</p>";

$conn->close();
echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    border-collapse: collapse;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3, h4 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}

ol, ul {
    line-height: 1.6;
}
</style>
