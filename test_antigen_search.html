<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Antigen Search & Update Test - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
        
        .step-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        
        .antigen-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
        }
        
        .search-demo {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h3 class="center-align">
                <i class="material-icons left blue-text">medical_services</i>
                Antigen Search & Update System
            </h3>
            <p class="center-align">Search children by name and update their antigen records</p>
        </div>

        <div class="test-section">
            <h4>✅ Features Implemented</h4>
            
            <div class="feature-card">
                <h6><i class="material-icons left green-text">search</i>Advanced Search</h6>
                <p>Search children by first name, last name, and middle name with partial matching</p>
            </div>
            
            <div class="feature-card">
                <h6><i class="material-icons left green-text">list</i>Search Results</h6>
                <p>Display multiple matching children with key information for easy selection</p>
            </div>
            
            <div class="feature-card">
                <h6><i class="material-icons left green-text">edit</i>Editable Antigens</h6>
                <p>Complete antigen form with all vaccines and dates that can be updated</p>
            </div>
            
            <div class="feature-card">
                <h6><i class="material-icons left green-text">save</i>Database Update</h6>
                <p>Save changes directly to the database with confirmation messages</p>
            </div>
        </div>

        <div class="test-section">
            <h4>🔍 Search Functionality</h4>
            
            <div class="search-demo">
                <h6>Search Options:</h6>
                <ul>
                    <li><strong>First Name:</strong> Search by child's first name (partial match)</li>
                    <li><strong>Last Name:</strong> Search by child's last name (partial match)</li>
                    <li><strong>Middle Name:</strong> Search by child's middle name (partial match)</li>
                    <li><strong>Combined Search:</strong> Use multiple fields for more specific results</li>
                </ul>
            </div>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Search Examples</span>
                            <ul>
                                <li><strong>First Name:</strong> "Juan" → finds all children named Juan</li>
                                <li><strong>Last Name:</strong> "Cruz" → finds all children with Cruz surname</li>
                                <li><strong>Combined:</strong> "Juan" + "Cruz" → finds Juan Cruz specifically</li>
                                <li><strong>Partial:</strong> "Ju" → finds Juan, Julia, etc.</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Search Results</span>
                            <ul>
                                <li>Child's full name displayed</li>
                                <li>Birth date and mother's name</li>
                                <li>Barangay information</li>
                                <li>Clickable cards for easy selection</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>💉 Editable Antigens</h4>
            
            <div class="antigen-list">
                <h6>Available Antigens for Update:</h6>
                <div class="row">
                    <div class="col s12 m6">
                        <ul>
                            <li><strong>BCG</strong> - Bacillus Calmette-Guérin</li>
                            <li><strong>PENTA-HIB 1, 2, 3</strong> - Pentavalent + Haemophilus influenzae type b</li>
                            <li><strong>OPV 1, 2, 3</strong> - Oral Polio Vaccine</li>
                            <li><strong>IPV 1, 2</strong> - Inactivated Polio Vaccine</li>
                            <li><strong>PCV 1, 2, 3</strong> - Pneumococcal Conjugate Vaccine</li>
                        </ul>
                    </div>
                    <div class="col s12 m6">
                        <ul>
                            <li><strong>HEPA at Birth</strong> - Hepatitis A at Birth</li>
                            <li><strong>HEPA B1</strong> - Hepatitis B1</li>
                            <li><strong>MCV 1, 2</strong> - Measles-Containing Vaccine</li>
                            <li><strong>FIC</strong> - Fully Immunized Child</li>
                            <li><strong>Immunization Status</strong> - Overall status</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col s12 m4">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Antigen Status</span>
                            <p>Mark each antigen as given or not given</p>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m4">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">Date Given</span>
                            <p>Record the exact date when antigen was administered</p>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m4">
                    <div class="card teal lighten-4">
                        <div class="card-content">
                            <span class="card-title">Consultation Date</span>
                            <p>Record the date of consultation for each antigen</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>🔧 How to Use</h4>
            
            <div class="step-card">
                <h6>Step 1: Search for Child</h6>
                <ol>
                    <li>Go to the antigen update page</li>
                    <li>Enter child's first name, last name, or middle name</li>
                    <li>Click "Search Children" button</li>
                    <li>Review the search results</li>
                </ol>
            </div>
            
            <div class="step-card">
                <h6>Step 2: Select Child</h6>
                <ol>
                    <li>Click on the child card from search results</li>
                    <li>Verify the selected child's information</li>
                    <li>The antigen form will load with current data</li>
                </ol>
            </div>
            
            <div class="step-card">
                <h6>Step 3: Update Antigens</h6>
                <ol>
                    <li>Edit antigen status (YES/NO for BCG)</li>
                    <li>Update dates when antigens were given</li>
                    <li>Update consultation dates</li>
                    <li>Modify any other antigen information</li>
                </ol>
            </div>
            
            <div class="step-card">
                <h6>Step 4: Save Changes</h6>
                <ol>
                    <li>Review all changes made</li>
                    <li>Click "Update Antigens" button</li>
                    <li>Wait for confirmation message</li>
                    <li>Changes are saved to database</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h4>🧪 Test Scenarios</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card red lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 1: Search by First Name</span>
                            <ol>
                                <li>Enter "Juan" in first name field</li>
                                <li>Click search</li>
                                <li>Should show all children named Juan</li>
                                <li>Select one to edit antigens</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 2: Search by Last Name</span>
                            <ol>
                                <li>Enter "Dela Cruz" in last name field</li>
                                <li>Click search</li>
                                <li>Should show all children with that surname</li>
                                <li>Select one to edit antigens</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 3: Combined Search</span>
                            <ol>
                                <li>Enter both first and last name</li>
                                <li>Click search</li>
                                <li>Should show more specific results</li>
                                <li>Select child to edit antigens</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 4: Update Antigens</span>
                            <ol>
                                <li>Select a child from search results</li>
                                <li>Change BCG status to YES</li>
                                <li>Add date for BCG administration</li>
                                <li>Save changes and verify update</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>🛡️ Security Features</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">Data Protection</span>
                            <ul>
                                <li>Prepared statements prevent SQL injection</li>
                                <li>HTML escaping prevents XSS attacks</li>
                                <li>Input validation on all fields</li>
                                <li>Session-based access control</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card teal lighten-4">
                        <div class="card-content">
                            <span class="card-title">User Experience</span>
                            <ul>
                                <li>Responsive design for all devices</li>
                                <li>Clear search and selection process</li>
                                <li>Confirmation messages for updates</li>
                                <li>Easy navigation between search and edit</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section center-align">
            <h4>🔗 Test the Antigen Search & Update</h4>
            <p>The search and update functionality is ready to use!</p>
            
            <a href="update_antigen.php" class="btn large blue waves-effect">
                <i class="material-icons left">medical_services</i>Test Antigen Update
            </a>
            <br><br>
            <a href="filter_records.php" class="btn green waves-effect">
                <i class="material-icons left">search</i>View All Records
            </a>
            <a href="nip.php" class="btn orange waves-effect">
                <i class="material-icons left">add</i>Register New Child
            </a>
        </div>

        <div class="test-section">
            <h4>📊 System Benefits</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">For Healthcare Workers</span>
                            <ul>
                                <li>Quick child lookup by name</li>
                                <li>Easy antigen record updates</li>
                                <li>Complete immunization tracking</li>
                                <li>Efficient workflow management</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">For Data Management</span>
                            <ul>
                                <li>Accurate immunization records</li>
                                <li>Real-time data updates</li>
                                <li>Comprehensive antigen tracking</li>
                                <li>Better health program monitoring</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
