<?php
session_start();
include 'db.php';
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Function to verify health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

// Verify access
if (!verifyHealthCenterAccess($conn, $_SESSION['health_center'])) {
    echo json_encode([
        'error' => 'Wrong password - Excel download blocked. Data will not proceed.',
        'blocked_reason' => 'health_center_mismatch'
    ]);
    exit;
}

// Function to build dynamic SQL query based on filters
function buildFilterQuery($filters, $health_center) {
    $sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ?";
    $params = [$health_center];
    $types = "s";
    
    // Date range filter
    if (!empty($filters['date_from'])) {
        $sql .= " AND DATE(DateOfRegistration) >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }
    
    if (!empty($filters['date_to'])) {
        $sql .= " AND DATE(DateOfRegistration) <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }
    
    // Age range filter
    if (!empty($filters['age_from'])) {
        $sql .= " AND TIMESTAMPDIFF(MONTH, DateOfBirth, CURDATE()) >= ?";
        $params[] = intval($filters['age_from']);
        $types .= "i";
    }
    
    if (!empty($filters['age_to'])) {
        $sql .= " AND TIMESTAMPDIFF(MONTH, DateOfBirth, CURDATE()) <= ?";
        $params[] = intval($filters['age_to']);
        $types .= "i";
    }
    
    // Gender filter
    if (!empty($filters['gender']) && $filters['gender'] !== 'all') {
        $sql .= " AND Sex = ?";
        $params[] = $filters['gender'];
        $types .= "s";
    }
    
    // Birth weight range
    if (!empty($filters['weight_from'])) {
        $sql .= " AND BirthWeightInGrams >= ?";
        $params[] = floatval($filters['weight_from']);
        $types .= "d";
    }
    
    if (!empty($filters['weight_to'])) {
        $sql .= " AND BirthWeightInGrams <= ?";
        $params[] = floatval($filters['weight_to']);
        $types .= "d";
    }
    
    // Immunization status filter
    if (!empty($filters['immunization_status']) && $filters['immunization_status'] !== 'all') {
        $sql .= " AND IMMUNIZATIONSTATUS = ?";
        $params[] = $filters['immunization_status'];
        $types .= "s";
    }
    
    // BCG vaccination filter
    if (!empty($filters['bcg_status']) && $filters['bcg_status'] !== 'all') {
        if ($filters['bcg_status'] === 'given') {
            $sql .= " AND (BCG IS NOT NULL AND BCG != '')";
        } else {
            $sql .= " AND (BCG IS NULL OR BCG = '')";
        }
    }
    
    // Search in child name
    if (!empty($filters['child_name'])) {
        $sql .= " AND (NameOfChild LIKE ? OR lastnameOfChild LIKE ?)";
        $search_term = "%" . $filters['child_name'] . "%";
        $params[] = $search_term;
        $params[] = $search_term;
        $types .= "ss";
    }
    
    // Search in mother name
    if (!empty($filters['mother_name'])) {
        $sql .= " AND NameofMother LIKE ?";
        $params[] = "%" . $filters['mother_name'] . "%";
        $types .= "s";
    }
    
    // Address search
    if (!empty($filters['address'])) {
        $sql .= " AND Address LIKE ?";
        $params[] = "%" . $filters['address'] . "%";
        $types .= "s";
    }
    
    // Specific barangay filter (within health center)
    if (!empty($filters['specific_barangay'])) {
        $sql .= " AND Address LIKE ?";
        $params[] = "%" . $filters['specific_barangay'] . "%";
        $types .= "s";
    }
    
    // Registration month filter
    if (!empty($filters['registration_month'])) {
        $sql .= " AND MONTH(DateOfRegistration) = ?";
        $params[] = intval($filters['registration_month']);
        $types .= "i";
    }
    
    // Registration year filter
    if (!empty($filters['registration_year'])) {
        $sql .= " AND YEAR(DateOfRegistration) = ?";
        $params[] = intval($filters['registration_year']);
        $types .= "i";
    }
    
    // Sort options
    $sort_field = $filters['sort_by'] ?? 'DateOfRegistration';
    $sort_order = $filters['sort_order'] ?? 'DESC';
    
    // Validate sort fields for security
    $allowed_sort_fields = [
        'DateOfRegistration', 'NameOfChild', 'NameofMother', 
        'DateOfBirth', 'BirthWeightInGrams', 'id'
    ];
    
    if (in_array($sort_field, $allowed_sort_fields)) {
        $sql .= " ORDER BY " . $sort_field . " " . ($sort_order === 'ASC' ? 'ASC' : 'DESC');
    } else {
        $sql .= " ORDER BY DateOfRegistration DESC";
    }
    
    // Limit for preview
    if (!empty($filters['limit'])) {
        $sql .= " LIMIT ?";
        $params[] = intval($filters['limit']);
        $types .= "i";
    }
    
    return ['sql' => $sql, 'params' => $params, 'types' => $types];
}

// Function to get filter statistics
function getFilterStatistics($conn, $filters, $health_center) {
    $query_data = buildFilterQuery($filters, $health_center);
    $count_sql = str_replace("SELECT *", "SELECT COUNT(*) as count", $query_data['sql']);
    
    // Remove ORDER BY and LIMIT for count query
    $count_sql = preg_replace('/ORDER BY.*$/', '', $count_sql);
    $count_sql = preg_replace('/LIMIT.*$/', '', $count_sql);
    
    $stmt = $conn->prepare($count_sql);
    
    // Adjust parameters for count query (remove limit parameter if exists)
    $count_params = $query_data['params'];
    $count_types = $query_data['types'];
    
    if (!empty($filters['limit'])) {
        array_pop($count_params); // Remove limit parameter
        $count_types = substr($count_types, 0, -1); // Remove limit type
    }
    
    if (!empty($count_params)) {
        $stmt->bind_param($count_types, ...$count_params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    $stmt->close();
    
    return $count;
}

// Function to get available filter options
function getFilterOptions($conn, $health_center) {
    $options = [];
    
    // Get available years
    $year_stmt = $conn->prepare("
        SELECT DISTINCT YEAR(DateOfRegistration) as year 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        ORDER BY year DESC
    ");
    $year_stmt->bind_param("s", $health_center);
    $year_stmt->execute();
    $year_result = $year_stmt->get_result();
    
    $options['years'] = [];
    while ($row = $year_result->fetch_assoc()) {
        $options['years'][] = $row['year'];
    }
    $year_stmt->close();
    
    // Get immunization statuses
    $status_stmt = $conn->prepare("
        SELECT DISTINCT IMMUNIZATIONSTATUS 
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? AND IMMUNIZATIONSTATUS IS NOT NULL 
        ORDER BY IMMUNIZATIONSTATUS
    ");
    $status_stmt->bind_param("s", $health_center);
    $status_stmt->execute();
    $status_result = $status_stmt->get_result();
    
    $options['immunization_statuses'] = [];
    while ($row = $status_result->fetch_assoc()) {
        if (!empty($row['IMMUNIZATIONSTATUS'])) {
            $options['immunization_statuses'][] = $row['IMMUNIZATIONSTATUS'];
        }
    }
    $status_stmt->close();
    
    // Get age ranges
    $age_stmt = $conn->prepare("
        SELECT 
            MIN(TIMESTAMPDIFF(MONTH, DateOfBirth, CURDATE())) as min_age,
            MAX(TIMESTAMPDIFF(MONTH, DateOfBirth, CURDATE())) as max_age
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? AND DateOfBirth IS NOT NULL
    ");
    $age_stmt->bind_param("s", $health_center);
    $age_stmt->execute();
    $age_result = $age_stmt->get_result();
    $age_data = $age_result->fetch_assoc();
    $options['age_range'] = $age_data;
    $age_stmt->close();
    
    // Get weight ranges
    $weight_stmt = $conn->prepare("
        SELECT 
            MIN(BirthWeightInGrams) as min_weight,
            MAX(BirthWeightInGrams) as max_weight
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? AND BirthWeightInGrams IS NOT NULL AND BirthWeightInGrams > 0
    ");
    $weight_stmt->bind_param("s", $health_center);
    $weight_stmt->execute();
    $weight_result = $weight_stmt->get_result();
    $weight_data = $weight_result->fetch_assoc();
    $options['weight_range'] = $weight_data;
    $weight_stmt->close();
    
    return $options;
}

try {
    $health_center = $_SESSION['health_center'];
    $action = $_GET['action'] ?? 'filter';
    
    switch ($action) {
        case 'filter':
            $filters = $_GET;
            unset($filters['action']);
            
            $query_data = buildFilterQuery($filters, $health_center);
            
            $stmt = $conn->prepare($query_data['sql']);
            
            if (!empty($query_data['params'])) {
                $stmt->bind_param($query_data['types'], ...$query_data['params']);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            
            $data = [];
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;
            }
            
            $stmt->close();
            
            // Get total count
            $total_count = getFilterStatistics($conn, $filters, $health_center);
            
            echo json_encode([
                'success' => true,
                'data' => $data,
                'count' => count($data),
                'total_count' => $total_count,
                'filters_applied' => $filters
            ]);
            break;
            
        case 'count':
            $filters = $_GET;
            unset($filters['action']);
            
            $count = getFilterStatistics($conn, $filters, $health_center);
            
            echo json_encode([
                'success' => true,
                'count' => $count
            ]);
            break;
            
        case 'options':
            $options = getFilterOptions($conn, $health_center);
            
            echo json_encode([
                'success' => true,
                'options' => $options
            ]);
            break;
            
        case 'export':
            $filters = $_GET;
            unset($filters['action']);
            unset($filters['limit']); // Remove limit for full export
            
            $query_data = buildFilterQuery($filters, $health_center);
            
            $stmt = $conn->prepare($query_data['sql']);
            
            if (!empty($query_data['params'])) {
                $stmt->bind_param($query_data['types'], ...$query_data['params']);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            
            // Set headers for Excel download
            $filename = 'filtered_export_' . date('Y-m-d_H-i-s') . '.xls';
            header("Content-Type: application/vnd.ms-excel");
            header("Content-Disposition: attachment; filename=" . $filename);
            header("Pragma: no-cache");
            header("Expires: 0");
            
            // Output headers
            echo "ID\tRegistration Date\tChild Name\tMother Name\tGender\tDate of Birth\tAge (Months)\tBirth Weight\tAddress\tImmunization Status\tBCG\n";
            
            // Output data
            while ($row = $result->fetch_assoc()) {
                $age_months = '';
                if (!empty($row['DateOfBirth'])) {
                    $birth_date = new DateTime($row['DateOfBirth']);
                    $current_date = new DateTime();
                    $age_months = $birth_date->diff($current_date)->m + ($birth_date->diff($current_date)->y * 12);
                }
                
                echo implode("\t", [
                    $row['id'] ?? '',
                    $row['DateOfRegistration'] ?? '',
                    ($row['NameOfChild'] ?? '') . ' ' . ($row['lastnameOfChild'] ?? ''),
                    $row['NameofMother'] ?? '',
                    $row['Sex'] ?? '',
                    $row['DateOfBirth'] ?? '',
                    $age_months,
                    $row['BirthWeightInGrams'] ?? '',
                    $row['Address'] ?? '',
                    $row['IMMUNIZATIONSTATUS'] ?? '',
                    $row['BCG'] ?? ''
                ]) . "\n";
            }
            
            $stmt->close();
            exit;
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Filter error: ' . $e->getMessage()
    ]);
}
?>
