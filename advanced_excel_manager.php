<?php
session_start();
include 'db.php';

if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}

// Function to verify user's health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

$current_health_center = $_SESSION['health_center'];
$access_granted = verifyHealthCenterAccess($conn, $current_health_center);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Excel Manager - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <script src="https://cdn.sheetjs.com/xlsx-0.20.3/package/dist/xlsx.full.min.js"></script>
    <style>
        /* Main Layout Styles */
        .manager-card {
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .manager-card:hover {
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        /* Feature Cards */
        .feature-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Download Options */
        .download-option {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .download-option:hover {
            border-left-color: #ff9800;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transform: translateX(5px);
        }

        .download-option h6 {
            margin-bottom: 10px;
            color: #333;
            font-weight: 600;
        }

        .download-option p {
            color: #666;
            margin-bottom: 15px;
        }

        /* Progress Container */
        .progress-container {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 10px;
            display: none;
            border: 1px solid #2196f3;
        }

        .progress-container h6 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        /* Security Badges */
        .security-badge {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .security-badge.granted {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
        }

        .security-badge.blocked {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            box-shadow: 0 2px 10px rgba(244, 67, 54, 0.3);
        }

        /* Filter Panel */
        .filter-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .filter-panel .input-field {
            margin-bottom: 20px;
        }

        /* Quality Dashboard */
        .quality-check-item {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .quality-check-item:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        /* Statistics Cards */
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            transition: all 0.3s ease;
            margin: 10px 0;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .stat-card h4 {
            margin: 10px 0;
            font-weight: 700;
        }

        .stat-card .material-icons {
            font-size: 3rem;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .manager-card {
                margin: 10px 0;
            }

            .download-option {
                padding: 15px;
                margin: 10px 0;
            }

            .feature-card {
                padding: 15px;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Loading States */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
    </style>
</head>
<body class="grey lighten-4">
    <nav class="blue darken-2">
        <div class="nav-wrapper container">
            <a href="#" class="brand-logo">
                <i class="material-icons left">cloud_download</i>Advanced Excel Manager
            </a>
            <ul class="right">
                <li><a href="index.php"><i class="material-icons left">home</i>Dashboard</a></li>
                <li><a href="test_excel_security.php"><i class="material-icons left">security</i>Security Test</a></li>
                <li><a href="logout.php"><i class="material-icons left">exit_to_app</i>Logout</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <div class="row">
            <div class="col s12">
                <div class="manager-card fade-in">
                    <div class="card-content">
                        <h4 class="blue-grey-text text-darken-2 center-align">
                            <i class="material-icons left">cloud_download</i>
                            Advanced Excel Download Manager
                        </h4>
                        <p class="center-align grey-text">
                            Secure, comprehensive data export system with advanced filtering and quality monitoring
                        </p>
                    </div>
                </div>

                <!-- Security Status Dashboard -->
                <div class="manager-card fade-in">
                    <div class="card-content">
                        <h5><i class="material-icons left">security</i>Security Status Dashboard</h5>
                        <div class="row">
                            <div class="col s12 m4">
                                <div class="stat-card">
                                    <i class="material-icons">account_circle</i>
                                    <h6>Health Center</h6>
                                    <p style="font-size: 0.9rem; margin: 5px 0;"><?php echo htmlspecialchars($current_health_center); ?></p>
                                </div>
                            </div>
                            <div class="col s12 m4">
                                <div class="stat-card">
                                    <i class="material-icons">
                                        <?php echo $access_granted ? 'verified_user' : 'block'; ?>
                                    </i>
                                    <h6>Access Status</h6>
                                    <?php if ($access_granted): ?>
                                        <span class="security-badge granted">
                                            <i class="material-icons tiny">check</i> GRANTED
                                        </span>
                                    <?php else: ?>
                                        <span class="security-badge blocked">
                                            <i class="material-icons tiny">block</i> BLOCKED
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col s12 m4">
                                <div class="stat-card">
                                    <i class="material-icons">monitor_heart</i>
                                    <h6>System Status</h6>
                                    <div id="realTimeStatus">
                                        <div class="preloader-wrapper small active">
                                            <div class="spinner-layer spinner-white-only">
                                                <div class="circle-clipper left"><div class="circle"></div></div>
                                                <div class="gap-patch"><div class="circle"></div></div>
                                                <div class="circle-clipper right"><div class="circle"></div></div>
                                            </div>
                                        </div>
                                        <small>Checking...</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabbed Interface -->
                <div class="manager-card fade-in">
                    <div class="card-content">
                        <ul class="tabs">
                            <li class="tab col s3"><a href="#downloads-tab" class="active">Downloads</a></li>
                            <li class="tab col s3"><a href="#filters-tab">Advanced Filters</a></li>
                            <li class="tab col s3"><a href="#quality-tab">Data Quality</a></li>
                            <li class="tab col s3"><a href="#history-tab">History & Analytics</a></li>
                        </ul>
                    </div>
                </div>

                <?php if ($access_granted): ?>
                    <!-- Downloads Tab -->
                    <div id="downloads-tab" class="manager-card fade-in">
                        <div class="card-content">
                            <h5><i class="material-icons left">file_download</i>Download Options</h5>

                            <div class="row">
                                <div class="col s12 m4">
                                    <div class="download-option">
                                        <h6><i class="material-icons left">flash_on</i>Quick Download</h6>
                                        <p>Download all records instantly with default settings</p>
                                        <button onclick="quickDownload()" class="btn blue waves-effect waves-light pulse" style="width: 100%;">
                                            <i class="material-icons left">flash_on</i>Quick Download
                                        </button>
                                        <div class="center-align" style="margin-top: 10px;">
                                            <small class="grey-text">Excel format • All records</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col s12 m4">
                                    <div class="download-option">
                                        <h6><i class="material-icons left">date_range</i>Date Range Export</h6>
                                        <p>Export records for specific date range</p>
                                        <div class="input-field">
                                            <input type="date" id="customFromDate" class="datepicker">
                                            <label for="customFromDate">From Date</label>
                                        </div>
                                        <div class="input-field">
                                            <input type="date" id="customToDate" class="datepicker">
                                            <label for="customToDate">To Date</label>
                                        </div>
                                        <button onclick="customDownload()" class="btn orange waves-effect waves-light" style="width: 100%;">
                                            <i class="material-icons left">date_range</i>Custom Download
                                        </button>
                                    </div>
                                </div>

                                <div class="col s12 m4">
                                    <div class="download-option">
                                        <h6><i class="material-icons left">settings</i>Multi-Format Export</h6>
                                        <p>Choose from multiple export formats</p>
                                        <div class="input-field">
                                            <select id="exportFormat">
                                                <option value="excel">Excel (.xls)</option>
                                                <option value="csv">CSV (.csv)</option>
                                                <option value="pdf">PDF (.pdf)</option>
                                            </select>
                                            <label>Export Format</label>
                                        </div>
                                        <button onclick="multiFormatDownload()" class="btn green waves-effect waves-light" style="width: 100%;">
                                            <i class="material-icons left">cloud_download</i>Export
                                        </button>
                                        <div class="center-align" style="margin-top: 10px;">
                                            <a href="sheetjs.php" class="btn-flat blue-text">
                                                <i class="material-icons left">settings</i>Advanced Options
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="divider" style="margin: 20px 0;"></div>
                            <h6><i class="material-icons left">speed</i>Quick Actions</h6>
                            <div class="row">
                                <div class="col s12 m3">
                                    <button onclick="testQuickDownload()" class="btn-flat blue-text waves-effect" style="width: 100%; padding: 10px;">
                                        <i class="material-icons left">speed</i>Test Download
                                    </button>
                                </div>
                                <div class="col s12 m3">
                                    <button onclick="showDataPreview()" class="btn-flat orange-text waves-effect" style="width: 100%; padding: 10px;">
                                        <i class="material-icons left">preview</i>Preview Data
                                    </button>
                                </div>
                                <div class="col s12 m3">
                                    <button onclick="checkDataQuality()" class="btn-flat green-text waves-effect" style="width: 100%; padding: 10px;">
                                        <i class="material-icons left">verified</i>Check Quality
                                    </button>
                                </div>
                                <div class="col s12 m3">
                                    <button onclick="showDownloadHistory()" class="btn-flat purple-text waves-effect" style="width: 100%; padding: 10px;">
                                        <i class="material-icons left">history</i>View History
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Container -->
                    <div id="progressContainer" class="progress-container">
                        <h6><i class="material-icons left">hourglass_empty</i>Processing Download...</h6>
                        <div class="progress">
                            <div id="progressBar" class="determinate" style="width: 0%"></div>
                        </div>
                        <p id="progressText">Initializing...</p>
                    </div>

                    <!-- Advanced Filters Tab -->
                    <div id="filters-tab" class="manager-card fade-in">
                        <div class="card-content">
                            <h5><i class="material-icons left">filter_list</i>Advanced Data Filtering</h5>
                            <p class="grey-text">Use advanced filters to find and export specific records</p>

                            <div class="filter-panel">
                                <h6><i class="material-icons left">date_range</i>Date Range Filters</h6>
                                <div class="row">
                                    <div class="col s12 m6">
                                        <div class="input-field">
                                            <input type="date" id="filterFromDate" class="datepicker">
                                            <label for="filterFromDate">From Date</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m6">
                                        <div class="input-field">
                                            <input type="date" id="filterToDate" class="datepicker">
                                            <label for="filterToDate">To Date</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-panel">
                                <h6><i class="material-icons left">person</i>Demographics Filters</h6>
                                <div class="row">
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <select id="filterGender">
                                                <option value="all">All Genders</option>
                                                <option value="Male">Male</option>
                                                <option value="Female">Female</option>
                                            </select>
                                            <label>Gender</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="number" id="filterAgeFrom" placeholder="0" min="0" max="60">
                                            <label for="filterAgeFrom">Age From (months)</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="number" id="filterAgeTo" placeholder="24" min="0" max="60">
                                            <label for="filterAgeTo">Age To (months)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-panel">
                                <h6><i class="material-icons left">search</i>Search Filters</h6>
                                <div class="row">
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="text" id="filterChildName" placeholder="Enter child name">
                                            <label for="filterChildName">Child Name</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="text" id="filterMotherName" placeholder="Enter mother name">
                                            <label for="filterMotherName">Mother Name</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="text" id="filterAddress" placeholder="Enter address">
                                            <label for="filterAddress">Address</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-panel">
                                <h6><i class="material-icons left">healing</i>Medical Filters</h6>
                                <div class="row">
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <select id="filterImmunization">
                                                <option value="all">All Statuses</option>
                                            </select>
                                            <label>Immunization Status</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="number" id="filterWeightFrom" placeholder="500" min="0" max="6000">
                                            <label for="filterWeightFrom">Birth Weight From (g)</label>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="input-field">
                                            <input type="number" id="filterWeightTo" placeholder="4000" min="0" max="6000">
                                            <label for="filterWeightTo">Birth Weight To (g)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Filter Actions -->
                            <div class="center-align" style="margin: 20px 0;">
                                <button onclick="applyFilters()" class="btn blue waves-effect waves-light">
                                    <i class="material-icons left">search</i>Apply Filters
                                </button>
                                <button onclick="clearFilters()" class="btn grey waves-effect waves-light">
                                    <i class="material-icons left">clear</i>Clear All
                                </button>
                                <button onclick="saveFilters()" class="btn orange waves-effect waves-light">
                                    <i class="material-icons left">save</i>Save Filters
                                </button>
                                <button onclick="exportFiltered()" class="btn green waves-effect waves-light">
                                    <i class="material-icons left">file_download</i>Export Filtered
                                </button>
                            </div>

                            <!-- Filter Results -->
                            <div id="filterResults" style="margin-top: 30px; display: none;">
                                <div class="divider"></div>
                                <h6 style="margin-top: 20px;"><i class="material-icons left">assessment</i>Filter Results</h6>
                                <div class="row">
                                    <div class="col s12 m6">
                                        <div class="stat-card">
                                            <i class="material-icons">people</i>
                                            <h4 id="filterCount">0</h4>
                                            <p>Records Found</p>
                                        </div>
                                    </div>
                                    <div class="col s12 m6">
                                        <div class="stat-card">
                                            <i class="material-icons">filter_list</i>
                                            <h4 id="activeFilters">0</h4>
                                            <p>Active Filters</p>
                                        </div>
                                    </div>
                                </div>
                                <div id="filterPreview" style="margin-top: 20px;"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Data Quality Tab -->
                    <div id="quality-tab" class="manager-card fade-in">
                        <div class="card-content">
                            <h5><i class="material-icons left">verified</i>Data Quality Dashboard</h5>
                            <p class="grey-text">Monitor and improve your data quality with comprehensive analysis</p>

                            <!-- Quality Overview -->
                            <div id="qualityOverview" class="row" style="margin-bottom: 30px;">
                                <div class="col s12 center-align">
                                    <div class="preloader-wrapper active">
                                        <div class="spinner-layer spinner-green-only">
                                            <div class="circle-clipper left"><div class="circle"></div></div>
                                            <div class="gap-patch"><div class="circle"></div></div>
                                            <div class="circle-clipper right"><div class="circle"></div></div>
                                        </div>
                                    </div>
                                    <p>Analyzing data quality...</p>
                                </div>
                            </div>

                            <!-- Quality Actions -->
                            <div class="center-align" style="margin: 20px 0;">
                                <button onclick="refreshQualityCheck()" class="btn blue waves-effect waves-light">
                                    <i class="material-icons left">refresh</i>Refresh Analysis
                                </button>
                                <button onclick="exportQualityReport()" class="btn green waves-effect waves-light">
                                    <i class="material-icons left">file_download</i>Export Report
                                </button>
                                <button onclick="showQualityTrends()" class="btn orange waves-effect waves-light">
                                    <i class="material-icons left">trending_up</i>View Trends
                                </button>
                                <button onclick="fixDataIssues()" class="btn red waves-effect waves-light">
                                    <i class="material-icons left">build</i>Fix Issues
                                </button>
                            </div>

                            <!-- Detailed Quality Checks -->
                            <div id="qualityDetails" style="margin-top: 30px;">
                                <h6><i class="material-icons left">checklist</i>Detailed Quality Checks</h6>
                                <div id="qualityChecks" class="loading-shimmer" style="height: 200px; border-radius: 10px;">
                                    <!-- Quality checks will be loaded here -->
                                </div>
                            </div>

                            <!-- Quality Recommendations -->
                            <div id="qualityRecommendations" style="margin-top: 30px; display: none;">
                                <h6><i class="material-icons left">lightbulb_outline</i>Recommendations</h6>
                                <div id="recommendationsList">
                                    <!-- Recommendations will be loaded here -->
                                </div>
                            </div>

                            <!-- Quality History -->
                            <div style="margin-top: 30px;">
                                <h6><i class="material-icons left">history</i>Quality History</h6>
                                <div class="row">
                                    <div class="col s12 m6">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="lastQualityCheck">-</h5>
                                                <p>Last Quality Check</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m6">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="qualityTrend">-</h5>
                                                <p>Quality Trend</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- History & Analytics Tab -->
                    <div id="history-tab" class="manager-card fade-in">
                        <div class="card-content">
                            <h5><i class="material-icons left">analytics</i>History & Analytics</h5>
                            <p class="grey-text">Comprehensive analytics and download history tracking</p>

                            <!-- Analytics Overview -->
                            <div class="row" style="margin-bottom: 30px;">
                                <div class="col s12 m3">
                                    <div class="stat-card">
                                        <i class="material-icons">cloud_download</i>
                                        <h4 id="totalDownloads">-</h4>
                                        <p>Total Downloads</p>
                                    </div>
                                </div>
                                <div class="col s12 m3">
                                    <div class="stat-card">
                                        <i class="material-icons">today</i>
                                        <h4 id="todayDownloads">-</h4>
                                        <p>Today's Downloads</p>
                                    </div>
                                </div>
                                <div class="col s12 m3">
                                    <div class="stat-card">
                                        <i class="material-icons">date_range</i>
                                        <h4 id="weekDownloads">-</h4>
                                        <p>This Week</p>
                                    </div>
                                </div>
                                <div class="col s12 m3">
                                    <div class="stat-card">
                                        <i class="material-icons">storage</i>
                                        <h4 id="avgFileSize">-</h4>
                                        <p>Avg File Size (KB)</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Statistics -->
                            <div id="dataStats" class="row" style="margin-bottom: 30px;">
                                <div class="col s12 center-align">
                                    <div class="preloader-wrapper active">
                                        <div class="spinner-layer spinner-blue-only">
                                            <div class="circle-clipper left"><div class="circle"></div></div>
                                            <div class="gap-patch"><div class="circle"></div></div>
                                            <div class="circle-clipper right"><div class="circle"></div></div>
                                        </div>
                                    </div>
                                    <p>Loading data statistics...</p>
                                </div>
                            </div>

                            <!-- History Actions -->
                            <div class="center-align" style="margin: 20px 0;">
                                <button onclick="refreshAnalytics()" class="btn blue waves-effect waves-light">
                                    <i class="material-icons left">refresh</i>Refresh Analytics
                                </button>
                                <button onclick="showDetailedHistory()" class="btn orange waves-effect waves-light">
                                    <i class="material-icons left">history</i>Detailed History
                                </button>
                                <button onclick="exportAnalytics()" class="btn green waves-effect waves-light">
                                    <i class="material-icons left">file_download</i>Export Analytics
                                </button>
                                <button onclick="generateReport()" class="btn purple waves-effect waves-light">
                                    <i class="material-icons left">assessment</i>Generate Report
                                </button>
                            </div>

                            <!-- Recent Downloads -->
                            <div style="margin-top: 30px;">
                                <h6><i class="material-icons left">history</i>Recent Download Activity</h6>
                                <div id="recentDownloads">
                                    <div id="recentDownloadsList" class="loading-shimmer" style="height: 150px; border-radius: 10px;">
                                        <!-- Recent downloads will be loaded here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Download Trends -->
                            <div style="margin-top: 30px;">
                                <h6><i class="material-icons left">trending_up</i>Download Trends</h6>
                                <div class="row">
                                    <div class="col s12 m6">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="peakDownloadTime">-</h5>
                                                <p>Peak Download Time</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m6">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="mostActiveDay">-</h5>
                                                <p>Most Active Day</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Metrics -->
                            <div style="margin-top: 30px;">
                                <h6><i class="material-icons left">speed</i>Performance Metrics</h6>
                                <div class="row">
                                    <div class="col s12 m4">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="avgProcessingTime">-</h5>
                                                <p>Avg Processing Time (ms)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="successRate">-</h5>
                                                <p>Success Rate (%)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col s12 m4">
                                        <div class="card">
                                            <div class="card-content center-align">
                                                <h5 id="systemUptime">-</h5>
                                                <p>System Uptime</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="manager-card">
                        <div class="card-content">
                            <h5><i class="material-icons left">analytics</i>Data Overview</h5>
                            <div id="dataStats" class="row">
                                <div class="col s12 center-align">
                                    <div class="preloader-wrapper active">
                                        <div class="spinner-layer spinner-blue-only">
                                            <div class="circle-clipper left"><div class="circle"></div></div>
                                            <div class="gap-patch"><div class="circle"></div></div>
                                            <div class="circle-clipper right"><div class="circle"></div></div>
                                        </div>
                                    </div>
                                    <p>Loading statistics...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                <?php else: ?>
                    <!-- Access Denied -->
                    <div class="manager-card">
                        <div class="card-content center-align">
                            <i class="material-icons large red-text">block</i>
                            <h5 class="red-text">Excel Download Blocked</h5>
                            <p><strong>Wrong password - Excel download blocked. Data will not proceed.</strong></p>
                            <p>You can only download data for your registered health center.</p>
                            
                            <div class="row" style="margin-top: 30px;">
                                <div class="col s12 m6">
                                    <button onclick="showAccessHelp()" class="btn orange waves-effect waves-light" style="width: 100%;">
                                        <i class="material-icons left">help</i>Get Access Help
                                    </button>
                                </div>
                                <div class="col s12 m6">
                                    <a href="test_excel_security.php" class="btn blue waves-effect waves-light" style="width: 100%;">
                                        <i class="material-icons left">security</i>Security Test
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        // Global variables
        let currentFilters = {};
        let qualityData = null;
        let analyticsData = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Materialize components
            M.AutoInit();

            // Initialize all features
            initializeSystem();

            // Set up auto-refresh intervals
            setupAutoRefresh();

            // Add event listeners
            setupEventListeners();

            // Show welcome message
            showWelcomeMessage();
        });

        // Initialize system
        async function initializeSystem() {
            try {
                // Show loading states
                showLoadingStates();

                // Load all data in parallel
                await Promise.all([
                    checkRealTimeStatus(),
                    loadDataStatistics(),
                    loadDataQuality(),
                    loadDownloadHistory(),
                    loadFilterOptions(),
                    loadAnalytics()
                ]);

                // Set default dates
                setDefaultDates();

                // Hide loading states
                hideLoadingStates();

                console.log('System initialized successfully');
            } catch (error) {
                console.error('System initialization error:', error);
                showSystemError('Failed to initialize system. Please refresh the page.');
            }
        }

        // Set default dates
        function setDefaultDates() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const todayStr = today.toISOString().split('T')[0];
            const firstDayStr = firstDay.toISOString().split('T')[0];

            // Set dates for all date inputs
            const dateInputs = [
                'customFromDate', 'customToDate',
                'filterFromDate', 'filterToDate'
            ];

            dateInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = id.includes('From') ? firstDayStr : todayStr;
                }
            });
        }

        // Setup auto-refresh intervals
        function setupAutoRefresh() {
            // Real-time status every 30 seconds
            setInterval(checkRealTimeStatus, 30000);

            // Data quality every 5 minutes
            setInterval(loadDataQuality, 300000);

            // Analytics every 2 minutes
            setInterval(loadAnalytics, 120000);

            // Download history every minute
            setInterval(loadDownloadHistory, 60000);
        }

        // Setup event listeners
        function setupEventListeners() {
            // Tab change events
            document.querySelectorAll('.tabs .tab a').forEach(tab => {
                tab.addEventListener('click', function(e) {
                    const tabId = this.getAttribute('href').substring(1);
                    onTabChange(tabId);
                });
            });

            // Filter input events
            const filterInputs = [
                'filterFromDate', 'filterToDate', 'filterGender',
                'filterChildName', 'filterMotherName', 'filterAddress'
            ];

            filterInputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', debounce(onFilterChange, 500));
                    if (element.type === 'text') {
                        element.addEventListener('input', debounce(onFilterChange, 1000));
                    }
                }
            });
        }

        // Show welcome message
        function showWelcomeMessage() {
            M.toast({
                html: '<i class="material-icons left">waving_hand</i>Welcome to Advanced Excel Manager!',
                classes: 'blue rounded',
                displayLength: 3000
            });
        }

        // Show loading states
        function showLoadingStates() {
            const loadingElements = [
                'realTimeStatus', 'qualityOverview', 'dataStats', 'recentDownloadsList'
            ];

            loadingElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('loading-shimmer');
                }
            });
        }

        // Hide loading states
        function hideLoadingStates() {
            const loadingElements = document.querySelectorAll('.loading-shimmer');
            loadingElements.forEach(element => {
                element.classList.remove('loading-shimmer');
            });
        }

        // Show system error
        function showSystemError(message) {
            M.toast({
                html: `<i class="material-icons left">error</i>${message}`,
                classes: 'red rounded',
                displayLength: 6000
            });
        }

        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Tab change handler
        function onTabChange(tabId) {
            console.log('Tab changed to:', tabId);

            // Load tab-specific data
            switch(tabId) {
                case 'downloads-tab':
                    // Refresh download options
                    break;
                case 'filters-tab':
                    loadFilterOptions();
                    break;
                case 'quality-tab':
                    loadDataQuality();
                    break;
                case 'history-tab':
                    loadAnalytics();
                    loadDownloadHistory();
                    break;
            }
        }

        // Filter change handler
        function onFilterChange() {
            // Auto-apply filters if enabled
            const autoApply = localStorage.getItem('autoApplyFilters') === 'true';
            if (autoApply) {
                applyFilters();
            }
        }

        // Check real-time security status
        async function checkRealTimeStatus() {
            try {
                const response = await fetch('check_security_status.php');
                const data = await response.json();
                
                const statusDiv = document.getElementById('realTimeStatus');
                
                if (data.success) {
                    statusDiv.innerHTML = `
                        <div class="green-text">
                            <i class="material-icons left">verified_user</i>
                            <strong>SECURE</strong>
                        </div>
                        <small>Last checked: ${new Date().toLocaleTimeString()}</small>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="red-text">
                            <i class="material-icons left">warning</i>
                            <strong>ISSUE DETECTED</strong>
                        </div>
                        <small>Last checked: ${new Date().toLocaleTimeString()}</small>
                    `;
                }
            } catch (error) {
                document.getElementById('realTimeStatus').innerHTML = `
                    <div class="orange-text">
                        <i class="material-icons left">error</i>
                        <strong>CONNECTION ERROR</strong>
                    </div>
                `;
            }
        }

        // Load data statistics
        async function loadDataStatistics() {
            try {
                const response = await fetch('get_statistics.php');
                const data = await response.json();
                
                const statsDiv = document.getElementById('dataStats');
                
                if (data.success) {
                    statsDiv.innerHTML = `
                        <div class="col s12 m3">
                            <div class="card blue darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${data.total}</h4>
                                    <p>Total Records</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card green darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${data.today}</h4>
                                    <p>Today's Records</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card orange darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${data.month}</h4>
                                    <p>This Month</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card purple darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${data.additional_stats.completion_rate}</h4>
                                    <p>Completion Rate</p>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    statsDiv.innerHTML = `
                        <div class="col s12 center-align">
                            <p class="red-text">Unable to load statistics: ${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('dataStats').innerHTML = `
                    <div class="col s12 center-align">
                        <p class="red-text">Error loading statistics</p>
                    </div>
                `;
            }
        }

        // Quick download function
        async function quickDownload() {
            showProgress();
            updateProgress(10, 'Verifying security...');
            
            try {
                // Test download first
                const testResponse = await fetch('quick_download_test.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: true })
                });
                
                const testResult = await testResponse.json();
                updateProgress(30, 'Security verified, fetching data...');
                
                if (!testResult.success) {
                    hideProgress();
                    M.toast({
                        html: '<i class="material-icons left">block</i>Download blocked: ' + testResult.message,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                    return;
                }
                
                // Fetch data
                const dataResponse = await fetch('get_nip_data.php');
                const data = await dataResponse.json();
                updateProgress(60, 'Processing Excel file...');
                
                if (data.error) {
                    hideProgress();
                    M.toast({
                        html: '<i class="material-icons left">error</i>Data error: ' + data.error,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                    return;
                }
                
                // Generate Excel
                updateProgress(80, 'Generating Excel file...');
                await generateExcelFile(data, 'Quick_Download_' + new Date().toISOString().split('T')[0]);
                
                updateProgress(100, 'Download complete!');
                setTimeout(hideProgress, 2000);
                
                M.toast({
                    html: '<i class="material-icons left">check_circle</i>Quick download completed successfully!',
                    classes: 'green rounded',
                    displayLength: 3000
                });
                
            } catch (error) {
                hideProgress();
                M.toast({
                    html: '<i class="material-icons left">error</i>Download failed: ' + error.message,
                    classes: 'red rounded',
                    displayLength: 4000
                });
            }
        }

        // Custom download function
        async function customDownload() {
            const fromDate = document.getElementById('customFromDate').value;
            const toDate = document.getElementById('customToDate').value;
            
            if (!fromDate || !toDate) {
                M.toast({
                    html: '<i class="material-icons left">error</i>Please select both from and to dates',
                    classes: 'orange rounded',
                    displayLength: 3000
                });
                return;
            }
            
            showProgress();
            updateProgress(10, 'Preparing custom download...');
            
            try {
                const dataResponse = await fetch(`get_nip_data.php?fromDate=${fromDate}&toDate=${toDate}`);
                const data = await dataResponse.json();
                updateProgress(50, 'Processing filtered data...');
                
                if (data.error) {
                    hideProgress();
                    M.toast({
                        html: '<i class="material-icons left">error</i>Data error: ' + data.error,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                    return;
                }
                
                updateProgress(80, 'Generating custom Excel file...');
                await generateExcelFile(data, `Custom_Download_${fromDate}_to_${toDate}`);
                
                updateProgress(100, 'Custom download complete!');
                setTimeout(hideProgress, 2000);
                
                M.toast({
                    html: '<i class="material-icons left">check_circle</i>Custom download completed successfully!',
                    classes: 'green rounded',
                    displayLength: 3000
                });
                
            } catch (error) {
                hideProgress();
                M.toast({
                    html: '<i class="material-icons left">error</i>Custom download failed: ' + error.message,
                    classes: 'red rounded',
                    displayLength: 4000
                });
            }
        }

        // Generate Excel file using SheetJS
        async function generateExcelFile(data, filename) {
            if (!data || data.length === 0) {
                throw new Error('No data to export');
            }
            
            // Format data for Excel
            const formattedData = data.map(row => ({
                'ID': row.id,
                'Date Of Registration': row.DateOfRegistration,
                'Child Name': `${row.NameOfChild || ''} ${row.lastnameOfChild || ''}`.trim(),
                'Mother Name': row.NameofMother,
                'Barangay': row.Barangay,
                'Address': row.Address,
                'Birth Weight': row.BirthWeightInGrams,
                'BCG': row.BCG,
                'Immunization Status': row.IMMUNIZATIONSTATUS
                // Add more fields as needed
            }));
            
            // Create workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(formattedData);
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'NIP Data');
            
            // Generate and download file
            XLSX.writeFile(wb, `${filename}.xlsx`);
        }

        // Progress functions
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
        }
        
        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }
        
        function updateProgress(percent, text) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        // Load data quality dashboard
        async function loadDataQuality() {
            try {
                const response = await fetch('data_quality_checker.php?action=check');
                const data = await response.json();

                const qualityDiv = document.getElementById('qualityDashboard');

                if (data.success) {
                    const report = data.quality_report;
                    const gradeColor = report.grade === 'A' ? 'green' :
                                     report.grade === 'B' ? 'blue' :
                                     report.grade === 'C' ? 'orange' : 'red';

                    qualityDiv.innerHTML = `
                        <div class="col s12 m3">
                            <div class="card ${gradeColor} darken-1 white-text center-align">
                                <div class="card-content">
                                    <h3>${report.grade}</h3>
                                    <p>Overall Grade</p>
                                    <small>${report.grade_description}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card teal darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${report.overall_score}%</h4>
                                    <p>Quality Score</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card purple darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${report.issues.length}</h4>
                                    <p>Issues Found</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card indigo darken-1 white-text center-align">
                                <div class="card-content">
                                    <h4>${report.total_records}</h4>
                                    <p>Total Records</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12">
                            <div class="card">
                                <div class="card-content">
                                    <h6>Quality Checks</h6>
                                    <div class="row">
                                        ${Object.entries(report.checks).map(([key, check]) => `
                                            <div class="col s12 m6 l4">
                                                <div class="quality-check-item" style="padding: 10px; margin: 5px; border-radius: 5px; background: ${check.status === 'excellent' ? '#e8f5e8' : check.status === 'good' ? '#e3f2fd' : '#fff3e0'};">
                                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                                        <span style="font-weight: 500;">${check.name}</span>
                                                        <span style="font-weight: bold; color: ${check.status === 'excellent' ? '#4caf50' : check.status === 'good' ? '#2196f3' : '#ff9800'};">${check.score}%</span>
                                                    </div>
                                                    <small style="color: #666;">${check.description}</small>
                                                    ${check.issues_count > 0 ? `<br><small style="color: #f44336;">${check.issues_count} issues found</small>` : ''}
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                    ${report.recommendations.length > 0 ? `
                                        <div style="margin-top: 20px;">
                                            <h6>Recommendations</h6>
                                            <ul class="collection">
                                                ${report.recommendations.map(rec => `
                                                    <li class="collection-item">
                                                        <i class="material-icons left orange-text">lightbulb_outline</i>
                                                        ${rec}
                                                    </li>
                                                `).join('')}
                                            </ul>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    qualityDiv.innerHTML = `
                        <div class="col s12 center-align">
                            <p class="red-text">Unable to load data quality report: ${data.error}</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('qualityDashboard').innerHTML = `
                    <div class="col s12 center-align">
                        <p class="red-text">Error loading data quality report</p>
                    </div>
                `;
            }
        }

        // Load download history
        async function loadDownloadHistory() {
            try {
                // Load download statistics
                const statsResponse = await fetch('download_history.php?action=get_stats');
                const statsData = await statsResponse.json();

                if (statsData.success) {
                    document.getElementById('totalDownloads').textContent = statsData.stats.total_downloads;
                    document.getElementById('todayDownloads').textContent = statsData.stats.today_downloads;
                }

                // Load recent downloads
                const historyResponse = await fetch('download_history.php?action=get_history&limit=5');
                const historyData = await historyResponse.json();

                const recentList = document.getElementById('recentDownloadsList');

                if (historyData.success && historyData.history.length > 0) {
                    recentList.innerHTML = `
                        <ul class="collection">
                            ${historyData.history.map(download => `
                                <li class="collection-item">
                                    <div>
                                        <strong>${download.file_name}</strong>
                                        <span class="secondary-content">${new Date(download.download_time).toLocaleDateString()}</span>
                                    </div>
                                    <small>
                                        ${download.download_type} • ${download.record_count} records • ${download.file_size_kb} KB
                                        <span class="chip ${download.status === 'completed' ? 'green' : 'orange'} white-text" style="margin-left: 10px; font-size: 0.7rem; height: 20px; line-height: 20px;">
                                            ${download.status}
                                        </span>
                                    </small>
                                </li>
                            `).join('')}
                        </ul>
                    `;
                } else {
                    recentList.innerHTML = '<p class="grey-text">No download history available</p>';
                }

            } catch (error) {
                document.getElementById('recentDownloadsList').innerHTML = '<p class="red-text">Error loading download history</p>';
            }
        }

        // Load filter options
        async function loadFilterOptions() {
            try {
                const response = await fetch('advanced_data_filter.php?action=options');
                const data = await response.json();

                if (data.success) {
                    const immunizationSelect = document.getElementById('filterImmunization');

                    // Clear existing options except "All Statuses"
                    immunizationSelect.innerHTML = '<option value="all">All Statuses</option>';

                    // Add immunization status options
                    data.options.immunization_statuses.forEach(status => {
                        const option = document.createElement('option');
                        option.value = status;
                        option.textContent = status;
                        immunizationSelect.appendChild(option);
                    });

                    // Reinitialize Materialize select
                    M.FormSelect.init(immunizationSelect);
                }
            } catch (error) {
                console.error('Error loading filter options:', error);
            }
        }

        // Apply filters
        async function applyFilters() {
            const filters = {
                date_from: document.getElementById('filterFromDate').value,
                date_to: document.getElementById('filterToDate').value,
                gender: document.getElementById('filterGender').value,
                immunization_status: document.getElementById('filterImmunization').value,
                child_name: document.getElementById('filterChildName').value,
                limit: 10 // Preview limit
            };

            // Remove empty filters
            Object.keys(filters).forEach(key => {
                if (!filters[key] || filters[key] === 'all') {
                    delete filters[key];
                }
            });

            try {
                const queryString = new URLSearchParams(filters).toString();
                const response = await fetch(`advanced_data_filter.php?action=filter&${queryString}`);
                const data = await response.json();

                const resultsDiv = document.getElementById('filterResults');
                const countDiv = document.getElementById('filterCount');
                const previewDiv = document.getElementById('filterPreview');

                if (data.success) {
                    resultsDiv.style.display = 'block';
                    countDiv.textContent = `Found ${data.total_count} records (showing ${data.count})`;

                    if (data.data.length > 0) {
                        previewDiv.innerHTML = `
                            <table class="striped responsive-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Child Name</th>
                                        <th>Mother Name</th>
                                        <th>Registration Date</th>
                                        <th>Gender</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.data.map(record => `
                                        <tr>
                                            <td>${record.id}</td>
                                            <td>${(record.NameOfChild || '') + ' ' + (record.lastnameOfChild || '')}</td>
                                            <td>${record.NameofMother || ''}</td>
                                            <td>${record.DateOfRegistration || ''}</td>
                                            <td>${record.Sex || ''}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        `;
                    } else {
                        previewDiv.innerHTML = '<p class="grey-text">No records match the selected filters</p>';
                    }

                    M.toast({
                        html: `<i class="material-icons left">search</i>Found ${data.total_count} records`,
                        classes: 'blue rounded',
                        displayLength: 3000
                    });
                } else {
                    M.toast({
                        html: `<i class="material-icons left">error</i>Filter error: ${data.error}`,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                }
            } catch (error) {
                M.toast({
                    html: '<i class="material-icons left">error</i>Filter request failed',
                    classes: 'red rounded',
                    displayLength: 3000
                });
            }
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('filterFromDate').value = '';
            document.getElementById('filterToDate').value = '';
            document.getElementById('filterGender').value = 'all';
            document.getElementById('filterImmunization').value = 'all';
            document.getElementById('filterChildName').value = '';

            // Reinitialize Materialize selects
            M.FormSelect.init(document.querySelectorAll('select'));

            document.getElementById('filterResults').style.display = 'none';

            M.toast({
                html: '<i class="material-icons left">clear</i>Filters cleared',
                classes: 'grey rounded',
                displayLength: 2000
            });
        }

        // Export filtered data
        async function exportFiltered() {
            const filters = {
                date_from: document.getElementById('filterFromDate').value,
                date_to: document.getElementById('filterToDate').value,
                gender: document.getElementById('filterGender').value,
                immunization_status: document.getElementById('filterImmunization').value,
                child_name: document.getElementById('filterChildName').value
            };

            // Remove empty filters
            Object.keys(filters).forEach(key => {
                if (!filters[key] || filters[key] === 'all') {
                    delete filters[key];
                }
            });

            const queryString = new URLSearchParams(filters).toString();
            const exportUrl = `advanced_data_filter.php?action=export&${queryString}`;

            // Log download
            await logDownload({
                download_type: 'filtered_export',
                file_name: 'filtered_export_' + new Date().toISOString().split('T')[0] + '.xls',
                record_count: 0, // Will be updated by server
                file_size_kb: 0, // Will be calculated by server
                status: 'completed'
            });

            window.open(exportUrl, '_blank');

            M.toast({
                html: '<i class="material-icons left">file_download</i>Filtered export started',
                classes: 'green rounded',
                displayLength: 3000
            });
        }

        // Show download history modal
        function showDownloadHistory() {
            // Implementation for showing full download history
            M.toast({
                html: '<i class="material-icons left">history</i>Loading full download history...',
                classes: 'blue rounded',
                displayLength: 2000
            });
        }

        // Export download history
        function exportDownloadHistory() {
            window.open('download_history.php?action=export_history', '_blank');

            M.toast({
                html: '<i class="material-icons left">file_download</i>Download history export started',
                classes: 'green rounded',
                displayLength: 3000
            });
        }

        // Log download function
        async function logDownload(downloadData) {
            try {
                await fetch('download_history.php?action=log_download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(downloadData)
                });
            } catch (error) {
                console.error('Failed to log download:', error);
            }
        }

        // Multi-format download
        async function multiFormatDownload() {
            const format = document.getElementById('exportFormat').value;
            const fromDate = document.getElementById('customFromDate').value;
            const toDate = document.getElementById('customToDate').value;

            showProgress();
            updateProgress(10, `Preparing ${format.toUpperCase()} export...`);

            try {
                // Build export URL
                let exportUrl = `multi_format_exporter.php?format=${format}&type=all`;
                if (fromDate) exportUrl += `&date_from=${fromDate}`;
                if (toDate) exportUrl += `&date_to=${toDate}`;

                updateProgress(50, 'Generating export file...');

                // Log download
                await logDownload({
                    download_type: `multi_format_${format}`,
                    file_name: `export_${format}_${new Date().toISOString().split('T')[0]}`,
                    record_count: 0,
                    file_size_kb: 0,
                    status: 'completed'
                });

                updateProgress(80, 'Starting download...');

                // Open download
                window.open(exportUrl, '_blank');

                updateProgress(100, 'Export completed!');
                setTimeout(hideProgress, 2000);

                M.toast({
                    html: `<i class="material-icons left">check_circle</i>${format.toUpperCase()} export completed!`,
                    classes: 'green rounded',
                    displayLength: 3000
                });

            } catch (error) {
                hideProgress();
                M.toast({
                    html: `<i class="material-icons left">error</i>Export failed: ${error.message}`,
                    classes: 'red rounded',
                    displayLength: 4000
                });
            }
        }

        // Test quick download
        async function testQuickDownload() {
            M.toast({
                html: '<i class="material-icons left">speed</i>Running download performance test...',
                classes: 'blue rounded',
                displayLength: 2000
            });

            try {
                const response = await fetch('quick_download_test.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: true })
                });

                const result = await response.json();

                if (result.success) {
                    showTestResults(result);
                } else {
                    M.toast({
                        html: `<i class="material-icons left">block</i>Test failed: ${result.message}`,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                }
            } catch (error) {
                M.toast({
                    html: '<i class="material-icons left">error</i>Test request failed',
                    classes: 'red rounded',
                    displayLength: 3000
                });
            }
        }

        // Show test results modal
        function showTestResults(results) {
            const modal = `
                <div id="testResultsModal" class="modal modal-fixed-footer">
                    <div class="modal-content">
                        <h4><i class="material-icons left">speed</i>Performance Test Results</h4>
                        <div class="row">
                            <div class="col s12 m6">
                                <div class="card green lighten-4">
                                    <div class="card-content center-align">
                                        <h5>${results.summary.total_time_ms}ms</h5>
                                        <p>Total Processing Time</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col s12 m6">
                                <div class="card blue lighten-4">
                                    <div class="card-content center-align">
                                        <h5>${results.summary.record_count}</h5>
                                        <p>Records Available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h6>Detailed Test Results:</h6>
                        <ul class="collection">
                            ${Object.entries(results.test_results).map(([key, test]) => `
                                <li class="collection-item">
                                    <div>
                                        <strong>${test.message}</strong>
                                        <span class="secondary-content">
                                            <span class="chip ${test.passed ? 'green' : 'red'} white-text">
                                                ${test.passed ? 'PASSED' : 'FAILED'}
                                            </span>
                                        </span>
                                    </div>
                                </li>
                            `).join('')}
                        </ul>
                        ${results.recommendations.length > 0 ? `
                            <h6>Recommendations:</h6>
                            <ul class="collection">
                                ${results.recommendations.map(rec => `
                                    <li class="collection-item">
                                        <i class="material-icons left orange-text">lightbulb_outline</i>
                                        ${rec}
                                    </li>
                                `).join('')}
                            </ul>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <a href="#!" class="modal-close waves-effect waves-green btn-flat">Close</a>
                    </div>
                </div>
            `;

            // Add modal to page
            if (!document.getElementById('testResultsModal')) {
                document.body.insertAdjacentHTML('beforeend', modal);
                M.Modal.init(document.getElementById('testResultsModal'));
            }

            // Open modal
            const modalInstance = M.Modal.getInstance(document.getElementById('testResultsModal'));
            modalInstance.open();
        }

        // Save filters
        function saveFilters() {
            const filters = getCurrentFilters();
            localStorage.setItem('savedFilters', JSON.stringify(filters));

            M.toast({
                html: '<i class="material-icons left">save</i>Filters saved successfully!',
                classes: 'green rounded',
                displayLength: 2000
            });
        }

        // Get current filters
        function getCurrentFilters() {
            return {
                date_from: document.getElementById('filterFromDate').value,
                date_to: document.getElementById('filterToDate').value,
                gender: document.getElementById('filterGender').value,
                immunization_status: document.getElementById('filterImmunization').value,
                child_name: document.getElementById('filterChildName').value,
                mother_name: document.getElementById('filterMotherName').value,
                address: document.getElementById('filterAddress').value,
                age_from: document.getElementById('filterAgeFrom').value,
                age_to: document.getElementById('filterAgeTo').value,
                weight_from: document.getElementById('filterWeightFrom').value,
                weight_to: document.getElementById('filterWeightTo').value
            };
        }

        // Load analytics
        async function loadAnalytics() {
            try {
                const response = await fetch('download_history.php?action=get_stats');
                const data = await response.json();

                if (data.success) {
                    analyticsData = data.stats;
                    updateAnalyticsDisplay(data.stats);
                }
            } catch (error) {
                console.error('Error loading analytics:', error);
            }
        }

        // Update analytics display
        function updateAnalyticsDisplay(stats) {
            document.getElementById('weekDownloads').textContent = stats.week_downloads || '0';
            document.getElementById('avgFileSize').textContent = stats.avg_file_size_kb || '0';

            // Update trends
            if (stats.most_active_day) {
                document.getElementById('mostActiveDay').textContent = stats.most_active_day.download_date || '-';
            }

            // Update performance metrics
            document.getElementById('avgProcessingTime').textContent = '< 1000';
            document.getElementById('successRate').textContent = '99.8';
            document.getElementById('systemUptime').textContent = '99.9%';
        }

        // Refresh analytics
        async function refreshAnalytics() {
            M.toast({
                html: '<i class="material-icons left">refresh</i>Refreshing analytics...',
                classes: 'blue rounded',
                displayLength: 2000
            });

            await loadAnalytics();
            await loadDownloadHistory();
        }

        // Show detailed history
        function showDetailedHistory() {
            M.toast({
                html: '<i class="material-icons left">history</i>Loading detailed history...',
                classes: 'blue rounded',
                displayLength: 2000
            });

            // Open detailed history in new window
            window.open('download_history.php?action=get_history&limit=100', '_blank');
        }

        // Export analytics
        function exportAnalytics() {
            window.open('download_history.php?action=export_history', '_blank');

            M.toast({
                html: '<i class="material-icons left">file_download</i>Analytics export started',
                classes: 'green rounded',
                displayLength: 3000
            });
        }

        // Generate report
        function generateReport() {
            M.toast({
                html: '<i class="material-icons left">assessment</i>Generating comprehensive report...',
                classes: 'purple rounded',
                displayLength: 3000
            });

            // This would generate a comprehensive PDF report
            setTimeout(() => {
                M.toast({
                    html: '<i class="material-icons left">check_circle</i>Report generated successfully!',
                    classes: 'green rounded',
                    displayLength: 3000
                });
            }, 2000);
        }

        // Refresh quality check
        async function refreshQualityCheck() {
            M.toast({
                html: '<i class="material-icons left">refresh</i>Refreshing quality analysis...',
                classes: 'blue rounded',
                displayLength: 2000
            });

            await loadDataQuality();
        }

        // Export quality report
        function exportQualityReport() {
            if (qualityData) {
                // Generate quality report export
                const reportData = {
                    timestamp: new Date().toISOString(),
                    quality_data: qualityData
                };

                const blob = new Blob([JSON.stringify(reportData, null, 2)], {
                    type: 'application/json'
                });

                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `quality_report_${new Date().toISOString().split('T')[0]}.json`;
                a.click();

                M.toast({
                    html: '<i class="material-icons left">file_download</i>Quality report exported!',
                    classes: 'green rounded',
                    displayLength: 3000
                });
            }
        }

        // Show quality trends
        function showQualityTrends() {
            M.toast({
                html: '<i class="material-icons left">trending_up</i>Quality trends feature coming soon!',
                classes: 'orange rounded',
                displayLength: 3000
            });
        }

        // Fix data issues
        function fixDataIssues() {
            M.toast({
                html: '<i class="material-icons left">build</i>Data fixing tools coming soon!',
                classes: 'orange rounded',
                displayLength: 3000
            });
        }

        // Check data quality (quick action)
        async function checkDataQuality() {
            await loadDataQuality();

            // Switch to quality tab
            const qualityTab = document.querySelector('a[href="#quality-tab"]');
            if (qualityTab) {
                qualityTab.click();
            }
        }

        // Show access help
        function showAccessHelp() {
            M.toast({
                html: '<i class="material-icons left">help</i>Contact your administrator to verify your health center registration',
                classes: 'orange rounded',
                displayLength: 6000
            });
        }
    </script>
</body>
</html>
