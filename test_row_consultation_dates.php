<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$row Consultation Date Fields - Complete Implementation</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .field-list {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
        
        .usage-example {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left blue-text">event</i>
                $row Consultation Date Fields - Complete Implementation
            </h3>
            <p class="center-align">Using all consultation date fields from database $row data in conditions</p>
        </div>

        <div class="demo-section">
            <h4>✅ All Consultation Date Fields Added</h4>
            <p>The function now checks all these $row fields in conditions:</p>
            
            <div class="field-list">
                <h6>Complete List of $row Consultation Date Fields:</h6>
                <div class="row">
                    <div class="col s12 m6">
                        <ul style="font-size: 13px; line-height: 1.8;">
                            <li><code>$row['DateofConsultationBCG']</code></li>
                            <li><code>$row['DateofConsultationPENTAHIB1']</code></li>
                            <li><code>$row['DateofConsultationPENTAHIB2']</code></li>
                            <li><code>$row['DateofConsultationPENTAHIB3']</code></li>
                            <li><code>$row['DateofConsultationOPV1v']</code></li>
                            <li><code>$row['DateofConsultationOPV2']</code></li>
                            <li><code>$row['DateofConsultationOPV3']</code></li>
                            <li><code>$row['DateofConsultationIPV1']</code></li>
                            <li><code>$row['DateofConsultationIPV2']</code></li>
                            <li><code>$row['DateofConsultationPCV1']</code></li>
                            <li><code>$row['DateofConsultationPCV2']</code></li>
                            <li><code>$row['DateofConsultationPCV3']</code></li>
                            <li><code>$row['DateofConsultationHEPAatBirth']</code></li>
                            <li><code>$row['DateofConsultationHEPAB1']</code></li>
                        </ul>
                    </div>
                    <div class="col s12 m6">
                        <ul style="font-size: 13px; line-height: 1.8;">
                            <li><code>$row['DateofConsultationHEPAB2']</code></li>
                            <li><code>$row['DateofConsultationHEPAB3']</code></li>
                            <li><code>$row['DateofConsultationHEPAB4']</code></li>
                            <li><code>$row['DateofConsultationAMV1']</code></li>
                            <li><code>$row['DateofConsultationMMR']</code></li>
                            <li><code>$row['DateofConsultationFIC']</code></li>
                            <li><code>$row['DateofConsultationCIC']</code></li>
                            <li><code>$row['DateofConsultationAMV2']</code></li>
                            <li><code>$row['DATEOFCONSULTATIONEXCLUSIVEBF']</code></li>
                            <li><code>$row['DATEOFCONSULTTT1']</code></li>
                            <li><code>$row['DATEOFCONSULTTT2']</code></li>
                            <li><code>$row['DATEOFCONSULTTT3']</code></li>
                            <li><code>$row['DATEOFCONSULTTT4']</code></li>
                            <li><code>$row['DATEOFCONSULTT5']</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>💻 Function Implementation</h4>
            <p>Here's how the function checks all consultation date fields in conditions:</p>
            
            <div class="code-block">
function getEarliestConsultationFromRow($row) {
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Check each date in conditions
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00') {
            $valid_dates[] = $date;
        }
    }
    
    // Return earliest date or null
    return !empty($valid_dates) ? min($valid_dates) : null;
}
            </div>
        </div>

        <div class="demo-section">
            <h4>🎯 Usage Examples</h4>
            
            <h5>1. Simple Print in &lt;p&gt; Tag</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
include 'earliest_consultation_row.php';

// In your database loop
while ($row = mysqli_fetch_assoc($result)) {
    echo "Child: " . $row['NameOfChild'];
    echo " | Earliest consultation: ";
    printEarliestConsultationFromRow($row);
}
?&gt;
                </div>
                <p><strong>Output:</strong> <code>&lt;p&gt;2023-01-20&lt;/p&gt;</code></p>
            </div>
            
            <h5>2. Conditional Logic</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
$earliest = getEarliestConsultationFromRow($row);

if ($earliest) {
    echo "First consultation was on: " . $earliest;
    
    // Calculate days since birth
    if (!empty($row['DateOfBirth'])) {
        $days = (strtotime($earliest) - strtotime($row['DateOfBirth'])) / (60*60*24);
        echo " (That was " . round($days) . " days after birth)";
    }
} else {
    echo "No consultation dates found for this child.";
}
?&gt;
                </div>
            </div>
            
            <h5>3. In Tables and Forms</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;!-- In table cells --&gt;
&lt;td&gt;
    &lt;?php printEarliestConsultationFromRow($row); ?&gt;
&lt;/td&gt;

&lt;!-- In form displays --&gt;
&lt;div class="consultation-info"&gt;
    &lt;label&gt;First Consultation Date:&lt;/label&gt;
    &lt;?php printEarliestConsultationFromRow($row); ?&gt;
&lt;/div&gt;

&lt;!-- With styling --&gt;
&lt;?php printEarliestConsultationFromRowStyled($row, 'highlight', 'color: blue; font-weight: bold;'); ?&gt;
                </div>
            </div>
            
            <h5>4. Advanced Conditions</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
// Check if child has any consultation dates
if (hasConsultationDates($row)) {
    $count = countConsultationDates($row);
    $earliest = getEarliestConsultationFromRow($row);
    
    echo "This child has " . $count . " consultation dates.";
    echo "First consultation was on: " . $earliest;
    
    // Check if consultation was within first month
    if (!empty($row['DateOfBirth'])) {
        $birth_time = strtotime($row['DateOfBirth']);
        $consult_time = strtotime($earliest);
        $days_diff = ($consult_time - $birth_time) / (60*60*24);
        
        if ($days_diff <= 30) {
            echo " ✅ Consultation within first month";
        } else {
            echo " ⚠️ Late consultation (" . round($days_diff) . " days)";
        }
    }
} else {
    echo "❌ No consultation dates recorded for this child.";
}
?&gt;
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Integration in Your Files</h4>
            
            <h5>✅ Already Updated:</h5>
            <ul>
                <li><strong>filter_records.php</strong> - Now uses all consultation date fields</li>
                <li><strong>demo_earliest_p_tag.php</strong> - Shows live examples with database data</li>
                <li><strong>earliest_consultation_row.php</strong> - Complete function library</li>
            </ul>
            
            <h5>🔧 Easy to Add to Any File:</h5>
            <div class="code-block">
&lt;?php
// Step 1: Include the function file
include 'earliest_consultation_row.php';

// Step 2: Use in your database loop
$sql = "SELECT * FROM nip_table WHERE ...";
$result = mysqli_query($conn, $sql);

while ($row = mysqli_fetch_assoc($result)) {
    // Step 3: Print earliest consultation date
    echo "Earliest consultation: ";
    printEarliestConsultationFromRow($row);
}
?&gt;
            </div>
        </div>

        <div class="demo-section">
            <h4>🧪 Test Conditions</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Condition 1: Has Dates</span>
                            <div class="code-block" style="font-size: 11px;">
if (hasConsultationDates($row)) {
    echo "Child has consultations";
    printEarliestConsultationFromRow($row);
}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Condition 2: Count Dates</span>
                            <div class="code-block" style="font-size: 11px;">
$count = countConsultationDates($row);
if ($count >= 5) {
    echo "Well documented child";
}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Condition 3: Early Consultation</span>
                            <div class="code-block" style="font-size: 11px;">
$earliest = getEarliestConsultationFromRow($row);
if ($earliest && strtotime($earliest) <= strtotime($row['DateOfBirth']) + (30*24*60*60)) {
    echo "Early consultation ✅";
}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">Condition 4: No Dates</span>
                            <div class="code-block" style="font-size: 11px;">
if (!hasConsultationDates($row)) {
    echo "⚠️ No consultations recorded";
    echo "&lt;p&gt;Please schedule consultation&lt;/p&gt;";
}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>All consultation date fields are now working in conditions!</p>
            
            <div class="row">
                <div class="col s12 m4">
                    <a href="demo_earliest_p_tag.php" class="btn large blue waves-effect">
                        <i class="material-icons left">event</i>Live Demo
                    </a>
                    <p><small>See with real database data</small></p>
                </div>
                
                <div class="col s12 m4">
                    <a href="filter_records.php" class="btn large green waves-effect">
                        <i class="material-icons left">table_chart</i>Records Table
                    </a>
                    <p><small>Updated with all fields</small></p>
                </div>
                
                <div class="col s12 m4">
                    <a href="earliest_consultation_row.php" class="btn large orange waves-effect">
                        <i class="material-icons left">code</i>Function File
                    </a>
                    <p><small>Complete function library</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📋 Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>✅ All $row Consultation Date Fields Added to Conditions</h6>
                    <ul>
                        <li><strong>28 Fields:</strong> All consultation date fields from your list</li>
                        <li><strong>Conditions:</strong> Each field checked with proper validation</li>
                        <li><strong>Print in &lt;p&gt;:</strong> <code>printEarliestConsultationFromRow($row)</code></li>
                        <li><strong>Get Value:</strong> <code>getEarliestConsultationFromRow($row)</code></li>
                        <li><strong>Check Existence:</strong> <code>hasConsultationDates($row)</code></li>
                        <li><strong>Count Dates:</strong> <code>countConsultationDates($row)</code></li>
                        <li><strong>Easy Integration:</strong> Just include file and call function</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
