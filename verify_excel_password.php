<?php
/**
 * Excel Password Verification Endpoint
 * Uses the specific SQL query: SELECT * FROM health_facility WHERE (username = ? OR mobile_number = ?) AND password = ?
 */

session_start();
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include configuration
include_once 'config.php';

function sendResponse($success, $data = null, $message = '') {
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

function verifyExcelCredentials($username, $password) {
    // Database connection
    $conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
    
    if (!$conn) {
        logMessage('ERROR', 'Database connection failed for Excel verification', [
            'error' => mysqli_error($conn)
        ]);
        return false;
    }
    
    // Use the exact SQL query as specified
    $sql = "SELECT * FROM health_facility WHERE (username = ? OR mobile_number = ?) AND password = ?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        logMessage('ERROR', 'Failed to prepare Excel verification statement', [
            'error' => $conn->error,
            'sql' => $sql
        ]);
        $conn->close();
        return false;
    }
    
    // Bind parameters: username for both username and mobile_number fields
    $stmt->bind_param("sss", $username, $username, $password);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        logMessage('INFO', 'Excel credentials verified successfully', [
            'username' => $username,
            'facility' => $user['health_center'] ?? 'Unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 0, 100)
        ]);
        
        $stmt->close();
        $conn->close();
        return $user;
    } else {
        logMessage('WARNING', 'Excel credentials verification failed', [
            'username' => $username,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown', 0, 100),
            'sql_query' => 'SELECT * FROM health_facility WHERE (username = ? OR mobile_number = ?) AND password = ?'
        ]);
        
        $stmt->close();
        $conn->close();
        return false;
    }
}

try {
    // Check request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(false, null, 'Only POST requests are allowed');
    }

    // Get input data
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    // Validate input
    if (empty($username) || empty($password)) {
        sendResponse(false, [
            'access_granted' => false,
            'reason' => 'missing_credentials'
        ], 'Username and password are required for Excel download');
    }

    // Rate limiting check for Excel downloads
    $rateLimitKey = $clientIP . '_excel_password_attempts';
    if (!checkRateLimit($rateLimitKey, 5, 900)) { // 5 attempts per 15 minutes
        logMessage('WARNING', 'Rate limit exceeded for Excel password verification', [
            'ip' => $clientIP,
            'username' => $username
        ]);
        sendResponse(false, [
            'access_granted' => false,
            'reason' => 'rate_limit_exceeded'
        ], 'Too many failed attempts. Please try again later.');
    }

    // Verify credentials using the specific SQL query
    $user = verifyExcelCredentials($username, $password);

    if ($user) {
        // Credentials CORRECT - GRANT ACCESS
        $_SESSION['excel_verified'] = true;
        $_SESSION['excel_user'] = $user;
        $_SESSION['excel_verification_time'] = time();
        $_SESSION['excel_verification_ip'] = $clientIP;

        sendResponse(true, [
            'user' => $user['username'],
            'facility' => $user['health_center'] ?? 'Unknown',
            'access_granted' => true,
            'status' => 'verified',
            'verification_time' => date('Y-m-d H:i:s'),
            'expires_in' => 3600 // 1 hour
        ], 'Password verified successfully - Excel download access granted');

    } else {
        // Credentials WRONG - BLOCK ACCESS
        sendResponse(false, [
            'access_granted' => false,
            'status' => 'blocked',
            'reason' => 'invalid_credentials'
        ], 'Invalid username or password - Excel download blocked. Data will not proceed.');
    }

} catch (Exception $e) {
    logMessage('ERROR', 'Excel password verification error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'username' => $username ?? 'Unknown',
        'ip' => $clientIP ?? 'Unknown'
    ]);
    
    sendResponse(false, [
        'access_granted' => false,
        'status' => 'error',
        'reason' => 'system_error'
    ], 'An error occurred while verifying credentials - Excel download blocked');
}
?>
