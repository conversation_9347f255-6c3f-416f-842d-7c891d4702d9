<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Duplicate Record Toast - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .toast-demo {
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 25px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            animation: slideInRight 0.3s ease-out;
        }
        
        .toast-demo.success {
            background: #4caf50;
        }
        
        .toast-demo i {
            margin-right: 10px;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
        }
        
        .step-card {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="test-section">
            <h3 class="center-align">
                <i class="material-icons left red-text">error</i>
                Duplicate Record Toast Test
            </h3>
            <p class="center-align">Testing the red toast notification for duplicate records</p>
        </div>

        <div class="test-section">
            <h4>🔴 Red Toast for Duplicate Records</h4>
            <p>When a duplicate record is detected, this red toast notification will appear:</p>
            
            <div class="toast-demo">
                <i class="material-icons">error</i>
                Record already exists! Child with same name, birth date, and mother already registered.
            </div>
            
            <button class="btn red waves-effect" onclick="showDuplicateToast()">
                <i class="material-icons left">error</i>Test Duplicate Toast
            </button>
        </div>

        <div class="test-section">
            <h4>🟢 Green Toast for Success</h4>
            <p>When a record is successfully added, this green toast notification will appear:</p>
            
            <div class="toast-demo success">
                <i class="material-icons">check_circle</i>
                Record successfully added to the system!
            </div>
            
            <button class="btn green waves-effect" onclick="showSuccessToast()">
                <i class="material-icons left">check_circle</i>Test Success Toast
            </button>
        </div>

        <div class="test-section">
            <h4>⚙️ Implementation in db.php</h4>
            <p>The duplicate validation is implemented in your db.php file:</p>
            
            <div class="code-block">
// Check for duplicate records before inserting
$duplicate_check_sql = "SELECT id FROM nip_table 
                       WHERE NameOfChild = ? 
                       AND LastNameOfChild = ? 
                       AND DateOfBirth = ? 
                       AND NameofMother = ? 
                       AND (deleted IS NULL OR deleted = 0)";

if (mysqli_num_rows($duplicate_result) > 0) {
    // Show red toast notification
    echo '&lt;script&gt;
        document.addEventListener("DOMContentLoaded", function() {
            M.toast({
                html: "&lt;i class=\"material-icons left\"&gt;error&lt;/i&gt;Record already exists!",
                classes: "red darken-2 white-text",
                displayLength: 6000
            });
        });
    &lt;/script&gt;';
}
            </div>
        </div>

        <div class="test-section">
            <h4>🧪 How to Test Duplicate Detection</h4>
            
            <div class="step-card">
                <h5>Step 1: Register a Child</h5>
                <p>Go to the registration form and register a child with these details:</p>
                <ul>
                    <li><strong>First Name:</strong> Juan</li>
                    <li><strong>Last Name:</strong> Dela Cruz</li>
                    <li><strong>Birth Date:</strong> 2023-05-15</li>
                    <li><strong>Mother's Name:</strong> Maria Dela Cruz</li>
                </ul>
            </div>
            
            <div class="step-card">
                <h5>Step 2: Try to Register the Same Child Again</h5>
                <p>Use the exact same details and submit the form again. You should see:</p>
                <ul>
                    <li>🔴 Red toast notification</li>
                    <li>📋 Error card with details</li>
                    <li>🚫 Record will NOT be inserted</li>
                </ul>
            </div>
            
            <div class="step-card">
                <h5>Step 3: Register a Different Child</h5>
                <p>Change any of the key fields (name, birth date, or mother) and submit:</p>
                <ul>
                    <li>🟢 Green toast notification</li>
                    <li>✅ Success message</li>
                    <li>💾 Record will be inserted</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h4>🔍 Duplicate Detection Rules</h4>
            <p>A record is considered duplicate if ALL these fields match:</p>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Required Matches</span>
                            <ul>
                                <li>✅ Child's First Name</li>
                                <li>✅ Child's Last Name</li>
                                <li>✅ Date of Birth</li>
                                <li>✅ Mother's Name</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Allowed Differences</span>
                            <ul>
                                <li>📝 Middle Name</li>
                                <li>🏠 Address</li>
                                <li>📞 Phone Number</li>
                                <li>💉 Vaccination Status</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section center-align">
            <h4>🔗 Test the Registration Form</h4>
            <a href="nip.php" class="btn large blue waves-effect">
                <i class="material-icons left">add</i>Go to Registration Form
            </a>
            <br><br>
            <a href="filter_records.php" class="btn orange waves-effect">
                <i class="material-icons left">search</i>View Existing Records
            </a>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });

        function showDuplicateToast() {
            M.toast({
                html: '<i class="material-icons left">error</i>Record already exists! Child with same name, birth date, and mother already registered.',
                classes: 'red darken-2 white-text',
                displayLength: 6000
            });
        }

        function showSuccessToast() {
            M.toast({
                html: '<i class="material-icons left">check_circle</i>Record successfully added to the system!',
                classes: 'green darken-1 white-text',
                displayLength: 4000
            });
        }

        // Show demo toast on page load
        setTimeout(function() {
            M.toast({
                html: '<i class="material-icons left">info</i>Click the buttons above to test toast notifications!',
                classes: 'blue darken-1 white-text',
                displayLength: 3000
            });
        }, 1000);
    </script>
</body>
</html>
