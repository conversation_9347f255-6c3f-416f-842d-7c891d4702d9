<?php
// Function to find and print the earliest consultation date
function getEarliestConsultationDate($data) {
    // Array of all consultation date fields
    $consultation_dates = [
        'DateofConsultationBCG',
        'DateofConsultationPENTAHIB1',
        'DateofConsultationPENTAHIB2', 
        'DateofConsultationPENTAHIB3',
        'DateofConsultationOPV1v',
        'DateofConsultationOPV2',
        'DateofConsultationOPV3',
        'DateofConsultationIPV1',
        'DateofConsultationIPV2',
        'DateofConsultationPCV1',
        'DateofConsultationPCV2',
        'DateofConsultationPCV3',
        'DateofConsultationHEPAatBirth',
        'DateofConsultationHEPAB1',
        'DateofConsultationHEPAB2',
        'DateofConsultationHEPAB3',
        'DateofConsultationHEPAB4',
        'DateofConsultationAMV1',
        'DateofConsultationMMR',
        'DateofConsultationFIC',
        'DateofConsultationCIC',
        'DateofConsultationAMV2',
        'DATEOFCONSULTATIONEXCLUSIVEBF',
        'DATEOFCONSULTTT1',
        'DATEOFCONSULTTT2',
        'DATEOFCONSULTTT3',
        'DATEOFCONSULTTT4',
        'DATEOFCONSULTT5'
    ];
    
    $valid_dates = [];
    
    // Collect all valid dates
    foreach ($consultation_dates as $field) {
        if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== '0000-00-00') {
            // Validate date format
            $date = DateTime::createFromFormat('Y-m-d', $data[$field]);
            if ($date && $date->format('Y-m-d') === $data[$field]) {
                $valid_dates[] = $data[$field];
            }
        }
    }
    
    // Return earliest date or null if no valid dates
    return empty($valid_dates) ? null : min($valid_dates);
}

// Function to print earliest consultation date with details
function printEarliestConsultationDate($data) {
    $earliest_date = getEarliestConsultationDate($data);
    
    if ($earliest_date) {
        echo '<div class="card blue lighten-4" style="margin: 20px 0; padding: 15px;">';
        echo '<h6><i class="material-icons left">event</i>Earliest Consultation Date</h6>';
        echo '<p style="font-size: 18px; font-weight: bold; color: #1976d2;">' . date('F j, Y', strtotime($earliest_date)) . '</p>';
        echo '<p><strong>Date:</strong> ' . $earliest_date . '</p>';
        echo '<p><strong>Days since birth:</strong> ' . calculateDaysSinceBirth($data['DateOfBirth'], $earliest_date) . ' days</p>';
        echo '</div>';
        
        return $earliest_date;
    } else {
        echo '<div class="card orange lighten-4" style="margin: 20px 0; padding: 15px;">';
        echo '<h6><i class="material-icons left">warning</i>No Consultation Dates</h6>';
        echo '<p>No valid consultation dates found for this child.</p>';
        echo '</div>';
        
        return null;
    }
}

// Helper function to calculate days since birth
function calculateDaysSinceBirth($birth_date, $consultation_date) {
    if (empty($birth_date) || empty($consultation_date)) {
        return 0;
    }
    
    $birth = new DateTime($birth_date);
    $consultation = new DateTime($consultation_date);
    $interval = $birth->diff($consultation);
    
    return $interval->days;
}

// Function to get earliest consultation date from database record
function getEarliestConsultationFromDB($child_id, $conn) {
    $sql = "SELECT DateofConsultationBCG, DateofConsultationPENTAHIB1, DateofConsultationPENTAHIB2, 
                   DateofConsultationPENTAHIB3, DateofConsultationOPV1v, DateofConsultationOPV2, 
                   DateofConsultationOPV3, DateofConsultationIPV1, DateofConsultationIPV2,
                   DateofConsultationPCV1, DateofConsultationPCV2, DateofConsultationPCV3,
                   DateofConsultationHEPAatBirth, DateofConsultationHEPAB1, DateofConsultationHEPAB2,
                   DateofConsultationHEPAB3, DateofConsultationHEPAB4, DateofConsultationAMV1,
                   DateofConsultationMMR, DateofConsultationFIC, DateofConsultationCIC,
                   DateofConsultationAMV2, DATEOFCONSULTATIONEXCLUSIVEBF, DATEOFCONSULTTT1,
                   DATEOFCONSULTTT2, DATEOFCONSULTTT3, DATEOFCONSULTTT4, DATEOFCONSULTT5,
                   DateOfBirth, NameOfChild, LastNameOfChild
            FROM nip_table 
            WHERE id = ? AND (deleted IS NULL OR deleted = 0)";
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $child_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($row = mysqli_fetch_assoc($result)) {
        return $row;
    }
    
    return null;
}

// Example usage with POST data
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Include the date.php variables
     
    
    // Create data array from POST variables
    $consultation_data = [
        'DateofConsultationBCG' => $DateofConsultationBCG ?? '',
        'DateofConsultationPENTAHIB1' => $DateofConsultationPENTAHIB1 ?? '',
        'DateofConsultationPENTAHIB2' => $DateofConsultationPENTAHIB2 ?? '',
        'DateofConsultationPENTAHIB3' => $DateofConsultationPENTAHIB3 ?? '',
        'DateofConsultationOPV1v' => $DateofConsultationOPV1v ?? '',
        'DateofConsultationOPV2' => $DateofConsultationOPV2 ?? '',
        'DateofConsultationOPV3' => $DateofConsultationOPV3 ?? '',
        'DateofConsultationIPV1' => $DateofConsultationIPV1 ?? '',
        'DateofConsultationIPV2' => $DateofConsultationIPV2 ?? '',
        'DateofConsultationPCV1' => $DateofConsultationPCV1 ?? '',
        'DateofConsultationPCV2' => $DateofConsultationPCV2 ?? '',
        'DateofConsultationPCV3' => $DateofConsultationPCV3 ?? '',
        'DateofConsultationHEPAatBirth' => $DateofConsultationHEPAatBirth ?? '',
        'DateofConsultationHEPAB1' => $DateofConsultationHEPAB1 ?? '',
        'DateofConsultationHEPAB2' => $DateofConsultationHEPAB2 ?? '',
        'DateofConsultationHEPAB3' => $DateofConsultationHEPAB3 ?? '',
        'DateofConsultationHEPAB4' => $DateofConsultationHEPAB4 ?? '',
        'DateofConsultationAMV1' => $DateofConsultationAMV1 ?? '',
        'DateofConsultationMMR' => $DateofConsultationMMR ?? '',
        'DateofConsultationFIC' => $DateofConsultationFIC ?? '',
        'DateofConsultationCIC' => $DateofConsultationCIC ?? '',
        'DateofConsultationAMV2' => $DateofConsultationAMV2 ?? '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => $DATEOFCONSULTATIONEXCLUSIVEBF ?? '',
        'DATEOFCONSULTTT1' => $DATEOFCONSULTTT1 ?? '',
        'DATEOFCONSULTTT2' => $DATEOFCONSULTTT2 ?? '',
        'DATEOFCONSULTTT3' => $DATEOFCONSULTTT3 ?? '',
        'DATEOFCONSULTTT4' => $DATEOFCONSULTTT4 ?? '',
        'DATEOFCONSULTT5' => $DATEOFCONSULTT5 ?? '',
        'DateOfBirth' => $_POST['DateOfBirth'] ?? ''
    ];
    
    // Print the earliest consultation date
    echo '<div style="max-width: 600px; margin: 20px auto; font-family: Arial, sans-serif;">';
    echo '<h4 style="text-align: center; color: #1976d2;">Earliest Consultation Date</h4>';
    printEarliestConsultationDate($consultation_data);
    echo '</div>';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Earliest Consultation Date - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .date-display {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .earliest-date {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h4><i class="material-icons left">event</i>Earliest Consultation Date Finder</h4>
            <p>This tool finds and displays the earliest consultation date from all antigen consultation dates.</p>
            
            <h5>How it works:</h5>
            <ul>
                <li>Collects all consultation dates from antigen records</li>
                <li>Validates date formats (YYYY-MM-DD)</li>
                <li>Filters out empty or invalid dates</li>
                <li>Returns the earliest valid date</li>
                <li>Calculates days since birth</li>
            </ul>
        </div>

        <div class="demo-section">
            <h5>Test with Sample Data</h5>
            <form method="POST" action="">
                <div class="row">
                    <div class="input-field col s12 m6">
                        <input type="date" id="DateOfBirth" name="DateOfBirth" value="2023-01-15">
                        <label for="DateOfBirth">Child's Birth Date</label>
                    </div>
                    <div class="input-field col s12 m6">
                        <input type="date" id="DateofConsultationBCG" name="DateofConsultationBCG" value="2023-02-15">
                        <label for="DateofConsultationBCG">BCG Consultation</label>
                    </div>
                </div>
                
                <div class="row">
                    <div class="input-field col s12 m6">
                        <input type="date" id="DateofConsultationPENTAHIB1" name="DateofConsultationPENTAHIB1" value="2023-03-15">
                        <label for="DateofConsultationPENTAHIB1">PENTA-HIB 1 Consultation</label>
                    </div>
                    <div class="input-field col s12 m6">
                        <input type="date" id="DateofConsultationOPV1v" name="DateofConsultationOPV1v" value="2023-02-10">
                        <label for="DateofConsultationOPV1v">OPV 1 Consultation</label>
                    </div>
                </div>
                
                <div class="row">
                    <div class="input-field col s12 m6">
                        <input type="date" id="DateofConsultationPCV1" name="DateofConsultationPCV1" value="2023-04-01">
                        <label for="DateofConsultationPCV1">PCV 1 Consultation</label>
                    </div>
                    <div class="input-field col s12 m6">
                        <input type="date" id="DateofConsultationMMR" name="DateofConsultationMMR" value="2023-05-20">
                        <label for="DateofConsultationMMR">MMR Consultation</label>
                    </div>
                </div>
                
                <div class="center-align">
                    <button type="submit" class="btn blue waves-effect">
                        <i class="material-icons left">search</i>Find Earliest Date
                    </button>
                </div>
            </form>
        </div>

        <div class="demo-section">
            <h5>Usage in Your System</h5>
            <p>To use this in your existing system, you can:</p>
            
            <div class="card blue lighten-4">
                <div class="card-content">
                    <h6>1. In Forms (like nip.php):</h6>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">
&lt;?php
include 'earliest_consultation_date.php';
$earliest = printEarliestConsultationDate($row);
?&gt;</pre>
                </div>
            </div>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>2. In Database Queries:</h6>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">
&lt;?php
$child_data = getEarliestConsultationFromDB($child_id, $conn);
$earliest = getEarliestConsultationDate($child_data);
echo "Earliest consultation: " . $earliest;
?&gt;</pre>
                </div>
            </div>
            
            <div class="card orange lighten-4">
                <div class="card-content">
                    <h6>3. In Reports:</h6>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">
&lt;?php
foreach ($children as $child) {
    $earliest = getEarliestConsultationDate($child);
    echo $child['NameOfChild'] . ": " . $earliest;
}
?&gt;</pre>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h5>Features</h5>
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Date Validation</span>
                            <ul>
                                <li>Validates YYYY-MM-DD format</li>
                                <li>Filters out empty dates</li>
                                <li>Ignores 0000-00-00 dates</li>
                                <li>Handles invalid date formats</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Additional Info</span>
                            <ul>
                                <li>Calculates days since birth</li>
                                <li>Formats date for display</li>
                                <li>Shows warning if no dates</li>
                                <li>Database integration ready</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
