<?php
/**
 * Database Migration to Add Disapprove Functionality
 * Run this script once to add disapproved_date and disapproval_reason columns
 */

include 'db.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Adding Disapprove Functionality</h2>";
echo "<p>Starting migration...</p>";

try {
    // Check if disapproved_date column already exists
    $check_column = "SHOW COLUMNS FROM health_facility LIKE 'disapproved_date'";
    $result = $conn->query($check_column);
    
    if ($result->num_rows == 0) {
        // Add disapproved_date column
        $add_column_sql = "ALTER TABLE health_facility ADD COLUMN disapproved_date TIMESTAMP NULL DEFAULT NULL";
        
        if ($conn->query($add_column_sql) === TRUE) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "✓ Successfully added disapproved_date column to health_facility table";
            echo "</div>";
        } else {
            throw new Exception("Error adding disapproved_date column: " . $conn->error);
        }
    } else {
        echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "ℹ disapproved_date column already exists in health_facility table";
        echo "</div>";
    }
    
    // Check if disapproval_reason column already exists
    $check_reason_column = "SHOW COLUMNS FROM health_facility LIKE 'disapproval_reason'";
    $result_reason = $conn->query($check_reason_column);
    
    if ($result_reason->num_rows == 0) {
        // Add disapproval_reason column
        $add_reason_column_sql = "ALTER TABLE health_facility ADD COLUMN disapproval_reason TEXT NULL";
        
        if ($conn->query($add_reason_column_sql) === TRUE) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "✓ Successfully added disapproval_reason column to health_facility table";
            echo "</div>";
        } else {
            throw new Exception("Error adding disapproval_reason column: " . $conn->error);
        }
    } else {
        echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "ℹ disapproval_reason column already exists in health_facility table";
        echo "</div>";
    }
    
    // Check if approved_date column already exists
    $check_approved_column = "SHOW COLUMNS FROM health_facility LIKE 'approved_date'";
    $result_approved = $conn->query($check_approved_column);
    
    if ($result_approved->num_rows == 0) {
        // Add approved_date column
        $add_approved_column_sql = "ALTER TABLE health_facility ADD COLUMN approved_date TIMESTAMP NULL DEFAULT NULL";
        
        if ($conn->query($add_approved_column_sql) === TRUE) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "✓ Successfully added approved_date column to health_facility table";
            echo "</div>";
        } else {
            throw new Exception("Error adding approved_date column: " . $conn->error);
        }
    } else {
        echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "ℹ approved_date column already exists in health_facility table";
        echo "</div>";
    }
    
    // Create audit log table if it doesn't exist
    $create_audit_table = "
    CREATE TABLE IF NOT EXISTS user_action_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        table_name VARCHAR(50) NOT NULL,
        action_type VARCHAR(20) NOT NULL,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        performed_by VARCHAR(100),
        reason TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        INDEX idx_user_id (user_id),
        INDEX idx_action_date (action_date),
        INDEX idx_action_type (action_type)
    )";
    
    if ($conn->query($create_audit_table) === TRUE) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "✓ Successfully created/verified user_action_logs table";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "⚠ Warning: Could not create user_action_logs table: " . $conn->error;
        echo "</div>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✓ Migration Completed Successfully!</h4>";
    echo "<p>The disapprove functionality has been added to your database.</p>";
    echo "<p><strong>Features added:</strong></p>";
    echo "<ul>";
    echo "<li>disapproved_date column in health_facility table</li>";
    echo "<li>disapproval_reason column in health_facility table</li>";
    echo "<li>approved_date column in health_facility table</li>";
    echo "<li>user_action_logs table for audit trail</li>";
    echo "</ul>";
    echo "<p><strong>You can now approve and disapprove users with full audit trail!</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ Migration Failed!</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
    echo "</div>";
}

$conn->close();
?>
