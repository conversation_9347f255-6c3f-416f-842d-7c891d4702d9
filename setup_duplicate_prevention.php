<?php
// Setup script for duplicate prevention system
include 'db.php';

echo "<h2>🛡️ Setting Up Duplicate Prevention System</h2>";

// Read and execute SQL file
$sql_file = 'create_duplicate_block_log_table.sql';
if (file_exists($sql_file)) {
    $sql_content = file_get_contents($sql_file);
    
    // Split SQL statements
    $statements = array_filter(array_map('trim', explode(';', $sql_content)));
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            if ($conn->query($statement)) {
                $success_count++;
                echo "<p style='color: green;'>✅ Executed: " . substr($statement, 0, 50) . "...</p>";
            } else {
                $error_count++;
                echo "<p style='color: red;'>❌ Error: " . $conn->error . "</p>";
                echo "<p style='color: red;'>Statement: " . substr($statement, 0, 100) . "...</p>";
            }
        } catch (Exception $e) {
            $error_count++;
            echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>📊 Setup Summary:</h3>";
    echo "<p><strong>Successful statements:</strong> $success_count</p>";
    echo "<p><strong>Failed statements:</strong> $error_count</p>";
    
} else {
    echo "<p style='color: red;'>❌ SQL file not found: $sql_file</p>";
}

// Test the duplicate prevention system
echo "<hr>";
echo "<h3>🧪 Testing Duplicate Prevention System</h3>";

try {
    // Test 1: Check if duplicate_block_log table exists
    $result = $conn->query("SHOW TABLES LIKE 'duplicate_block_log'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ duplicate_block_log table exists</p>";
        
        // Show table structure
        $structure = $conn->query("DESCRIBE duplicate_block_log");
        echo "<h4>Table Structure:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>❌ duplicate_block_log table not found</p>";
    }
    
    // Test 2: Check if nip_table has deleted column
    $result = $conn->query("SHOW COLUMNS FROM nip_table LIKE 'deleted'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ nip_table has 'deleted' column</p>";
    } else {
        echo "<p style='color: red;'>❌ nip_table missing 'deleted' column</p>";
    }
    
    // Test 3: Check if user_action_logs table exists
    $result = $conn->query("SHOW TABLES LIKE 'user_action_logs'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ user_action_logs table exists</p>";
    } else {
        echo "<p style='color: red;'>❌ user_action_logs table not found</p>";
    }
    
    // Test 4: Test duplicate checking functions
    echo "<h4>Testing Duplicate Functions:</h4>";
    
    if (function_exists('generateUniqueFamilySerial')) {
        $test_serial = generateUniqueFamilySerial($conn);
        echo "<p style='color: green;'>✅ generateUniqueFamilySerial() works: $test_serial</p>";
    } else {
        echo "<p style='color: red;'>❌ generateUniqueFamilySerial() function not found</p>";
    }
    
    if (function_exists('checkForDuplicates')) {
        $test_check = checkForDuplicates($conn, 'Test', 'Child', '2020-01-01', 'Test Mother', '', '', '');
        echo "<p style='color: green;'>✅ checkForDuplicates() works - Found: " . ($test_check['found'] ? 'Yes' : 'No') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ checkForDuplicates() function not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Testing error: " . $e->getMessage() . "</p>";
}

// Show current database statistics
echo "<hr>";
echo "<h3>📈 Current Database Statistics</h3>";

try {
    // Total records
    $total_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE deleted = 0");
    $total_row = $total_result->fetch_assoc();
    echo "<p><strong>Total Active Records:</strong> {$total_row['total']}</p>";
    
    // Deleted records
    $deleted_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE deleted = 1");
    if ($deleted_result) {
        $deleted_row = $deleted_result->fetch_assoc();
        echo "<p><strong>Deleted Records:</strong> {$deleted_row['total']}</p>";
    }
    
    // Blocked attempts
    $blocked_result = $conn->query("SELECT COUNT(*) as total FROM duplicate_block_log");
    if ($blocked_result) {
        $blocked_row = $blocked_result->fetch_assoc();
        echo "<p><strong>Blocked Duplicate Attempts:</strong> {$blocked_row['total']}</p>";
    }
    
    // Recent blocked attempts
    $recent_blocked = $conn->query("SELECT * FROM duplicate_block_log ORDER BY blocked_date DESC LIMIT 5");
    if ($recent_blocked && $recent_blocked->num_rows > 0) {
        echo "<h4>Recent Blocked Attempts:</h4>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Child Name</th><th>DOB</th><th>Mother</th><th>Blocked Date</th><th>Reason</th></tr>";
        while ($row = $recent_blocked->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['child_name']} {$row['last_name']}</td>";
            echo "<td>{$row['dob']}</td>";
            echo "<td>{$row['mother_name']}</td>";
            echo "<td>{$row['blocked_date']}</td>";
            echo "<td>{$row['blocked_reason']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No blocked attempts recorded yet.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Statistics error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test Registration:</strong> Try registering a child through the normal form</li>";
echo "<li><strong>Test Duplicate:</strong> Try registering the same child again to see blocking in action</li>";
echo "<li><strong>Check Logs:</strong> View the duplicate_block_log table for blocked attempts</li>";
echo "<li><strong>Monitor System:</strong> Use the duplicate checker tools to manage data quality</li>";
echo "</ol>";

echo "<h3>🔗 Useful Links:</h3>";
echo "<p>";
echo "<a href='nip.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='duplicate_checker.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Duplicate Checker</a>";
echo "<a href='realtime_duplicate_check.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>⚡ Real-time Check</a>";
echo "<a href='notification_demo.php' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎮 Notification Demo</a>";
echo "</p>";

echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin-top: 20px;'>";
echo "<h4>✅ Duplicate Prevention System Features:</h4>";
echo "<ul>";
echo "<li><strong>Automatic Blocking:</strong> Prevents exact duplicate registrations</li>";
echo "<li><strong>Smart Detection:</strong> Uses multiple criteria (name, DOB, mother)</li>";
echo "<li><strong>Audit Trail:</strong> Logs all blocked attempts for review</li>";
echo "<li><strong>User Notifications:</strong> Clear feedback when duplicates are found</li>";
echo "<li><strong>Data Integrity:</strong> Maintains clean, duplicate-free database</li>";
echo "<li><strong>Soft Delete:</strong> Marks records as deleted instead of permanent removal</li>";
echo "<li><strong>Real-time Checking:</strong> Live duplicate detection as users type</li>";
echo "<li><strong>Administrative Tools:</strong> Comprehensive duplicate management interface</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th {
    background-color: #2196f3;
    color: white;
    font-weight: bold;
    text-align: left;
    padding: 8px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3, h4 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

ol, ul {
    line-height: 1.6;
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}
</style>
