# 🚫 Complete Disapprove Functionality Guide

## 📋 **Overview**

This guide documents the complete disapprove functionality implementation for the user management system. The system now supports both **approve** and **disapprove** actions with comprehensive audit logging and user feedback.

## 🎯 **Features Implemented**

### **1. Dual Action System**
- ✅ **Approve Button**: Green button with check icon
- ✅ **Disapprove Button**: Red button with close icon  
- ✅ **Status Tracking**: Three states (Pending, Approved, Disapproved)
- ✅ **Visual Feedback**: Color-coded status badges

### **2. Enhanced Modal Interface**
- **User Information Display**: Complete user details
- **Action Confirmation**: Clear confirmation messages
- **Disapproval Reason**: Optional text field for reasons
- **Visual Indicators**: Color-coded icons and styling

### **3. Database Enhancements**
- **Dynamic Column Creation**: Automatically adds missing columns
- **Audit Logging**: Comprehensive action tracking
- **Flexible Schema**: Works with existing and new databases
- **Multi-table Support**: Handles both `users` and `health_facility` tables

## 🔧 **Technical Implementation**

### **Frontend Changes (user_settings.php)**

#### **Status Display:**
```php
<?php if($row['approved'] == 1) {  ?>
    <span class="new badge teal accent-3">Approved</span>
<?php } elseif($row['approved'] == -1) { ?>
    <span class="new badge red accent-3">Disapproved</span>
<?php } else {  ?>
    <span class="new badge blue-grey lighten-4 black-text">Pending</span>
<?php } ?>
```

#### **Action Buttons:**
```php
<button class="approve-btn btn btn-small green" data-action="approve">
    <i class="material-icons left">check</i>Approve
</button>
<button class="disapprove-btn btn btn-small red" data-action="disapprove">
    <i class="material-icons left">close</i>Disapprove
</button>
```

#### **Enhanced JavaScript:**
```javascript
// Handle approve/disapprove button clicks
$(document).on('click', '.approve-btn, .disapprove-btn', function() {
    const action = $(this).data('action');
    openActionModal($(this), action);
});

// Dynamic modal configuration
function openActionModal(button, action) {
    if (action === 'approve') {
        $("#modalIcon").text("check_circle").css("color", "#4caf50");
        $("#confirmationCard").addClass("green lighten-4");
        $("#disapprovalReasonSection").hide();
    } else {
        $("#modalIcon").text("cancel").css("color", "#f44336");
        $("#confirmationCard").addClass("red lighten-4");
        $("#disapprovalReasonSection").show();
    }
}
```

### **Backend Changes (update_user.php)**

#### **Action Handling:**
```php
// Validate action
if (!in_array($action, ['approve', 'disapprove'])) {
    echo "error: Invalid action";
    exit;
}

// Dynamic SQL building
if ($action === 'approve') {
    $update_fields = ["approved = 1"];
    if (in_array('approved_date', $available_columns)) {
        $update_fields[] = "approved_date = NOW()";
    }
} else if ($action === 'disapprove') {
    $update_fields = ["approved = -1"];
    if (in_array('disapproved_date', $available_columns)) {
        $update_fields[] = "disapproved_date = NOW()";
    }
    if (in_array('disapproval_reason', $available_columns)) {
        $update_fields[] = "disapproval_reason = ?";
    }
}
```

#### **Audit Logging:**
```php
// Create audit log entry
$log_sql = "INSERT INTO user_action_logs 
           (user_id, table_name, action_type, performed_by, reason, ip_address, user_agent) 
           VALUES (?, ?, ?, ?, ?, ?, ?)";

$performed_by = $_SESSION['username'] ?? $_SESSION['health_center'] ?? 'System Admin';
$ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
```

## 📊 **Database Schema**

### **Enhanced User Tables:**
```sql
-- Additional columns added automatically
ALTER TABLE users ADD COLUMN approved_date TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE users ADD COLUMN disapproved_date TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE users ADD COLUMN disapproval_reason TEXT NULL;

ALTER TABLE health_facility ADD COLUMN approved_date TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE health_facility ADD COLUMN disapproved_date TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE health_facility ADD COLUMN disapproval_reason TEXT NULL;
```

### **Audit Log Table:**
```sql
CREATE TABLE user_action_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    action_type VARCHAR(20) NOT NULL,
    action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    performed_by VARCHAR(100),
    reason TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_user_id (user_id),
    INDEX idx_action_date (action_date),
    INDEX idx_action_type (action_type)
);
```

## 🎪 **User Experience**

### **Visual States:**
- **Pending**: Grey badge, both buttons enabled when selected
- **Approved**: Green badge, buttons replaced with "Approved" message
- **Disapproved**: Red badge, buttons replaced with "Disapproved" message

### **Interactive Flow:**
1. **Select User**: Check checkbox to enable action buttons
2. **Choose Action**: Click Approve (green) or Disapprove (red)
3. **Confirm Details**: Review user information in modal
4. **Add Reason**: (Optional for disapprove) Enter disapproval reason
5. **Execute Action**: Confirm to process the action
6. **Visual Feedback**: Toast notification and table update

### **Error Handling:**
- **Validation Errors**: Clear error messages for invalid data
- **Network Errors**: Timeout and connection error handling
- **Server Errors**: Detailed error messages with logging
- **Fallback Options**: Graceful degradation when features unavailable

## 🔒 **Security Features**

### **Input Validation:**
```php
// Sanitize all inputs
$username = trim(htmlspecialchars($_POST['username'], ENT_QUOTES, 'UTF-8'));
$email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);

// Validate email format
if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo "error: Invalid email format";
    exit;
}
```

### **Access Control:**
```php
// Session validation
if (!isset($_SESSION['health_center']) || empty($_SESSION['health_center'])) {
    echo "error: Unauthorized access - Please login";
    exit;
}
```

### **Audit Trail:**
- **Complete Logging**: All actions logged with timestamps
- **User Attribution**: Tracks who performed each action
- **IP Tracking**: Records IP addresses for security
- **Reason Storage**: Preserves disapproval reasons

## 🚀 **Testing Guide**

### **1. Run Test Script:**
```bash
# Access the test script in your browser
http://your-domain/test_disapprove_functionality.php
```

### **2. Manual Testing Steps:**
1. **Access User Settings**: Go to `user_settings.php`
2. **Find Pending User**: Look for users with "Pending" status
3. **Select User**: Check the checkbox next to a user
4. **Test Approve**: Click green "Approve" button, confirm action
5. **Test Disapprove**: Click red "Disapprove" button, add reason, confirm
6. **Verify Results**: Check status updates and audit logs

### **3. Database Verification:**
```sql
-- Check user status
SELECT id, username, approved, approved_date, disapproved_date, disapproval_reason 
FROM users WHERE id = [USER_ID];

-- Check audit logs
SELECT * FROM user_action_logs WHERE user_id = [USER_ID] ORDER BY action_date DESC;
```

## 📈 **Monitoring & Maintenance**

### **Regular Checks:**
- **Audit Log Review**: Monitor for unusual patterns
- **Error Log Monitoring**: Check server error logs
- **Performance Tracking**: Monitor query performance
- **User Feedback**: Collect user experience feedback

### **Maintenance Tasks:**
```sql
-- Clean old audit logs (older than 1 year)
DELETE FROM user_action_logs WHERE action_date < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- Check for orphaned records
SELECT * FROM user_action_logs l 
LEFT JOIN users u ON l.user_id = u.id 
WHERE u.id IS NULL;
```

## 🔧 **Troubleshooting**

### **Common Issues:**

#### **1. Buttons Not Appearing:**
- Check if user status is "Pending" (approved = 0)
- Verify JavaScript is loading correctly
- Check browser console for errors

#### **2. AJAX Requests Failing:**
- Verify `update_user.php` file exists and is accessible
- Check server error logs for PHP errors
- Ensure database connection is working

#### **3. Database Errors:**
- Run the test script to check database structure
- Verify required columns exist
- Check database user permissions

#### **4. Modal Not Opening:**
- Ensure Materialize CSS/JS is loaded
- Check for JavaScript conflicts
- Verify modal HTML structure

### **Debug Commands:**
```javascript
// Check if elements exist
console.log($('.approve-btn').length);
console.log($('.disapprove-btn').length);
console.log($('#actionModal').length);

// Test AJAX endpoint
$.post('update_user.php', {id: 1, action: 'test'}, function(response) {
    console.log('Response:', response);
});
```

## 📞 **Support Information**

### **File Structure:**
```
project/
├── user_settings.php          # Main user management interface
├── update_user.php           # Backend processing script
├── test_disapprove_functionality.php  # Testing script
├── DISAPPROVE_FUNCTIONALITY_GUIDE.md  # This documentation
└── db.php                    # Database connection
```

### **Key Functions:**
- `enable(rowId)` - Enables/disables action buttons
- `openActionModal(button, action)` - Opens confirmation modal
- `updateTableRow(data)` - Updates table after action
- `showToast(message, type)` - Shows notification messages

### **Database Tables:**
- `users` - User accounts
- `health_facility` - Health facility accounts  
- `user_action_logs` - Audit trail

The disapprove functionality is now **fully functional** with comprehensive error handling, security measures, and user experience enhancements! 🚀✨
