<?php
ob_start(); // Start output buffering
session_start();
if (isset($_SESSION['health_center'])) {
  header('Location:index.php');
  exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">

    <!-- Compiled and minified JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <title>Login</title>
    <?php include 'style.php'; ?>

    <style>
        body {
            background: linear-gradient(135deg, #eceff1 0%, #cfd8dc 100%);
            
            font-family: 'Roboto', sans-serif;
            
        }

      

        nav.white {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%) !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08), 0 2px 8px rgba(0,0,0,0.04);
            padding: 0 1rem;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0,172,193,0.1);
        }

 

        nav a {
            color: #00acc1 !important;
            font-weight: 500;
            font-size: 1.25rem;
            
            
        }

        nav a:hover {
            color: #0097a7 !important;
           
        }

        .card {
            border: none;
           
            background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
            box-shadow: 0 25px 50px rgba(0,0,0,0.1),
                        0 15px 35px rgba(0,0,0,0.05),
                        0 5px 15px rgba(0,0,0,0.03);
        
            overflow: hidden;
            position: relative;
           
            border: 1px solid rgba(255,255,255,0.3);
        }

      

      

        .card-content {
            padding: 3rem 2.5rem !important;
            background: rgba(255,255,255,0.95);
            position: relative;
        }

        .page-title {
            font-size: 29.1px;
            color: #212121;
            
            margin-left: 0;
             
          
            margin-bottom: 2rem;
        }

        .input-field {
            margin-bottom: 2rem;
            position: relative;
        }

        .input-field input {
            border-bottom: 2px solid #e0e0e0;
            padding: 1rem 0 0.5rem 0;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: transparent;
        }

    

        .input-field label {
            color: #757575 !important;
            font-size: 1rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

      

        .input-field .prefix {
            color: #00acc1;
            font-size: 1.5rem;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
        }

        .input-field .prefix.active {
            color: #0097a7;
          
        }

     

        .btn {
            font-weight: 600;
           
           
            background: linear-gradient(135deg, #00acc1 0%, #0097a7 100%) !important;
         
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            font-size: 1rem;
            padding: 1rem 2rem;
        }

    

      

        .btn i {
            font-size: 1.2rem;
            
        }

        .btn:hover {
            background: linear-gradient(135deg, #26c6da 0%, #00acc1 100%) !important;
          
          
        }

        .btn:active {
             
            box-shadow: 0 6px 15px rgba(0,172,193,0.25);
        }

     
      

       
  

        .error-message {
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
            border: 1px solid #f8bbd9;
            border-left: 4px solid #f44336;
            border-radius:6px;
            color: #c62828;
            font-size: 0.9rem;
            margin: 1.5rem 0;
            padding: 1rem;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.15);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            animation: slideInError 0.4s ease;
        }

        @keyframes slideInError {
            from {
                opacity: 0;
             
            }
            to {
                opacity: 1;
                 
            }
        }

        .error-message i {
            font-size: 1.25rem;
            color: #f44336;
            
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo-container img {
            
            border-radius: 50%;
            
            
        }

     

        /* Enhanced Container */
        .container {
            position: relative;
        }

     

        /* Enhanced Page Title */
        .page-title {
            font-size: 1.75rem;
            color: #37474f;
            margin-bottom: 2.5rem;
            text-align: center;
            font-weight: 300;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

    

        /* Enhanced Responsive Design */
        @media screen and (max-width: 600px) {
            .card-content {
                padding: 2rem 1.5rem !important;
            }

            .page-title {
                font-size: 1.5rem;
            }

            nav a {
                font-size: 1rem;
            }

            .logo-container {
                display: none;
            }

            .input-field {
                margin-bottom: 1.5rem;
            }

            .btn {
                padding: 0.875rem 1.5rem;
                font-size: 0.9rem;
            }

            .register-link {
                padding: 1rem;
                font-size: 0.875rem;
            }
        }

        @media screen and (max-width: 400px) {
            .card {
                margin: 1rem 0.5rem;
                border-radius: 16px;
            }

            .card-content {
                padding: 1.5rem 1rem !important;
            }
        }
    </style>
</head>
<body>
    <nav class="white z-depth-0">
        <div class="nav-wrapper" style="padding-left:1em;">
           <a href="" class="">City Health Office Parañaque</a>
            <ul id="nav-mobile" class="right hide-on-med-and-down logo-container">
                <li><img class="responsive-img" src="img/log.png" width="60" alt=""></li>
                <li><img class="responsive-img" src="alaga.jpg" width="180" alt=""></li>
                <li><img class="responsive-img" src="img/bago.jpg" width="60" alt=""></li>
                <li><img class="responsive-img" src="img/h.png" width="60" height="58" alt=""></li>
            </ul>
        </div>
    </nav>
<br>
    <div class="container">
   
        <div class="row ">
 
        <div class="col  s12 m5 push-m4">
       
        <div class="card z-depth-0" style="border:1px solid #ddd;">
            
                    <div class="card-content">
                    <h2 style="font-size:22px; " class="center cyan-text">National Immunization Program<br><span class="blue-grey-text" style="font-size:20px;">Login</span></h2>
                       
                        <form action="" method="POST">
                            <div class="row">
                                <div class="input-field col s12 m12">
                                    <i class="material-icons prefix">person</i>
                                    <input id="username" name="username" type="text" class="validate">
                                    <label for="username" class="active">Username / Mobile number</label>
                                </div>
                                <div class="input-field col s12 m12">
                                    <i class="material-icons prefix">lock</i>
                                    <input id="password" name="password" type="password" class="validate">
                                    <label for="password" class="active">Password</label>
                                </div>
                                <div class="input-field col s12 m12">
                                    <p>
                                        <label>
                                            <input type="checkbox" name="remember_me" id="remember_me" />
                                            <span>Remember me for 30 days</span>
                                        </label>
                                    </p>
                                </div>
                                <div class="input-field col s12 m12">
                                    <button type="submit" name="btn_login" class="  btn cyan accent-5 col s12 m12">

                                        Login
                                    </button>
                                </div>
                            </div>
                            <span class="register-link">Not yet Registered? <a class="blue-text darken-3" href="register.php">Register Here</a></span>
                        </form>

                        <?php
                        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['btn_login'])) {
                            include 'db.php';

                            $login_input = $_POST['username'];
                            $password = $_POST['password'];

                            if (empty($login_input)) {
                                echo '<div class="error-message"><i class="material-icons left">error</i>Username or mobile number is required</div>';
                            } else {
                                $stmt = $conn->prepare("SELECT * FROM health_facility WHERE (username = ? OR mobile_number = ?) AND password = ?");
                                $stmt->bind_param("sss", $login_input, $login_input, $password);
                                $stmt->execute();
                                $result = $stmt->get_result();

                                if ($result->num_rows > 0) {
                                    $row = $result->fetch_assoc();
                                    if($row['approved'] == 1) {
                                        // Set session variables
                                        $_SESSION['username'] = $row['username'];
                                        $_SESSION['health_center'] = $row['health_center'];
                                        $_SESSION['email'] = $row['email'];
                                        $_SESSION['fullname'] = $row['fullname'];
                                        $_SESSION['AddressofFacility'] = $row['AddressofFacility'];
                                        $_SESSION['mobile_number'] = $row['mobile_number'];
                                        $_SESSION['user_id'] = $row['id'];

                                        // Handle remember me functionality
                                        if (isset($_POST['remember_me']) && $_POST['remember_me'] == 'on') {
                                            // Generate secure remember token
                                            $remember_token = bin2hex(random_bytes(32));

                                            // Update database with remember token
                                            $update_stmt = $conn->prepare("UPDATE health_facility SET remember_token = ? WHERE id = ?");
                                            $update_stmt->bind_param("si", $remember_token, $row['id']);
                                            $update_stmt->execute();

                                            // Set cookies (30 days)
                                            setcookie('remember_user', $row['health_center'], time() + (30 * 24 * 60 * 60), '/', '', false, true);
                                            setcookie('remember_token', $remember_token, time() + (30 * 24 * 60 * 60), '/', '', false, true);

                                            $update_stmt->close();
                                        }

                                        header('Location: index.php');
                                        exit();
                                    } else {
                                        echo '<div class="error-message"><i class="material-icons left">warning</i>Please Coordinate with Administrator in City Health Office for Account Approval</div>';
                                    }
                                } else {
                                    echo '<div class="error-message"><i class="material-icons left">error</i>Invalid username or password</div>';
                                }
                            }
                        }
                        ?>
                    </div>
                </div>
            </div>
           
        </div>

        </div>
            <div class="col s12 m5 offset-m5">
                
    </div>

    <script>
        // Enhanced interactive features and animations
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Materialize components
            M.AutoInit();

            // Enhanced input focus effects
            const inputs = document.querySelectorAll('input');
            const prefixes = document.querySelectorAll('.prefix');

            inputs.forEach((input, index) => {
                const prefix = prefixes[index];

                input.addEventListener('focus', function() {
                    if (prefix) {
                        prefix.classList.add('active');
                       
                    }
                   
                    this.parentElement.style.transition = 'all 0.3s ease';
                });

                input.addEventListener('blur', function() {
                    if (this.value === '' && prefix) {
                        prefix.classList.remove('active');
                    
                    }
                    
                });

                // Enhanced typing effect
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.style.borderBottomColor = '#4dd0e1';
                    } else {
                        this.style.borderBottomColor = '#e0e0e0';
                    }
                });
            });

            // Enhanced button interactions
            const loginBtn = document.querySelector('button[name="btn_login"]');
            if (loginBtn) {
                loginBtn.addEventListener('mouseenter', function() {
                    
                });

                loginBtn.addEventListener('mouseleave', function() {
                  
                });

                loginBtn.addEventListener('click', function() {
                    
                    setTimeout(() => {
                       
                    }, 150);
                });
            }

            // Enhanced card hover effects
            const card = document.querySelector('.card');
            if (card) {
                card.addEventListener('mouseenter', function() {
                   
                    this.style.boxShadow = '0 35px 60px rgba(0,0,0,0.15), 0 20px 40px rgba(0,0,0,0.08)';
                });

                card.addEventListener('mouseleave', function() {
                  
                    this.style.boxShadow = '0 25px 50px rgba(0,0,0,0.1), 0 15px 35px rgba(0,0,0,0.05), 0 5px 15px rgba(0,0,0,0.03)';
                });
            }

            // Auto-hide error messages with enhanced animation
            const errorMessages = document.querySelectorAll('.error-message');
            errorMessages.forEach(message => {
                // Add entrance animation
                message.style.animation = 'slideInError 0.4s ease';

                setTimeout(() => {
                    message.style.opacity = '0';
                     
                    message.style.transition = 'all 0.4s ease';
                    setTimeout(() => {
                        message.remove();
                    }, 300);
                }, 6000);
            });

            // Enhanced register link hover effect
            const registerLink = document.querySelector('.register-link');
            if (registerLink) {
                registerLink.addEventListener('mouseenter', function() {
                   
                    this.style.boxShadow = '0 8px 20px rgba(0,172,193,0.15)';
                });

                registerLink.addEventListener('mouseleave', function() {
                 
                    this.style.boxShadow = '0 4px 12px rgba(0,172,193,0.1)';
                });
            }

            // Enhanced logo animations
            const logoImages = document.querySelectorAll('.logo-container img');
            logoImages.forEach((img, index) => {
                img.addEventListener('mouseenter', function() {
                 
                });

                img.addEventListener('mouseleave', function() {
                  
                });
            });

            // Parallax effect for background
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const parallax = document.querySelector('body::before');
                if (parallax) {
                    document.body.style.backgroundPosition = `center ${scrolled * 0.5}px`;
                }
            });
        });

        // Remove the warning notice pop-up and enhance backward compatibility
        (function() {
            if (window.history && window.history.replaceState) {
                window.addEventListener('load', function() {
                    window.history.replaceState(null, null, window.location.href);
                });
            }
        })();

        // Add loading animation on page load
        window.addEventListener('load', function() {
            const card = document.querySelector('.card');
            if (card) {
                card.style.opacity = '0';
              
                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                   
                }, 100);
            }
        });
    </script>
</body>
</html>
