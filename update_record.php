<?php
include 'db.php'; 

// Retrieve POST data
$id = $_POST['id']; // The ID to update
$DateOfBirth = $_POST['DateOfBirth'];
$AgeofChild = $_POST['AgeofChild'];
$FamilySerialNumber = $_POST['FamilySerialNumber'];
$ChildNumber = $_POST['ChildNumber'];
$lastnameOfChild = $_POST['lastnameOfChild'];
$NameOfChild = $_POST['NameOfChild'];
$middlename_of_child = $_POST['middlename_of_child'];
$Sex = $_POST['Sex'];
$NameofMother = $_POST['NameofMother'];
$BirthdateofMother = $_POST['BirthdateofMother'];
$AgeofMother = $_POST['AgeofMother'];
$Barangay = $_POST['Barangay'];
$PurokStreetSitio = $_POST['PurokStreetSitio'];
$HouseNo = $_POST['HouseNo'];
$Address = $_POST['Address'];
$PlaceofDelivery = $_POST['PlaceofDelivery'];
$NameofFacility = $_POST['NameofFacility'];
$Attendant = $_POST['Attendant'];
$TypeofDelivery = $_POST['TypeofDelivery'];
$BirthWeightInGrams = $_POST['BirthWeightInGrams'];
$BirthWeightClassification = $_POST['BirthWeightClassification'];
$WastheChildReferredforNewbornScreening = $_POST['WastheChildReferredforNewbornScreening'];
$DateReferredforNewbornScreening = $_POST['DateReferredforNewbornScreening'];
$DateofConsultationNewbornScreening = $_POST['DateofConsultationNewbornScreening'];
$TTStatusofMother = $_POST['TTStatusofMother'];
$Dateassessed = $_POST['Dateassessed'];
$WastheChildProtectedatBirth = $_POST['WastheChildProtectedatBirth'];
$DateofConsultationCPAB = $_POST['DateofConsultationCPAB'];
$BCG = $_POST['BCG'];
$DateBCGwasgiven = $_POST['DateBCGwasgiven'];
$DateofConsultationBCG = $_POST['DateofConsultationBCG'];
$PENTAHIB1 = $_POST['PENTAHIB1'];
$DatePenta1wasgiven = $_POST['DatePenta1wasgiven'];
$DateofConsultationPENTAHIB1 = $_POST['DateofConsultationPENTAHIB1'];
$PENTAHIB2 = $_POST['PENTAHIB2'];
$DatePentahib2wasgiven = $_POST['DatePentahib2wasgiven'];
$DateofConsultationPENTAHIB2 = $_POST['DateofConsultationPENTAHIB2'];
$PENTAHIB3 = $_POST['PENTAHIB3'];
$DatePentahib3wasgiven = $_POST['DatePentahib3wasgiven'];
$DateofConsultationPENTAHIB3 = $_POST['DateofConsultationPENTAHIB3'];
$OPV1 = $_POST['OPV1'];
$DateOPV1wasgiven = $_POST['DateOPV1wasgiven'];
$DateofConsultationOPV1v = $_POST['DateofConsultationOPV1v'];
$OPV2 = $_POST['OPV2'];
$DateOPV2wasgiven = $_POST['DateOPV2wasgiven'];
$DateofConsultationOPV2 = $_POST['DateofConsultationOPV2'];
$OPV3 = $_POST['OPV3'];
$dateOPV3wasgiven = $_POST['dateOPV3wasgiven'];
$DateofConsultationOPV3 = $_POST['DateofConsultationOPV3'];
$HEPAatBirth = $_POST['HEPAatBirth'];
$TimingHEPAatBirth = $_POST['TimingHEPAatBirth'];
$DateHEPAatBirthwasgiven = $_POST['DateHEPAatBirthwasgiven'];
$DateofConsultationHEPAatBirth = $_POST['DateofConsultationHEPAatBirth'];
$HEPAB1 = $_POST['HEPAB1'];
$dateHEPA1wasgiven = $_POST['dateHEPA1wasgiven'];
$DateofConsultationHEPAB1 = $_POST['DateofConsultationHEPAB1'];
$AMV19monthstobelow12months = $_POST['AMV19monthstobelow12months'];
$DateAMV1WASGIVEN = $_POST['DateAMV1WASGIVEN'];
$DateofConsultationAMV1 = $_POST['DateofConsultationAMV1'];
$MMR12MOSTO15MOS = $_POST['MMR12MOSTO15MOS'];
$dateMMRWASGIVEN = $_POST['dateMMRWASGIVEN'];
$DateofConsultationMMR = $_POST['DateofConsultationMMR'];
$AMV2_16MOSTO5YRSOLD = $_POST['AMV2_16MOSTO5YRSOLD'];
$dateAMV2WASGIVEN = $_POST['dateAMV2WASGIVEN'];
$DateofConsultationAMV2 = $_POST['DateofConsultationAMV2'];
$IMMUNIZATIONSTATUS = $_POST['IMMUNIZATIONSTATUS'];
$DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = $_POST['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'];
$FIRSTMONTH = $_POST['FIRSTMONTH'];
$SECONDMONTH = $_POST['SECONDMONTH'];
$THIRDMONTH = $_POST['THIRDMONTH'];
$FOURTHMONTH = $_POST['FOURTHMONTH'];
$FIFTHMONTH = $_POST['FIFTHMONTH'];
$SIXTHMONTH = $_POST['SIXTHMONTH'];
$date6MONTHS = $_POST['date6MONTHS'];
$WASTHECHILDEXCLUSIVELY = $_POST['WASTHECHILDEXCLUSIVELY'];
$DATEOFCONSULTATIONEXCLUSIVEBF = $_POST['DATEOFCONSULTATIONEXCLUSIVEBF'];
$TT1 = $_POST['TT1'];
$DATEOFCONSULTTT1 = $_POST['DATEOFCONSULTTT1'];
$TT2 = $_POST['TT2'];
$DATEOFCONSULTTT2 = $_POST['DATEOFCONSULTTT2'];
$TT3 = $_POST['TT3'];
$DATEOFCONSULTTT3 = $_POST['DATEOFCONSULTTT3'];
$TT4 = $_POST['TT4'];
$DATEOFCONSULTTT4 = $_POST['DATEOFCONSULTTT4'];
$TT5 = $_POST['TT5'];
$DATEOFCONSULTT5 = $_POST['DATEOFCONSULTT5'];
$PhoneNumber = $_POST['PhoneNumber'];
// Prepare the SQL query to update the record
$sql = "UPDATE nip_table SET DateOfBirth = ?, AgeofChild = ?, FamilySerialNumber = ?, ChildNumber = ?,lastnameOfChild = ?,  NameOfChild = ?,middlename_of_child = ?, Sex = ?, NameofMother = ?, BirthdateofMother = ?,Age = ?,Barangay = ?,PurokStreetSitio = ?,HouseNo = ?,Address = ?,PlaceofDelivery = ?,NameofFacility = ?,Attendant = ?,TypeofDelivery = ?,BirthWeightInGrams = ?,BirthWeightClassification = ?,WastheChildReferredforNewbornScreening = ?,DateReferredforNewbornScreening = ?,DateofConsultationNewbornScreening = ?,TTStatusofMother = ?,Dateassessed = ?,WastheChildProtectedatBirth = ?,DateofConsultationCPAB = ?,BCG = ?,DateBCGwasgiven = ?,DateofConsultationBCG = ?,PENTAHIB1 = ?,DatePenta1wasgiven = ?,DateofConsultationPENTAHIB1 = ?,PENTAHIB2 = ?,DatePentahib2wasgiven = ?,DateofConsultationPENTAHIB2 = ?,PENTAHIB3 = ?,DatePentahib3wasgiven = ?,DateofConsultationPENTAHIB3 = ?,OPV1 = ?,DateOPV1wasgiven = ?,DateofConsultationOPV1v = ?,OPV2 = ?,DateOPV2wasgiven = ?,DateofConsultationOPV2 = ?,OPV3 = ?,dateOPV3wasgiven = ?,DateofConsultationOPV3 = ?,HEPAatBirth = ?,TimingHEPAatBirth = ?,DateHEPAatBirthwasgiven = ?,DateofConsultationHEPAatBirth = ?,HEPAB1 = ?,dateHEPA1wasgiven = ?,DateofConsultationHEPAB1 = ?,AMV19monthstobelow12months = ?,DateAMV1WASGIVEN = ?,DateofConsultationAMV1 = ?,MMR12MOSTO15MOS = ?,dateMMRWASGIVEN = ?,DateofConsultationMMR = ?,AMV2_16MOSTO5YRSOLD = ?,dateAMV2WASGIVEN = ?,DateofConsultationAMV2 = ?,IMMUNIZATIONSTATUS = ?,DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = ?,FIRSTMONTH = ?,SECONDMONTH = ?,THIRDMONTH = ?,FOURTHDMONTH = ?,FIFTMONTH = ?,SIXTHMONTH = ?,date6MONTHS = ?,WASTHECHILDEXCLUSIVELY = ?,DATEOFCONSULTATIONEXCLUSIVEBF = ?,TT1 = ?,DATEOFCONSULTTT1 = ?,TT2 = ?,DATEOFCONSULTTT2 = ?,TT3 = ?,DATEOFCONSULTTT3 = ?,TT4 = ?,DATEOFCONSULTTT4 = ?,TT5 = ?,DATEOFCONSULTT5 = ?, PhoneNumber = ?  WHERE id = ?";
$stmt = $conn->prepare($sql);

// Bind parameters
// 'ssssssss' for all the string fields, 'i' for the integer 'id'
$stmt->bind_param("sssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssi", $DateOfBirth, $AgeofChild, $FamilySerialNumber, $ChildNumber,$lastnameOfChild, $NameOfChild,$middlename_of_child, $Sex, $NameofMother,$BirthdateofMother,$AgeofMother,$Barangay,$PurokStreetSitio,$HouseNo,$Address,$PlaceofDelivery,$NameofFacility,$Attendant,$TypeofDelivery,$BirthWeightInGrams,$BirthWeightClassification,$WastheChildReferredforNewbornScreening,$DateReferredforNewbornScreening,$DateofConsultationNewbornScreening,$TTStatusofMother,$Dateassessed,$WastheChildProtectedatBirth,$DateofConsultationCPAB,$BCG,$DateBCGwasgiven,$DateofConsultationBCG,$PENTAHIB1,$DatePenta1wasgiven,$DateofConsultationPENTAHIB1,$PENTAHIB2,$DatePentahib2wasgiven,$DateofConsultationPENTAHIB2,$PENTAHIB3,$DatePentahib3wasgiven,$DateofConsultationPENTAHIB3,$OPV1,$DateOPV1wasgiven,$DateofConsultationOPV1v,$OPV2,$DateOPV2wasgiven,$DateofConsultationOPV2,$OPV3,$dateOPV3wasgiven,$DateofConsultationOPV3,$HEPAatBirth,$TimingHEPAatBirth,$DateHEPAatBirthwasgiven,$DateofConsultationHEPAatBirth,$HEPAB1,$dateHEPA1wasgiven,$DateofConsultationHEPAB1,$AMV19monthstobelow12months,$DateAMV1WASGIVEN,$DateofConsultationAMV1,$MMR12MOSTO15MOS,$dateMMRWASGIVEN,$DateofConsultationMMR,$AMV2_16MOSTO5YRSOLD,$dateAMV2WASGIVEN,$DateofConsultationAMV2,$IMMUNIZATIONSTATUS,$DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED,$FIRSTMONTH,$SECONDMONTH,$THIRDMONTH,$FOURTHMONTH,$FIFTHMONTH,$SIXTHMONTH,$date6MONTHS,$WASTHECHILDEXCLUSIVELY,$DATEOFCONSULTATIONEXCLUSIVEBF,$TT1,$DATEOFCONSULTTT1,$TT2,$DATEOFCONSULTTT2,$TT3,$DATEOFCONSULTTT3,$TT4,$DATEOFCONSULTTT4,$TT5,$DATEOFCONSULTT5,$PhoneNumber, $id);

// Execute the statement
if ($stmt->execute()) {
    echo "Record updated successfully";
} else {
    echo "Error updating record: " . $conn->error;
}

// Close the statement and connection
$stmt->close();
$conn->close();
?>
