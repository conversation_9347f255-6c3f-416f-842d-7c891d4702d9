<?php
/**
 * Configuration file for NIP QR Scanner System
 */

// Security Configuration
define('DOWNLOAD_PASSWORD', 'nip2024'); // Change this to your desired password
define('ADMIN_PASSWORD', 'admin2024');  // For admin functions

// Database Configuration (if needed for centralized config)
// These can override db.php settings if needed

// Application Settings
define('APP_NAME', 'NIP QR Scanner System');
define('APP_VERSION', '1.0.0');
define('APP_AUTHOR', 'NIP Development Team');

// QR Code Settings
define('DEFAULT_QR_SIZE', 300);
define('QR_ERROR_CORRECTION', 'M'); // L, M, Q, H
define('QR_MARGIN', 2);

// File Upload/Download Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['png', 'jpg', 'jpeg', 'gif']);
define('UPLOAD_DIR', 'uploads/');
define('DOWNLOAD_DIR', 'downloads/');

// Session Settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);

// Logging Settings
define('ENABLE_LOGGING', true);
define('LOG_FILE', 'logs/scanner.log');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR

// Feature Flags
define('ENABLE_PASSWORD_PROTECTION', true);
define('ENABLE_AUDIT_LOGGING', true);
define('ENABLE_DEBUG_MODE', false);

// Security Headers
define('SECURITY_HEADERS', [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'DENY',
    'X-XSS-Protection' => '1; mode=block',
    'Referrer-Policy' => 'strict-origin-when-cross-origin'
]);

// Password validation function
function validateDownloadPassword($password) {
    return $password === DOWNLOAD_PASSWORD;
}

// Password validation function for admin
function validateAdminPassword($password) {
    return $password === ADMIN_PASSWORD;
}

// Get application info
function getAppInfo() {
    return [
        'name' => APP_NAME,
        'version' => APP_VERSION,
        'author' => APP_AUTHOR
    ];
}

// Apply security headers
function applySecurityHeaders() {
    foreach (SECURITY_HEADERS as $header => $value) {
        header("$header: $value");
    }
}

// Log function
function logMessage($level, $message, $context = []) {
    if (!ENABLE_LOGGING) return;
    
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' - ' . json_encode($context) : '';
    $logEntry = "[$timestamp] [$level] $message$contextStr\n";
    
    // Ensure log directory exists
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// Check if feature is enabled
function isFeatureEnabled($feature) {
    switch ($feature) {
        case 'password_protection':
            return ENABLE_PASSWORD_PROTECTION;
        case 'audit_logging':
            return ENABLE_AUDIT_LOGGING;
        case 'debug_mode':
            return ENABLE_DEBUG_MODE;
        default:
            return false;
    }
}

// Initialize application
function initializeApp() {
    // Apply security headers
    applySecurityHeaders();
    
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Log application start
    logMessage('INFO', 'Application initialized');
}

// Password strength checker
function checkPasswordStrength($password) {
    $strength = 0;
    $feedback = [];
    
    if (strlen($password) >= 8) {
        $strength++;
    } else {
        $feedback[] = 'Password should be at least 8 characters long';
    }
    
    if (preg_match('/[A-Z]/', $password)) {
        $strength++;
    } else {
        $feedback[] = 'Password should contain uppercase letters';
    }
    
    if (preg_match('/[a-z]/', $password)) {
        $strength++;
    } else {
        $feedback[] = 'Password should contain lowercase letters';
    }
    
    if (preg_match('/[0-9]/', $password)) {
        $strength++;
    } else {
        $feedback[] = 'Password should contain numbers';
    }
    
    if (preg_match('/[^A-Za-z0-9]/', $password)) {
        $strength++;
    } else {
        $feedback[] = 'Password should contain special characters';
    }
    
    return [
        'strength' => $strength,
        'max_strength' => 5,
        'feedback' => $feedback,
        'is_strong' => $strength >= 3
    ];
}

// Rate limiting for password attempts
function checkRateLimit($identifier, $maxAttempts = MAX_LOGIN_ATTEMPTS, $timeWindow = 900) {
    if (!isset($_SESSION['rate_limit'])) {
        $_SESSION['rate_limit'] = [];
    }
    
    $now = time();
    $key = md5($identifier);
    
    // Clean old attempts
    if (isset($_SESSION['rate_limit'][$key])) {
        $_SESSION['rate_limit'][$key] = array_filter(
            $_SESSION['rate_limit'][$key],
            function($timestamp) use ($now, $timeWindow) {
                return ($now - $timestamp) < $timeWindow;
            }
        );
    } else {
        $_SESSION['rate_limit'][$key] = [];
    }
    
    // Check if limit exceeded
    if (count($_SESSION['rate_limit'][$key]) >= $maxAttempts) {
        return false;
    }
    
    // Record this attempt
    $_SESSION['rate_limit'][$key][] = $now;
    
    return true;
}

// Generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Validate CSRF token
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Initialize the application when this file is included
if (!defined('CONFIG_LOADED')) {
    define('CONFIG_LOADED', true);
    initializeApp();
}
?>
