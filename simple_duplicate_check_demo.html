<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Duplicate Check - Implementation</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .success-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        
        .error-box {
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #f44336;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left red-text">error</i>
                Simple Duplicate Check - Successfully Added!
            </h3>
            <p class="center-align">Clean duplicate detection with simple error message</p>
        </div>

        <div class="demo-section">
            <h4>✅ What Was Implemented</h4>
            
            <div class="success-box">
                <h6>Simple Duplicate Check Features:</h6>
                <ul>
                    <li>✅ <strong>Clean Implementation:</strong> Simple and straightforward duplicate check</li>
                    <li>✅ <strong>5-Field Matching:</strong> Uses key identifying fields</li>
                    <li>✅ <strong>Simple Error Message:</strong> "This record is already exist"</li>
                    <li>✅ <strong>Prevents Duplicates:</strong> Stops duplicate records from being added</li>
                    <li>✅ <strong>Maintains Style:</strong> Uses same card design as other errors</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔍 Duplicate Detection Logic</h4>
            
            <div class="code-block">
// Duplicate check - Check if record already exists
$duplicate_check_sql = "SELECT id FROM nip_table WHERE 
    NameOfChild = ? AND 
    LastNameOfChild = ? AND 
    NameofMother = ? AND 
    DateOfBirth = ? AND 
    Barangay = ? AND 
    (deleted IS NULL OR deleted = 0)";

$stmt = mysqli_prepare($conn, $duplicate_check_sql);
mysqli_stmt_bind_param($stmt, "sssss", $NameOfChild, $LastNameOfChild, $NameofMother, $DateOfBirth, $Barangay);
mysqli_stmt_execute($stmt);
$duplicate_result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($duplicate_result) > 0) {
    // Duplicate found - show error message
    echo '
    &lt;div class="card red lighten-4 red-text text-darken-4"&gt;
        &lt;div class="card-content"&gt;
            &lt;p&gt;&lt;i class="material-icons left"&gt;error&lt;/i&gt;This record is already exist&lt;/p&gt;
        &lt;/div&gt;
    &lt;/div&gt;';
} else {
    // No duplicate found - proceed with insertion
    // ... INSERT query here ...
}
            </div>
        </div>

        <div class="demo-section">
            <h4>🎯 How It Works</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 1: Check</span>
                            <p>Before inserting, search for existing records with same:</p>
                            <ul style="font-size: 13px;">
                                <li>Child's first name</li>
                                <li>Child's last name</li>
                                <li>Mother's name</li>
                                <li>Date of birth</li>
                                <li>Barangay</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 2: Result</span>
                            <p>Based on search result:</p>
                            <ul style="font-size: 13px;">
                                <li><strong>Found:</strong> Show "This record is already exist"</li>
                                <li><strong>Not Found:</strong> Add the record normally</li>
                                <li><strong>Deleted:</strong> Ignore soft-deleted records</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 User Experience</h4>
            
            <h5>When Duplicate is Found:</h5>
            <div class="error-box">
                <div class="card red lighten-4 red-text text-darken-4" style="margin: 0;">
                    <div class="card-content">
                        <p><i class="material-icons left">error</i>This record is already exist</p>
                    </div>
                </div>
                <p style="margin-top: 10px;"><strong>Result:</strong> Record is NOT added to database</p>
            </div>
            
            <h5>When No Duplicate:</h5>
            <div class="success-box">
                <div class="card green lighten-5 green-text" style="margin: 0; padding: 10px;">
                    <div class="card-content" style="padding: 0;">
                        <p style="margin: 0;">Record Successfully Added</p>
                    </div>
                </div>
                <p style="margin-top: 10px;"><strong>Result:</strong> Record is added to database</p>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔧 Integration Details</h4>
            
            <h5>Location in db.php:</h5>
            <div class="code-block">
// After BCG validation check
} else {
    // Duplicate check added here
    $duplicate_check_sql = "SELECT id FROM nip_table WHERE ...";
    
    if (mysqli_num_rows($duplicate_result) > 0) {
        echo 'This record is already exist';
    } else {
        // Original INSERT query continues here
        $sql = "INSERT INTO nip_table ...";
    }
}
            </div>
            
            <h5>Error Message Style:</h5>
            <div class="code-block">
&lt;div class="card red lighten-4 red-text text-darken-4"&gt;
    &lt;div class="card-content"&gt;
        &lt;p&gt;&lt;i class="material-icons left"&gt;error&lt;/i&gt;This record is already exist&lt;/p&gt;
    &lt;/div&gt;
&lt;/div&gt;
            </div>
        </div>

        <div class="demo-section">
            <h4>🧪 Testing the Duplicate Check</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test Steps</span>
                            <ol style="font-size: 13px;">
                                <li>Go to registration form</li>
                                <li>Fill in child information</li>
                                <li>Submit the form</li>
                                <li>Try submitting the same data again</li>
                                <li>Should see "This record is already exist"</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card teal lighten-4">
                        <div class="card-content">
                            <span class="card-title">What to Check</span>
                            <ul style="font-size: 13px;">
                                <li>Same child name + mother name</li>
                                <li>Same date of birth</li>
                                <li>Same barangay</li>
                                <li>Error message appears</li>
                                <li>Record is not duplicated in database</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>The duplicate check is now active and ready to test!</p>
            
            <div class="row">
                <div class="col s12 m6">
                    <a href="nip.php" class="btn large blue waves-effect">
                        <i class="material-icons left">add</i>Test Registration Form
                    </a>
                    <p><small>Try adding duplicate records</small></p>
                </div>
                
                <div class="col s12 m6">
                    <a href="records.php" class="btn large green waves-effect">
                        <i class="material-icons left">table_chart</i>View Records
                    </a>
                    <p><small>See existing records</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>✅ Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>Simple Duplicate Check - Complete!</h6>
                    <ul>
                        <li><strong>Detection:</strong> Checks 5 key fields for duplicates</li>
                        <li><strong>Message:</strong> Simple "This record is already exist" error</li>
                        <li><strong>Prevention:</strong> Stops duplicate records from being added</li>
                        <li><strong>Style:</strong> Consistent with existing error messages</li>
                        <li><strong>Security:</strong> Uses prepared statements</li>
                        <li><strong>Status:</strong> ✅ Active and working!</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
