

<style>
  @import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');
    /* Custom styles for the navigation bar */
    #datatables_buttons_info {
        background: white !important;
        border-radius: 0 !important;
    }

    #datatables_buttons_info h2 {
        font-size: 35px !important;
    }

    /* Enhanced Sidebar Styles (Matching Image) */
    .sidenav {
        background-color: #fff;
        /* White background */
        border: none;
        /* Remove border */
        padding: 0;
        /* Remove padding */
        width: 280px;
        /* Adjust width as needed */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        /* Subtle shadow */
    }

    .sidenav li a {
      font-family: "Roboto", sans-serif;
        
        /* Dark grey text */
        padding: 14px 16px;
        display: flex;
        align-items: center;
        /* Vertically center icon and text */
       
        /* Add a subtle divider */
        transition: background-color 0.3s ease;
        /* Smooth transition on hover */
       color:grey;
        /* Adjust font size */
    }

    .sidenav li a:hover {
        background-color: #eceff1 ;
        /* Lighter grey on hover */
        color: #546e7a ;
        /* Blue text on hover */
    }

    

    .sidenav li.active a:hover {
        background-color: #d1c4e9;
        /* Lighter purple on hover for active item */
    }

    .sidenav li button {
        font-size: 1rem;
        /* Adjust font size */
        
        /* Dark grey text */
        background: none;
        border: none;
        padding: 14px 16px;
        width: 100%;
        text-align: left;
        display: flex;
        align-items: center;
        /* Vertically center icon and text */
        border-bottom: 1px solid #e9ecef;
        transition: background-color 0.3s ease;
        /* Smooth transition on hover */
    }

    .sidenav li button:hover {
        background-color: #f8f9fa;
        /* Lighter grey on hover */
        color: #007bff;
        /* Blue text on hover */
        cursor: pointer;
    }

    .sidenav .material-icons {
        margin-right: 8px;
        /* Add spacing between icon and text */
        font-size: 1.2rem;
        /* Adjust icon size */
    }

    /* Profile Section */
    .sidenav .profile-section {
        padding: 16px;
        
        /* Add a divider */
    }

    .sidenav .profile-section img {
        width: 50px;
        /* Adjust profile image size */
        height: 50px;
        border-radius: 50%;
        /* Make it a circle */
        margin-right: 12px;
        /* Add spacing */
    }

    .sidenav .profile-section .profile-info {
        display: flex;
        flex-direction: column;
    }

    .sidenav .profile-section .profile-info span {
        font-size: 1rem;
        
        color: #212529;
        /* Dark grey text */
    }

    .sidenav .profile-section .profile-info small {
        font-size: 0.85rem;
        color: #6c757d;
        /* Medium grey text */
    }
</style>

<nav class="   z-depth-0" role="navigation" style="background:#009bd4 !important;padding-top:0px !important;  ">
    <div class="nav-wrapper"><a id="logo-container" href="/nip/" class="brand-logo" style="font-size:17px; margin-left:12px;"></a>

        <ul class="right hide-on-med-and-down">

            <li><a href="/nip/">Home</a></li>
            <li><a href="nip.php">Data Entry</a></li>
            <li><a href="records.php">Records</a></li>
            <li><a href="filter_records.php">Filter & Print</a></li>

            <li><a href="map.php">Map</a></li>




            <li class="active "> <a href="profile.php" class="right"> <i class="material-icons left">account_circle</i><?php echo $_SESSION['health_center']; ?></a>
            </li>
            </li>
            <li class="active">




            </li>
            <li class="active  " style="background:#009bd4;">
                <a class='dropdown-trigger  ' href='#' data-target='dropdown1' href=""><i class="material-icons  ">arrow_drop_down</i></a>

            </li>
        </ul>
        <ul id='dropdown1' class='dropdown-content dropdown-tabbed'>
            <li><a class="black-text" style="font-size:14.4px;" href="profile.php">Profile</a></li>
            <li class="divider" tabindex="-1"></li>
            <li><a class="black-text" style="font-size:14.4px;" href="statistics.php">Statistics</a></li>
            <li><a class="black-text" style="font-size:14.4px;" href="sheetjs.php">Export</a></li>

            <?php if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') { ?>
                <li class="divider" tabindex="-1"></li>
                <li><a class="black-text" style="font-size:14.4px;" href="nip_test.php">UHC</a></li>
                </li>
            <?php } else {
                echo '';
            }
            ?>
            <li class="divider" tabindex="-1"></li>
            <?php if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') { ?>
                <li><a class="black-text" style="font-size:13.5px;" href="user_settings.php">User&nbspSettings</a></li>
            <?php } else {
                echo '';
            }
            ?>
            <li> <button  type="button" onclick="window.location.href='logout.php';" class="btn-flat transparent"> Logout </button>


        </ul>


        <ul id="nav-mobile" class="sidenav">
            <!-- Profile Section -->
            <li class="profile-section">
                <div class="d-flex align-items-center">
                <i class="material-icons  grey-text  " style="font-size:5em; ">person</i>
                    <div class="profile-info">
                        <span class="blue-text  " style="line-height:25px;"><b style="font-size:16.4px;"><?php echo $_SESSION['health_center']; ?></b> <small><?php echo $_SESSION['email']; ?></small></span>
                        
                    </div>
                </div>
            </li>
            <li><a href="/nip/"><i class="material-icons">home</i>Home</a></li>
            <li><a href="nip.php"><i class="material-icons">assignment</i>Data Entry</a></li>
            <li><a href="records.php"><i class="material-icons">folder</i>Records</a></li>
            <li><a href="filter_records.php"><i class="material-icons">search</i>Filter & Print</a></li>
            <li><a href="map.php"><i class="material-icons">map</i>Map</a></li>

            <li><a href="statistics.php"><i class="material-icons">insert_chart</i>Statistics</a></li>
            <li><a href="sheetjs.php"><i class="material-icons">import_export</i>Export</a></li>
            <li class=""> <a href="profile.php"> <i class="material-icons">account_circle</i>Profile</a></li>

            <li class="active">
                <button type="button" onclick="window.location.href='logout.php';"><i class="material-icons">exit_to_app</i>Logout</button>


            </li>


        </ul>


        <a href="#" data-target="nav-mobile" class="sidenav-trigger" style="display:block;"><i class="material-icons">menu</i> </a>
        <a href="#" data-target=" " class="sidenav-trigger brand-logo left" style="font-size:18.5px; margin-left:60px;font-weight:500;"> NIP Parañaque </a>
    </div>
</nav>

<script src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
<script src="js/init.js"></script>
<script src="js/materialize.js"></script>
<script src="js/materialize.min.js"></script>


<script>
    $('.dropdown-trigger').dropdown();
</script>