<?php
session_start();
include 'db.php';

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}

// Function to verify health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

// Verify access
if (!verifyHealthCenterAccess($conn, $_SESSION['health_center'])) {
    die('Wrong password - Excel download blocked. Data will not proceed. You can only download data for your registered health center.');
}

// Function to get data based on filters
function getData($conn, $health_center, $filters = []) {
    $sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ?";
    $params = [$health_center];
    $types = "s";
    
    // Apply filters (reuse logic from advanced_data_filter.php)
    if (!empty($filters['date_from'])) {
        $sql .= " AND DATE(DateOfRegistration) >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }
    
    if (!empty($filters['date_to'])) {
        $sql .= " AND DATE(DateOfRegistration) <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }
    
    if (!empty($filters['gender']) && $filters['gender'] !== 'all') {
        $sql .= " AND Sex = ?";
        $params[] = $filters['gender'];
        $types .= "s";
    }
    
    $sql .= " ORDER BY DateOfRegistration DESC";
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    $stmt->close();
    return $data;
}

// Function to export as Excel (XLS)
function exportAsExcel($data, $filename, $health_center) {
    header("Content-Type: application/vnd.ms-excel");
    header("Content-Disposition: attachment; filename=" . $filename . ".xls");
    header("Pragma: no-cache");
    header("Expires: 0");
    
    echo "<html>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<style>";
    echo "table { border-collapse: collapse; width: 100%; }";
    echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }";
    echo "th { background-color: #4CAF50; color: white; font-weight: bold; }";
    echo "tr:nth-child(even) { background-color: #f2f2f2; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<h2>NIP Data Export - " . htmlspecialchars($health_center) . "</h2>";
    echo "<p>Export Date: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>Total Records: " . count($data) . "</p>";
    echo "<hr>";
    
    echo "<table>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>Registration Date</th>";
    echo "<th>Child First Name</th>";
    echo "<th>Child Last Name</th>";
    echo "<th>Mother's Name</th>";
    echo "<th>Gender</th>";
    echo "<th>Date of Birth</th>";
    echo "<th>Age (Months)</th>";
    echo "<th>Birth Weight (g)</th>";
    echo "<th>Address</th>";
    echo "<th>Barangay</th>";
    echo "<th>Immunization Status</th>";
    echo "<th>BCG</th>";
    echo "<th>DPT1</th>";
    echo "<th>DPT2</th>";
    echo "<th>DPT3</th>";
    echo "<th>OPV1</th>";
    echo "<th>OPV2</th>";
    echo "<th>OPV3</th>";
    echo "<th>Measles</th>";
    echo "</tr>";
    
    foreach ($data as $row) {
        $age_months = '';
        if (!empty($row['DateOfBirth']) && $row['DateOfBirth'] !== '0000-00-00') {
            $birth_date = new DateTime($row['DateOfBirth']);
            $current_date = new DateTime();
            $interval = $birth_date->diff($current_date);
            $age_months = $interval->m + ($interval->y * 12);
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['DateOfRegistration'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['NameOfChild'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['lastnameOfChild'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['NameofMother'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['Sex'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['DateOfBirth'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($age_months) . "</td>";
        echo "<td>" . htmlspecialchars($row['BirthWeightInGrams'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['Address'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['Barangay'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['IMMUNIZATIONSTATUS'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['BCG'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['DPT1'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['DPT2'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['DPT3'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['OPV1'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['OPV2'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['OPV3'] ?? '') . "</td>";
        echo "<td>" . htmlspecialchars($row['Measles'] ?? '') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "<hr>";
    echo "<p><small>Generated by NIP System - " . date('Y-m-d H:i:s') . "</small></p>";
    echo "</body>";
    echo "</html>";
}

// Function to export as CSV
function exportAsCSV($data, $filename) {
    header("Content-Type: text/csv");
    header("Content-Disposition: attachment; filename=" . $filename . ".csv");
    header("Pragma: no-cache");
    header("Expires: 0");
    
    $output = fopen('php://output', 'w');
    
    // CSV Headers
    $headers = [
        'ID', 'Registration Date', 'Child First Name', 'Child Last Name', 
        'Mother Name', 'Gender', 'Date of Birth', 'Age (Months)', 
        'Birth Weight (g)', 'Address', 'Barangay', 'Immunization Status',
        'BCG', 'DPT1', 'DPT2', 'DPT3', 'OPV1', 'OPV2', 'OPV3', 'Measles'
    ];
    
    fputcsv($output, $headers);
    
    foreach ($data as $row) {
        $age_months = '';
        if (!empty($row['DateOfBirth']) && $row['DateOfBirth'] !== '0000-00-00') {
            $birth_date = new DateTime($row['DateOfBirth']);
            $current_date = new DateTime();
            $interval = $birth_date->diff($current_date);
            $age_months = $interval->m + ($interval->y * 12);
        }
        
        $csv_row = [
            $row['id'] ?? '',
            $row['DateOfRegistration'] ?? '',
            $row['NameOfChild'] ?? '',
            $row['lastnameOfChild'] ?? '',
            $row['NameofMother'] ?? '',
            $row['Sex'] ?? '',
            $row['DateOfBirth'] ?? '',
            $age_months,
            $row['BirthWeightInGrams'] ?? '',
            $row['Address'] ?? '',
            $row['Barangay'] ?? '',
            $row['IMMUNIZATIONSTATUS'] ?? '',
            $row['BCG'] ?? '',
            $row['DPT1'] ?? '',
            $row['DPT2'] ?? '',
            $row['DPT3'] ?? '',
            $row['OPV1'] ?? '',
            $row['OPV2'] ?? '',
            $row['OPV3'] ?? '',
            $row['Measles'] ?? ''
        ];
        
        fputcsv($output, $csv_row);
    }
    
    fclose($output);
}

// Function to export as PDF (basic HTML to PDF)
function exportAsPDF($data, $filename, $health_center) {
    header("Content-Type: application/pdf");
    header("Content-Disposition: attachment; filename=" . $filename . ".pdf");
    header("Pragma: no-cache");
    header("Expires: 0");
    
    // For basic PDF, we'll use HTML that browsers can print to PDF
    // In production, you might want to use a proper PDF library like TCPDF or FPDF
    
    echo "<!DOCTYPE html>";
    echo "<html>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>NIP Data Export</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; font-size: 10px; }";
    echo "table { border-collapse: collapse; width: 100%; margin-top: 20px; }";
    echo "th, td { border: 1px solid #ddd; padding: 4px; text-align: left; }";
    echo "th { background-color: #4CAF50; color: white; font-weight: bold; }";
    echo "tr:nth-child(even) { background-color: #f9f9f9; }";
    echo ".header { text-align: center; margin-bottom: 20px; }";
    echo ".summary { margin-bottom: 15px; }";
    echo "@media print { body { margin: 0; } }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    echo "<div class='header'>";
    echo "<h1>National Immunization Program (NIP)</h1>";
    echo "<h2>Data Export Report</h2>";
    echo "<h3>" . htmlspecialchars($health_center) . "</h3>";
    echo "</div>";
    
    echo "<div class='summary'>";
    echo "<p><strong>Export Date:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "<p><strong>Total Records:</strong> " . count($data) . "</p>";
    echo "<p><strong>Health Center:</strong> " . htmlspecialchars($health_center) . "</p>";
    echo "</div>";
    
    if (count($data) > 0) {
        echo "<table>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Reg. Date</th>";
        echo "<th>Child Name</th>";
        echo "<th>Mother</th>";
        echo "<th>Gender</th>";
        echo "<th>Birth Date</th>";
        echo "<th>Weight</th>";
        echo "<th>Status</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($data as $row) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['DateOfRegistration'] ?? '', 0, 10)) . "</td>";
            echo "<td>" . htmlspecialchars(($row['NameOfChild'] ?? '') . ' ' . ($row['lastnameOfChild'] ?? '')) . "</td>";
            echo "<td>" . htmlspecialchars($row['NameofMother'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['Sex'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($row['DateOfBirth'] ?? '', 0, 10)) . "</td>";
            echo "<td>" . htmlspecialchars($row['BirthWeightInGrams'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['IMMUNIZATIONSTATUS'] ?? '') . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
    } else {
        echo "<p>No records found for export.</p>";
    }
    
    echo "<div style='margin-top: 30px; text-align: center; font-size: 8px; color: #666;'>";
    echo "<p>Generated by NIP System on " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";
    
    echo "</body>";
    echo "</html>";
}

// Main export logic
try {
    $health_center = $_SESSION['health_center'];
    $format = $_GET['format'] ?? 'excel';
    $export_type = $_GET['type'] ?? 'all';
    
    // Get filters from request
    $filters = [];
    if (!empty($_GET['date_from'])) $filters['date_from'] = $_GET['date_from'];
    if (!empty($_GET['date_to'])) $filters['date_to'] = $_GET['date_to'];
    if (!empty($_GET['gender']) && $_GET['gender'] !== 'all') $filters['gender'] = $_GET['gender'];
    
    // Get data
    $data = getData($conn, $health_center, $filters);
    
    // Generate filename
    $timestamp = date('Y-m-d_H-i-s');
    $filter_suffix = '';
    if (!empty($filters['date_from']) || !empty($filters['date_to'])) {
        $filter_suffix = '_filtered';
    }
    
    $base_filename = "nip_export_{$export_type}{$filter_suffix}_{$timestamp}";
    
    // Log download
    include_once 'download_history.php';
    $download_data = [
        'health_center' => $health_center,
        'user_name' => $_SESSION['fullname'] ?? 'Unknown',
        'download_type' => "multi_format_{$format}",
        'file_name' => $base_filename . '.' . $format,
        'record_count' => count($data),
        'file_size_kb' => 0, // Will be calculated
        'download_time' => date('Y-m-d H:i:s'),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'date_range_from' => $filters['date_from'] ?? null,
        'date_range_to' => $filters['date_to'] ?? null,
        'status' => 'completed',
        'processing_time_ms' => 0,
        'security_verified' => true
    ];
    
    // Export based on format
    switch ($format) {
        case 'excel':
        case 'xls':
            exportAsExcel($data, $base_filename, $health_center);
            break;
            
        case 'csv':
            exportAsCSV($data, $base_filename);
            break;
            
        case 'pdf':
            exportAsPDF($data, $base_filename, $health_center);
            break;
            
        default:
            throw new Exception('Unsupported export format');
    }
    
    // Log the download (in a real implementation, you'd call this after successful export)
    // logDownload($conn, $download_data);
    
} catch (Exception $e) {
    header('Content-Type: text/html');
    echo "<h2>Export Error</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='advanced_excel_manager.php'>Return to Excel Manager</a></p>";
}
?>
