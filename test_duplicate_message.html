<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Check Test - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .duplicate-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            border-left: 4px solid #f44336;
        }
        
        .duplicate-message i {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            border-left: 4px solid #4caf50;
        }
        
        .success-message i {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
        }
        
        .test-scenario {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #9c27b0;
        }
        
        .step-card {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h3 class="center-align">
                <i class="material-icons left red-text">error</i>
                Duplicate Check Implementation
            </h3>
            <p class="center-align">Simple duplicate detection with "This record is already exist" message</p>
        </div>

        <div class="test-section">
            <h4>✅ Implementation Added</h4>
            <p>The duplicate check has been successfully added to your db.php file. Here's what happens:</p>
            
            <div class="code-block">
// Check if record already exists
$check_sql = "SELECT id FROM nip_table 
              WHERE NameOfChild = ? 
              AND LastNameOfChild = ? 
              AND DateOfBirth = ? 
              AND NameofMother = ? 
              AND (deleted IS NULL OR deleted = 0)";

if (mysqli_num_rows($check_result) > 0) {
    // Show duplicate message and stop
    echo 'This record is already exist';
    return; // Stop execution
}
            </div>
        </div>

        <div class="test-section">
            <h4>🔍 Duplicate Detection Logic</h4>
            <p>A record is considered duplicate if ALL these fields match an existing record:</p>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Required Matches</span>
                            <ul>
                                <li>✅ Child's First Name</li>
                                <li>✅ Child's Last Name</li>
                                <li>✅ Date of Birth</li>
                                <li>✅ Mother's Name</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Safety Features</span>
                            <ul>
                                <li>🗑️ Ignores deleted records</li>
                                <li>🔒 Uses prepared statements</li>
                                <li>🛑 Stops execution on duplicate</li>
                                <li>📝 Shows clear error message</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>📋 Error Message Display</h4>
            <p>When a duplicate record is detected, users will see:</p>
            
            <div class="duplicate-message">
                <i class="material-icons">error</i>
                This record is already exist
            </div>
            
            <p>This message appears in a red-colored box with an error icon, making it clear that the registration was not successful due to a duplicate.</p>
        </div>

        <div class="test-section">
            <h4>🧪 Test Scenarios</h4>
            
            <div class="test-scenario">
                <h5>❌ Scenario 1: Exact Duplicate (BLOCKED)</h5>
                <p><strong>First Registration:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                <p><strong>Second Attempt:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                <p><strong>Result:</strong> "This record is already exist" message + No database insert</p>
            </div>
            
            <div class="test-scenario">
                <h5>✅ Scenario 2: Different Birth Date (ALLOWED)</h5>
                <p><strong>Existing:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                <p><strong>New:</strong> Juan Dela Cruz, Born: 2023-06-15, Mother: Maria Dela Cruz</p>
                <p><strong>Result:</strong> Record successfully added (different birth date)</p>
            </div>
            
            <div class="test-scenario">
                <h5>✅ Scenario 3: Different Mother (ALLOWED)</h5>
                <p><strong>Existing:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                <p><strong>New:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Ana Santos</p>
                <p><strong>Result:</strong> Record successfully added (different mother)</p>
            </div>
            
            <div class="test-scenario">
                <h5>✅ Scenario 4: Different Name (ALLOWED)</h5>
                <p><strong>Existing:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                <p><strong>New:</strong> John Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                <p><strong>Result:</strong> Record successfully added (different first name)</p>
            </div>
        </div>

        <div class="test-section">
            <h4>🔧 How to Test</h4>
            
            <div class="step-card">
                <h5>Step 1: Register a Child</h5>
                <p>Go to the registration form (nip.php) and register a child with these details:</p>
                <ul>
                    <li><strong>First Name:</strong> Juan</li>
                    <li><strong>Last Name:</strong> Dela Cruz</li>
                    <li><strong>Birth Date:</strong> 2023-05-15</li>
                    <li><strong>Mother's Name:</strong> Maria Dela Cruz</li>
                    <li>Fill in other required fields</li>
                </ul>
                <p>You should see a success message.</p>
            </div>
            
            <div class="step-card">
                <h5>Step 2: Try to Register the Same Child Again</h5>
                <p>Use the exact same details from Step 1 and submit the form again.</p>
                <p>You should see:</p>
                <div class="duplicate-message">
                    <i class="material-icons">error</i>
                    This record is already exist
                </div>
                <p>The record will NOT be added to the database.</p>
            </div>
            
            <div class="step-card">
                <h5>Step 3: Register a Different Child</h5>
                <p>Change any of the key fields (name, birth date, or mother) and submit the form.</p>
                <p>You should see a success message and the record will be added.</p>
            </div>
        </div>

        <div class="test-section">
            <h4>🛡️ Benefits</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Data Quality</span>
                            <ul>
                                <li>Prevents duplicate children</li>
                                <li>Maintains clean database</li>
                                <li>Accurate immunization records</li>
                                <li>Reliable health statistics</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">User Experience</span>
                            <ul>
                                <li>Clear error message</li>
                                <li>Immediate feedback</li>
                                <li>Prevents confusion</li>
                                <li>Simple to understand</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>⚙️ Technical Details</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <h5>Execution Flow:</h5>
                    <ol>
                        <li>Form submitted to nip.php</li>
                        <li>db.php included and executed</li>
                        <li>Duplicate check runs first</li>
                        <li>If duplicate: show message + stop</li>
                        <li>If unique: insert record</li>
                    </ol>
                </div>
                
                <div class="col s12 m6">
                    <h5>Security Features:</h5>
                    <ul>
                        <li>🔒 Prepared statements prevent SQL injection</li>
                        <li>🧹 HTML escaping prevents XSS</li>
                        <li>🗑️ Ignores soft-deleted records</li>
                        <li>🛑 Complete execution stop on duplicate</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section center-align">
            <h4>🔗 Test the Duplicate Check</h4>
            <p>The duplicate check is now active and ready to prevent duplicate records!</p>
            
            <a href="nip.php" class="btn large red waves-effect">
                <i class="material-icons left">bug_report</i>Test Duplicate Check
            </a>
            <br><br>
            <a href="filter_records.php" class="btn blue waves-effect">
                <i class="material-icons left">search</i>View Existing Records
            </a>
            <a href="/nip/" class="btn green waves-effect">
                <i class="material-icons left">dashboard</i>Dashboard
            </a>
        </div>

        <div class="test-section">
            <h4>📊 Expected Results</h4>
            <div class="success-message">
                <i class="material-icons">check_circle</i>
                <div>
                    <strong>No More Duplicate Records</strong>
                    <p>The system will now show "This record is already exist" message when duplicate registrations are attempted, and will not save duplicate records to the database.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
