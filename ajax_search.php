<?php
session_start();
include 'db.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Get all search parameters
    $LName = isset($_POST['LName']) ? $_POST['LName'] : '';
    $FName = isset($_POST['FName']) ? $_POST['FName'] : '';
    $MName = isset($_POST['MName']) ? $_POST['MName'] : '';
    $Bday = isset($_POST['Bday']) ? $_POST['Bday'] : '';
    $Sex = isset($_POST['Sex']) ? $_POST['Sex'] : '';
    $NameofMother = isset($_POST['NameofMother']) ? $_POST['NameofMother'] : '';
    $Barangay = isset($_POST['Barangay']) ? $_POST['Barangay'] : '';
    $PhoneNumber = isset($_POST['PhoneNumber']) ? $_POST['PhoneNumber'] : '';

    // Medical information filters
    $IMMUNIZATIONSTATUS = isset($_POST['IMMUNIZATIONSTATUS']) ? $_POST['IMMUNIZATIONSTATUS'] : '';
    $BirthWeightInGrams = isset($_POST['BirthWeightInGrams']) ? $_POST['BirthWeightInGrams'] : '';
    $BirthWeightClassification = isset($_POST['BirthWeightClassification']) ? $_POST['BirthWeightClassification'] : '';
    $PlaceofDelivery = isset($_POST['PlaceofDelivery']) ? $_POST['PlaceofDelivery'] : '';

    // Vaccination filters
    $BCG = isset($_POST['BCG']) ? $_POST['BCG'] : '';
    $PENTAHIB1 = isset($_POST['PENTAHIB1']) ? $_POST['PENTAHIB1'] : '';
    $PENTAHIB2 = isset($_POST['PENTAHIB2']) ? $_POST['PENTAHIB2'] : '';
    $PENTAHIB3 = isset($_POST['PENTAHIB3']) ? $_POST['PENTAHIB3'] : '';
    $OPV1 = isset($_POST['OPV1']) ? $_POST['OPV1'] : '';
    $OPV2 = isset($_POST['OPV2']) ? $_POST['OPV2'] : '';

    // Date range filters
    $RegDateFrom = isset($_POST['RegDateFrom']) ? $_POST['RegDateFrom'] : '';
    $RegDateTo = isset($_POST['RegDateTo']) ? $_POST['RegDateTo'] : '';
    $BirthDateFrom = isset($_POST['BirthDateFrom']) ? $_POST['BirthDateFrom'] : '';
    $BirthDateTo = isset($_POST['BirthDateTo']) ? $_POST['BirthDateTo'] : '';

    $deleted = 0;
    $health_center = $_SESSION['health_center'];

    // Start timing for performance monitoring
    $start_time = microtime(true);

    // Build dynamic WHERE clause
    $where_conditions = [];
    $params = [];
    $param_types = '';

    // Basic filters
    if (!empty($LName)) {
        $where_conditions[] = "lastnameOfChild LIKE ?";
        $params[] = "%$LName%";
        $param_types .= 's';
    }

    if (!empty($FName)) {
        $where_conditions[] = "NameOfChild LIKE ?";
        $params[] = "%$FName%";
        $param_types .= 's';
    }

    if (!empty($MName)) {
        $where_conditions[] = "middlename_of_child LIKE ?";
        $params[] = "%$MName%";
        $param_types .= 's';
    }

    if (!empty($Bday)) {
        $where_conditions[] = "DateOfBirth = ?";
        $params[] = $Bday;
        $param_types .= 's';
    }

    if (!empty($Sex)) {
        $where_conditions[] = "Sex = ?";
        $params[] = $Sex;
        $param_types .= 's';
    }

    if (!empty($NameofMother)) {
        $where_conditions[] = "NameofMother LIKE ?";
        $params[] = "%$NameofMother%";
        $param_types .= 's';
    }

    if (!empty($PhoneNumber)) {
        $where_conditions[] = "PhoneNumber LIKE ?";
        $params[] = "%$PhoneNumber%";
        $param_types .= 's';
    }

    // Medical filters
    if (!empty($IMMUNIZATIONSTATUS)) {
        $where_conditions[] = "IMMUNIZATIONSTATUS = ?";
        $params[] = $IMMUNIZATIONSTATUS;
        $param_types .= 's';
    }

    if (!empty($BirthWeightInGrams)) {
        $where_conditions[] = "BirthWeightInGrams = ?";
        $params[] = $BirthWeightInGrams;
        $param_types .= 'i';
    }

    // Birth weight range filters
    $BirthWeightMin = isset($_POST['BirthWeightMin']) ? $_POST['BirthWeightMin'] : '';
    $BirthWeightMax = isset($_POST['BirthWeightMax']) ? $_POST['BirthWeightMax'] : '';

    if (!empty($BirthWeightMin)) {
        $where_conditions[] = "BirthWeightInGrams >= ?";
        $params[] = $BirthWeightMin;
        $param_types .= 'i';
    }

    if (!empty($BirthWeightMax)) {
        $where_conditions[] = "BirthWeightInGrams <= ?";
        $params[] = $BirthWeightMax;
        $param_types .= 'i';
    }

    if (!empty($BirthWeightClassification)) {
        $where_conditions[] = "BirthWeightClassification = ?";
        $params[] = $BirthWeightClassification;
        $param_types .= 's';
    }

    if (!empty($PlaceofDelivery)) {
        $where_conditions[] = "PlaceofDelivery = ?";
        $params[] = $PlaceofDelivery;
        $param_types .= 's';
    }

    // Vaccination filters
    if (!empty($BCG)) {
        $where_conditions[] = "BCG = ?";
        $params[] = $BCG;
        $param_types .= 's';
    }

    if (!empty($PENTAHIB1)) {
        $where_conditions[] = "PENTAHIB1 = ?";
        $params[] = $PENTAHIB1;
        $param_types .= 's';
    }

    if (!empty($PENTAHIB2)) {
        $where_conditions[] = "PENTAHIB2 = ?";
        $params[] = $PENTAHIB2;
        $param_types .= 's';
    }

    if (!empty($PENTAHIB3)) {
        $where_conditions[] = "PENTAHIB3 = ?";
        $params[] = $PENTAHIB3;
        $param_types .= 's';
    }

    if (!empty($OPV1)) {
        $where_conditions[] = "OPV1 = ?";
        $params[] = $OPV1;
        $param_types .= 's';
    }

    if (!empty($OPV2)) {
        $where_conditions[] = "OPV2 = ?";
        $params[] = $OPV2;
        $param_types .= 's';
    }

    // Additional vaccination filters
    $OPV3 = isset($_POST['OPV3']) ? $_POST['OPV3'] : '';
    $HEPAatBirth = isset($_POST['HEPAatBirth']) ? $_POST['HEPAatBirth'] : '';
    $HEPAB1 = isset($_POST['HEPAB1']) ? $_POST['HEPAB1'] : '';
    $MMR1 = isset($_POST['MMR1']) ? $_POST['MMR1'] : '';
    $MMR2 = isset($_POST['MMR2']) ? $_POST['MMR2'] : '';
    $FIC = isset($_POST['FIC']) ? $_POST['FIC'] : '';
    $CIC = isset($_POST['CIC']) ? $_POST['CIC'] : '';

    if (!empty($OPV3)) {
        $where_conditions[] = "OPV3 = ?";
        $params[] = $OPV3;
        $param_types .= 's';
    }

    if (!empty($HEPAatBirth)) {
        $where_conditions[] = "HEPAatBirth = ?";
        $params[] = $HEPAatBirth;
        $param_types .= 's';
    }

    if (!empty($HEPAB1)) {
        $where_conditions[] = "HEPAB1 = ?";
        $params[] = $HEPAB1;
        $param_types .= 's';
    }

    if (!empty($MMR1)) {
        $where_conditions[] = "MMR12MOSTO15MOS = ?";
        $params[] = $MMR1;
        $param_types .= 's';
    }

    if (!empty($FIC)) {
        $where_conditions[] = "FIC = ?";
        $params[] = $FIC;
        $param_types .= 's';
    }

    if (!empty($CIC)) {
        $where_conditions[] = "CIC = ?";
        $params[] = $CIC;
        $param_types .= 's';
    }

    // Additional medical filters
    $Attendant = isset($_POST['Attendant']) ? $_POST['Attendant'] : '';
    $TypeofDelivery = isset($_POST['TypeofDelivery']) ? $_POST['TypeofDelivery'] : '';
    $NameofFacility = isset($_POST['NameofFacility']) ? $_POST['NameofFacility'] : '';
    $Address = isset($_POST['Address']) ? $_POST['Address'] : '';
    $PurokStreetSitio = isset($_POST['PurokStreetSitio']) ? $_POST['PurokStreetSitio'] : '';

    if (!empty($Attendant)) {
        $where_conditions[] = "Attendant LIKE ?";
        $params[] = "%$Attendant%";
        $param_types .= 's';
    }

    if (!empty($TypeofDelivery)) {
        $where_conditions[] = "TypeofDelivery = ?";
        $params[] = $TypeofDelivery;
        $param_types .= 's';
    }

    if (!empty($NameofFacility)) {
        $where_conditions[] = "NameofFacility LIKE ?";
        $params[] = "%$NameofFacility%";
        $param_types .= 's';
    }

    if (!empty($Address)) {
        $where_conditions[] = "Address LIKE ?";
        $params[] = "%$Address%";
        $param_types .= 's';
    }

    if (!empty($PurokStreetSitio)) {
        $where_conditions[] = "PurokStreetSitio LIKE ?";
        $params[] = "%$PurokStreetSitio%";
        $param_types .= 's';
    }

    // Date range filters
    if (!empty($RegDateFrom)) {
        $where_conditions[] = "DateOfRegistration >= ?";
        $params[] = $RegDateFrom;
        $param_types .= 's';
    }

    if (!empty($RegDateTo)) {
        $where_conditions[] = "DateOfRegistration <= ?";
        $params[] = $RegDateTo;
        $param_types .= 's';
    }

    if (!empty($BirthDateFrom)) {
        $where_conditions[] = "DateOfBirth >= ?";
        $params[] = $BirthDateFrom;
        $param_types .= 's';
    }

    if (!empty($BirthDateTo)) {
        $where_conditions[] = "DateOfBirth <= ?";
        $params[] = $BirthDateTo;
        $param_types .= 's';
    }

    // Always filter by deleted status
    $where_conditions[] = "deleted = ?";
    $params[] = $deleted;
    $param_types .= 'i';

    // Add health center filter if not CITY HEALTH OFFICE
    if ($health_center != 'CITY HEALTH OFFICE') {
        if (!empty($Barangay)) {
            $where_conditions[] = "Barangay LIKE ?";
            $params[] = "%$Barangay%";
            $param_types .= 's';
        } else {
            $where_conditions[] = "Barangay = ?";
            $params[] = $health_center;
            $param_types .= 's';
        }
    } else if (!empty($Barangay)) {
        $where_conditions[] = "Barangay LIKE ?";
        $params[] = "%$Barangay%";
        $param_types .= 's';
    }
    
    // Build dynamic SQL query
    $sql = "SELECT * FROM nip_table";

    if (!empty($where_conditions)) {
        $sql .= " WHERE " . implode(" AND ", $where_conditions);
    }

    $sql .= " ORDER BY id DESC LIMIT 500";

    // Prepare and execute query
    $stmt = $conn->prepare($sql);

    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    
    // Execute query
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Calculate search time
    $search_time = round((microtime(true) - $start_time) * 1000, 2);
    
    // Prepare response data
    $records = [];
    $total_records = 0;
    
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Calculate age
            $birthdate = $row['DateOfBirth'];
            $today = new DateTime();
            $dob = new DateTime($birthdate);
            $age_diff = $dob->diff($today);
            $age_in_years = $age_diff->y;
            $age_in_months = $age_diff->m + ($age_in_years * 12);
            
            // Format age display
            if ($age_in_years < 1) {
                $age_display = $age_in_months . ' Months old';
            } else {
                $age_display = $age_in_years . ' Year' . ($age_in_years > 1 ? 's' : '') . ' old';
            }
            
            // Format dates
            $formatted_birth_date = date('F d, Y', strtotime($row['DateOfBirth']));
            $formatted_reg_date = date('F d, Y', strtotime($row['DateOfRegistration']));
            
            // Prepare record data
            $record = [
                'id' => $row['id'],
                'DateOfRegistration' => $formatted_reg_date,
                'DateOfBirth' => $formatted_birth_date,
                'Age' => $age_display,
                'NameOfChild' => $row['NameOfChild'],
                'lastnameOfChild' => $row['lastnameOfChild'],
                'middlename_of_child' => $row['middlename_of_child'],
                'Sex' => $row['Sex'],
                'NameofMother' => $row['NameofMother'],
                'BirthdateofMother' => $row['BirthdateofMother'],
                'MotherAge' => $row['Age'],
                'Barangay' => $row['Barangay'],
                'PurokStreetSitio' => $row['PurokStreetSitio'],
                'HouseNo' => $row['HouseNo'],
                'Address' => $row['Address'],
                'PlaceofDelivery' => $row['PlaceofDelivery'],
                'NameofFacility' => $row['NameofFacility'],
                'Attendant' => $row['Attendant'],
                'TypeofDelivery' => $row['TypeofDelivery'],
                'BirthWeightInGrams' => $row['BirthWeightInGrams'],
                'BirthWeightClassification' => $row['BirthWeightClassification'],
                'IMMUNIZATIONSTATUS' => $row['IMMUNIZATIONSTATUS'],
                'PhoneNumber' => $row['PhoneNumber'],
                'deleted' => $row['deleted']
            ];
            
            $records[] = $record;
            $total_records++;
        }
    }
    
    // Count active filters
    $active_filters = 0;
    $filter_summary = [];

    foreach ($_POST as $key => $value) {
        if (!empty($value) && $key !== 'action') {
            $active_filters++;
            $filter_summary[$key] = $value;
        }
    }

    // Prepare success response
    $response = [
        'success' => true,
        'data' => $records,
        'total_records' => $total_records,
        'search_time' => $search_time,
        'active_filters' => $active_filters,
        'filter_summary' => $filter_summary,
        'search_params' => [
            'LName' => $LName,
            'FName' => $FName,
            'MName' => $MName,
            'Bday' => $Bday,
            'Sex' => $Sex,
            'NameofMother' => $NameofMother,
            'Barangay' => $Barangay,
            'PhoneNumber' => $PhoneNumber,
            'IMMUNIZATIONSTATUS' => $IMMUNIZATIONSTATUS,
            'BirthWeightInGrams' => $BirthWeightInGrams,
            'BirthWeightClassification' => $BirthWeightClassification,
            'PlaceofDelivery' => $PlaceofDelivery,
            'BCG' => $BCG,
            'PENTAHIB1' => $PENTAHIB1,
            'PENTAHIB2' => $PENTAHIB2,
            'PENTAHIB3' => $PENTAHIB3,
            'OPV1' => $OPV1,
            'OPV2' => $OPV2,
            'RegDateFrom' => $RegDateFrom,
            'RegDateTo' => $RegDateTo,
            'BirthDateFrom' => $BirthDateFrom,
            'BirthDateTo' => $BirthDateTo
        ],
        'health_center' => $health_center,
        'sql_query' => $sql, // For debugging (remove in production)
        'message' => $total_records > 0 ?
            "Found $total_records record" . ($total_records > 1 ? 's' : '') . " with $active_filters filter" . ($active_filters > 1 ? 's' : '') . " in {$search_time}ms" :
            "No records found matching your $active_filters filter" . ($active_filters > 1 ? 's' : '')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Handle errors
    $response = [
        'success' => false,
        'error' => 'Database error occurred',
        'message' => 'Please try again later',
        'debug' => $e->getMessage() // Remove in production
    ];
    
    echo json_encode($response);
} finally {
    // Close connections
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
