<?php
session_start();
include 'db.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $health_center = $_SESSION['health_center'];
    
    // Get filter parameters
    $from_date = isset($_POST['from_date']) ? $_POST['from_date'] : '';
    $to_date = isset($_POST['to_date']) ? $_POST['to_date'] : '';
    $barangay = isset($_POST['barangay']) ? $_POST['barangay'] : '';
    $sex = isset($_POST['sex']) ? $_POST['sex'] : '';
    $immunization_status = isset($_POST['immunization_status']) ? $_POST['immunization_status'] : '';
    $place_of_delivery = isset($_POST['place_of_delivery']) ? $_POST['place_of_delivery'] : '';
    $facility_name = isset($_POST['facility_name']) ? $_POST['facility_name'] : '';
    
    // Start timing for performance monitoring
    $start_time = microtime(true);
    
    // Build dynamic WHERE clause
    $where_conditions = ["deleted = 0"];
    $params = [];
    $param_types = '';
    
    // Health center filter
    if ($health_center != 'CITY HEALTH OFFICE') {
        $where_conditions[] = "Barangay = ?";
        $params[] = $health_center;
        $param_types .= 's';
    }
    
    // Date range filter
    if (!empty($from_date) && !empty($to_date)) {
        $where_conditions[] = "DateOfRegistration BETWEEN ? AND ?";
        $params[] = $from_date;
        $params[] = $to_date;
        $param_types .= 'ss';
    }
    
    // Additional filters
    if (!empty($barangay) && $health_center == 'CITY HEALTH OFFICE') {
        $where_conditions[] = "Barangay LIKE ?";
        $params[] = "%$barangay%";
        $param_types .= 's';
    }
    
    if (!empty($sex)) {
        $where_conditions[] = "Sex = ?";
        $params[] = $sex;
        $param_types .= 's';
    }
    
    if (!empty($immunization_status)) {
        $where_conditions[] = "IMMUNIZATIONSTATUS = ?";
        $params[] = $immunization_status;
        $param_types .= 's';
    }
    
    if (!empty($place_of_delivery)) {
        $where_conditions[] = "PlaceofDelivery = ?";
        $params[] = $place_of_delivery;
        $param_types .= 's';
    }
    
    if (!empty($facility_name)) {
        $where_conditions[] = "NameofFacility LIKE ?";
        $params[] = "%$facility_name%";
        $param_types .= 's';
    }
    
    $where_clause = implode(" AND ", $where_conditions);
    
    // 1. Get registration data by date
    $registration_data = [];
    $sql = "SELECT DateOfRegistration, COUNT(*) AS count FROM nip_table WHERE $where_clause GROUP BY DateOfRegistration ORDER BY DateOfRegistration";
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $registration_data[] = [
            'date' => date('F d, Y', strtotime($row['DateOfRegistration'])),
            'count' => (int)$row['count']
        ];
    }
    $stmt->close();
    
    // 2. Get gender distribution
    $gender_data = [];
    $sql = "SELECT Sex, COUNT(*) AS count FROM nip_table WHERE $where_clause GROUP BY Sex";
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        $gender_data[] = [
            'sex' => $row['Sex'],
            'count' => (int)$row['count']
        ];
    }
    $stmt->close();
    
    // 3. Get barangay distribution (for CITY HEALTH OFFICE only)
    $barangay_data = [];
    if ($health_center == 'CITY HEALTH OFFICE') {
        $sql = "SELECT Barangay, COUNT(*) AS count FROM nip_table WHERE $where_clause GROUP BY Barangay ORDER BY count DESC";
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($param_types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $barangay_data[] = [
                'barangay' => $row['Barangay'],
                'count' => (int)$row['count']
            ];
        }
        $stmt->close();
    }
    
    // 4. Get purok/sitio distribution
    $purok_data = [];
    $sql = "SELECT PurokStreetSitio, Barangay, COUNT(*) AS count FROM nip_table WHERE $where_clause GROUP BY PurokStreetSitio, Barangay ORDER BY count DESC";
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $total_purok_count = 0;
    while ($row = $result->fetch_assoc()) {
        $count = (int)$row['count'];
        $total_purok_count += $count;
        $purok_data[] = [
            'purok' => $row['PurokStreetSitio'],
            'barangay' => $row['Barangay'],
            'count' => $count
        ];
    }
    
    // Calculate percentages for purok data
    foreach ($purok_data as &$item) {
        $item['percentage'] = $total_purok_count > 0 ? ($item['count'] / $total_purok_count) * 100 : 0;
    }
    $stmt->close();
    
    // 5. Get facility distribution (for CITY HEALTH OFFICE only)
    $facility_data = [];
    if ($health_center == 'CITY HEALTH OFFICE') {
        $sql = "SELECT NameofFacility, COUNT(*) AS count FROM nip_table WHERE $where_clause GROUP BY NameofFacility ORDER BY count DESC";
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($param_types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $total_facility_count = 0;
        while ($row = $result->fetch_assoc()) {
            $count = (int)$row['count'];
            $total_facility_count += $count;
            $facility_data[] = [
                'facility' => $row['NameofFacility'],
                'count' => $count
            ];
        }
        
        // Calculate percentages for facility data
        foreach ($facility_data as &$item) {
            $item['percentage'] = $total_facility_count > 0 ? ($item['count'] / $total_facility_count) * 100 : 0;
        }
        $stmt->close();
    }
    
    // 6. Get vaccination statistics
    $vaccination_data = [];
    $vaccines = ['BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3', 'OPV1', 'OPV2', 'OPV3', 'HEPAatBirth', 'HEPAB1', 'MMR12MOSTO15MOS', 'FIC', 'CIC'];
    
    foreach ($vaccines as $vaccine) {
        // Total count for this vaccine
        $sql = "SELECT COUNT(*) AS total, 
                       SUM(CASE WHEN Sex = 'MALE' THEN 1 ELSE 0 END) AS male_count,
                       SUM(CASE WHEN Sex = 'FEMALE' THEN 1 ELSE 0 END) AS female_count
                FROM nip_table WHERE $vaccine = 'YES' AND $where_clause";
        $stmt = $conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($param_types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        $vaccination_data[] = [
            'vaccine' => $vaccine,
            'total' => (int)$row['total'],
            'male' => (int)$row['male_count'],
            'female' => (int)$row['female_count']
        ];
        $stmt->close();
    }
    
    // 7. Get total records count
    $sql = "SELECT COUNT(*) AS total FROM nip_table WHERE $where_clause";
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($param_types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $total_records = $result->fetch_assoc()['total'];
    $stmt->close();
    
    // Calculate processing time
    $processing_time = round((microtime(true) - $start_time) * 1000, 2);
    
    // Prepare response
    $response = [
        'success' => true,
        'data' => [
            'registration_data' => $registration_data,
            'gender_data' => $gender_data,
            'barangay_data' => $barangay_data,
            'purok_data' => $purok_data,
            'facility_data' => $facility_data,
            'vaccination_data' => $vaccination_data,
            'total_records' => (int)$total_records
        ],
        'filters' => [
            'from_date' => $from_date,
            'to_date' => $to_date,
            'barangay' => $barangay,
            'sex' => $sex,
            'immunization_status' => $immunization_status,
            'place_of_delivery' => $place_of_delivery,
            'facility_name' => $facility_name
        ],
        'health_center' => $health_center,
        'processing_time' => $processing_time,
        'message' => "Statistics generated successfully in {$processing_time}ms"
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Handle errors
    $response = [
        'success' => false,
        'error' => 'Database error occurred',
        'message' => 'Please try again later',
        'debug' => $e->getMessage() // Remove in production
    ];
    
    echo json_encode($response);
} finally {
    // Close connections
    if (isset($conn)) {
        $conn->close();
    }
}
?>
