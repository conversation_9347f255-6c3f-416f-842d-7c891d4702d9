<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Database connection failed: ' . mysqli_connect_error());
}

// Function to log antigen updates
function logAntigenUpdate($conn, $child_id, $updated_by, $changes) {
    $log_sql = "INSERT INTO antigen_update_log (child_id, updated_by, changes, update_date) VALUES (?, ?, ?, NOW())";
    $log_stmt = mysqli_prepare($conn, $log_sql);
    if ($log_stmt) {
        mysqli_stmt_bind_param($log_stmt, "iss", $child_id, $updated_by, $changes);
        mysqli_stmt_execute($log_stmt);
        mysqli_stmt_close($log_stmt);
    }
}

// Function to validate antigen data
function validateAntigenData($data) {
    $errors = [];
    
    // Validate dates
    $date_fields = [
        'DateBCGwasgiven', 'DateofConsultationBCG',
        'DatePenta1wasgiven', 'DateofConsultationPENTAHIB1',
        'DatePentahib2wasgiven', 'DateofConsultationPENTAHIB2',
        'DatePentahib3wasgiven', 'DateofConsultationPENTAHIB3',
        'DateOPV1wasgiven', 'DateofConsultationOPV1v',
        'DateOPV2wasgiven', 'DateofConsultationOPV2',
        'dateOPV3wasgiven', 'DateofConsultationOPV3',
        'dateIPV1wasgiven', 'DateofConsultationIPV1',
        'dateIPV2wasgiven', 'DateofConsultationIPV2',
        'datePCV1wasgiven', 'DateofConsultationPCV1',
        'datePCV2wasgiven', 'DateofConsultationPCV2',
        'datePCV3wasgiven', 'DateofConsultationPCV3',
        'DateHEPAatBirthwasgiven', 'DateofConsultationHEPAatBirth',
        'dateHEPA1wasgiven', 'DateofConsultationHEPAB1',
        'DateAMV1WASGIVEN', 'DateofConsultationAMV1',
        'dateMMRWASGIVEN', 'DateofConsultationMMR',
        'dateFICWASGIVEN', 'DateofConsultationFIC',
        'DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'
    ];
    
    foreach ($date_fields as $field) {
        if (!empty($data[$field])) {
            $date = DateTime::createFromFormat('Y-m-d', $data[$field]);
            if (!$date || $date->format('Y-m-d') !== $data[$field]) {
                $errors[] = "Invalid date format for $field";
            }
        }
    }
    
    return $errors;
}

// Handle AJAX antigen update
if (isset($_POST['action']) && $_POST['action'] === 'update_antigens_ajax') {
    header('Content-Type: application/json');
    
    $child_id = intval($_POST['child_id']);
    $response = ['success' => false, 'message' => '', 'errors' => []];
    
    // Validate input data
    $validation_errors = validateAntigenData($_POST);
    if (!empty($validation_errors)) {
        $response['errors'] = $validation_errors;
        $response['message'] = 'Validation errors found';
        echo json_encode($response);
        exit;
    }
    
    // Build update query for all antigen fields
    $update_sql = "UPDATE nip_table SET 
                   BCG = ?, DateBCGwasgiven = ?, DateofConsultationBCG = ?,
                   PENTAHIB1 = ?, DatePenta1wasgiven = ?, DateofConsultationPENTAHIB1 = ?,
                   PENTAHIB2 = ?, DatePentahib2wasgiven = ?, DateofConsultationPENTAHIB2 = ?,
                   PENTAHIB3 = ?, DatePentahib3wasgiven = ?, DateofConsultationPENTAHIB3 = ?,
                   OPV1 = ?, DateOPV1wasgiven = ?, DateofConsultationOPV1v = ?,
                   OPV2 = ?, DateOPV2wasgiven = ?, DateofConsultationOPV2 = ?,
                   OPV3 = ?, dateOPV3wasgiven = ?, DateofConsultationOPV3 = ?,
                   IPV1 = ?, dateIPV1wasgiven = ?, DateofConsultationIPV1 = ?,
                   IPV2 = ?, dateIPV2wasgiven = ?, DateofConsultationIPV2 = ?,
                   PCV1 = ?, datePCV1wasgiven = ?, DateofConsultationPCV1 = ?,
                   PCV2 = ?, datePCV2wasgiven = ?, DateofConsultationPCV2 = ?,
                   PCV3 = ?, datePCV3wasgiven = ?, DateofConsultationPCV3 = ?,
                   HEPAatBirth = ?, TimingHEPAatBirth = ?, DateHEPAatBirthwasgiven = ?, DateofConsultationHEPAatBirth = ?,
                   HEPAB1 = ?, dateHEPA1wasgiven = ?, DateofConsultationHEPAB1 = ?,
                   AMV19monthstobelow12months = ?, DateAMV1WASGIVEN = ?, DateofConsultationAMV1 = ?,
                   MMR12MOSTO15MOS = ?, dateMMRWASGIVEN = ?, DateofConsultationMMR = ?,
                   FIC = ?, dateFICWASGIVEN = ?, DateofConsultationFIC = ?,
                   IMMUNIZATIONSTATUS = ?, DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = ?,
                   last_updated = NOW()
                   WHERE id = ?";
    
    $stmt = mysqli_prepare($conn, $update_sql);
    
    if ($stmt) {
        // Prepare all the values
        $bcg = $_POST['BCG'] ?? '';
        $date_bcg = $_POST['DateBCGwasgiven'] ?? '';
        $consult_bcg = $_POST['DateofConsultationBCG'] ?? '';
        
        $penta1 = $_POST['PENTAHIB1'] ?? '';
        $date_penta1 = $_POST['DatePenta1wasgiven'] ?? '';
        $consult_penta1 = $_POST['DateofConsultationPENTAHIB1'] ?? '';
        
        $penta2 = $_POST['PENTAHIB2'] ?? '';
        $date_penta2 = $_POST['DatePentahib2wasgiven'] ?? '';
        $consult_penta2 = $_POST['DateofConsultationPENTAHIB2'] ?? '';
        
        $penta3 = $_POST['PENTAHIB3'] ?? '';
        $date_penta3 = $_POST['DatePentahib3wasgiven'] ?? '';
        $consult_penta3 = $_POST['DateofConsultationPENTAHIB3'] ?? '';
        
        $opv1 = $_POST['OPV1'] ?? '';
        $date_opv1 = $_POST['DateOPV1wasgiven'] ?? '';
        $consult_opv1 = $_POST['DateofConsultationOPV1v'] ?? '';
        
        $opv2 = $_POST['OPV2'] ?? '';
        $date_opv2 = $_POST['DateOPV2wasgiven'] ?? '';
        $consult_opv2 = $_POST['DateofConsultationOPV2'] ?? '';
        
        $opv3 = $_POST['OPV3'] ?? '';
        $date_opv3 = $_POST['dateOPV3wasgiven'] ?? '';
        $consult_opv3 = $_POST['DateofConsultationOPV3'] ?? '';
        
        $ipv1 = $_POST['IPV1'] ?? '';
        $date_ipv1 = $_POST['dateIPV1wasgiven'] ?? '';
        $consult_ipv1 = $_POST['DateofConsultationIPV1'] ?? '';
        
        $ipv2 = $_POST['IPV2'] ?? '';
        $date_ipv2 = $_POST['dateIPV2wasgiven'] ?? '';
        $consult_ipv2 = $_POST['DateofConsultationIPV2'] ?? '';
        
        $pcv1 = $_POST['PCV1'] ?? '';
        $date_pcv1 = $_POST['datePCV1wasgiven'] ?? '';
        $consult_pcv1 = $_POST['DateofConsultationPCV1'] ?? '';
        
        $pcv2 = $_POST['PCV2'] ?? '';
        $date_pcv2 = $_POST['datePCV2wasgiven'] ?? '';
        $consult_pcv2 = $_POST['DateofConsultationPCV2'] ?? '';
        
        $pcv3 = $_POST['PCV3'] ?? '';
        $date_pcv3 = $_POST['datePCV3wasgiven'] ?? '';
        $consult_pcv3 = $_POST['DateofConsultationPCV3'] ?? '';
        
        $hepa_birth = $_POST['HEPAatBirth'] ?? '';
        $timing_hepa = $_POST['TimingHEPAatBirth'] ?? '';
        $date_hepa_birth = $_POST['DateHEPAatBirthwasgiven'] ?? '';
        $consult_hepa_birth = $_POST['DateofConsultationHEPAatBirth'] ?? '';
        
        $hepa_b1 = $_POST['HEPAB1'] ?? '';
        $date_hepa1 = $_POST['dateHEPA1wasgiven'] ?? '';
        $consult_hepa1 = $_POST['DateofConsultationHEPAB1'] ?? '';
        
        $mcv1 = $_POST['AMV19monthstobelow12months'] ?? '';
        $date_mcv1 = $_POST['DateAMV1WASGIVEN'] ?? '';
        $consult_mcv1 = $_POST['DateofConsultationAMV1'] ?? '';
        
        $mcv2 = $_POST['MMR12MOSTO15MOS'] ?? '';
        $date_mcv2 = $_POST['dateMMRWASGIVEN'] ?? '';
        $consult_mcv2 = $_POST['DateofConsultationMMR'] ?? '';
        
        $fic = $_POST['FIC'] ?? '';
        $date_fic = $_POST['dateFICWASGIVEN'] ?? '';
        $consult_fic = $_POST['DateofConsultationFIC'] ?? '';
        
        $immunization_status = $_POST['IMMUNIZATIONSTATUS'] ?? '';
        $fully_immunized_date = $_POST['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'] ?? '';
        
        mysqli_stmt_bind_param($stmt, "ssssssssssssssssssssssssssssssssssssssssssssssssi",
            $bcg, $date_bcg, $consult_bcg,
            $penta1, $date_penta1, $consult_penta1,
            $penta2, $date_penta2, $consult_penta2,
            $penta3, $date_penta3, $consult_penta3,
            $opv1, $date_opv1, $consult_opv1,
            $opv2, $date_opv2, $consult_opv2,
            $opv3, $date_opv3, $consult_opv3,
            $ipv1, $date_ipv1, $consult_ipv1,
            $ipv2, $date_ipv2, $consult_ipv2,
            $pcv1, $date_pcv1, $consult_pcv1,
            $pcv2, $date_pcv2, $consult_pcv2,
            $pcv3, $date_pcv3, $consult_pcv3,
            $hepa_birth, $timing_hepa, $date_hepa_birth, $consult_hepa_birth,
            $hepa_b1, $date_hepa1, $consult_hepa1,
            $mcv1, $date_mcv1, $consult_mcv1,
            $mcv2, $date_mcv2, $consult_mcv2,
            $fic, $date_fic, $consult_fic,
            $immunization_status, $fully_immunized_date,
            $child_id
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $response['success'] = true;
            $response['message'] = 'Antigens updated successfully!';
            
            // Log the update
            $updated_by = $_SESSION['health_center'] ?? 'Unknown';
            $changes = json_encode($_POST);
            logAntigenUpdate($conn, $child_id, $updated_by, $changes);
            
        } else {
            $response['message'] = 'Database error: ' . mysqli_error($conn);
        }
        mysqli_stmt_close($stmt);
    } else {
        $response['message'] = 'Failed to prepare database statement';
    }
    
    echo json_encode($response);
    exit;
}

// Handle bulk antigen update
if (isset($_POST['action']) && $_POST['action'] === 'bulk_update_antigens') {
    header('Content-Type: application/json');
    
    $child_ids = $_POST['child_ids'] ?? [];
    $antigen_data = $_POST['antigen_data'] ?? [];
    $response = ['success' => false, 'message' => '', 'updated_count' => 0];
    
    if (empty($child_ids) || empty($antigen_data)) {
        $response['message'] = 'Missing required data for bulk update';
        echo json_encode($response);
        exit;
    }
    
    $updated_count = 0;
    $errors = [];
    
    foreach ($child_ids as $child_id) {
        $child_id = intval($child_id);
        
        // Build update query for specific antigens
        $update_fields = [];
        $update_values = [];
        $types = '';
        
        foreach ($antigen_data as $field => $value) {
            $update_fields[] = "$field = ?";
            $update_values[] = $value;
            $types .= 's';
        }
        
        if (!empty($update_fields)) {
            $update_sql = "UPDATE nip_table SET " . implode(', ', $update_fields) . ", last_updated = NOW() WHERE id = ?";
            $update_values[] = $child_id;
            $types .= 'i';
            
            $stmt = mysqli_prepare($conn, $update_sql);
            if ($stmt) {
                mysqli_stmt_bind_param($stmt, $types, ...$update_values);
                if (mysqli_stmt_execute($stmt)) {
                    $updated_count++;
                } else {
                    $errors[] = "Failed to update child ID $child_id: " . mysqli_error($conn);
                }
                mysqli_stmt_close($stmt);
            }
        }
    }
    
    if ($updated_count > 0) {
        $response['success'] = true;
        $response['message'] = "Successfully updated $updated_count children";
        $response['updated_count'] = $updated_count;
    }
    
    if (!empty($errors)) {
        $response['errors'] = $errors;
    }
    
    echo json_encode($response);
    exit;
}

mysqli_close($conn);
?>
