<?php
session_start();
include 'db.php';

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $health_center = $_SESSION['health_center'];
    
    // Start timing for performance monitoring
    $start_time = microtime(true);
    
    // Build query based on health center
    if ($health_center == 'CITY HEALTH OFFICE') {
        $sql = "SELECT * FROM nip_table WHERE deleted = 0 ORDER BY id DESC LIMIT 1000";
        $stmt = $conn->prepare($sql);
    } else {
        $sql = "SELECT * FROM nip_table WHERE Barangay = ? AND deleted = 0 ORDER BY id DESC LIMIT 1000";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $health_center);
    }
    
    // Execute query
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Calculate load time
    $load_time = round((microtime(true) - $start_time) * 1000, 2);
    
    // Prepare response data
    $records = [];
    $total_records = 0;
    
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Calculate age
            $birthdate = $row['DateOfBirth'];
            $today = new DateTime();
            $dob = new DateTime($birthdate);
            $age_diff = $dob->diff($today);
            $age_in_years = $age_diff->y;
            $age_in_months = $age_diff->m + ($age_in_years * 12);
            
            // Format age display
            if ($age_in_years < 1) {
                $age_display = $age_in_months . ' Months old';
            } else {
                $age_display = $age_in_years . ' Year' . ($age_in_years > 1 ? 's' : '') . ' old';
            }
            
            // Format dates
            $formatted_birth_date = date('F d, Y', strtotime($row['DateOfBirth']));
            $formatted_reg_date = date('F d, Y', strtotime($row['DateOfRegistration']));
            
            // Prepare comprehensive record data with all fields
            $record = [
                'id' => $row['id'],
                'DateOfRegistration' => $formatted_reg_date,
                'DateOfBirth' => $formatted_birth_date,
                'Age' => $age_display,
                'NameOfChild' => $row['NameOfChild'],
                'lastnameOfChild' => $row['lastnameOfChild'],
                'middlename_of_child' => $row['middlename_of_child'],
                'Sex' => $row['Sex'],
                'NameofMother' => $row['NameofMother'],
                'BirthdateofMother' => $row['BirthdateofMother'],
                'MotherAge' => $row['Age'],
                'Barangay' => $row['Barangay'],
                'PurokStreetSitio' => $row['PurokStreetSitio'],
                'HouseNo' => $row['HouseNo'],
                'Address' => $row['Address'],
                'PlaceofDelivery' => $row['PlaceofDelivery'],
                'NameofFacility' => $row['NameofFacility'],
                'Attendant' => $row['Attendant'],
                'TypeofDelivery' => $row['TypeofDelivery'],
                'BirthWeightInGrams' => $row['BirthWeightInGrams'],
                'BirthWeightClassification' => $row['BirthWeightClassification'],
                'WastheChildReferredforNewbornScreening' => $row['WastheChildReferredforNewbornScreening'],
                'DateReferredforNewbornScreening' => $row['DateReferredforNewbornScreening'],
                'DateofConsultationNewbornScreening' => $row['DateofConsultationNewbornScreening'],
                'TTStatusofMother' => $row['TTStatusofMother'],
                'Dateassessed' => $row['Dateassessed'],
                'WastheChildProtectedatBirth' => $row['WastheChildProtectedatBirth'],
                'DateofConsultationCPAB' => $row['DateofConsultationCPAB'],
                'BCG' => $row['BCG'],
                'DateBCGwasgiven' => $row['DateBCGwasgiven'],
                'DateofConsultationBCG' => $row['DateofConsultationBCG'],
                'PENTAHIB1' => $row['PENTAHIB1'],
                'DatePenta1wasgiven' => $row['DatePenta1wasgiven'],
                'DateofConsultationPENTAHIB1' => $row['DateofConsultationPENTAHIB1'],
                'PENTAHIB2' => $row['PENTAHIB2'],
                'DatePentahib2wasgiven' => $row['DatePentahib2wasgiven'],
                'DateofConsultationPENTAHIB2' => $row['DateofConsultationPENTAHIB2'],
                'PENTAHIB3' => $row['PENTAHIB3'],
                'DatePentahib3wasgiven' => $row['DatePentahib3wasgiven'],
                'DateofConsultationPENTAHIB3' => $row['DateofConsultationPENTAHIB3'],
                'OPV1' => $row['OPV1'],
                'DateOPV1wasgiven' => $row['DateOPV1wasgiven'],
                'DateofConsultationOPV1v' => $row['DateofConsultationOPV1v'],
                'OPV2' => $row['OPV2'],
                'DateOPV2wasgiven' => $row['DateOPV2wasgiven'],
                'DateofConsultationOPV2' => $row['DateofConsultationOPV2'],
                'OPV3' => $row['OPV3'],
                'dateOPV3wasgiven' => $row['dateOPV3wasgiven'],
                'DateofConsultationOPV3' => $row['DateofConsultationOPV3'],
                'HEPAatBirth' => $row['HEPAatBirth'],
                'TimingHEPAatBirth' => $row['TimingHEPAatBirth'],
                'DateHEPAatBirthwasgiven' => $row['DateHEPAatBirthwasgiven'],
                'DateofConsultationHEPAatBirth' => $row['DateofConsultationHEPAatBirth'],
                'HEPAB1' => $row['HEPAB1'],
                'dateHEPA1wasgiven' => $row['dateHEPA1wasgiven'],
                'DateofConsultationHEPAB1' => $row['DateofConsultationHEPAB1'],
                'MMR12MOSTO15MOS' => $row['MMR12MOSTO15MOS'],
                'dateMMRWASGIVEN' => $row['dateMMRWASGIVEN'],
                'DateofConsultationMMR' => $row['DateofConsultationMMR'],
                'FIC' => $row['FIC'],
                'dateFICWASGIVEN' => $row['dateFICWASGIVEN'],
                'DateofConsultationFIC' => $row['DateofConsultationFIC'],
                'CIC' => $row['CIC'],
                'dateCICWASGIVEN' => $row['dateCICWASGIVEN'],
                'DateofConsultationCIC' => $row['DateofConsultationCIC'],
                'IMMUNIZATIONSTATUS' => $row['IMMUNIZATIONSTATUS'],
                'DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED' => $row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'],
                'FIRSTMONTH' => $row['FIRSTMONTH'],
                'SECONDMONTH' => $row['SECONDMONTH'],
                'THIRDMONTH' => $row['THIRDMONTH'],
                'FOURTHDMONTH' => $row['FOURTHDMONTH'],
                'FIFTMONTH' => $row['FIFTMONTH'],
                'SIXTHMONTH' => $row['SIXTHMONTH'],
                'date6MONTHS' => $row['date6MONTHS'],
                'WASTHECHILDEXCLUSIVELY' => $row['WASTHECHILDEXCLUSIVELY'],
                'DATEOFCONSULTATIONEXCLUSIVEBF' => $row['DATEOFCONSULTATIONEXCLUSIVEBF'],
                'TT1' => $row['TT1'],
                'DATEOFCONSULTTT1' => $row['DATEOFCONSULTTT1'],
                'TT2' => $row['TT2'],
                'DATEOFCONSULTTT2' => $row['DATEOFCONSULTTT2'],
                'TT3' => $row['TT3'],
                'DATEOFCONSULTTT3' => $row['DATEOFCONSULTTT3'],
                'TT4' => $row['TT4'],
                'DATEOFCONSULTTT4' => $row['DATEOFCONSULTTT4'],
                'TT5' => $row['TT5'],
                'DATEOFCONSULTT5' => $row['DATEOFCONSULTT5'],
                'PhoneNumber' => $row['PhoneNumber'],
                'deleted' => $row['deleted']
            ];
            
            $records[] = $record;
            $total_records++;
        }
    }
    
    // Prepare success response
    $response = [
        'success' => true,
        'data' => $records,
        'total_records' => $total_records,
        'load_time' => $load_time,
        'health_center' => $health_center,
        'message' => "Loaded $total_records records in {$load_time}ms"
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Handle errors
    $response = [
        'success' => false,
        'error' => 'Database error occurred',
        'message' => 'Please try again later',
        'debug' => $e->getMessage() // Remove in production
    ];
    
    echo json_encode($response);
} finally {
    // Close connections
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
