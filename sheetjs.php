 
 

<?php
include 'db.php';
session_start();

if (!isset($_SESSION['health_center'])) {
  header('Location: login.php');
  exit;
}

$barangay = $_SESSION['health_center'];
 
date_default_timezone_set('Asia/Manila'); // Set your default TZ to Asia/Manila

// strtotime() will convert all timezones to your default
 

  $sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ? ORDER BY id DESC";
  $stmt = $conn->prepare($sql);
$stmt->bind_param("s", $barangay);
$stmt->execute();
 

if ($stmt->error) {
    die("SQL Error: " . $stmt->error);
}
$result = $stmt->get_result();

$data = [];

while ($row = $result->fetch_assoc()) {
    $data[] = $row;
   
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Export NIP Data to Excel</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
  <script src="https://cdn.sheetjs.com/xlsx-0.20.3/package/dist/xlsx.full.min.js"></script>
  <?php include 'style.php'; ?>
</head>
<body class="  " style="background:#eceff1;">
  <?php include 'nav.php'; ?>
  <br><br>
   
 
 
  <div class="row  contaier white z-depth-1  " style="padding:5px;margin:10px;border-radius:2px;">
    <h5 style="margin-left:2px; font-size:21.1px;padding-top:10px;  padding-bottom:10px; padding-left:10px;font-weight:500;" class="blue-grey lighten-5">Export Excel - (<?php echo $_SESSION['health_center']; ?>)</h5> <br>
    <div class="   col s12 m12">
      <form id="exportForm" method="post"   style="padding:17px;">
        <div class="date-filter-section">
          <div class="filter-title" style="font-size:16.2px;">Date Range Filter</div>
          <div class="row">
            <div class="col s12 m5">
              <label for="fromDate">From Date:</label>
              <input type="date" name="fromDate" id="fromDate" class="date-input">
            </div>
            <div class="col s12 m5">
              <label for="toDate">To Date:</label>
              <input type="date" name="toDate" id="toDate" class="date-input">
            </div>
          </div>
          
          <div class="row">
            <div class="col s12">
              <button style="font-weight:500;" type="button" class="btn blue btn-filter" id="filterBtn">
                <i class="material-icons left">filter_list</i> Apply Filter
              </button>
             
      <button style="font-weight:500;" type="button" class="btn green darken-3" onclick="requestPasswordForExcel()"> <i class="material-icons left">lock</i>Download Excel <i class="yellow-text" style="font-size:12.5px;font-weight:lighter;">(Password Required)</i></button>

              <button style="font-weight:500;" type="reset" class="btn orange darken-1 btn-filter" id="resetBtn">
                <i class="material-icons left">refresh</i> Reset
              </button>
            </div>
          </div>
        </div>
        
        <div id="recordCount" class="record-count "  ></div>
      </form>


  </div>

</div>

<!-- Password Modal for Excel Download -->
<div id="excelPasswordModal" class="modal">
  <h5 style="font-size:24.2px;margin-left:0.9em;margin-top:1em;" class="flow-text blue-grey-text"><i class="material-icons left">lock</i>Password Required for Excel Download</h5>
  <div class="modal-content">
    <div class="password-notice" style="background: #e0f2f1; border: 1px solid #26a69a ; border-radius: 5px; padding: 15px; margin: 15px 0;">
      <p style="margin: 0; color: #009688; font-weight: 500;">
        <i class="material-icons left tiny">warning</i>
        <strong>Enter your password first</strong> - Excel download will not proceed without correct credentials
      </p>
    </div>

    <div class="row">
      <div class="col s12">
        <div class="input-field"> 
          <input type="text" id="excelUsername"   class="validate"  >
          <label for="excelUsername">Username or Mobile Number</label>
          <span class="helper-text">Enter your username or mobile number</span>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col s12">
        <div class="input-field">
          <input type="password" id="excelPassword" class="validate" required>
          <label for="excelPassword">Password</label>
          <span class="helper-text">Enter your password to proceed with Excel download</span>
        </div>
      </div>
    </div>

    <div id="excelPasswordError" style="font-size:13.4px;display: none; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; padding: 10px; margin-top: 15px; color: #c62828;">
      <i class="material-icons left tiny">error</i>
      <span id="excelPasswordErrorText">Wrong password - Excel download blocked. Data will not proceed.</span>
    </div>

    <div id="excelPasswordSuccess" style="display: none; background: #e8f5e8; border: 1px solid #4caf50; border-radius: 5px; padding: 10px; margin-top: 15px; color: #2e7d32;">
      <i class="material-icons left tiny">check_circle</i>
      <span>Password verified! Proceeding with Excel download...</span>
    </div>
  </div>

  <div class="modal-footer">
    <button class="modal-close  small-btn btn red lighten-1">
      <i class="material-icons left">cancel</i>Cancel
    </button>
    <button id="confirmExcelPasswordBtn" class=" small-btn btn blue">
      <i class="material-icons left">check</i>Verify Password
    </button>
  </div>
</div>

  <script>
    let isExcelPasswordVerified = false;
    let excelPasswordModal;

    document.addEventListener('DOMContentLoaded', function() {
      // Initialize Materialize components
      M.AutoInit();

      // Initialize password modal
      excelPasswordModal = M.Modal.init(document.getElementById('excelPasswordModal'), {
        dismissible: true,
        opacity: 0.5,
        inDuration: 300,
        outDuration: 200
      });

      // Set default dates (current month)
      const firstDay = '';
      const lastDay = '';

      document.getElementById('fromDate').value ;
      document.getElementById('toDate').value;

      // Add event listener for filter button
      document.getElementById('filterBtn').addEventListener('click', function(e) {
        e.preventDefault();
        updateRecordCount();
      });

      // Add event listener for password confirmation
      document.getElementById('confirmExcelPasswordBtn').addEventListener('click', function(e) {
        e.preventDefault();
        verifyExcelPassword();
      });

      // Add enter key support for password fields
      document.getElementById('excelPassword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          verifyExcelPassword();
        }
      });
      
      // Add event listener for reset button
      document.getElementById('resetBtn').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('fromDate').value = formatDate(firstDay);
        document.getElementById('toDate').value = formatDate(lastDay);
        updateRecordCount();
      });
      
      // Prevent form submission
      document.getElementById('exportForm').addEventListener('submit', function(e) {
        e.preventDefault();
      });
      
      // Initial record count
      updateRecordCount();
    });
    
    function formatDate(date) {
      return date.toISOString().split('T')[0];
    }
    
    function updateRecordCount() {
      const fromDate = document.getElementById('fromDate').value;
      const toDate = document.getElementById('toDate').value;
      
      // Show loading indicator
      document.getElementById('recordCount').innerHTML = 
        '<div class="progress  light-blue lighten-2"><div class="indeterminate"></div></div>';
      
      fetch('get_nip_data.php?count=true&fromDate=' + fromDate + '&toDate=' + toDate)
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            document.getElementById('recordCount').innerHTML = 
              `<div class="red-text">Error: ${data.error}</div>`;
          } else {
            document.getElementById('recordCount').innerHTML = 
              `<div   class=" cyan lighten-5" style="padding:0.5em;color:#00838f ;border:1px solid #00bcd4  ; border-radius:5px;">Records found: <b>${data.count}</b> for the selected date range
              
              <b>(${new Date(fromDate).toLocaleDateString('PH', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
})}
 - ${new Date(toDate).toLocaleDateString('PH', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
})})</b></div>`;
          }
        })
        .catch(error => {
          console.error('Error fetching record count:', error);
          document.getElementById('recordCount').innerHTML = 
            '<div class="red-text">Error fetching record count. Please try again.</div>';
        });
    }

    function requestPasswordForExcel() {
      // Clear previous states
      document.getElementById('excelUsername').value = '';
      document.getElementById('excelPassword').value = '';
      document.getElementById('excelPasswordError').style.display = 'none';
      document.getElementById('excelPasswordSuccess').style.display = 'none';

      // Show explicit message about password requirement
      M.toast({
        html: '<i class="material-icons left">lock</i>Enter your password first before Excel download',
        classes: 'amber darken-4',
        displayLength: 4000
      });

      // Open password modal
      excelPasswordModal.open();

      // Focus on username input
      setTimeout(() => {
        document.getElementById('excelUsername').focus();
      }, 300);

      console.log('Excel download requested - password verification required');
    }

    async function verifyExcelPassword() {
      const username = document.getElementById('excelUsername').value.trim();
      const password = document.getElementById('excelPassword').value.trim();
      const passwordError = document.getElementById('excelPasswordError');
      const passwordSuccess = document.getElementById('excelPasswordSuccess');
      const confirmBtn = document.getElementById('confirmExcelPasswordBtn');

      // Clear previous states
      passwordError.style.display = 'none';
      passwordSuccess.style.display = 'none';

      if (!username || !password) {
        passwordError.style.display = 'block';
        document.getElementById('excelPasswordErrorText').textContent = 'Username and password required - Excel download blocked';

        M.toast({
          html: '<i class="material-icons left">block</i>Enter your credentials - Excel download blocked',
          classes: 'red darken-2',
          displayLength: 4000
        });
        return;
      }

      // Show loading state
      confirmBtn.disabled = true;
      confirmBtn.innerHTML = '<i class="material-icons left">hourglass_empty</i>Verifying Password...';

      console.log('Verifying Excel download credentials - access currently blocked');

      try {
        const formData = new FormData();
        formData.append('verify_password', '1');
        formData.append('username', username);
        formData.append('password', password);

        const response = await fetch('verify_excel_password.php', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          // Password CORRECT - Show success and proceed
          isExcelPasswordVerified = true;
          passwordSuccess.style.display = 'block';
          console.log('Excel password verified successfully - download access granted');

          M.toast({
            html: '<i class="material-icons left">check_circle</i>Password verified! Proceeding with Excel download...',
            classes: 'green darken-1',
            displayLength: 3000
          });

          // Wait a moment to show success message
          setTimeout(() => {
            excelPasswordModal.close();

            // Execute Excel download ONLY after successful verification
            exportToExcel();
          }, 1500);

        } else {
          // Password WRONG - Block download and show error
          isExcelPasswordVerified = false;
          passwordError.style.display = 'block';
          document.getElementById('excelPasswordErrorText').textContent = 'Wrong password - Excel download blocked. Data will not proceed.';
          document.getElementById('excelUsername').value = '';
          document.getElementById('excelPassword').value = '';
          document.getElementById('excelUsername').focus();

          console.log('Excel password verification failed - download access DENIED');

          M.toast({
            html: '<i class="material-icons left">block</i>Wrong password - Excel download blocked!',
            classes: 'red darken-2',
            displayLength: 5000
          });
        }
      } catch (error) {
        // Connection error - Block download
        isExcelPasswordVerified = false;
        passwordError.style.display = 'block';
        document.getElementById('excelPasswordErrorText').textContent = 'Connection error - Excel download blocked until password verified.';

        console.log('Excel password verification error - download access BLOCKED');

        M.toast({
          html: '<i class="material-icons left">error</i>Connection error - Excel download blocked',
          classes: 'red darken-2',
          displayLength: 4000
        });
      } finally {
        // Reset button state
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="material-icons left">check</i>Verify Password';
      }
    }

    async function exportToExcel() {
      // Security check - this method should only be called after password verification
      if (!isExcelPasswordVerified) {
        M.toast({
          html: '<i class="material-icons left">block</i>Password verification required - Excel download blocked',
          classes: 'red darken-2',
          displayLength: 4000
        });
        return;
      }

      console.log('Excel download initiated - password verification completed');

      const fromDate = document.getElementById('fromDate').value;
      const toDate = document.getElementById('toDate').value;
      
      // Show loading indicator
      document.getElementById('recordCount').innerHTML = 
        '<div class="progress"><div class="indeterminate"></div></div>';
      
      try {
        const response = await fetch('get_nip_data.php?fromDate=' + fromDate + '&toDate=' + toDate);
        const data = await response.json();

        if (data.error) {
          document.getElementById('recordCount').innerHTML = 
            `<div class="red-text">Error: ${data.error}</div>`;
          return;
        }

        if (data.length === 0) {
          document.getElementById('recordCount').innerHTML = 
            '<div class="orange-text">No data found for the selected date range</div>';
          return;
        }

        // Format data
        const formatted = data.map(row => ({
          ID: row.id,
          "Date Of Registration": row.DateOfRegistration,
          "Date Of Birth": row.DateOfBirth,
          "Age of Child": row.AgeofChild,
          "Family Serial Number": row.FamilySerialNumber,
          "Child Number": row.ChildNumber,
          "Name of Child": `${row.NameOfChild} ${row.lastnameOfChild} ${row.middlename_of_child}`,
          "Sex": row.Sex,
          "Mother's Name": row.NameofMother,
          "Birthdate of Mother": row.BirthdateofMother,
          "Age of Mother": row.Age,
          "Barangay": row.Barangay,
          "Purok/Street/Sitio": row.PurokStreetSitio,
          "HouseNo": row.HouseNo,
          "Address": row.Address,
          "Place of Delivery": row.PlaceofDelivery,
          "Name of Facility": row.NameofFacility,
          "Attendant": row.Attendant,
          "Type of Delivery": row.TypeofDelivery,
          "BirthWeight In Grams": row.BirthWeightInGrams,
          "BirthWeight Classification": row.BirthWeightClassification,
          "Was the Child Referred for NewbornScreening": row.WastheChildReferredforNewbornScreening,
          "Date Referred for NewbornScreening": row.DateReferredforNewbornScreening,
          "Date of Consultation NewbornScreening": row.DateofConsultationNewbornScreening,
          "TT Status of Mother": row.TTStatusofMother,
          "Date assessed": row.Dateassessed,
          "Was the Child Protected at Birth": row.WastheChildProtectedatBirth,
          "Date of Consultation CPAB": row.DateofConsultationCPAB,
          "BCG": row.BCG,
          "Date BCG was given": row.DateBCGwasgiven,
          "Date of Consultation BCG": row.DateofConsultationBCG,
          "PENTAHIB1": row.PENTAHIB1,
          "Date Penta1 wasgiven": row.DatePenta1wasgiven,
          "Date of Consultation PENTAHIB1": row.DateofConsultationPENTAHIB1,
          "PENTAHIB2": row.PENTAHIB2,
          "Date Pentahib2 was given": row.DatePentahib2wasgiven,
          "Date of Consultation PENTAHIB2": row.DateofConsultationPENTAHIB2,
          "PENTAHIB3": row.PENTAHIB3,
          "Date Pentahib3 was given": row.DatePentahib3wasgiven,
          "DateofConsultationPENTAHIB3":row.DateofConsultationPENTAHIB3,
          "OPV1":row.OPV1,
          "Date OPV1 was given":row.DateOPV1wasgiven,
          "Date of Consultation OPV1":row.DateofConsultationOPV1v,
          "OPV2":row.OPV2,
          "Date OPV2 was given":row.DateOPV2wasgiven,
          "Date of Consultation OPV2":row.DateofConsultationOPV2,
          "OPV3":row.OPV3,
          "date OPV3 was given":row.dateOPV3wasgiven,
          "Date of Consultation OPV3":row.DateofConsultationOPV3,
          "HEPA at Birth":row.HEPAatBirth,
          "TimingHEPAatBirth":row.TimingHEPAatBirth,
          "DateHEPAatBirthwasgiven":row.DateHEPAatBirthwasgiven,
          "DateofConsultationHEPAatBirth":row.DateofConsultationHEPAatBirth,
          "HEPAB1":row.HEPAB1,
          "dateHEPA1wasgiven":row.dateHEPA1wasgiven,
          "DateofConsultationHEPAB1":row.DateofConsultationHEPAB1,
          "MCV 1 (at 9months)":row.MMR12MOSTO15MOS,
          "DATE MCV 1 WAS GIVEN":row.dateMMRWASGIVEN,
          "DATE OF CONSULTATON (MCV 1)":row.DateofConsultationMMR,
          "MCV 2(at 12 Months)":row.MMR12MOSTO15MOS,
          "DATE MCV2 WAS GIVEN":row.dateMMRWASGIVEN,
          "DATE OF CONSULTATION (MCV2)":row.DateofConsultationMMR,
          "FIC":row.FIC,
          "DATE FIC WAS GIVEN":row.dateFICWASGIVEN,
          "DATE OF CONSULTATION (FIC)":row.DateofConsultationFIC,
          "CIC":row.CIC,
          "DATE CIC WAS GIVEN":row.dateCICWASGIVEN,
          "DATE OF CONSULTATION (CIC)":row.DateofConsultationCIC,
          "IMMUNIZATION STATUS":row.IMMUNIZATIONSTATUS,
          "DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED":row.DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED,
          "FIRSTMONTH":row.FIRSTMONTH,
          "SECONDMONTH":row.SECONDMONTH,
          "THIRDMONTH":row.THIRDMONTH,
          "FOURTHDMONTH":row.FOURTHDMONTH,
          "FIFTMONTH":row.FIFTMONTH,
          "SIXTHMONTH":row.SIXTHMONTH,
          "DATE 6 MONTHS":row.date6MONTHS,
          "WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?":row.WASTHECHILDEXCLUSIVELY,
          "DATE OF CONSULTATION (EXCLUSIVE)":row.DATEOFCONSULTATIONEXCLUSIVEBF,
          "TD1":row.TT1,
          "DATE OF CONSULT (TT1)":row.DATEOFCONSULTTT1,
          "TT2":row.TT2,
          "DATE OF CONSULT (TT2)":row.DATEOFCONSULTTT2,
          "TT3":row.TT3,
          "DATE OF CONSULT (TT3)":row.DATEOFCONSULTTT3,
          "TT4":row.TT4,
          "DATE OF CONSULT (TT4)":row.DATEOFCONSULTTT4,
          "TT5":row.TT5,
          "DATE OF CONSULT (TT5)":row.DATEOFCONSULTT5,
          "Phone Number":row.PhoneNumber,
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(formatted);
        
        // Set column widths
        const colWidths = [];
        for (let i = 0; i < Object.keys(formatted[0]).length; i++) {
          colWidths.push({ wch: 20 }); // Default width of 20 characters
        }
        ws['!cols'] = colWidths;
        
        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, "NIP Data");
        
        // Format dates for filename
        const formattedFromDate = fromDate.replace(/-/g, '');
        const formattedToDate = toDate.replace(/-/g, '');
        
        // Generate filename with date range and health center
        const healthCenter = "<?php echo $_SESSION['health_center']; ?>";
        const filename = `NIP_Data_${healthCenter}_${formattedFromDate}_to_${formattedToDate}.xlsx`;
        
        // Write the workbook and trigger download
        XLSX.writeFile(wb, filename);
        
        console.log(`Excel download completed: ${filename} - Password verified`);

        // Update record count with success message
        document.getElementById('recordCount').innerHTML =
          `<div class="green lighten-5" style="padding:0.5em;color:#2e7d32;border:1px solid #a5d6a7 ; border-radius:5px;">
            <i class="material-icons left tiny">check_circle</i>
            Successfully exported ${data.length} records to Excel - Password verified
          </div>`;

        // Reset password verification for next download
        isExcelPasswordVerified = false;

      } catch (error) {
        console.error("Error exporting data:", error);
        document.getElementById('recordCount').innerHTML =
          '<div class="red-text">An error occurred while exporting data. Please try again.</div>';

        // Reset password verification on error
        isExcelPasswordVerified = false;
      }
    }
  </script>

</body>
</html>
