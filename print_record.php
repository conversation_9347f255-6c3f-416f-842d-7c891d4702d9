<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Record - NIP System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border: 1px solid #ddd;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .header h2 {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 18px;
            font-weight: normal;
        }
        
        .record-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }
        
        .info-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            width: 120px;
            color: #555;
        }
        
        .info-value {
            flex: 1;
            color: #333;
        }
        
        .immunization-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .immunization-table th,
        .immunization-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .immunization-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .immunization-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        
        .no-print {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .print-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        .back-btn {
            background: #666;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        
        .print-btn:hover,
        .back-btn:hover {
            opacity: 0.8;
        }
        
        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
            margin: 20px 0;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .print-container {
                border: none;
                box-shadow: none;
                padding: 0;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <?php
    // Database connection
    $conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
    if (!$conn) {
        echo '<div class="error-message">Database connection failed: ' . mysqli_connect_error() . '</div>';
        exit;
    }

    $record = null;
    $error_message = '';

    if (isset($_GET['id']) && !empty($_GET['id'])) {
        $record_id = intval($_GET['id']);
        
        // Fetch record details
        $sql = "SELECT * FROM nip_table WHERE id = ? AND (deleted IS NULL OR deleted = 0)";
        $stmt = mysqli_prepare($conn, $sql);
        
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "i", $record_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            
            if (mysqli_num_rows($result) > 0) {
                $record = mysqli_fetch_assoc($result);
            } else {
                $error_message = 'Record not found or has been deleted.';
            }
            mysqli_stmt_close($stmt);
        } else {
            $error_message = 'Database query failed.';
        }
    } else {
        $error_message = 'No record ID provided.';
    }

    mysqli_close($conn);
    ?>

    <div class="no-print">
        <button onclick="window.print()" class="print-btn">🖨️ Print Record</button>
        <a href="filter_records.php" class="back-btn">← Back to Search</a>
    </div>

    <?php if ($error_message): ?>
        <div class="error-message">
            <h3>Error</h3>
            <p><?php echo htmlspecialchars($error_message); ?></p>
        </div>
    <?php else: ?>
        <div class="print-container">
            <div class="header">
                <h1>National Immunization Program</h1>
                <h2>Child Immunization Record</h2>
            </div>

            <div class="record-info">
                <div class="info-section">
                    <h3>Child Information</h3>
                    <div class="info-row">
                        <span class="info-label">Record ID:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['id']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">First Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['NameOfChild']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Middle Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['middlename_of_child'] ?? ''); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Last Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['LastNameOfChild']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Birth Date:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['DateOfBirth']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Sex:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['Sex']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Birth Weight:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['BirthWeightInGrams'] ?? 'N/A'); ?> grams</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>Family Information</h3>
                    <div class="info-row">
                        <span class="info-label">Family Serial:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['FamilySerialNumber']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Mother's Name:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['NameofMother']); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Mother's DOB:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['BirthdateofMother'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Phone Number:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['PhoneNumber'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Address:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['Address'] ?? 'N/A'); ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Barangay:</span>
                        <span class="info-value"><?php echo htmlspecialchars($record['Barangay']); ?></span>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h3>Birth Information</h3>
                <div class="record-info">
                    <div>
                        <div class="info-row">
                            <span class="info-label">Place of Delivery:</span>
                            <span class="info-value"><?php echo htmlspecialchars($record['PlaceofDelivery'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Facility Name:</span>
                            <span class="info-value"><?php echo htmlspecialchars($record['NameofFacility'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Attendant:</span>
                            <span class="info-value"><?php echo htmlspecialchars($record['Attendant'] ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <div>
                        <div class="info-row">
                            <span class="info-label">Type of Delivery:</span>
                            <span class="info-value"><?php echo htmlspecialchars($record['TypeofDelivery'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Birth Weight Class:</span>
                            <span class="info-value"><?php echo htmlspecialchars($record['BirthWeightClassification'] ?? 'N/A'); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Registration Date:</span>
                            <span class="info-value"><?php echo htmlspecialchars($record['DateOfRegistration']); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p><strong>Generated:</strong> <?php echo date('F j, Y g:i A'); ?></p>
                <p>National Immunization Program - Child Health Record</p>
                <p>This is an official record. Please keep for your reference.</p>
            </div>
        </div>
    <?php endif; ?>

    <script>
        // Auto-print if requested
        if (window.location.search.includes('autoprint=1')) {
            window.onload = function() {
                window.print();
            };
        }
    </script>
</body>
</html>
