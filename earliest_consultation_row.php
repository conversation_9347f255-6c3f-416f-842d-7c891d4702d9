<?php
/**
 * Functions to get earliest consultation date from $row database data
 * Works with all the consultation date fields you specified
 */

function getEarliestConsultationFromRow($row) {
    // All consultation date fields from $row
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Collect all valid dates
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00') {
            $valid_dates[] = $date;
        }
    }
    
    // Return earliest date or null if no valid dates
    return !empty($valid_dates) ? min($valid_dates) : null;
}

// Function to print earliest consultation date in <p> tag from $row
function printEarliestConsultationFromRow($row) {
    $earliest = getEarliestConsultationFromRow($row);
    
    if ($earliest) {
        echo '<p>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p>No consultation date recorded</p>';
    }
}

// Function to print with custom styling
function printEarliestConsultationFromRowStyled($row, $class = '', $style = '') {
    $earliest = getEarliestConsultationFromRow($row);
    
    $class_attr = $class ? ' class="' . htmlspecialchars($class) . '"' : '';
    $style_attr = $style ? ' style="' . htmlspecialchars($style) . '"' : '';
    
    if ($earliest) {
        echo '<p' . $class_attr . $style_attr . '>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p' . $class_attr . $style_attr . '>No consultation date recorded</p>';
    }
}

// Function to get HTML string instead of printing
function getEarliestConsultationFromRowHTML($row) {
    $earliest = getEarliestConsultationFromRow($row);
    
    if ($earliest) {
        return '<p>' . htmlspecialchars($earliest) . '</p>';
    } else {
        return '<p>No consultation date recorded</p>';
    }
}

// Function to print with formatted date
function printEarliestConsultationFromRowFormatted($row, $format = 'Y-m-d') {
    $earliest = getEarliestConsultationFromRow($row);
    
    if ($earliest) {
        $formatted_date = date($format, strtotime($earliest));
        echo '<p>' . htmlspecialchars($formatted_date) . '</p>';
    } else {
        echo '<p>No consultation date recorded</p>';
    }
}

// Function to check if consultation dates exist
function hasConsultationDates($row) {
    $earliest = getEarliestConsultationFromRow($row);
    return $earliest !== null;
}

// Function to count how many consultation dates are recorded
function countConsultationDates($row) {
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $count = 0;
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00') {
            $count++;
        }
    }
    
    return $count;
}

// Function to get all consultation dates (for debugging)
function getAllConsultationDatesFromRow($row) {
    $consultation_dates = [
        'DateofConsultationBCG' => $row['DateofConsultationBCG'] ?? '',
        'DateofConsultationPENTAHIB1' => $row['DateofConsultationPENTAHIB1'] ?? '',
        'DateofConsultationPENTAHIB2' => $row['DateofConsultationPENTAHIB2'] ?? '',
        'DateofConsultationPENTAHIB3' => $row['DateofConsultationPENTAHIB3'] ?? '',
        'DateofConsultationOPV1v' => $row['DateofConsultationOPV1v'] ?? '',
        'DateofConsultationOPV2' => $row['DateofConsultationOPV2'] ?? '',
        'DateofConsultationOPV3' => $row['DateofConsultationOPV3'] ?? '',
        'DateofConsultationIPV1' => $row['DateofConsultationIPV1'] ?? '',
        'DateofConsultationIPV2' => $row['DateofConsultationIPV2'] ?? '',
        'DateofConsultationPCV1' => $row['DateofConsultationPCV1'] ?? '',
        'DateofConsultationPCV2' => $row['DateofConsultationPCV2'] ?? '',
        'DateofConsultationPCV3' => $row['DateofConsultationPCV3'] ?? '',
        'DateofConsultationHEPAatBirth' => $row['DateofConsultationHEPAatBirth'] ?? '',
        'DateofConsultationHEPAB1' => $row['DateofConsultationHEPAB1'] ?? '',
        'DateofConsultationHEPAB2' => $row['DateofConsultationHEPAB2'] ?? '',
        'DateofConsultationHEPAB3' => $row['DateofConsultationHEPAB3'] ?? '',
        'DateofConsultationHEPAB4' => $row['DateofConsultationHEPAB4'] ?? '',
        'DateofConsultationAMV1' => $row['DateofConsultationAMV1'] ?? '',
        'DateofConsultationMMR' => $row['DateofConsultationMMR'] ?? '',
        'DateofConsultationFIC' => $row['DateofConsultationFIC'] ?? '',
        'DateofConsultationCIC' => $row['DateofConsultationCIC'] ?? '',
        'DateofConsultationAMV2' => $row['DateofConsultationAMV2'] ?? '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        'DATEOFCONSULTTT1' => $row['DATEOFCONSULTTT1'] ?? '',
        'DATEOFCONSULTTT2' => $row['DATEOFCONSULTTT2'] ?? '',
        'DATEOFCONSULTTT3' => $row['DATEOFCONSULTTT3'] ?? '',
        'DATEOFCONSULTTT4' => $row['DATEOFCONSULTTT4'] ?? '',
        'DATEOFCONSULTT5' => $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    // Filter out empty dates
    $valid_dates = [];
    foreach ($consultation_dates as $field => $date) {
        if (!empty($date) && $date !== '0000-00-00') {
            $valid_dates[$field] = $date;
        }
    }
    
    return $valid_dates;
}

/*
USAGE EXAMPLES:

// Basic usage - print in <p> tag
printEarliestConsultationFromRow($row);

// Get the date value for processing
$earliest = getEarliestConsultationFromRow($row);
if ($earliest) {
    echo "First consultation: " . $earliest;
}

// Print with custom styling
printEarliestConsultationFromRowStyled($row, 'highlight', 'color: blue; font-weight: bold;');

// Print with formatted date
printEarliestConsultationFromRowFormatted($row, 'F j, Y'); // January 15, 2023

// Check if consultation dates exist
if (hasConsultationDates($row)) {
    echo "This child has consultation records.";
}

// Count consultation dates
$count = countConsultationDates($row);
echo "This child has " . $count . " consultation dates recorded.";

// Get all consultation dates for debugging
$all_dates = getAllConsultationDatesFromRow($row);
foreach ($all_dates as $field => $date) {
    echo $field . ": " . $date . "<br>";
}
*/
?>
