<?php
session_start();
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "national_immunization_program";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$query = $_GET['q'] ?? '';

$sql = "SELECT street_Name FROM street WHERE healthCenter LIKE '".$_SESSION['health_center']."'";
$stmt = $conn->prepare($sql);
$search = "%" . $query . "%";
$stmt->bind_param("s", $search);
$stmt->execute();
$result = $stmt->get_result();

$healthCenter = [];
while ($row = $result->fetch_assoc()) {
    $street_Name[] = $row['street_Name'];
}

header('Content-Type: application/json');
echo json_encode($street_Name);

$conn->close();
?>