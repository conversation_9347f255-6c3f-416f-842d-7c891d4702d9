<?php
session_start();

// Security check - user must be logged in
if (!isset($_SESSION['health_center'])) {
    die('Unauthorized access - Please login first');
}

$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');

if (!$conn) {
    die('Connection failed ' . mysqli_error($conn));
}

// Function to verify user's health center matches their account
function verifyHealthCenterAccess($conn, $session_health_center) {
    // Check if the session health_center exists in health_facility table
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

// Check if the export button is clicked
if (isset($_POST['export'])) {
    // Verify health center access
    if (!verifyHealthCenterAccess($conn, $_SESSION['health_center'])) {
        die('Wrong password - Excel download blocked. Data will not proceed. You can only download data for your registered health center.');
    }

    // SQL query - only get data for the user's health center
    $sql = "SELECT * FROM nip_table WHERE health_center = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $_SESSION['health_center']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Set headers for Excel download with health center name
        $health_center_safe = preg_replace('/[^a-zA-Z0-9_-]/', '_', $_SESSION['health_center']);
        header("Content-Type: application/vnd.ms-excel");
        header("Content-Disposition: attachment; filename=" . $health_center_safe . "_nip_export_" . date('Ymd') . ".xls");
        header("Pragma: no-cache");
        header("Expires: 0");

        // Column headers
        $column_names = array();
        $fieldinfo = $result->fetch_fields();
        foreach ($fieldinfo as $field) {
            $column_names[] = $field->name;
        }
        echo implode("\t", $column_names) . "\n";

        // Output data rows
        while($row = $result->fetch_assoc()) {
            $line = array();
            foreach ($column_names as $column_name) {
                $line[] = str_replace('"', '""', $row[$column_name]); // Escape double quotes
            }
            echo implode("\t", $line) . "\n";
        }

        // Log successful download
        error_log("Excel download successful for health center: " . $_SESSION['health_center'] . " at " . date('Y-m-d H:i:s'));

        $stmt->close();
        exit(); // Stop further execution to prevent HTML from being appended
    } else {
        echo "No data found for your health center: " . htmlspecialchars($_SESSION['health_center']);
        $stmt->close();
    }
}

// Close connection
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export to Excel</title>
</head>
<body>
    <form method="post">
        <button type="submit" name="export">Export to Excel</button>
    </form>
</body>
</html>