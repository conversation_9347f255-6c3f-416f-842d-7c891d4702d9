<?php
session_start();
include 'db.php';
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access - Please login first'
    ]);
    exit;
}

// Function to verify user's health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

// Get request data
$input = json_decode(file_get_contents('php://input'), true);

try {
    // Verify health center access
    if (!verifyHealthCenterAccess($conn, $_SESSION['health_center'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Wrong password - Excel download blocked. Data will not proceed. You can only download data for your registered health center.',
            'blocked_reason' => 'health_center_mismatch'
        ]);
        exit;
    }
    
    $health_center = $_SESSION['health_center'];
    
    // Perform quick download test
    $test_results = [];
    
    // Test 1: Database connectivity
    $db_test_start = microtime(true);
    $db_stmt = $conn->prepare("SELECT COUNT(*) as count FROM nip_table WHERE deleted = 0 AND Barangay = ?");
    $db_stmt->bind_param("s", $health_center);
    $db_stmt->execute();
    $db_result = $db_stmt->get_result();
    $record_count = $db_result->fetch_assoc()['count'];
    $db_stmt->close();
    $db_test_time = round((microtime(true) - $db_test_start) * 1000, 2);
    
    $test_results['database'] = [
        'passed' => true,
        'time_ms' => $db_test_time,
        'record_count' => $record_count,
        'message' => "Database query successful ({$db_test_time}ms)"
    ];
    
    // Test 2: Sample data retrieval
    $data_test_start = microtime(true);
    $sample_stmt = $conn->prepare("SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ? LIMIT 5");
    $sample_stmt->bind_param("s", $health_center);
    $sample_stmt->execute();
    $sample_result = $sample_stmt->get_result();
    $sample_data = [];
    while ($row = $sample_result->fetch_assoc()) {
        $sample_data[] = $row;
    }
    $sample_stmt->close();
    $data_test_time = round((microtime(true) - $data_test_start) * 1000, 2);
    
    $test_results['data_retrieval'] = [
        'passed' => count($sample_data) >= 0,
        'time_ms' => $data_test_time,
        'sample_count' => count($sample_data),
        'message' => "Sample data retrieved ({$data_test_time}ms)"
    ];
    
    // Test 3: Excel generation simulation
    $excel_test_start = microtime(true);
    
    // Simulate Excel processing time
    usleep(100000); // 100ms delay to simulate processing
    
    $excel_test_time = round((microtime(true) - $excel_test_start) * 1000, 2);
    
    $test_results['excel_generation'] = [
        'passed' => true,
        'time_ms' => $excel_test_time,
        'estimated_size' => round($record_count * 0.5, 1) . ' KB',
        'message' => "Excel generation test passed ({$excel_test_time}ms)"
    ];
    
    // Test 4: Security validation
    $security_test_start = microtime(true);
    
    // Check session validity
    $session_valid = isset($_SESSION['health_center']) && 
                    isset($_SESSION['email']) && 
                    isset($_SESSION['fullname']);
    
    // Check health center approval
    $approval_stmt = $conn->prepare("SELECT approved FROM health_facility WHERE health_center = ?");
    $approval_stmt->bind_param("s", $health_center);
    $approval_stmt->execute();
    $approval_result = $approval_stmt->get_result();
    $is_approved = false;
    if ($approval_result->num_rows > 0) {
        $approval_row = $approval_result->fetch_assoc();
        $is_approved = $approval_row['approved'] == 1;
    }
    $approval_stmt->close();
    
    $security_test_time = round((microtime(true) - $security_test_start) * 1000, 2);
    
    $test_results['security'] = [
        'passed' => $session_valid && $is_approved,
        'time_ms' => $security_test_time,
        'session_valid' => $session_valid,
        'account_approved' => $is_approved,
        'message' => ($session_valid && $is_approved) ? 
                    "Security validation passed ({$security_test_time}ms)" : 
                    "Security validation failed - account not approved or invalid session"
    ];
    
    // Calculate overall results
    $total_time = $db_test_time + $data_test_time + $excel_test_time + $security_test_time;
    $all_tests_passed = $test_results['database']['passed'] && 
                       $test_results['data_retrieval']['passed'] && 
                       $test_results['excel_generation']['passed'] && 
                       $test_results['security']['passed'];
    
    // Estimate download time based on record count
    $estimated_download_time = max(2, round($record_count / 1000 * 3, 1)); // Rough estimate
    
    $response = [
        'success' => $all_tests_passed,
        'message' => $all_tests_passed ? 
                    'Quick download test successful - Excel download ready' : 
                    'Quick download test failed - Excel download may be blocked',
        'health_center' => $health_center,
        'timestamp' => date('Y-m-d H:i:s'),
        'test_results' => $test_results,
        'summary' => [
            'total_time_ms' => round($total_time, 2),
            'record_count' => $record_count,
            'estimated_download_time' => $estimated_download_time . ' seconds',
            'estimated_file_size' => round($record_count * 0.5, 1) . ' KB',
            'all_tests_passed' => $all_tests_passed
        ],
        'recommendations' => []
    ];
    
    // Add recommendations based on test results
    if (!$all_tests_passed) {
        if (!$test_results['security']['passed']) {
            $response['recommendations'][] = 'Contact administrator to verify account approval';
        }
        if ($test_results['database']['time_ms'] > 1000) {
            $response['recommendations'][] = 'Database response is slow - consider downloading during off-peak hours';
        }
        if ($record_count > 10000) {
            $response['recommendations'][] = 'Large dataset detected - download may take longer than usual';
        }
    } else {
        if ($record_count > 5000) {
            $response['recommendations'][] = 'Large dataset - download may take ' . $estimated_download_time . ' seconds';
        }
        if ($total_time > 500) {
            $response['recommendations'][] = 'System response is slower than optimal - consider downloading later';
        }
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Quick download test failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'error' => true
    ]);
    error_log("Quick Download Test Error: " . $e->getMessage());
}
?>
