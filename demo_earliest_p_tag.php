<?php
// Connect to database
$host = "localhost";
$username = "root";
$password = ""; // adjust if needed
$database = "national_immunization_program"; // replace with your DB

$conn = new mysqli($host, $username, $password, $database);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Select only the necessary date fields
$sql = "
    SELECT 
        id,
        DateBCGwasgiven,
        DatePenta1wasgiven
    FROM nip_table
";

$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1' cellpadding='8'>";
    echo "<tr><th>ID</th><th>Date of Registration (Earliest)</th></tr>";

    while ($row = $result->fetch_assoc()) {
        $date1 = $row['DateBCGwasgiven'];
        $date2 = $row['DatePenta1wasgiven'];

        // Convert to timestamps
        $ts1 = $date1 ? strtotime($date1) : false;
        $ts2 = $date2 ? strtotime($date2) : false;

        // Find earliest date
        if ($ts1 && $ts2) {
            $earliest = ($ts1 <= $ts2) ? $ts1 : $ts2;
        } elseif ($ts1) {
            $earliest = $ts1;
        } elseif ($ts2) {
            $earliest = $ts2;
        } else {
            $earliest = null;
        }

        // Output
        echo "<tr>
                <td>{$row['id']}</td>
                <td><strong>" . ($earliest ? date('F d, Y', $earliest) : 'No date available') . "</strong></td>
              </tr>";
    }

    echo "</table>";
} else {
    echo "No records found.";
}

$conn->close();
?>
