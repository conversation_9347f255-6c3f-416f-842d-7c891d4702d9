<?php
// Function to find earliest consultation date from $row data
function getEarliestConsultationFromRow($row) {
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];

    $valid_dates = [];

    // Collect all valid dates
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00') {
            $valid_dates[] = $date;
        }
    }

    // Return earliest date or null if no valid dates
    return !empty($valid_dates) ? min($valid_dates) : null;
}

// Function to print earliest consultation date in <p> tag from $row
function printEarliestConsultationFromRow($row) {
    $earliest = getEarliestConsultationFromRow($row);

    if ($earliest) {
        echo '<p>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p>No consultation date recorded</p>';
    }
}

// Connect to database
$host = "localhost";
$username = "root";
$password = ""; // adjust if needed
$database = "national_immunization_program"; // replace with your DB

$conn = new mysqli($host, $username, $password, $database);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Select all consultation date fields
$sql = "
    SELECT
        id,
        NameOfChild,
        LastNameOfChild,
        DateOfBirth,
        DateofConsultationBCG,
        DateofConsultationPENTAHIB1,
        DateofConsultationPENTAHIB2,
        DateofConsultationPENTAHIB3,
        DateofConsultationOPV1v,
        DateofConsultationOPV2,
        DateofConsultationOPV3,
        DateofConsultationIPV1,
        DateofConsultationIPV2,
        DateofConsultationPCV1,
        DateofConsultationPCV2,
        DateofConsultationPCV3,
        DateofConsultationHEPAatBirth,
        DateofConsultationHEPAB1,
        DateofConsultationHEPAB2,
        DateofConsultationHEPAB3,
        DateofConsultationHEPAB4,
        DateofConsultationAMV1,
        DateofConsultationMMR,
        DateofConsultationFIC,
        DateofConsultationCIC,
        DateofConsultationAMV2,
        DATEOFCONSULTATIONEXCLUSIVEBF,
        DATEOFCONSULTTT1,
        DATEOFCONSULTTT2,
        DATEOFCONSULTTT3,
        DATEOFCONSULTTT4,
        DATEOFCONSULTT5
    FROM nip_table
    WHERE (deleted IS NULL OR deleted = 0)
    LIMIT 10
";

$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Earliest Consultation Date from $row Data</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .child-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }

        .earliest-date {
            color: #1976d2;
            font-weight: bold;
            background: #e3f2fd;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
        }

        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left blue-text">event</i>
                Earliest Consultation Date from $row Data
            </h3>
            <p class="center-align">Using all consultation date fields from database rows</p>
        </div>

        <div class="demo-section">
            <h4>📋 Sample Records with Earliest Consultation Dates</h4>

            <?php
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $earliest = getEarliestConsultationFromRow($row);
                    $child_name = trim(($row['NameOfChild'] ?? '') . ' ' . ($row['LastNameOfChild'] ?? ''));

                    echo '<div class="child-card">';
                    echo '<div class="row" style="margin-bottom: 0;">';
                    echo '<div class="col s12 m8">';
                    echo '<h6 style="margin: 0; color: #2196f3;">ID: ' . $row['id'] . ' - ' . htmlspecialchars($child_name) . '</h6>';
                    echo '<p style="margin: 5px 0;"><strong>Birth Date:</strong> ' . htmlspecialchars($row['DateOfBirth'] ?? 'Not recorded') . '</p>';
                    echo '<p style="margin: 0;"><strong>Earliest Consultation:</strong> ';

                    if ($earliest) {
                        echo '<span class="earliest-date">' . htmlspecialchars($earliest) . '</span>';
                    } else {
                        echo '<span style="color: #ff9800;">No consultation dates recorded</span>';
                    }

                    echo '</p>';
                    echo '</div>';
                    echo '<div class="col s12 m4">';
                    echo '<strong>Using Function:</strong><br>';
                    printEarliestConsultationFromRow($row);
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                }
            } else {
                echo '<p>No records found in the database.</p>';
            }
            ?>
        </div>

        <div class="demo-section">
            <h4>💻 How to Use in Your Code</h4>

            <h5>Method 1: Print in &lt;p&gt; tag</h5>
            <div class="code-block">
&lt;?php
// Include the function (add to top of your file)
function printEarliestConsultationFromRow($row) {
    // Function code here...
}

// In your code where you have $row from database
while ($row = mysqli_fetch_assoc($result)) {
    echo "Child: " . $row['NameOfChild'];
    echo " | Earliest consultation: ";
    printEarliestConsultationFromRow($row);
}
?&gt;
            </div>

            <h5>Method 2: Get date value for processing</h5>
            <div class="code-block">
&lt;?php
// Get the earliest date value
$earliest_date = getEarliestConsultationFromRow($row);

if ($earliest_date) {
    echo "First consultation was on: " . $earliest_date;

    // Calculate days since birth
    if (!empty($row['DateOfBirth'])) {
        $days = (strtotime($earliest_date) - strtotime($row['DateOfBirth'])) / (60*60*24);
        echo " (That was " . round($days) . " days after birth)";
    }
} else {
    echo "No consultation dates found for this child.";
}
?&gt;
            </div>

            <h5>Method 3: In forms and displays</h5>
            <div class="code-block">
&lt;div class="consultation-info"&gt;
    &lt;label&gt;First Consultation Date:&lt;/label&gt;
    &lt;?php printEarliestConsultationFromRow($row); ?&gt;
&lt;/div&gt;

&lt;!-- Or in tables --&gt;
&lt;td&gt;
    &lt;?php printEarliestConsultationFromRow($row); ?&gt;
&lt;/td&gt;
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Consultation Date Fields Checked</h4>
            <p>The function checks all these fields from your $row data:</p>

            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Primary Vaccines</span>
                            <ul style="font-size: 13px;">
                                <li>$row['DateofConsultationBCG']</li>
                                <li>$row['DateofConsultationPENTAHIB1']</li>
                                <li>$row['DateofConsultationPENTAHIB2']</li>
                                <li>$row['DateofConsultationPENTAHIB3']</li>
                                <li>$row['DateofConsultationOPV1v']</li>
                                <li>$row['DateofConsultationOPV2']</li>
                                <li>$row['DateofConsultationOPV3']</li>
                                <li>$row['DateofConsultationIPV1']</li>
                                <li>$row['DateofConsultationIPV2']</li>
                                <li>$row['DateofConsultationPCV1']</li>
                                <li>$row['DateofConsultationPCV2']</li>
                                <li>$row['DateofConsultationPCV3']</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Additional Consultations</span>
                            <ul style="font-size: 13px;">
                                <li>$row['DateofConsultationHEPAatBirth']</li>
                                <li>$row['DateofConsultationHEPAB1']</li>
                                <li>$row['DateofConsultationHEPAB2']</li>
                                <li>$row['DateofConsultationHEPAB3']</li>
                                <li>$row['DateofConsultationHEPAB4']</li>
                                <li>$row['DateofConsultationAMV1']</li>
                                <li>$row['DateofConsultationMMR']</li>
                                <li>$row['DateofConsultationFIC']</li>
                                <li>$row['DateofConsultationCIC']</li>
                                <li>$row['DateofConsultationAMV2']</li>
                                <li>$row['DATEOFCONSULTATIONEXCLUSIVEBF']</li>
                                <li>$row['DATEOFCONSULTTT1/2/3/4/5']</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>✅ Complete Function Code</h4>
            <p>Copy this function to use in your files:</p>

            <div class="code-block">
&lt;?php
function getEarliestConsultationFromRow($row) {
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];

    $valid_dates = [];
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00') {
            $valid_dates[] = $date;
        }
    }

    return !empty($valid_dates) ? min($valid_dates) : null;
}

function printEarliestConsultationFromRow($row) {
    $earliest = getEarliestConsultationFromRow($row);

    if ($earliest) {
        echo '&lt;p&gt;' . htmlspecialchars($earliest) . '&lt;/p&gt;';
    } else {
        echo '&lt;p&gt;No consultation date recorded&lt;/p&gt;';
    }
}
?&gt;
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>

<?php
$conn->close();
?>
