<?php
/**
 * Comprehensive Test Script for Multi-Action User Management System
 * Tests all functional buttons: Approve, Disapprove, Suspend, Review, Edit, View, Delete, etc.
 */

session_start();
include 'db.php';

// Set test session
if (!isset($_SESSION['health_center'])) {
    $_SESSION['health_center'] = 'TEST_CENTER';
    $_SESSION['username'] = 'test_admin';
}

echo "<h1>🚀 Multi-Action User Management System Test</h1>";

// Test 1: Database Structure Check
echo "<h2>1. 📊 Database Structure Verification</h2>";

$tables_to_check = ['users', 'health_facility'];
foreach ($tables_to_check as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<div style='background: #e8f5e8; padding: 10px; margin: 5px; border-radius: 5px;'>";
        echo "<h3 style='color: green;'>✓ Table '$table' exists</h3>";
        
        // Check columns
        $columns_result = $conn->query("SHOW COLUMNS FROM $table");
        echo "<details><summary>View Columns</summary><ul>";
        while ($column = $columns_result->fetch_assoc()) {
            echo "<li><strong>{$column['Field']}</strong> - {$column['Type']} {$column['Null']} {$column['Default']}</li>";
        }
        echo "</ul></details>";
        echo "</div>";
    } else {
        echo "<div style='background: #ffe8e8; padding: 10px; margin: 5px; border-radius: 5px;'>";
        echo "<h3 style='color: red;'>✗ Table '$table' does not exist</h3>";
        echo "</div>";
    }
}

// Test 2: Sample Data Overview
echo "<h2>2. 📋 Sample Data Overview</h2>";

foreach ($tables_to_check as $table) {
    $result = $conn->query("SELECT COUNT(*) as count FROM $table");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<h3>Table '$table': {$row['count']} records</h3>";
        
        // Show users by status
        $status_query = "SELECT 
            approved,
            COUNT(*) as count,
            CASE 
                WHEN approved = 1 THEN 'Approved'
                WHEN approved = -1 THEN 'Disapproved'
                WHEN approved = 2 THEN 'Suspended'
                WHEN approved = 3 THEN 'Under Review'
                ELSE 'Pending'
            END as status_name
            FROM $table 
            GROUP BY approved 
            ORDER BY approved";
            
        $status_result = $conn->query($status_query);
        if ($status_result && $status_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Status</th><th>Count</th><th>Status Code</th></tr>";
            while ($status = $status_result->fetch_assoc()) {
                $color = '';
                switch($status['approved']) {
                    case 1: $color = 'background: #e8f5e8;'; break;
                    case -1: $color = 'background: #ffe8e8;'; break;
                    case 2: $color = 'background: #fff3e0;'; break;
                    case 3: $color = 'background: #f3e5f5;'; break;
                    default: $color = 'background: #f5f5f5;'; break;
                }
                echo "<tr style='$color'>";
                echo "<td>{$status['status_name']}</td>";
                echo "<td>{$status['count']}</td>";
                echo "<td>{$status['approved']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
}

// Test 3: Action Testing Interface
echo "<h2>3. 🎯 Action Testing Interface</h2>";

// Get sample users for testing
$sample_users = [];
foreach ($tables_to_check as $table) {
    $result = $conn->query("SELECT id, username, email, fullname, approved FROM $table LIMIT 5");
    if ($result && $result->num_rows > 0) {
        while ($user = $result->fetch_assoc()) {
            $user['table'] = $table;
            $sample_users[] = $user;
        }
    }
}

if (!empty($sample_users)) {
    echo "<h3>Available Test Users:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Table</th><th>Username</th><th>Email</th><th>Status</th><th>Actions</th></tr>";
    
    foreach ($sample_users as $user) {
        $status_text = '';
        $status_color = '';
        switch($user['approved']) {
            case 1: $status_text = 'Approved'; $status_color = 'color: green;'; break;
            case -1: $status_text = 'Disapproved'; $status_color = 'color: red;'; break;
            case 2: $status_text = 'Suspended'; $status_color = 'color: orange;'; break;
            case 3: $status_text = 'Under Review'; $status_color = 'color: purple;'; break;
            default: $status_text = 'Pending'; $status_color = 'color: gray;'; break;
        }
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['table']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td style='$status_color'><strong>$status_text</strong></td>";
        echo "<td>";
        
        // Show appropriate actions based on current status
        $actions = [];
        switch($user['approved']) {
            case 0: // Pending
                $actions = ['approve', 'disapprove', 'review', 'edit', 'view', 'delete'];
                break;
            case 1: // Approved
                $actions = ['suspend', 'revoke', 'edit', 'view'];
                break;
            case -1: // Disapproved
                $actions = ['reactivate', 'view', 'delete'];
                break;
            case 2: // Suspended
                $actions = ['unsuspend', 'view', 'delete'];
                break;
            case 3: // Under Review
                $actions = ['approve', 'disapprove', 'view'];
                break;
        }
        
        foreach ($actions as $action) {
            $color = '';
            switch($action) {
                case 'approve': case 'unsuspend': case 'reactivate': $color = 'green'; break;
                case 'disapprove': case 'revoke': case 'delete': $color = 'red'; break;
                case 'suspend': case 'edit': $color = 'orange'; break;
                case 'review': $color = 'purple'; break;
                case 'view': $color = 'blue'; break;
            }
            echo "<a href='?test_action=$action&test_id={$user['id']}&test_table={$user['table']}' 
                     style='background: $color; color: white; padding: 2px 6px; margin: 1px; text-decoration: none; border-radius: 3px; font-size: 11px;'>
                     $action</a> ";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No test users found. Please add some sample data first.</p>";
}

// Test 4: Process Test Action
if (isset($_GET['test_action']) && isset($_GET['test_id']) && isset($_GET['test_table'])) {
    $test_action = $_GET['test_action'];
    $test_id = intval($_GET['test_id']);
    $test_table = $_GET['test_table'];
    
    echo "<h2>4. 🧪 Testing Action: <span style='color: blue;'>$test_action</span> on User ID: $test_id</h2>";
    
    // Simulate the action
    $_POST['id'] = $test_id;
    $_POST['action'] = $test_action;
    $_POST['username'] = 'test_user_' . $test_id;
    $_POST['email'] = 'test' . $test_id . '@example.com';
    $_POST['health_center'] = 'TEST_CENTER';
    $_POST['fullname'] = 'Test User ' . $test_id;
    $_POST['mobile_number'] = '123456789' . $test_id;
    $_POST['reason'] = 'Test reason for ' . $test_action . ' action';
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Test Parameters:</h3>";
    echo "<ul>";
    echo "<li><strong>Action:</strong> $test_action</li>";
    echo "<li><strong>User ID:</strong> $test_id</li>";
    echo "<li><strong>Table:</strong> $test_table</li>";
    echo "<li><strong>Reason:</strong> " . $_POST['reason'] . "</li>";
    echo "</ul>";
    
    echo "<h3>Processing...</h3>";
    
    // Capture output from update_user.php
    ob_start();
    include 'update_user.php';
    $result = ob_get_clean();
    
    echo "<div style='background: " . ($result === 'success' ? '#e8f5e8' : '#ffe8e8') . "; padding: 10px; border-radius: 5px;'>";
    echo "<h4>Result: <span style='color: " . ($result === 'success' ? 'green' : 'red') . ";'>$result</span></h4>";
    echo "</div>";
    echo "</div>";
    
    // Show updated user status
    $updated_user = $conn->query("SELECT * FROM $test_table WHERE id = $test_id")->fetch_assoc();
    if ($updated_user) {
        echo "<h3>Updated User Status:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($updated_user as $field => $value) {
            if (in_array($field, ['approved', 'approved_date', 'disapproved_date', 'suspended_date', 'review_date', 'last_action_date', 'last_action_by'])) {
                echo "<tr><td><strong>$field</strong></td><td>$value</td></tr>";
            }
        }
        echo "</table>";
    }
}

// Test 5: Audit Log Check
echo "<h2>5. 📝 Recent Audit Logs</h2>";

$audit_result = $conn->query("SHOW TABLES LIKE 'user_action_logs'");
if ($audit_result && $audit_result->num_rows > 0) {
    echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
    echo "<h3 style='color: green;'>✓ Audit log table exists</h3>";
    
    $logs_result = $conn->query("SELECT * FROM user_action_logs ORDER BY action_date DESC LIMIT 15");
    if ($logs_result && $logs_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Table</th><th>Action</th><th>Date</th><th>Performed By</th><th>Reason</th><th>IP</th></tr>";
        while ($log = $logs_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$log['id']}</td>";
            echo "<td>{$log['user_id']}</td>";
            echo "<td>{$log['table_name']}</td>";
            echo "<td><strong>{$log['action_type']}</strong></td>";
            echo "<td>{$log['action_date']}</td>";
            echo "<td>{$log['performed_by']}</td>";
            echo "<td>" . substr($log['reason'], 0, 30) . (strlen($log['reason']) > 30 ? '...' : '') . "</td>";
            echo "<td>{$log['ip_address']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No audit logs found</p>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #fff3e0; padding: 10px; border-radius: 5px;'>";
    echo "<h3 style='color: orange;'>⚠ Audit log table does not exist yet</h3>";
    echo "</div>";
}

// Test 6: File Verification
echo "<h2>6. 📁 File Verification</h2>";

$files_to_check = [
    'user_settings.php' => 'Main user management interface',
    'update_user.php' => 'Backend processing script',
    'test_multi_action_functionality.php' => 'This testing script',
    'db.php' => 'Database connection'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<div style='background: #e8f5e8; padding: 5px; margin: 2px; border-radius: 3px;'>";
        echo "<span style='color: green;'>✓</span> <strong>$file</strong> - $description";
        echo "</div>";
    } else {
        echo "<div style='background: #ffe8e8; padding: 5px; margin: 2px; border-radius: 3px;'>";
        echo "<span style='color: red;'>✗</span> <strong>$file</strong> - $description (MISSING)";
        echo "</div>";
    }
}

echo "<hr>";
echo "<h2>🎉 Testing Summary</h2>";
echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 10px;'>";
echo "<h3>Available Actions:</h3>";
echo "<ul>";
echo "<li><strong>Primary Actions:</strong> Approve, Disapprove</li>";
echo "<li><strong>Status Management:</strong> Suspend, Unsuspend, Review</li>";
echo "<li><strong>User Management:</strong> Edit, View, Delete</li>";
echo "<li><strong>Access Control:</strong> Revoke, Reactivate</li>";
echo "</ul>";

echo "<h3>Status Codes:</h3>";
echo "<ul>";
echo "<li><strong>0:</strong> Pending (default)</li>";
echo "<li><strong>1:</strong> Approved</li>";
echo "<li><strong>-1:</strong> Disapproved</li>";
echo "<li><strong>2:</strong> Suspended</li>";
echo "<li><strong>3:</strong> Under Review</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test each action using the links above</li>";
echo "<li>Verify status changes in the database</li>";
echo "<li>Check audit logs for proper tracking</li>";
echo "<li>Test the main interface at <a href='user_settings.php'>user_settings.php</a></li>";
echo "</ol>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
    font-size: 14px;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

details {
    margin: 10px 0;
}

summary {
    cursor: pointer;
    font-weight: bold;
    padding: 5px;
    background: #f0f0f0;
    border-radius: 3px;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
