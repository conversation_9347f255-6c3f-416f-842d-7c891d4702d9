/**
 * Duplicate Detection Notification System
 * Enhanced web notifications for duplicate checking
 */

class DuplicateNotificationSystem {
    constructor() {
        this.notificationQueue = [];
        this.isInitialized = false;
        this.init();
    }

    init() {
        // Request notification permission on page load
        this.requestNotificationPermission();
        
        // Initialize Materialize toast if available
        if (typeof M !== 'undefined') {
            this.isInitialized = true;
        }
        
        // Add notification styles
        this.addNotificationStyles();
        
        // Create notification container
        this.createNotificationContainer();
    }

    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    this.showToast('success', 'Notifications enabled for duplicate alerts!');
                }
            });
        }
    }

    addNotificationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .duplicate-notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            }
            
            .duplicate-notification {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                margin-bottom: 10px;
                padding: 16px;
                border-left: 4px solid #f44336;
                animation: slideInRight 0.3s ease-out;
                pointer-events: all;
                max-width: 100%;
                word-wrap: break-word;
            }
            
            .duplicate-notification.warning {
                border-left-color: #ff9800;
            }
            
            .duplicate-notification.info {
                border-left-color: #2196f3;
            }
            
            .duplicate-notification.success {
                border-left-color: #4caf50;
            }
            
            .duplicate-notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .duplicate-notification-title {
                font-weight: 600;
                font-size: 14px;
                display: flex;
                align-items: center;
            }
            
            .duplicate-notification-title i {
                margin-right: 8px;
                font-size: 18px;
            }
            
            .duplicate-notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .duplicate-notification-close:hover {
                color: #333;
                background: #f0f0f0;
                border-radius: 50%;
            }
            
            .duplicate-notification-message {
                font-size: 13px;
                color: #666;
                line-height: 1.4;
                margin-bottom: 12px;
            }
            
            .duplicate-notification-actions {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }
            
            .duplicate-notification-btn {
                background: #2196f3;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 4px;
                transition: background 0.2s;
            }
            
            .duplicate-notification-btn:hover {
                background: #1976d2;
                color: white;
            }
            
            .duplicate-notification-btn.secondary {
                background: #666;
            }
            
            .duplicate-notification-btn.secondary:hover {
                background: #555;
            }
            
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            
            .duplicate-notification.removing {
                animation: slideOutRight 0.3s ease-in forwards;
            }
            
            .duplicate-alert-pulse {
                animation: duplicatePulse 2s infinite;
            }
            
            @keyframes duplicatePulse {
                0% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(244, 67, 54, 0); }
                100% { box-shadow: 0 0 0 0 rgba(244, 67, 54, 0); }
            }
            
            .duplicate-sound-toggle {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #2196f3;
                color: white;
                border: none;
                border-radius: 50%;
                width: 56px;
                height: 56px;
                font-size: 24px;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                transition: all 0.3s;
            }
            
            .duplicate-sound-toggle:hover {
                background: #1976d2;
                transform: scale(1.1);
            }
            
            .duplicate-sound-toggle.muted {
                background: #666;
            }
        `;
        document.head.appendChild(style);
    }

    createNotificationContainer() {
        if (!document.getElementById('duplicate-notification-container')) {
            const container = document.createElement('div');
            container.id = 'duplicate-notification-container';
            container.className = 'duplicate-notification-container';
            document.body.appendChild(container);
        }
    }

    showNotification(type, title, message, actions = []) {
        // Show toast notification
        this.showToast(type, title, message);
        
        // Show browser notification
        this.showBrowserNotification(title, message, type);
        
        // Show custom notification
        this.showCustomNotification(type, title, message, actions);
        
        // Play sound for errors
        if (type === 'error') {
            this.playAlertSound();
        }
        
        // Log to console
        console.log(`[DUPLICATE ${type.toUpperCase()}] ${title}: ${message}`);
    }

    showToast(type, title, message) {
        if (typeof M !== 'undefined' && M.toast) {
            let iconClass = this.getIconClass(type);
            let toastClass = this.getToastClass(type);
            
            M.toast({
                html: `<i class="material-icons left">${iconClass}</i><strong>${title}:</strong> ${message}`,
                classes: `${toastClass} white-text`,
                displayLength: type === 'error' ? 10000 : 6000
            });
        }
    }

    showBrowserNotification(title, message, type) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: this.getNotificationIcon(type),
                tag: 'duplicate-check-' + Date.now(),
                requireInteraction: type === 'error'
            });
            
            // Auto close after 8 seconds for non-errors
            if (type !== 'error') {
                setTimeout(() => notification.close(), 8000);
            }
        }
    }

    showCustomNotification(type, title, message, actions = []) {
        const container = document.getElementById('duplicate-notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `duplicate-notification ${type}`;
        
        // Default actions
        const defaultActions = [
            { text: 'View Duplicates', href: 'duplicate_checker.php', icon: 'search' },
            { text: 'Dismiss', action: 'dismiss', icon: 'close', class: 'secondary' }
        ];
        
        const allActions = [...actions, ...defaultActions];
        
        notification.innerHTML = `
            <div class="duplicate-notification-header">
                <div class="duplicate-notification-title">
                    <i class="material-icons">${this.getIconClass(type)}</i>
                    ${title}
                </div>
                <button class="duplicate-notification-close" onclick="this.closest('.duplicate-notification').remove()">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <div class="duplicate-notification-message">${message}</div>
            <div class="duplicate-notification-actions">
                ${allActions.map(action => {
                    if (action.href) {
                        return `<a href="${action.href}" class="duplicate-notification-btn ${action.class || ''}">
                                    <i class="material-icons">${action.icon}</i> ${action.text}
                                </a>`;
                    } else {
                        return `<button class="duplicate-notification-btn ${action.class || ''}" onclick="${action.action === 'dismiss' ? 'this.closest(\'.duplicate-notification\').remove()' : action.action}">
                                    <i class="material-icons">${action.icon}</i> ${action.text}
                                </button>`;
                    }
                }).join('')}
            </div>
        `;
        
        container.appendChild(notification);
        
        // Auto remove after delay (except for errors)
        if (type !== 'error') {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.add('removing');
                    setTimeout(() => notification.remove(), 300);
                }
            }, 8000);
        }
    }

    playAlertSound() {
        // Check if sound is enabled
        const soundEnabled = localStorage.getItem('duplicate-sound-enabled') !== 'false';
        if (!soundEnabled) return;
        
        // Create and play error sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }

    getIconClass(type) {
        const icons = {
            error: 'error',
            warning: 'warning',
            info: 'info',
            success: 'check_circle'
        };
        return icons[type] || 'info';
    }

    getToastClass(type) {
        const classes = {
            error: 'red',
            warning: 'orange',
            info: 'blue',
            success: 'green'
        };
        return classes[type] || 'blue';
    }

    getNotificationIcon(type) {
        // Base64 encoded SVG icons
        const icons = {
            error: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2Y0NDMzNiI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0yIDE1bDQtNEgxMGw0LTRIOHY4eiIvPjwvc3ZnPg==',
            warning: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iI2ZmOTgwMCI+PHBhdGggZD0iTTEgMjFoMjJMMTIgMiAxIDIxem0xMi0zaC0ydi0yaDJ2MnptMC00aC0ydi00aDJ2NHoiLz48L3N2Zz4=',
            info: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzIxOTZmMyI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0yIDE1bDQtNEgxMGw0LTRIOHY4eiIvPjwvc3ZnPg==',
            success: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0iIzRjYWY1MCI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0yIDE1bDQtNEgxMGw0LTRIOHY4eiIvPjwvc3ZnPg=='
        };
        return icons[type] || icons.info;
    }

    createSoundToggle() {
        const toggle = document.createElement('button');
        toggle.className = 'duplicate-sound-toggle';
        toggle.innerHTML = '<i class="material-icons">volume_up</i>';
        toggle.title = 'Toggle duplicate alert sounds';
        
        const soundEnabled = localStorage.getItem('duplicate-sound-enabled') !== 'false';
        if (!soundEnabled) {
            toggle.classList.add('muted');
            toggle.innerHTML = '<i class="material-icons">volume_off</i>';
        }
        
        toggle.addEventListener('click', () => {
            const isEnabled = localStorage.getItem('duplicate-sound-enabled') !== 'false';
            localStorage.setItem('duplicate-sound-enabled', !isEnabled);
            
            if (!isEnabled) {
                toggle.classList.remove('muted');
                toggle.innerHTML = '<i class="material-icons">volume_up</i>';
                this.showToast('success', 'Sound enabled', 'Duplicate alert sounds are now enabled');
            } else {
                toggle.classList.add('muted');
                toggle.innerHTML = '<i class="material-icons">volume_off</i>';
                this.showToast('info', 'Sound disabled', 'Duplicate alert sounds are now disabled');
            }
        });
        
        document.body.appendChild(toggle);
    }

    // Public methods for external use
    showDuplicateError(title, message, actions) {
        this.showNotification('error', title, message, actions);
    }

    showDuplicateWarning(title, message, actions) {
        this.showNotification('warning', title, message, actions);
    }

    showDuplicateInfo(title, message, actions) {
        this.showNotification('info', title, message, actions);
    }

    clearAllNotifications() {
        const container = document.getElementById('duplicate-notification-container');
        if (container) {
            container.innerHTML = '';
        }
    }
}

// Initialize the notification system
let duplicateNotifier;
document.addEventListener('DOMContentLoaded', function() {
    duplicateNotifier = new DuplicateNotificationSystem();
    duplicateNotifier.createSoundToggle();
});

// Global functions for backward compatibility
function showDuplicateNotification(type, title, message) {
    if (duplicateNotifier) {
        duplicateNotifier.showNotification(type, title, message);
    }
}

function showBrowserNotification(title, message, type) {
    if (duplicateNotifier) {
        duplicateNotifier.showBrowserNotification(title, message, type);
    }
}
