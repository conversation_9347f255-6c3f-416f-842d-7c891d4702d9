<?php
/**
 * Test Script for Disapprove Functionality
 * This script tests the approve/disapprove functionality
 */

session_start();
include 'db.php';

// Set a test session for testing purposes
if (!isset($_SESSION['health_center'])) {
    $_SESSION['health_center'] = 'TEST_CENTER';
    $_SESSION['username'] = 'test_admin';
}

echo "<h2>Testing Disapprove Functionality</h2>";

// Test 1: Check if tables exist
echo "<h3>1. Database Structure Check</h3>";

$tables_to_check = ['users', 'health_facility'];
foreach ($tables_to_check as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        
        // Check columns
        $columns_result = $conn->query("SHOW COLUMNS FROM $table");
        echo "<ul>";
        while ($column = $columns_result->fetch_assoc()) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Table '$table' does not exist</p>";
    }
}

// Test 2: Check for sample data
echo "<h3>2. Sample Data Check</h3>";

foreach ($tables_to_check as $table) {
    $result = $conn->query("SELECT COUNT(*) as count FROM $table");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>Table '$table' has {$row['count']} records</p>";
        
        // Show pending users
        $pending_result = $conn->query("SELECT id, username, email, fullname, approved FROM $table WHERE approved = 0 LIMIT 5");
        if ($pending_result && $pending_result->num_rows > 0) {
            echo "<h4>Pending Users in $table:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Status</th></tr>";
            while ($user = $pending_result->fetch_assoc()) {
                $status = $user['approved'] == 1 ? 'Approved' : ($user['approved'] == -1 ? 'Disapproved' : 'Pending');
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['fullname']}</td>";
                echo "<td>$status</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No pending users found in $table</p>";
        }
    }
}

// Test 3: Test approve functionality
echo "<h3>3. Testing Approve Functionality</h3>";

if (isset($_POST['test_approve'])) {
    $test_id = intval($_POST['test_id']);
    $test_table = $_POST['test_table'];
    
    // Simulate approve request
    $_POST['id'] = $test_id;
    $_POST['action'] = 'approve';
    $_POST['username'] = 'test_user';
    $_POST['email'] = '<EMAIL>';
    $_POST['health_center'] = 'TEST_CENTER';
    $_POST['fullname'] = 'Test User';
    $_POST['mobile_number'] = '**********';
    
    echo "<p>Testing approve for ID: $test_id in table: $test_table</p>";
    
    // Include the update script
    ob_start();
    include 'update_user.php';
    $result = ob_get_clean();
    
    echo "<p>Result: <strong>$result</strong></p>";
}

// Test 4: Test disapprove functionality
echo "<h3>4. Testing Disapprove Functionality</h3>";

if (isset($_POST['test_disapprove'])) {
    $test_id = intval($_POST['test_id']);
    $test_table = $_POST['test_table'];
    $reason = $_POST['disapproval_reason'];
    
    // Simulate disapprove request
    $_POST['id'] = $test_id;
    $_POST['action'] = 'disapprove';
    $_POST['disapproval_reason'] = $reason;
    
    echo "<p>Testing disapprove for ID: $test_id in table: $test_table</p>";
    echo "<p>Reason: $reason</p>";
    
    // Include the update script
    ob_start();
    include 'update_user.php';
    $result = ob_get_clean();
    
    echo "<p>Result: <strong>$result</strong></p>";
}

// Test 5: Check audit logs
echo "<h3>5. Audit Log Check</h3>";

$audit_result = $conn->query("SHOW TABLES LIKE 'user_action_logs'");
if ($audit_result && $audit_result->num_rows > 0) {
    echo "<p style='color: green;'>✓ Audit log table exists</p>";
    
    $logs_result = $conn->query("SELECT * FROM user_action_logs ORDER BY action_date DESC LIMIT 10");
    if ($logs_result && $logs_result->num_rows > 0) {
        echo "<h4>Recent Audit Logs:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Table</th><th>Action</th><th>Date</th><th>Performed By</th><th>Reason</th></tr>";
        while ($log = $logs_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$log['id']}</td>";
            echo "<td>{$log['user_id']}</td>";
            echo "<td>{$log['table_name']}</td>";
            echo "<td>{$log['action_type']}</td>";
            echo "<td>{$log['action_date']}</td>";
            echo "<td>{$log['performed_by']}</td>";
            echo "<td>" . substr($log['reason'], 0, 50) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No audit logs found</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Audit log table does not exist yet</p>";
}

// Test forms
echo "<h3>6. Manual Testing Forms</h3>";

// Get a sample user for testing
$sample_user = null;
foreach ($tables_to_check as $table) {
    $result = $conn->query("SELECT id, username, email, fullname FROM $table WHERE approved = 0 LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $sample_user = $result->fetch_assoc();
        $sample_user['table'] = $table;
        break;
    }
}

if ($sample_user) {
    echo "<h4>Test with User ID: {$sample_user['id']} ({$sample_user['username']})</h4>";
    
    echo "<form method='POST' style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";
    echo "<h5>Test Approve:</h5>";
    echo "<input type='hidden' name='test_id' value='{$sample_user['id']}'>";
    echo "<input type='hidden' name='test_table' value='{$sample_user['table']}'>";
    echo "<button type='submit' name='test_approve' style='background: green; color: white; padding: 10px;'>Test Approve</button>";
    echo "</form>";
    
    echo "<form method='POST' style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";
    echo "<h5>Test Disapprove:</h5>";
    echo "<input type='hidden' name='test_id' value='{$sample_user['id']}'>";
    echo "<input type='hidden' name='test_table' value='{$sample_user['table']}'>";
    echo "<textarea name='disapproval_reason' placeholder='Enter disapproval reason...' style='width: 100%; height: 60px;'></textarea><br><br>";
    echo "<button type='submit' name='test_disapprove' style='background: red; color: white; padding: 10px;'>Test Disapprove</button>";
    echo "</form>";
} else {
    echo "<p>No pending users found for testing. Please add some test data first.</p>";
    
    // Provide SQL to create test data
    echo "<h4>Create Test Data:</h4>";
    echo "<pre>";
    echo "-- Add test user to health_facility table\n";
    echo "INSERT INTO health_facility (username, email, health_center, fullname, approved, date_created) \n";
    echo "VALUES ('test_user', '<EMAIL>', 'TEST_CENTER', 'Test User', 0, NOW());\n\n";
    
    echo "-- Add test user to users table (if it exists)\n";
    echo "INSERT INTO users (username, email, health_center, fullname, mobile_number, approved, date_created) \n";
    echo "VALUES ('test_user2', '<EMAIL>', 'TEST_CENTER', 'Test User 2', '**********', 0, NOW());\n";
    echo "</pre>";
}

echo "<h3>7. File Check</h3>";
$files_to_check = ['user_settings.php', 'update_user.php'];
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $file missing</p>";
    }
}

echo "<hr>";
echo "<p><strong>Testing Complete!</strong></p>";
echo "<p>If all tests pass, the disapprove functionality should work correctly.</p>";
echo "<p><a href='user_settings.php'>Go to User Settings</a></p>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}

form {
    background-color: #f9f9f9;
    border-radius: 4px;
}

button {
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    opacity: 0.8;
}
</style>
