<?php
session_start();
include 'db.php';

echo "<h2>SSS.php Debug Information</h2>";

// Check session
echo "<h3>1. Session Check:</h3>";
if (isset($_SESSION['health_center'])) {
    echo "✅ Session exists: " . htmlspecialchars($_SESSION['health_center']) . "<br>";
} else {
    echo "❌ No session found<br>";
    echo "<a href='Main/index.php'>Go to Login</a><br>";
}

// Check database connection
echo "<h3>2. Database Connection:</h3>";
if (isset($conn) && $conn) {
    echo "✅ Database connected<br>";
    
    // Test query
    try {
        $test_result = $conn->query("SELECT COUNT(*) as count FROM nip_table");
        if ($test_result) {
            $row = $test_result->fetch_assoc();
            echo "✅ nip_table accessible, total records: " . $row['count'] . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Table access error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Database connection failed<br>";
}

// Check table structure
echo "<h3>3. Table Structure Check:</h3>";
try {
    $structure_query = "SHOW COLUMNS FROM nip_table";
    $structure_result = $conn->query($structure_query);
    
    if ($structure_result) {
        echo "✅ Table columns found:<br>";
        $vaccine_fields = [];
        while ($column = $structure_result->fetch_assoc()) {
            $field_name = $column['Field'];
            // Check for vaccine-related fields
            if (in_array($field_name, ['BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3', 'OPV1', 'OPV2', 'OPV3', 'HEPAatBirth', 'HEPAB1', 'HEPAB2', 'HEPAB3', 'HEPAB4', 'AMV19monthstobelow12months', 'MMR12MOSTO15MOS', 'FIC', 'CIC', 'AMV2_16MOSTO5YRSOLD', 'TT1', 'TT2', 'TT3', 'TT4', 'TT5'])) {
                $vaccine_fields[] = $field_name;
                echo "🩹 <strong>$field_name</strong> (" . $column['Type'] . ")<br>";
            } else if (in_array($field_name, ['NameOfChild', 'LastNameOfChild', 'lastnameOfChild', 'DateOfRegistration', 'DateOfBirth', 'Barangay'])) {
                echo "👤 <strong>$field_name</strong> (" . $column['Type'] . ")<br>";
            }
        }
        
        echo "<br><strong>Vaccine fields found:</strong> " . implode(', ', $vaccine_fields) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Structure check error: " . $e->getMessage() . "<br>";
}

// Test sample query
echo "<h3>4. Sample Query Test:</h3>";
if (isset($_SESSION['health_center'])) {
    try {
        $health_center = $_SESSION['health_center'];
        
        if ($health_center === 'CITY HEALTH OFFICE') {
            $test_sql = "SELECT * FROM nip_table WHERE deleted = 0 LIMIT 5";
            $stmt = $conn->prepare($test_sql);
        } else {
            $test_sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ? LIMIT 5";
            $stmt = $conn->prepare($test_sql);
            $stmt->bind_param("s", $health_center);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        echo "✅ Query executed successfully<br>";
        echo "Records found: " . $result->num_rows . "<br>";
        
        if ($result->num_rows > 0) {
            echo "<h4>Sample Record:</h4>";
            $sample = $result->fetch_assoc();
            echo "Name: " . htmlspecialchars($sample['NameOfChild'] ?? 'N/A') . " " . htmlspecialchars($sample['LastNameOfChild'] ?? $sample['lastnameOfChild'] ?? 'N/A') . "<br>";
            echo "BCG: " . htmlspecialchars($sample['BCG'] ?? 'N/A') . "<br>";
            echo "Registration: " . htmlspecialchars($sample['DateOfRegistration'] ?? 'N/A') . "<br>";
            echo "Barangay: " . htmlspecialchars($sample['Barangay'] ?? 'N/A') . "<br>";
        }
        
        $stmt->close();
    } catch (Exception $e) {
        echo "❌ Query test error: " . $e->getMessage() . "<br>";
    }
}

// Test filtering logic
echo "<h3>5. Filtering Logic Test:</h3>";
$vaccines = [
    'BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3', 'OPV1', 'OPV2', 'OPV3',
    'HEPAatBirth', 'HEPAB1', 'HEPAB2', 'HEPAB3', 'HEPAB4', 
    'AMV19monthstobelow12months', 'MMR12MOSTO15MOS', 'FIC', 'CIC',
    'AMV2_16MOSTO5YRSOLD', 'TT1', 'TT2', 'TT3', 'TT4', 'TT5'
];

echo "Vaccine fields to check: " . implode(', ', $vaccines) . "<br>";

// Test with sample data
$sample_record = [
    'BCG' => 'YES',
    'PENTAHIB1' => 'NO',
    'PENTAHIB2' => '',
    'TT1' => 'Given'
];

$hasValidVaccination = false;
foreach ($vaccines as $vaccine) {
    if (isset($sample_record[$vaccine])) {
        $value = trim(strtoupper($sample_record[$vaccine]));
        if (!empty($value) && $value !== 'NO' && $value !== 'NULL') {
            $hasValidVaccination = true;
            echo "✅ Valid vaccination found: $vaccine = $value<br>";
            break;
        }
    }
}

echo $hasValidVaccination ? "✅ Sample record would be shown" : "❌ Sample record would be hidden";

echo "<br><br><a href='sss.php'>Go to SSS.php</a> | <a href='sss.php?debug=1'>SSS.php with Debug</a>";
?>
