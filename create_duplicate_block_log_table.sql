-- Create table to log blocked duplicate registration attempts
CREATE TABLE IF NOT EXISTS duplicate_block_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    child_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    dob DATE NOT NULL,
    mother_name <PERSON><PERSON>HA<PERSON>(255),
    blocked_reason TEXT NOT NULL,
    blocked_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    blocked_by VARCHAR(255),
    health_center VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_blocked_date (blocked_date),
    INDEX idx_child_name (child_name, last_name),
    INDEX idx_dob (dob),
    INDEX idx_health_center (health_center)
);

-- Add deleted column to nip_table if it doesn't exist
ALTER TABLE nip_table 
ADD COLUMN IF NOT EXISTS deleted TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS deleted_reason TEXT,
ADD COLUMN IF NOT EXISTS deleted_date TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS deleted_by VARCHAR(255);

-- Create index for deleted column
CREATE INDEX IF NOT EXISTS idx_deleted ON nip_table(deleted);

-- Create user action logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_action_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    table_name VARCHAR(100),
    action_type VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    performed_by VARCHAR(255),
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    reason TEXT,
    INDEX idx_performed_at (performed_at),
    INDEX idx_action_type (action_type),
    INDEX idx_table_name (table_name),
    INDEX idx_user_id (user_id)
);
