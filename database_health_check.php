<?php
// Database Health Check and Repair Tool
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Database Health Check & Repair Tool</h2>";

// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'national_immunization_program';

$issues = [];
$fixes = [];

// Test 1: Basic Connection
echo "<h3>1. 🔌 Testing Database Connection</h3>";
try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    echo "<p><strong>Server Info:</strong> " . $conn->server_info . "</p>";
    echo "<p><strong>Host Info:</strong> " . $conn->host_info . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    
    // Try to create database if it doesn't exist
    echo "<h4>🛠️ Attempting to create database...</h4>";
    try {
        $conn_no_db = new mysqli($host, $username, $password);
        if ($conn_no_db->connect_error) {
            throw new Exception("Cannot connect to MySQL server: " . $conn_no_db->connect_error);
        }
        
        $sql = "CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8 COLLATE utf8_general_ci";
        if ($conn_no_db->query($sql)) {
            echo "<p style='color: green;'>✅ Database '$database' created successfully</p>";
            $conn = new mysqli($host, $username, $password, $database);
        } else {
            throw new Exception("Failed to create database: " . $conn_no_db->error);
        }
        
    } catch (Exception $e2) {
        echo "<p style='color: red;'>❌ Failed to create database: " . $e2->getMessage() . "</p>";
        exit;
    }
}

// Test 2: Check if main table exists
echo "<hr><h3>2. 📋 Checking Main Table Structure</h3>";
$table_exists = false;
try {
    $result = $conn->query("SHOW TABLES LIKE 'nip_table'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✅ nip_table exists</p>";
        $table_exists = true;
    } else {
        echo "<p style='color: red;'>❌ nip_table does not exist</p>";
        $issues[] = "Main table 'nip_table' is missing";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
    $issues[] = "Cannot check table structure: " . $e->getMessage();
}

// Test 3: Check table structure if table exists
if ($table_exists) {
    echo "<h4>📊 Table Structure Analysis</h4>";
    try {
        $result = $conn->query("DESCRIBE nip_table");
        $columns = [];
        $required_columns = [
            'id', 'DateOfRegistration', 'DateOfBirth', 'FamilySerialNumber',
            'NameOfChild', 'LastNameOfChild', 'middlename_of_child', 'Sex',
            'NameofMother', 'Barangay', 'Address', 'PhoneNumber'
        ];
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $columns[] = $row['Field'];
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for missing required columns
        $missing_columns = array_diff($required_columns, $columns);
        if (!empty($missing_columns)) {
            echo "<p style='color: orange;'>⚠️ Missing columns: " . implode(', ', $missing_columns) . "</p>";
            $issues[] = "Missing required columns: " . implode(', ', $missing_columns);
        } else {
            echo "<p style='color: green;'>✅ All required columns present</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error analyzing table structure: " . $e->getMessage() . "</p>";
        $issues[] = "Cannot analyze table structure: " . $e->getMessage();
    }
}

// Test 4: Check for duplicate checking support tables
echo "<hr><h3>3. 🔍 Checking Support Tables</h3>";
$support_tables = ['duplicate_block_log', 'user_action_logs'];

foreach ($support_tables as $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "<p style='color: green;'>✅ $table exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ $table does not exist (will be created automatically)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking $table: " . $e->getMessage() . "</p>";
    }
}

// Test 5: Test basic operations
echo "<hr><h3>4. 🧪 Testing Basic Database Operations</h3>";

if ($table_exists) {
    // Test SELECT
    try {
        $result = $conn->query("SELECT COUNT(*) as total FROM nip_table LIMIT 1");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p style='color: green;'>✅ SELECT operation works - Total records: {$row['total']}</p>";
        } else {
            throw new Exception($conn->error);
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ SELECT operation failed: " . $e->getMessage() . "</p>";
        $issues[] = "SELECT operation failed: " . $e->getMessage();
    }
    
    // Test INSERT (dry run)
    try {
        $test_sql = "INSERT INTO nip_table (DateOfRegistration, NameOfChild, LastNameOfChild, DateOfBirth) VALUES ('2024-01-01', 'TEST', 'CHILD', '2020-01-01')";
        $stmt = $conn->prepare($test_sql);
        if ($stmt) {
            echo "<p style='color: green;'>✅ INSERT statement preparation works</p>";
            $stmt->close();
        } else {
            throw new Exception($conn->error);
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT preparation failed: " . $e->getMessage() . "</p>";
        $issues[] = "INSERT operation failed: " . $e->getMessage();
    }
}

// Test 6: Check PHP extensions
echo "<hr><h3>5. 🔧 Checking PHP Extensions</h3>";
$required_extensions = ['mysqli', 'json', 'session'];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✅ $ext extension loaded</p>";
    } else {
        echo "<p style='color: red;'>❌ $ext extension not loaded</p>";
        $issues[] = "$ext PHP extension is missing";
    }
}

// Test 7: Check file permissions
echo "<hr><h3>6. 📁 Checking File Permissions</h3>";
$files_to_check = ['db.php', 'nip.php', 'config.php'];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p style='color: green;'>✅ $file is readable</p>";
        } else {
            echo "<p style='color: red;'>❌ $file is not readable</p>";
            $issues[] = "$file is not readable";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ $file does not exist</p>";
    }
}

// Test 8: Memory and execution limits
echo "<hr><h3>7. ⚙️ Checking PHP Configuration</h3>";
echo "<p><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds</p>";
echo "<p><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</p>";

// Summary and Fixes
echo "<hr><h3>8. 📋 Summary and Recommended Fixes</h3>";

if (empty($issues)) {
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
    echo "<h4 style='color: #2e7d32;'>✅ All Checks Passed!</h4>";
    echo "<p>Your database system appears to be working correctly.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3e0; padding: 15px; border-radius: 5px; border-left: 4px solid #ff9800;'>";
    echo "<h4 style='color: #ef6c00;'>⚠️ Issues Found</h4>";
    echo "<ol>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ol>";
    echo "</div>";
    
    // Provide automatic fixes
    echo "<h4>🛠️ Automatic Fixes</h4>";
    
    if (in_array("Main table 'nip_table' is missing", $issues)) {
        echo "<p><a href='setup_duplicate_prevention.php' class='btn blue'>Create Missing Tables</a></p>";
    }
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3;'>";
    echo "<h4>💡 Manual Fix Recommendations</h4>";
    echo "<ul>";
    echo "<li><strong>Database Connection Issues:</strong> Check MySQL service is running</li>";
    echo "<li><strong>Permission Issues:</strong> Ensure web server has read/write access to files</li>";
    echo "<li><strong>Missing Extensions:</strong> Install required PHP extensions</li>";
    echo "<li><strong>Table Issues:</strong> Run the setup script to create missing tables</li>";
    echo "</ul>";
    echo "</div>";
}

// Quick Actions
echo "<hr><h3>9. 🚀 Quick Actions</h3>";
echo "<p>";
echo "<a href='setup_duplicate_prevention.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🛠️ Setup System</a>";
echo "<a href='test_duplicate_prevention.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Test System</a>";
echo "<a href='nip.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='duplicate_checker.php' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔍 Duplicate Checker</a>";
echo "</p>";

// Close connection
if (isset($conn)) {
    $conn->close();
}

echo "<hr>";
echo "<p><em>Health check completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th {
    background-color: #2196f3;
    color: white;
    font-weight: bold;
    text-align: left;
    padding: 8px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3, h4 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}

.btn {
    padding: 10px 15px;
    border-radius: 5px;
    text-decoration: none;
    color: white;
    font-weight: bold;
}

.btn.blue { background-color: #2196f3; }
.btn:hover { opacity: 0.8; }
</style>
