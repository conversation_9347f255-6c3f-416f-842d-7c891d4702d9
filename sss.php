<?php
session_start();
include 'db.php'; // Include your database connection

// Check if user is logged in, if not redirect to login
if (!isset($_SESSION['health_center'])) {
    header("Location: Main/index.php");
    exit();
}

$health_center = $_SESSION['health_center'];

// Get filter parameters
$from_date = isset($_GET['from']) ? $_GET['from'] : date('Y-m-01');
$to_date = isset($_GET['to']) ? $_GET['to'] : date('Y-m-d');
$show_all = isset($_GET['show_all']) ? $_GET['show_all'] : false;

// Build the base query
if ($health_center === 'CITY HEALTH OFFICE') {
    $base_sql = "SELECT * FROM nip_table WHERE deleted = 0 AND DateOfRegistration BETWEEN ? AND ?";
    $params = [$from_date, $to_date];
    $param_types = "ss";
} else {
    $base_sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ? AND DateOfRegistration BETWEEN ? AND ?";
    $params = [$health_center, $from_date, $to_date];
    $param_types = "sss";
}

// Add BCG to TT5 filter condition if not showing all records
if (!$show_all) {
    $bcg_to_tt5_conditions = [];
    $vaccines = [
        'BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3', 'OPV1', 'OPV2', 'OPV3',
        'HEPAatBirth', 'HEPAB1', 'MMR12MOSTO15MOS', 'FIC', 'CIC',
        'AMV2_16MOSTO5YRSOLD', 'TD1', 'TT2', 'TT3', 'TT4', 'TT5'
    ];
    
    foreach ($vaccines as $vaccine) {
        $bcg_to_tt5_conditions[] = "($vaccine IS NOT NULL AND $vaccine != '' AND UPPER($vaccine) != 'NO')";
    }
    
    // Add condition to show only records with at least one valid vaccination
    $base_sql .= " AND (" . implode(" OR ", $bcg_to_tt5_conditions) . ")";
}

$base_sql .= " ORDER BY DateOfRegistration DESC";

try {
    $stmt = $conn->prepare($base_sql);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $records = [];
    while ($row = $result->fetch_assoc()) {
        // Additional filtering: Check if record has valid BCG to TT5 data
        $hasValidVaccination = false;
        $vaccines = [
            'BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3', 'OPV1', 'OPV2', 'OPV3',
            'HEPAatBirth', 'HEPAB1', 'MMR12MOSTO15MOS', 'FIC', 'CIC',
            'AMV2_16MOSTO5YRSOLD', 'TD1', 'TT2', 'TT3', 'TT4', 'TT5'
        ];
        
        foreach ($vaccines as $vaccine) {
            $value = trim(strtoupper($row[$vaccine] ?? ''));
            if (!empty($value) && $value !== 'NO') {
                $hasValidVaccination = true;
                break;
            }
        }
        
        // Only include records with valid vaccination data (unless showing all)
        if ($show_all || $hasValidVaccination) {
            $records[] = $row;
        }
    }
    
    $stmt->close();
} catch (Exception $e) {
    error_log("Database error in sss.php: " . $e->getMessage());
    $records = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaccination Records - National Immunization Program</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        .header-section {
            background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .filter-section {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .record-card {
            margin-bottom: 15px;
            border-left: 4px solid #1e88e5;
        }
        .vaccination-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .vaccine-item {
            background: #e8f5e8;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .vaccine-item.given {
            background: #c8e6c9;
            color: #2e7d32;
        }
        .vaccine-item.not-given {
            background: #ffcdd2;
            color: #c62828;
        }
        .stats-card {
            text-align: center;
            padding: 20px;
        }
        .no-records {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .action-buttons {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header-section">
        <div class="container">
            <div class="row">
                <div class="col s12">
                    <h4><i class="material-icons left">vaccines</i>Vaccination Records</h4>
                    <p>Health Center: <strong><?php echo htmlspecialchars($health_center); ?></strong></p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filter Section -->
        <div class="filter-section">
            <form method="GET" action="">
                <div class="row">
                    <div class="col s12 m3">
                        <label for="from">From Date:</label>
                        <input type="date" id="from" name="from" value="<?php echo $from_date; ?>" class="datepicker">
                    </div>
                    <div class="col s12 m3">
                        <label for="to">To Date:</label>
                        <input type="date" id="to" name="to" value="<?php echo $to_date; ?>" class="datepicker">
                    </div>
                    <div class="col s12 m3">
                        <label>
                            <input type="checkbox" name="show_all" value="1" <?php echo $show_all ? 'checked' : ''; ?>>
                            <span>Show All Records (including NO values)</span>
                        </label>
                    </div>
                    <div class="col s12 m3">
                        <button type="submit" class="btn blue waves-effect waves-light">
                            <i class="material-icons left">search</i>Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="Main/index.php" class="btn grey waves-effect waves-light">
                <i class="material-icons left">arrow_back</i>Back to Main
            </a>
            <a href="?<?php echo http_build_query(array_merge($_GET, ['show_all' => !$show_all])); ?>" 
               class="btn orange waves-effect waves-light">
                <i class="material-icons left">visibility</i>
                <?php echo $show_all ? 'Hide NO Values' : 'Show All Records'; ?>
            </a>
        </div>

        <!-- Statistics -->
        <div class="row">
            <div class="col s12 m4">
                <div class="card stats-card blue lighten-4">
                    <h5><?php echo count($records); ?></h5>
                    <p>Total Records</p>
                </div>
            </div>
            <div class="col s12 m4">
                <div class="card stats-card green lighten-4">
                    <h5><?php echo count(array_filter($records, function($r) { return !empty($r['IMMUNIZATIONSTATUS']); })); ?></h5>
                    <p>With Immunization Status</p>
                </div>
            </div>
            <div class="col s12 m4">
                <div class="card stats-card orange lighten-4">
                    <h5><?php echo date('M d, Y', strtotime($from_date)) . ' - ' . date('M d, Y', strtotime($to_date)); ?></h5>
                    <p>Date Range</p>
                </div>
            </div>
        </div>

        <!-- Records Display -->
        <?php if (empty($records)): ?>
            <div class="no-records">
                <i class="material-icons large grey-text">inbox</i>
                <h5>No Records Found</h5>
                <p>No vaccination records found for the selected criteria.</p>
                <?php if (!$show_all): ?>
                    <p><em>Try enabling "Show All Records" to include records with NO values.</em></p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <?php foreach ($records as $record): ?>
                <div class="card record-card">
                    <div class="card-content">
                        <div class="row">
                            <div class="col s12 m6">
                                <h6><strong><?php echo htmlspecialchars($record['NameOfChild'] . ' ' . $record['lastnameOfChild']); ?></strong></h6>
                                <p><strong>Registration:</strong> <?php echo date('M d, Y', strtotime($record['DateOfRegistration'])); ?></p>
                                <p><strong>Birth Date:</strong> <?php echo !empty($record['DateOfBirth']) ? date('M d, Y', strtotime($record['DateOfBirth'])) : 'Not specified'; ?></p>
                                <p><strong>Sex:</strong> <?php echo htmlspecialchars($record['Sex']); ?></p>
                                <p><strong>Address:</strong> <?php echo htmlspecialchars($record['Address']); ?></p>
                            </div>
                            <div class="col s12 m6">
                                <p><strong>Mother:</strong> <?php echo htmlspecialchars($record['NameofMother']); ?></p>
                                <p><strong>Barangay:</strong> <?php echo htmlspecialchars($record['Barangay']); ?></p>
                                <p><strong>Status:</strong> 
                                    <span class="chip <?php echo !empty($record['IMMUNIZATIONSTATUS']) ? 'green white-text' : 'grey white-text'; ?>">
                                        <?php echo !empty($record['IMMUNIZATIONSTATUS']) ? htmlspecialchars($record['IMMUNIZATIONSTATUS']) : 'Not Set'; ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Vaccination Status Grid -->
                        <div class="vaccination-grid">
                            <?php
                            $vaccines = [
                                'BCG' => 'BCG',
                                'PENTAHIB1' => 'Penta-HIB 1',
                                'PENTAHIB2' => 'Penta-HIB 2', 
                                'PENTAHIB3' => 'Penta-HIB 3',
                                'OPV1' => 'OPV 1',
                                'OPV2' => 'OPV 2',
                                'OPV3' => 'OPV 3',
                                'HEPAatBirth' => 'HEPA at Birth',
                                'HEPAB1' => 'HEPA B1',
                                'MMR12MOSTO15MOS' => 'MMR',
                                'FIC' => 'FIC',
                                'CIC' => 'CIC',
                                'TD1' => 'TD1',
                                'TT2' => 'TT2',
                                'TT3' => 'TT3',
                                'TT4' => 'TT4',
                                'TT5' => 'TT5'
                            ];
                            
                            foreach ($vaccines as $field => $label) {
                                $value = trim(strtoupper($record[$field] ?? ''));
                                $isGiven = !empty($value) && $value !== 'NO';
                                $class = $isGiven ? 'given' : 'not-given';
                                $status = $isGiven ? 'YES' : 'NO';
                                
                                echo "<div class='vaccine-item $class'>";
                                echo "<strong>$label:</strong> $status";
                                echo "</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Materialize components
            M.AutoInit();
            
            // Auto-redirect if no session (fallback)
            <?php if (!isset($_SESSION['health_center'])): ?>
                setTimeout(function() {
                    window.location.href = 'Main/index.php';
                }, 3000);
            <?php endif; ?>
        });
    </script>
</body>
</html>
