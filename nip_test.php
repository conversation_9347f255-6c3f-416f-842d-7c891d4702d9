<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<?php
 
try {
    $server = '*************';
    $database = 'PCHIMS';
    $user = 'sa';
    $pass = 'angelo';
    $mssqldriver = '{SQL Server Native Client 11.0}'; 
    $db = new PDO("odbc:Driver=$mssqldriver;Server=$server;Database=$database;charset=UTF-8", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
   // echo 'NIP';
} catch (PDOException $e) {
    echo "<div class='alert alert-warning' role='alert'>Failed to connect to server: " . htmlspecialchars($e->getMessage()) . "</div>";
    exit;
}
 
 
?>
<?php 

 
    
// include_once "../Main/Menu.php"; 

$ekonsultaRegIDNo = $_POST['ekonsultaRegIDNo'] ?? null;
$LName            = $_POST['LName'] ?? null;
$FName            = $_POST['FName'] ?? null;
$MName            = $_POST['MName'] ?? null;
$BirthDate        = $_POST['BirthDate'] ?? null;
$PhoneNumber        = $_POST['PhoneNumber'] ?? null;

$stmt=$db->prepare("EXEC [SP_UHC_eKonsulta_Reg_Find_PHP] ?,?,?,?,?,?");

$data = array
    (
        $ekonsultaRegIDNo,
        $LName,
        $FName,
        $MName,
        $BirthDate,
        $PhoneNumber
    );

 
 
?>

<!DOCTYPE html>
<html lang="en">
<head>
   
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>


   
 
  
    <title>National Immunization Program</title>
   <?php include 'style.php'; ?>
   <style>
 
       .autocomplete-content {
            list-style-type: none;
            padding: 0;
            margin: 0;
            border: 1px solid #ccc;
            max-height: 150px;
            overflow-y: auto;
            position: absolute;
            background-color: white;
            z-index: 1000;
        }

        .autocomplete-content li {
            padding: 8px 12px;
            cursor: pointer;
        }

        .autocomplete-content li:hover {
            background-color: #f0f0f0;
        }
    label {
      font-weight:500 !important;
      font-size:17.7px !important;
    }
    
    .tab a.active {
    background-color: transparent;
    color: #006064   !important;
    font-weight: bold;
    
    background-color:rgb(224, 254, 255)  !important; 
}
.tabs .indicator {
    position: absolute;
    bottom: 0;
    height: 2px;
    background-color: #006064 ;
    will-change: left, right;
}
  .tab a {
    color:black !important;
  }
    /* label color */
   .input-field label {
     color: #000 !important;
   }
   /* label focus color */
   .input-field input[type=text]:focus + label {
     color: #000;
   }
   /* label underline focus color */
   .input-field input[type=text]:focus {
     border-bottom: 1px solid #039be5  !important;
     box-shadow: 0 1px 0 0 #039be5 !important;
   }
   /* valid color */
   .input-field input[type=text]:focus {
    border-bottom: 1px solid #039be5  !important;
     box-shadow: 0 1px 0 0 #039be5 !important;
   }
   input {
    text-transform:uppercase !important;
   }
   /* invalid color */
   .input-field input[type=text]:focus {
    border-bottom: 1px solid #039be5  !important;
    box-shadow: 0 1px 0 0 #039be5 !important;
   }
   .input-field input[type=date]:focus {
    border-bottom: 1px solid #039be5  !important;
    box-shadow: 0 1px 0 0 #039be5 !important;
   }
   .input-field input[type=number]:focus {
    border-bottom: 1px solid #039be5  !important;
    box-shadow: 0 1px 0 0 #039be5 !important;
   }
   
   /* icon prefix focus color */
   .input-field .prefix.active {
     color: #000;
   }
   [type="radio"]:checked + span:after,
[type="radio"].with-gap:checked + span:before,
[type="radio"].with-gap:checked + span:after {
  border: 2px solid #212121  ;
}
[type="radio"]:checked + span:after,
[type="radio"].with-gap:checked + span:after {
  background-color: #212121  ;
}
   </style>
</head>
<body  class="grey lighten-5">
    <?php include 'nav.php'; ?>


   
   
    

      <div id="tab1" class=" ">
      
      <div class="row ">
        


        
      <div class="col s12 m10 offset-m1 "  >
          
     

 
          
          
          <h5 class="blue-grey darken-3 white-text" style="padding:20px; font-size:15.5px;font-weight:normal !important;"><i class="material-icons left">note_add</i>Target Client List for Children Under 1 Year Old</h5>
        <form id="searchForm" class="col s12 z-depth-1 white" style="border-radius: 5px;">
                <div class="row">  <p class=" blue-grey lighten-5" style="padding:0.6em;font-size:17px;">Universal Healthcare</a></p>
                <div class="  input-field col s12 m3"  >
              
              <input type="number" class="  " id="ekonsultaRegIDNo" name="ekonsultaRegIDNo" style="color:black; "  placeholder="" autofocus> 
              <label for="" style="font-size:16.2px !important;">Scan e-Konsulta Registration No</label>
            </div>
            <div class=" input-field col s12 m4 ">
                <button type="submit" class="btn   start btn-small blue lighten-1 " style="font-size: 13.7px;border-radius: 2px;font-weight: 500;font-size: 12.4px; ">Search Registration Number</button>
           <p id="result"></p>
            </div>
            <div class="  col s12 m12 ">
                <p style="font-size:13.9px;">This registration number is issued by <a class="cyan-text" target="_blank" href="http://192.168.88.11/fhsis/">Universal Health Care</a>.</p>
            </div>
           
            
        </div>       
        </form>
     
        <br><br><br>
   <br><br><br><br><br>
   <br><br> <br>
        <form action="" class="name white" method="post" class="col s12  " >
            
         
          <?php include 'db.php'; ?>
          
          <div class="z-depth-1 " style="border-radius: 5px;">
            <div class="row  " style="padding:20.1px;"> 
          
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">Information of Child</h5>
        
        
          
            
            <div class="input-field col s12 m3  ">
              
            <input type="text" class="teal-text" placeholder="" id="ekonsultaRegIDNo_" name="ekonsultaRegIDNo_" readonly>
              <label for="ekonsultaRegIDNo_">e-Konsulta Registration No</label>
            </div>
            <div class="input-field col s12 m3  ">
            <input class="teal-text" type="text" placeholder="" id="UHCRead" name="UHCRead" readonly>
              <label for="UHCRead">UHCID</label>
            </div>
            <div class="input-field col s12 m3  ">
              <input type="date" class="teal-text"   name="DateOfBirth" id="DateOfBirth"      readonly >
              <label for="DateOfBirth">Date of Birth</label>
            </div>
            <div class="input-field col s12 m2 ">
              <input  id="AgeofChild" class="teal-text" placeholder="" class=""  name="AgeofChild"   type="text"  readonly >
              <label for="AgeofChild">Age of Child</label>
            </div>
            
            


            <div class="input-field col s12 m3">
              <input   id="LastNameOfChild" class="teal-text" placeholder=""  name="LastNameOfChild"  type="text"  readonly>
              <label for="LastNameOfChild">Last Name of Child</label>
            </div>
            <div class="input-field col s12 m3">
              <input   id="NameOfChild" class="teal-text" placeholder=""  name="NameOfChild"  type="text" readonly >
              <label for="NameOfChild">First Name of Child</label>
            </div>
            <div class="input-field col s12 m3">
              <input   id="middlename_of_child" class="teal-text" placeholder=""  name="middlename_of_child"  type="text" readonly >
              <label for="middlename_of_child">Middle Name of Child</label>
            </div>
             
            <div class="input-field col s12 m2">
              <input placeholder="" class="teal-text" type="text" name="Sex" id="Sex" readonly>
               
               
              <label for="Sex">Sex</label>
            </div>
            </div>
           
            <div class="row  " style="padding:20.1px;"> 
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">Information of Mother</h5>

            <div class="input-field col s12 m4">
              <input placeholder=""  id="NameofMother" name="NameofMother" type="text"  >
              <label for="NameofMother">Name of Mother</label>
            </div>
            <div class="input-field col s12 m2">
              <input  id="BirthdateofMother" name="BirthdateofMother" placeholder="" type="date"  >
              <label for="BirthdateofMother">Birthdate of Mother</label>
            </div>
            <div class="input-field col s12 m2">
              <input  id="AgeofMother" name="AgeeofMother" placeholder="" type="text"  readonly>
              <label for="AgeofMother">Age of Mother</label>
            </div>
            <?php if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') { ?>
              <div class="input-field col s12 m4">
              <select class="browser" id="Barangay" name="Barangay" placeholder=""  >
            <option value=""></option>
                <?php
                $sql = "SELECT * FROM facility_list ";
                $result = $conn->query($sql);
                    while ($row = $result->fetch_assoc()) {
                        echo '<option value="'.$row["facility_name"].'">'.$row["facility_name"].'</option>';
                    } 
                ?>  
        </select>
        <label for="Barangay">Barangay</label>
                  </div>
              <?php } 
                 else { ?>
            <div class="input-field col s12 m4">
              <input  id="Barangay" name="Barangay" value="<?php echo $_SESSION['health_center']; ?>" type="text" readonly>
              <label for="Barangay">Barangay</label>
            </div>
            <?php } ?>
            <div class="input-field col s12 m3">
              <input  id="HouseNo" name="HouseNo"   type="text" placeholder="" >
              <label for="HouseNo">House No.</label>
            </div>
            <div class="input-field col s12 m2">
              <input list="address" id="PurokStreetSitio" oninput="myFunction()"  name="PurokStreetSitio" type="text" placeholder="" >
              <label for="PurokStreetSitio">Purok/Sitio</label>
              <datalist id="address">
              <?php 
 if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {

   $sql = "SELECT street_Name FROM street";
 }
 else {
   $sql = "SELECT street_Name FROM street WHERE healthCenter = '".$_SESSION['health_center']."'";

 }
$stmt = $conn->prepare($sql);
 
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows > 0) {
while ($row = $result->fetch_assoc()) {
   echo '<option value="'.$row['street_Name'].'">';
}
}

?>
</datalist>
            </div>
            
            <div class="input-field col s12 m7">
              <input placeholder="" id="Address" name="Address" type="text"  autocomplete="off"  readonly>
              <label for="Address">Address</label>
              
            </div>
        </div>
      </div>



        </div>
         

        
        <div class="col s12 m10 offset-m1"  >
          <div class="z-depth-1 white">
          <div class="row  " style="padding:20.1px;"> 
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">Post Partum Information</h5>
            
            <div class="input-field col s12 m12 z-depth-1">
              
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">Place of Delivery</h5>
                <p>
                  <label >
                    <input class="with-gap" name="PlaceofDelivery" value="HOSPITAL" type="radio"   />
                    <span>HOSPITAL</span>
                  </label>
                
                  <label>
                    <input class="with-gap" name="PlaceofDelivery" value="BIRTHING-HOME" type="radio" />
                    <span>BIRTHING-HOME</span>
                  </label>
              
                  <label>
                    <input class="with-gap" name="PlaceofDelivery"  value="HOME" type="radio"  />
                    <span>HOME</span>
                  </label>
               
                  <label>
                    <input class="with-gap" name="PlaceofDelivery" value="OTHER" type="radio"   />
                    <span>OTHER</span>
                  </label>
                </p>
      
             
            </div>
            <div class="input-field col s12 m12">
              <input  id="NameofFacility" placeholder="" name="NameofFacility"   autocomplete="off" type="text"  >
              <label for="NameofFacility">Name of Facility</label>
              <ul id="hospitalSuggestions" class="autocomplete-content  blue lighten-5 teal-text"></ul>
            </div>
            <div class="input-field col s12 z-depth-1">
             
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">Attendant</h5>
              <p>
                <label >
                  <input class="with-gap" name="Attendant" value="DOCTOR" type="radio"   />
                  <span>DOCTOR</span>
                </label>
              
                <label>
                  <input class="with-gap" name="Attendant" value="NURSE" type="radio" />
                  <span>NURSE</span>
                </label>
            
                <label>
                  <input class="with-gap" name="Attendant" value="MIDWIFE"  type="radio"  />
                  <span>MIDWIFE</span>
                </label>
            
                <label>
                  <input class="with-gap" name="Attendant" value="HILOT" type="radio"   />
                  <span>HILOT</span>
                </label>
              
                <label>
                  <input class="with-gap" name="Attendant" value="OTHER" type="radio"   />
                  <span>OTHER</span>
                </label>
              </p>
            </div>
            <div class="input-field col s12 z-depth-1">
            
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">Type of Delivery</h5>
              <p> 
                <label>
                  <input class="with-gap" name="TypeofDelivery" value="NSD" type="radio"   />
                  <span>NSD</span>
                </label>&nbsp&nbsp
                <label>
                  <input class="with-gap" name="TypeofDelivery" value="CS" type="radio"   />
                  <span>CS</span>
                </label>&nbsp&nbsp
                <label>
                  <input class="with-gap" name="TypeofDelivery" value="OTHER" type="radio"   />
                  <span>OTHER</span>
                </label>
              </p>
            </div>
            <div class="input-field col s12 m6">
              <input placeholder="" id="BirthWeightInGrams" name="BirthWeightInGrams" type="text"  oninput="classifyBirthWeight()" >
              <label for="BirthWeightInGrams">Birth Weight(In Grams)</label>
            </div>
            <div class="input-field col s12 m6">
            <input placeholder="" id="BirthWeightClassification" name="BirthWeightClassification" type="text"  readonly>
             
              <label for="BirthWeightClassification">BirthWeight Classification</label>
               
            </div>
            <div class=" col s11 m5 alert green lighten-5 light-green-text" style="padding:5px;margin-left: 12px;">
              <small class="" >&nbsp&nbsp Note:Birthweight <2000 Grams Should be Given HEPA B4</small>
                
            </div>
          </div>

          
      



          <div class="row  " style="padding:20.1px;"> 
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">NB Screening</h5>
            <div class="input-field col s12 z-depth-1">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">Was the Child Referred for Newborn Screening?</h5>
              <p> 
                <label>
                  <input class="with-gap" name="WastheChildReferredforNewbornScreening" value="YES" type="radio"   />
                  <span>YES</span>
                </label>&nbsp 
                <label>
                  <input class="with-gap" name="WastheChildReferredforNewbornScreening" value="NO" type="radio"   />
                  <span>NO</span>
                </label> 
                 
              </p>
             
            </div>
            <div class="input-field col s12 m12">
              <input  id="DateReferredforNewbornScreening"  name="DateReferredforNewbornScreening"    type="date"  >
              <label for="DateReferredforNewbornScreening">Date Referred for Newborn Screening</label>
            </div>
            <div class="input-field col s12 m12">
              <input  id="DateofConsultationNewbornScreening" name="DateofConsultationNewbornScreening" type="date"  >
              <label for="DateofConsultationNewbornScreening">Date of Consultation(Newborn Screening)</label>
            </div>
            
             
             
            
          </div>
          <div class="row  " style="padding:20.1px;"> 
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">Child Protected at Birth?</h5>
            <div class="input-field col s12 m12 z-depth-1">
              
         
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT Status of Mother</h5>
<p> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT1" type="radio"/>
                    <span>TT1</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT2 LESS THAN 1 MONTH" type="radio"/>
                    <span>TT2 LESS THAN 1 MONTH</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT2 MORE THAN 1 MONTH" type="radio"/>
                    <span>TT2 MORE THAN 1 MONTH</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT3" type="radio"/>
                    <span>TT3</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT4" type="radio"/>
                    <span>TT4</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT5" type="radio"/>
                    <span>TT5</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" value="TT5 PLUS" type="radio"/>
                    <span>TT5 PLUS</span>
                  </label> 
                </p>
           

 
            </div>
            <div class="input-field col s12 m5">
              <input  id="Dateassessed"  name="Dateassessed"  type="date"  >
              <label for="Dateassessed">Date Assessed</label>
            </div>
            <div class="input-field col s12 m7">
              <input  id="WastheChildProtectedatBirth" placeholder=""  name="WastheChildProtectedatBirth"  type="text"  >
              <label for="WastheChildProtectedatBirth">Was the Child Protected at Birth?</label>
            </div>
            <div class="input-field col s12 m5">
              <input  id="DateofConsultationCPAB"  name="DateofConsultationCPAB"    type="date"  >
              <label for="DateofConsultationCPAB">Date of Consultation(CPAB)</label>
            </div>
         
             
             
            
          </div>
       
        </div>
      </div>
      </div>


      </div>
     





<br>
      <div id="tab2"  >
        <div class="row ">
        
        <div class="col s12 m10 offset-m1"  >
            
             
              <div class="z-depth-1 white">
              <div class="row  " style="padding:20.1px;"> 
                
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">BCG Antigen</h5>
              <div class="input-field col s12 m12 z-depth-1">
                <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">BCG</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="BCG" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="BCG" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                
              </div>
              
              <div class="input-field col s12 m6">
                <input type="date" name="DateBCGwasgiven" id="DateBCGwasgiven" class="datepicker"  >
                <label for="DateBCGwasgiven">Date BCG was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationBCG" name="DateofConsultationBCG" type="date"  >
                <label for="DateofConsultationBCG">Date of Consultation(BCG)</label>
              </div>





              <div class=" col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">PENTA-HIB Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12 z-depth-1">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">PENTA-HIB1</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="PENTAHIB1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PENTAHIB1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
              </div>
              <div class="input-field col s12 m6">
                <input  placeholder="" name="DatePenta1wasgiven" id="DatePenta1wasgiven" type="date"  >
                <label for="DatePenta1wasgiven">Date PENTA-HIB1 was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationPENTAHIB1" name="DateofConsultationPENTAHIB1" type="date"  >
                <label for="DateofConsultationPENTAHIB1">Date of Consultation (PENTA-HIB1)</label>
              </div>
              


              <div class="input-field col s12 m12 z-depth-1">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">PENTA-HIB2</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="PENTAHIB2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PENTAHIB2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                
              </div>
              <div class="input-field col s12 m6">
                <input   id="DatePentahib2wasgiven" name="DatePentahib2wasgiven" type="date"  >
                <label for="DatePentahib2wasgiven">Date Penta-HIB2 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input    id="DateofConsultationPENTAHIB2" name="DateofConsultationPENTAHIB2" type="date"  >
                <label for="DateofConsultationPENTAHIB2">Date of Consultation (PENTA-HIB2)</label>
              </div>

              <div class="input-field col s12 m12 z-depth-1"> 
                <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">PENTA-HIB3</h5>
                
                <p> 
                  <label>
                    <input class="with-gap" name="PENTAHIB3" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PENTAHIB3" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s12 m6">
                <input    id="DatePentahib3wasgiven" name="DatePentahib3wasgiven" type="date"  >
                <label for="DatePentahib3wasgiven">Date PENTA-HIB3 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationPENTAHIB3" name="DateofConsultationPENTAHIB3" type="date"  >
                <label for="DateofConsultationPENTAHIB3">Date of Consultation (PENTA-HIB3)</label>
              </div>





              <div class="input-field col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">OPV Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12 z-depth-1">
               
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">OPV1</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="OPV1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="OPV1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s12 m6">
                <input  placeholder="" id="DateOPV1wasgiven" name="DateOPV1wasgiven" type="date"  >
                <label for="DateOPV1wasgiven">Date OPV1 was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationOPV1" name="DateofConsultationOPV1v" type="date"  >
                <label for="DateofConsultationOPV1">Date of Consultation (OPV1)</label>
              </div>
              
              <div class="input-field col s12 m12 z-depth-1">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">OPV2</h5>
             
                <p> 
                  <label>
                    <input class="with-gap" name="OPV2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="OPV2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateOPV2wasgiven" name="DateOPV2wasgiven" type="date"  >
                <label for="DateOPV2wasgiven">Date OPV2 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input    id="DateofConsultationOPV2" name="DateofConsultationOPV2" type="date"  >
                <label for="DateofConsultationOPV2">Date of Consultation (OPV2)</label>
              </div>

              <div class="input-field col s12 m12 z-depth-1"> 
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">OPV3</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="OPV3" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="OPV3" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s12 m6">
                <input    id="dateOPV3wasgiven" name="dateOPV3wasgiven" type="date"  >
                <label for="dateOPV3wasgiven">Date OPV3 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationOPV3" name="DateofConsultationOPV3" type="date"  >
                <label for="DateofConsultationOPV3">Date of Consultation (OPV3)</label>
              </div>
        
              <div class="input-field col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">HEPATITIS Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12 ">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">HEPA at Birth</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="HEPAatBirth" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="HEPAatBirth" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
               
              </div>
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">Timing of HEPA B Vaccine</h5>
                
                <p> 
                  <label>
                    <input class="with-gap" name="TimingHEPAatBirth" value="WITHIN 24 HOURS" type="radio"/>
                    <span>WITHIN 24 HOURS</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TimingHEPAatBirth" value="MORE THAN 24 HOURS" type="radio"/>
                    <span>MORE THAN 24 HOURS</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s3 s12 m6">
                <input   id="DateHEPAatBirthwasgiven" name="DateHEPAatBirthwasgiven" type="date"  >
                <label for="DateHEPAatBirthwasgiven">Date HEPA at Birth was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationHEPAatBirth" name="DateofConsultationHEPAatBirth" type="date"  >
                <label for="DateofConsultationHEPAatBirth">Date of Consultation(HEPA at Birth)</label>
              </div>
              
              <div class="input-field col s12 m12">
              
                <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">HEPA B1</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="HEPAB1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="HEPAB1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                
              </div>
              <div class="input-field col s12 m6">
                <input   id="dateHEPA1wasgiven" name="dateHEPA1wasgiven" type="date"  >
                <label for="dateHEPA1wasgiven">Date HEPA1 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input    id="DateofConsultationHEPAB1" name="DateofConsultationHEPAB1" type="date"  >
                <label for="DateofConsultationHEPAB1">Date of Consultation (HEPA B1)</label>
              </div>

              <div class="input-field col s4" style="display:none;"> 
                <input placeholder="" id="HEPAB2" value="" name="HEPAB2" type="text"  >
                <label for="HEPAB2">HEPA B2</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input   id="dateHEPA2wasgiven" value="" name="dateHEPA2wasgiven" type="date"  >
                <label for="dateHEPA2wasgiven">Date HEPA2 was given</label>
              </div>
                <div class="input-field col s4" style="display:none;">
                <input  placeholder="" value="" id="DateofConsultationHEPAB2" name="DateofConsultationHEPAB2" type="date"  >
                <label for="DateofConsultationHEPAB2">Date of Consultation (HEPA B2)</label>
              </div>

<!---->
              <div class="input-field col s4" style="display:none;"> 
                <input placeholder="" value="" name="HEPAB3" id="HEPAB3" type="text"  >
                <label for="HEPAB3">HEPA B3</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input   id="dateHEPA3WASGIVEN" value="" name="dateHEPA3WASGIVEN" type="date">
                <label for="dateHEPA3WASGIVEN">DATE HEPA3 WAS GIVEN</label>
              </div>
                <div class="input-field col s4" style="display:none;">
                <input  placeholder="" value="" id="DateofConsultationHEPAB3" name="DateofConsultationHEPAB3" type="date"  >
                <label for="DateofConsultationHEPAB3">DATE OF CONSULTATION (HEPA B3)</label>
              </div>
<!---->
<div class="input-field col s4" style="display:none;">
                <input placeholder="" value=""  id="HEPAB4" name="HEPAB4" type="text"  >
                <label for="HEPAB4">HEPA B4</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input id="dateHEPA4WASGIVEN" value=""  name="dateHEPA4WASGIVEN" type="date"  >
                <label for="dateHEPA4WASGIVEN">DATE HEPA4 WAS GIVEN</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input  placeholder="" value=""  id="DateofConsultationHEPAB4" name="DateofConsultationHEPAB4" type="date"  >
                <label for="DateofConsultationHEPAB4">DATE OF CONSULTATION (HEPA B4)</label>
              </div>
        


          <div class="input-field col s12">
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">MCV Antigen</h5>
          </div>
          
          <div class="input-field col s12 m12">
            
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">MCV 1 (at 9months)</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="AMV19monthstobelow12months" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="AMV19monthstobelow12months" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
          </div>
          
          <div class="input-field col s12 m6">
            <input   id="DateAMV1WASGIVEN" name="DateAMV1WASGIVEN" type="date"  >
            <label for="DateAMV1WASGIVEN">DATE MCV 1 WAS GIVEN</label>
          </div>
          <div class="input-field col s12 m6">
            <input  id="DateofConsultationAMV1" name="DateofConsultationAMV1" type="date"  >
            <label for="DateofConsultationAMV1">DATE OF CONSULTATON (MCV 1)</label>
          </div>
          
          <div class="input-field col s12 m12">
          <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">MCV 2(at 12 Months)</h5>
            
            <p> 
                  <label>
                    <input class="with-gap" name="MMR12MOSTO15MOS" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="MMR12MOSTO15MOS" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            
          </div>
          <div class="input-field col s12 m6">
            <input  id="dateMMRWASGIVEN" name="dateMMRWASGIVEN" type="date"  >
            <label for="dateMMRWASGIVEN">DATE MCV2 WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m6">
            <input    id="DateofConsultationMMR" name="DateofConsultationMMR" type="date"  >
            <label for="DateofConsultationMMR">DATE OF CONSULTATION (MCV2)</label>
          </div>



          <div class="input-field col s12 m12">
          <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">FIC</h5>
            
            <p> 
                  <label>
                    <input class="with-gap" name="FIC" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="FIC" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            
          </div>
          <div class="input-field col s12 m6">
            <input  id="dateFICWASGIVEN" name="dateFICWASGIVEN" type="date"  >
            <label for="dateFICWASGIVEN">DATE FIC WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m6">
            <input    id="DateofConsultationFIC" name="DateofConsultationFIC" type="date"  >
            <label for="DateofConsultationFIC">DATE OF CONSULTATION (FIC)</label>
          </div>






          <div class="input-field col s12 m12">
          <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">CIC</h5>
            
            <p> 
                  <label>
                    <input class="with-gap" name="CIC" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="CIC" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            
          </div>
          <div class="input-field col s12 m6">
            <input  id="dateCICWASGIVEN" name="dateCICWASGIVEN" type="date"  >
            <label for="dateCICWASGIVEN">DATE CIC WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m6">
            <input    id="DateofConsultationCIC" name="DateofConsultationCIC" type="date"  >
            <label for="DateofConsultationCIC">DATE OF CONSULTATION (CIC)</label>
          </div>







          

          <div class="input-field col s12 m12" style="display:none;"> 
           
            
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">AMV 2(16 MOS. TO 5 YRS. OLD)</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="AMAMV2_16MOSTO5YRSOLDV2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="AMAMV2_16MOSTO5YRSOLDV2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
          </div>
          <div class="input-field col s12 m5" style="display:none;">
            <input   id="dateAMV2WASGIVEN" name="dateAMV2WASGIVEN" type="date"  >
            <label for="dateAMV2WASGIVEN">DATE AMV2 WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m7" style="display:none;">
            <input  placeholder="" id="DateofConsultationAMV2"  name="DateofConsultationAMV2" type="date"  >
            <label for="DateofConsultationAMV2">DATE OF CONSULTATION (AMV2)</label>
          </div>
            <div class="input-field col   s12 m5">
            <input  placeholder="" id="IMMUNIZATIONSTATUS" name="IMMUNIZATIONSTATUS" type="text"  >
            <label for="IMMUNIZATIONSTATUS">IMMUNIZATION STATUS</label>
          </div>
            <div class="input-field col  s12 m7">
            <input  placeholder="" id="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" name="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" type="date"  >
            <label for="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED">DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED</label>
          </div>

<!---->
          
 


      </div>

        </div>
        </div>
  
  
        
       
          </div>
        </div>
        </div>
    </div>



    <div id="tab3"  >
      <div class="row ">
      
        <div class="col s12 m10 offset-m1"  >
         
           
            <div class="z-depth-1 white">
            <div class="row  " style="padding:20.1px;"> 
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">Exclusive Breastfeeding</h5>
           
            <div class="input-field col s12 z-depth-1">
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">CHILD WAS EXCLUSIVELY BREASTFED (PUT A CHECK)</h5>
              <p class="" style="padding:10px;"> 
                <label>
                  <input name="FIRSTMONTH" value="FIRST MONTH" type="checkbox" class="filled-in"   />
                  <span style="font-size:13.6px;font-weight:500;">FIRST MONTH</span>
                </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input   name="SECONDMONTH" value="SECOND MONTH" type="checkbox"  class="filled-in" />
                  <span style="font-size:13.6px;font-weight:500;">SECOND MONTH</span>
                </label> &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input   name="THIRDMONTH" value="THIRD MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">THIRD MONTH</span>
                </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input name="FOURTHDMONTH" value="FOURTH MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">FOURTH MONTH</span>
                </label> &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input   name="FIFTMONTH" value="FIFTH MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">FIFTH MONTH</span>
                </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input   name="SIXTHMONTH" value="SIXTH MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">SIXTH MONTH</span>
                </label> 
              </p>
              
            </div>
            
            <div class="input-field col s12 m2">
              <input type="date" name="date6MONTHS" id="date6MONTHS" class="datepicker"  >
              <label for="date6MONTHS">DATE 6 MONTHS</label>
            </div>
            <div class="input-field col s12 m6">
              <input   id="WASTHECHILDEXCLUSIVELY" name="WASTHECHILDEXCLUSIVELY" placeholder="" type="text"  >
              <label for="WASTHECHILDEXCLUSIVELY">WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?</label>
            </div>
            <div class="input-field col s12 m4">
              <input   id="DATEOFCONSULTATIONEXCLUSIVEBF" name="DATEOFCONSULTATIONEXCLUSIVEBF" type="date"  >
              <label for="DATEOFCONSULTATIONEXCLUSIVEBF">DATE OF CONSULTATION (EXCLUSIVE)</label>
            </div>





            <div class="input-field col m12  s12">
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold;background:#006064;">NON-PREGNANT GIVEN TETANUS TOXOID IMMUNIZATION</h5>
            </div>
            
            <div class="input-field col m8 s12">
              
              <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">TD1</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            </div>
            <div class="input-field col m8 s12">
              <input  placeholder="" id="DATEOFCONSULTTT1" name="DATEOFCONSULTTT1" type="date"  >
              <label for="DATEOFCONSULTTT1">DATE OF CONSULT (TT1)</label>
            </div>

            
            <div class="input-field col m8 s12">
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT2</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m8 s12">
              <input  placeholder="" id="DATEOFCONSULTTT2" name="DATEOFCONSULTTT2" type="date"  >
              <label for="DATEOFCONSULTTT2">DATE OF CONSULT (TT2)</label>
            </div>
             
            <div class="input-field col m8 s12">
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT3</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT3" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT3" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m8 s12">
              <input  placeholder="" id="DATEOFCONSULTTT3" name="DATEOFCONSULTTT3" type="date"  >
              <label for="DATEOFCONSULTTT3">DATE OF CONSULT (TT3)</label>
            </div>
             
            <div class="input-field col m8 s12">
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT4</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT4" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT4" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m8 s12">
              <input  placeholder="" id="DATEOFCONSULTTT4" name="DATEOFCONSULTTT4" type="date"  >
              <label for="DATEOFCONSULTTT4">DATE OF CONSULT (TT4)</label>
            </div>

            <div class="input-field col m8 s12">
            <h5 class="blue-grey lighten-5  black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT5</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT5" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT5" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m8 s12">
              <input  placeholder="" id="DATEOFCONSULTT5" name="DATEOFCONSULTT5" type="date"  >
              <label for="DATEOFCONSULTT5">DATE OF CONSULT (TT5)</label>
            </div>
        

            <div class="input-field col m8 s12">
              <input  placeholder="" id="PhoneNumber" name="PhoneNumber" type="number"  >
              <label for="PhoneNumber">Phone Number</label>
            </div>
           
          
<!---->
 

      </div>
      </div>


    
      
        </div>
      </div>
      </div>
      <div class=" row center  s12 m12 " style="margin-right:9em;margin-bottom:1em;">
              <button type="submit" id="submit_btn"   name="submitBtn" class="right btn cyan darken-3 btn-small" style="font-weight: 500;"><i class="material-icons right">send</i>Submit</button><br><br>
            </div>
            
          </form> 
  </div> 

      <div class="row right ">
        <ul id="tabs-swipe-demo" class="tabs" >
          <li class="tab col "><a style="font-size:13px;" href="#tab1" class="active"> 
  <div class="chip   white-text" style="background:#006064 ;">1</div>
   Personal Information</a> </li>
 
          <li class="tab col "><a style="font-size:11px;" href="#tab2">  <div class="chip   white-text" style="background:#006064 ;">2</div>Antigens</a></li>
          <li class="tab col "><a style="font-size:11px;" href="#tab3">  <div class="chip   white-text" style="background:#006064 ;">3</div>Breastfeeding</a></li>
      </ul>
      </div>
      
       
      
<script>
   $(document).ready(function(){
    $('.modal').modal();
  });
</script>
       
      <script>
  document.getElementById('DateOfBirth').addEventListener('change', function() {
    const birthDate = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    
    // If the current month is before the birth month or it's the birth month but the current day is before the birth day
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Add 0.1 to the calculated age
    age = (age + 0.1).toFixed(1); // Keeps one decimal place

    // Set the calculated age in the Age input
    document.getElementById('Age').value = age;
  });
</script>

<script>
  
  document.getElementById('BirthdateofMother').addEventListener('change', function() {
    const birthDate = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    
    // If the current month is before the birth month or it's the birth month but the current day is before the birth day
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    // Set the calculated age in the Age input
    document.getElementById('AgeofMother').value = age;
  });
</script>


      <script>
        document.addEventListener('DOMContentLoaded', function() {
            var elems = document.querySelectorAll('.tabs');
            var instances = M.Tabs.init(elems);
        });
    </script>
      <script>
        document.getElementById('DateOfBirth').addEventListener('change', function() {
          const birthDate = new Date(this.value);
          const today = new Date();
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDifference = today.getMonth() - birthDate.getMonth();
          
          // If the current month is before the birth month or it's the birth month but the current day is before the birth day
          if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }
          
          // Set the calculated age in the Age input
          document.getElementById('AgeofChild').value = age;
        });
      </script>
      <script>
        document.getElementById('Philhealth').addEventListener('input', function (e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove all non-digit characters
            if (value.length > 2) {
                value = value.slice(0, 2) + '-' + value.slice(2); // Add first hyphen after 4 digits
            }
            if (value.length > 12) {
                value = value.slice(0, 12) + '-' + value.slice(12); // Add second hyphen after another 4 digits
            }
            e.target.value = value; // Update the input value with formatted string
        });
    </script>
</body>
</html>
<script>
  function forms() {
      var name = document.getElementById('name').innerHTML;
      var birthDate = document.getElementById('bdate').value;
  }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
      var elems = document.querySelectorAll('select');
      M.FormSelect.init(elems);
  });
</script>

 

<script>
    // Remove the warning notice pop-up and enhance backward compatibility
    (function() {
        if (window.history && window.history.replaceState) {
            window.addEventListener('load', function() {
                // Ensure the script runs only after the page fully loads
                window.history.replaceState(null, null, window.location.href);
            });
        }
    })();
</script>
<script>
  function classifyBirthWeight() {
    const weightInput = document.getElementById('BirthWeightInGrams');
    const classificationInput = document.getElementById('BirthWeightClassification');

    const weight = parseFloat(weightInput.value);

    if (!isNaN(weight)) {
      classificationInput.value =
        weight < 2000 ? 'LOW BIRTHWEIGHT (<2000 GMS)' : 'NORMAL';
    } else {
      classificationInput.value = '';
    }
  }
</script>

<script>
        const nameInput = document.getElementById('NameofFacility');
        const suggestionsList = document.getElementById('hospitalSuggestions');

        nameInput.addEventListener('input', async () => {
            const query = nameInput.value.trim();
            suggestionsList.innerHTML = ''; // Clear previous suggestions

            if (query.length > 0) {
                const response = await fetch(`autocomplete.php?q=${query}`);
                const hospitals = await response.json();

                hospitals.forEach(hospital => {
                    const suggestion = document.createElement('li');
                    suggestion.textContent = hospital;
                    suggestion.style.cursor = 'pointer';

                    suggestion.addEventListener('click', () => {
                        nameInput.value = hospital; // Set selected value
                        suggestionsList.innerHTML = ''; // Clear suggestions
                    });

                    suggestionsList.appendChild(suggestion);
                });
            }
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', (event) => {
            if (!nameInput.contains(event.target)) {
                suggestionsList.innerHTML = '';
            }
        });
    </script>
 
 
 
 
 
 
 <script>
    $(document).ready(function() {
        $("#searchForm").on("submit", function(event) {
            event.preventDefault(); // Prevent page reload

            $.ajax({
                url: "fetchEkonsulta.php",  // Backend PHP script
                type: "POST",
                data: $(this).serialize(),
                dataType: "json", // Expect JSON response
                success: function(response) {
                    if (response.success) {
                        $("#ekonsultaRegIDNo_").val(response.ekonsultaRegIDNo);
                        $("#UHCRead").val(response.UHCID);
                        $("#LastNameOfChild").val(response.LName);
                        $("#NameOfChild").val(response.FName);
                        $("#middlename_of_child").val(response.MName);
                        $("#DateOfBirth").val(response.BirthDate);
                        $("#Sex").val(response.Sex);
                      

                        // Calculate Age and set value
                        let age = getAge(response.BirthDate);
                        $("#AgeofChild").val(age);
                        $("#PhoneNumber").val(response.ContactNo);
                        $("#result").html("");

                     
                    }
                 
                    else {
                        $("#ekonsultaRegIDNo_").val("");
                        $("#UHCRead").val("");
                        $("#LastNameOfChild").val("");
                        $("#NameOfChild").val("");
                        $("#middlename_of_child").val("");
                        $("#DateOfBirth").val("");
                        $("#Sex").val("");
                        $("#AgeofChild").val(""); // Clear age if no record
                        $("#PhoneNumber").val("");
                        $("#result").html("<small style='color: red;'>Please Proceed to First Patient Encounter</small>");
                    }
                },
                error: function() {
                    $("#result").html("<p style='color: red;'>Error fetching data.</p>");
                }
            });
        });

        // Function to calculate age from birthdate
        function getAge(birthdate) {
            let birthDate = new Date(birthdate);
            let today = new Date();

            let age = today.getFullYear() - birthDate.getFullYear();
            let monthDiff = today.getMonth() - birthDate.getMonth();
            let dayDiff = today.getDate() - birthDate.getDate();

            // Adjust age if birthday hasn't occurred yet this year
            if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
                age--;
            }

            return age;
        }
    });
</script>

 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 <script>
  function myFunction() {
    let PurokStreetSitio = document.getElementById("PurokStreetSitio").value;
    let HouseNo = document.getElementById("HouseNo").value;
    
 
    let healthCenter = <?php 
      $health_centers = [
        'BACLARAN HEALTH CENTER' => 'BACLARAN',
        'TAMBO HEALTH CENTER' => 'TAMBO',
        'VITALEZ HEALTH CENTER' => 'VITALEZ',
        'DON GALO HEALTH CENTER' => 'DON GALO',
        'STO.NIÑO HEALTH CENTER' => 'STO.NIÑO',
        'LA HUERTA HEALTH CENTER' => 'LA HUERTA',
        'MOONWALK HEALTH CENTER' => 'MOONWALK',
        'SAN DIONISIO HEALTH CENTER' => 'SAN DIONISIO',
        'SAN ISIDRO HEALTH CENTER' => 'SAN ISIDRO',
        'SAN ANTONIO HEALTH CENTER' => 'SAN ANTONIO',
        'DON BOSCO HEALTH CENTER' => 'DON BOSCO',
        'BF HEALTH CENTER' => 'BF',
        'MARCELO GREEN HEALTH CENTER' => 'MARCELO GREEN',
        'MERVILLE HEALTH CENTER' => 'MERVILLE',
        'SAN MARTIN DE PORRES HEALTH CENTER' => 'SAN MARTIN DE PORRES',
        'SUN VALLEY HEALTH CENTER' => 'SUN VALLEY'
      ];

      $current_center = $_SESSION['health_center'] ?? 'UNKNOWN'; 
      echo json_encode($health_centers[$current_center] ?? 'UNKNOWN');
    ?>;

    document.getElementById("Address").value = `${HouseNo} ${PurokStreetSitio}, ${healthCenter} PARAÑAQUE CITY`;
  }
</script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    var elems = document.querySelectorAll('.modal');
    var instances = M.Modal.init(elems, options);
  });
</script>


<script>
    $(document).ready(function(){
    $('.collapsible').collapsible();
    instance.open(1);
      
  });    
    
</script>
<script>
    function getAge(birthdate) {
    const birthDate = document.getElementById('DateOfBirth').value;
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    // Adjust age if birthday hasn't occurred yet this year
    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age--;
    }

    return age;
}

// Example usage
document.write((age)); // Replace with any birthdate (YYYY-MM-DD)
    
</script>