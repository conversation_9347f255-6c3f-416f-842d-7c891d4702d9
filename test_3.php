<?php include 'db.php'; ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
  <?php include 'style.php'; ?>
    <style>
        th {
            border:1px solid white;
            color:white;
            font-size: 12px;
            text-align: center;
        }
  
        table {
          border-collapse: collapse;
          border-spacing: 0;
          width: 100%;

           border:1px solid #546e7a;
        }
        td {
            font-size:14.7px;
            text-align: start !important;
        }
        th, td {
            border-radius: 0;
          text-align: center;
          padding: 3px;
         
          border:1px solid rgb(177, 186, 190);
        }
        
   
        </style>
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

           
   <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
   <script src="https://cdn.datatables.net/2.1.8/js/dataTables.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/dataTables.buttons.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.dataTables.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.html5.min.js"></script>
   <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.css">
   <link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.2.0/css/buttons.dataTables.css">
</head>
<body>


 <?php include 'nav.php'; ?>
 <br>
 <div style="padding:8px;">
    <form action="" method="post">
        <div class="row">
            <div class="col s5">

                <input type="date" name="date" id="">
            </div>
            <div class="col s2">
                <button name="filter" class="btn small-btn">Filter</button>
            </div>
        </div>
    </form>
 <strong class="small" style="font-size:13.2px;">
     <h5>Accomplishment Report</h5>

     Problem analysis of immunization (coverage, DOR, Access, Utilization, Category and priority)			 
     <br>Goal :  Increase Immunization Coverage to ≥95% with all Vaccines in every MunCities 	
    </strong>
 <button type="button" class="right   btn-small btn blue darken-2" style="font-weight:500;" onclick="exportToExcel()">Download</button>
<br><br>
    <div style="overflow-x:auto;">
    <table id="mainTable" style="  width: 130%;" border="1" class="nowrap responsive-table ">
        <tr style="background:#0097a7!important;">
        <th rowspan="4">Municipality: PARAÑAQUE CITY
        <th colspan="9" style="font-size:1.2em;">CY 2025 Compiled  Immunization Coverage Data</th>
        <th  colspan="7">Analysis Problem</th>
        <th colspan="3" >VPD Cases</th>
        <th rowspan="3">Priority (1, 2, 3 or4)</th>
    </tr>
        <tr style="background:#0097a7!important;">
          


            </th>
            <th rowspan="2"  >Municipality: PARAÑAQUE CITY
            </th>
            <th colspan="4">Children Vaccinated</th>
            <th colspan="4">Immunization Coverage (%)</th>
            <th colspan="2">Unimmunized (No.)</th>
            <th colspan="2">Drop-out Rates (%)</th>
            <th colspan="2">Identify Problem</th>
            <th  rowspan="2">Category (1, 2, 3 or )</th>
           
          
          
            <th rowspan="2">Measles
            </th>
            <th rowspan="2">Pertussis
            </th>
            <th rowspan="2">Diphtheria
            </th>
          
           
        </tr>
        <tr style="background:#0097a7!important;">
            
            <th colspan="1" >Penta1</th>
            <th>Penta3</th>
            <th>MCV1</th>
            <th>MCV2</th>
            <th title="(C11/B11*100)">Penta1 (b/a*100)</th>
            <th>Penta3(c/a*100)</th>
            <th>MCV1(d/a*100)</th>
            <th>MCV2(e/a*100)</th>
            <th>Penta1</th>
            <th>MCV2</th>
            <th>Penta1 vs Penta3</th>
            <th>Penta1 vs MCV2</th>
            <th>Access</th>
            <th>Utilization</th>
         
           
           
        </tr>
        <?php
                
                $sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = '".$_SESSION['health_center']."'  LIMIT 1 ";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $result = $stmt->get_result();
 
                    while ($row = $result->fetch_assoc()) {   ?>
                    <tr >
                  
                    <td style="font-size:13.2px !important;">a</td>
        <td  style="font-size:13.2px !important;">b</td>
        <td  style="font-size:13.2px !important;">c</td>
        <td  style="font-size:13.2px !important;">d</td>
        <td  style="font-size:13.2px !important;">e</td>
        <td  style="font-size:13.2px !important;">f</td>
        <td  style="font-size:13.2px !important;">g</td>
        <td  style="font-size:13.2px !important;">h</td>
        <td  style="font-size:13.2px !important;">i</td>
        <td  style="font-size:13.2px !important;">j</td>
        <td  style="font-size:13.2px !important;">k</td>
        <td  style="font-size:13.2px !important;">l</td>
        <td  style="font-size:13.2px !important;">m</td>
        <td  style="font-size:13.2px !important;">n</td>
        <td  style="font-size:13.2px !important;">o</td>
        <td  style="font-size:13.2px !important;">p</td>
        <td  style="font-size:13.2px !important;">q</td>
        <td  style="font-size:13.2px !important;">r</td>
        <td  style="font-size:13.2px !important;">s</td>
        <td  style="font-size:13.2px !important;">t</td>
                    </tr>
        <tr>
            <td style="font-size:12.3px;"><b><?php if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {echo 'All';} else { echo $row['Barangay']; } ?></b></td>
     
           
        </tr>
        <?php } ?>

        <?php
        if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {  
$sql = "SELECT 
    PurokStreetSitio, 
    COUNT(*) AS count_purok,
    SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB1_count,
    SUM(CASE WHEN PENTAHIB3 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB3_count,
    
    NULLIF(SUM(CASE WHEN PENTAHIB3 = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) AS PENTAHIB1_to_PENTAHIB3_ratio,

    NULLIF(SUM(CASE WHEN AMV19monthstobelow12months = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) AS MCV1_to_PENTAHIB1_ratio,

    NULLIF(SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) AS MCV2_to_PENTAHIB1_ratio,

    SUM(CASE WHEN AMV19monthstobelow12months = 'YES' THEN 1 ELSE 0 END) AS MCV1_count,
    SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END) AS MCV2_count,

    -- Corrected dv calculation (avoids alias use and division by zero)
    (
        NULLIF(SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
        NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0)
    ) / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) * 100 AS dv

FROM  nip_table WHERE deleted = 0
GROUP BY PurokStreetSitio";

        }
        else {
            $sql = "SELECT 
    PurokStreetSitio, 
    COUNT(*) AS count_purok,
    SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB1_count,
    SUM(CASE WHEN PENTAHIB3 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB3_count,
    
    NULLIF(SUM(CASE WHEN PENTAHIB3 = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) AS PENTAHIB1_to_PENTAHIB3_ratio,

    NULLIF(SUM(CASE WHEN AMV19monthstobelow12months = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) AS MCV1_to_PENTAHIB1_ratio,

    NULLIF(SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) AS MCV2_to_PENTAHIB1_ratio,

    SUM(CASE WHEN AMV19monthstobelow12months = 'YES' THEN 1 ELSE 0 END) AS MCV1_count,
    SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END) AS MCV2_count,

    -- Corrected dv calculation (avoids alias use and division by zero)
    (
        NULLIF(SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END), 0) * 1.0 / 
        NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0)
    ) / 
    NULLIF(SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END), 0) * 100 AS dv

FROM nip_table 
WHERE Barangay = '".$_SESSION['health_center']."'   AND   deleted = 0
GROUP BY PurokStreetSitio";




        }


$stmt = $conn->prepare($sql);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) { 
    $count_purok = $row['count_purok'];  
?>
    <tr>
        <td><?php echo $row['PurokStreetSitio']; ?></td>
        <td><?php echo $row['count_purok']; ?></td>
        <td><?php echo $row['PENTAHIB1_count']; ?></td>
        <td><?php echo $row['PENTAHIB3_count']; ?></td>
        <td><?php echo $row['MCV1_count']; ?></td>
        <td><?php echo $row['MCV2_count']; ?></td>
        
        <td><?php echo number_format($row['PENTAHIB1_to_PENTAHIB3_ratio'], 2); ?></td>
        <td><?php echo number_format($row['MCV1_to_PENTAHIB1_ratio'], 2); ?></td>
        <td><?php echo number_format($row['MCV2_to_PENTAHIB1_ratio'], 2); ?></td>
        <td><?php echo number_format($row['dv'], 2); ?></td>

        <td><?php echo number_format($row['PENTAHIB1_count'] - $row['PENTAHIB3_count']); ?></td>

        <td><?php echo number_format($row['PENTAHIB3_count'] - $row['MCV1_count']); ?></td>

        <td>
            <?php 
                echo ($row['PENTAHIB3_count'] != 0) 
                    ? number_format(($row['PENTAHIB3_count'] - $row['MCV1_count']) / $row['PENTAHIB3_count'] * 100, 1) 
                    : 0; 
            ?>
        </td>

        <td>
        <?php 
    echo ($row['PENTAHIB3_count'] != 0) 
        ? number_format(($row['PENTAHIB3_count'] - $row['PENTAHIB1_count']) / $row['PENTAHIB3_count'] * 100, 1) 
        : 0; 
?>

        </td>
        
        <!-- If these columns are not needed, remove them -->
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
<?php } ?>

           
<tr>
    <th class="black-text">Total</th>
    <th class="black-text" style="text-align:start;">
        <?php

        if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
            $sql = "SELECT 
            COUNT(*) AS count_purok,
            SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB1_count
        FROM nip_table  WHERE deleted = 0
       
        LIMIT 1";
        }

else {
        $sql = "SELECT 
                    COUNT(*) AS count_purok,
                    SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB1_count
                FROM nip_table 
                WHERE Barangay =  '".$_SESSION['health_center']."'    AND   deleted = 0
                LIMIT 1";
            }
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {    
            echo $row['count_purok']; // Displaying the count of PENTAHIB1 = 'YES'
        } else {
            echo "0"; // If no records are found
        }
        ?>
    </th>
    <th class="black-text" style="text-align:start;">
        <?php
        if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
        $sql = "SELECT 
                    COUNT(*) AS count_purok,
                    SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB1_count
                FROM nip_table WHERE deleted = 0
                
                LIMIT 1";
        }
                else {
                    $sql = "SELECT 
                    COUNT(*) AS count_purok,
                    SUM(CASE WHEN PENTAHIB1 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB1_count
                FROM nip_table 
                WHERE Barangay =  '".$_SESSION['health_center']."'  AND deleted = 0
                LIMIT 1";
                }

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {    
            echo $row['PENTAHIB1_count']; // Displaying the count of PENTAHIB1 = 'YES'
        } else {
            echo "0"; // If no records are found
        }
        ?>
    </th>
    <th class="black-text" style="text-align:start;">
        <?php
         if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
            $sql = "SELECT 
            COUNT(*) AS count_purok,
            SUM(CASE WHEN PENTAHIB3 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB3_count
        FROM nip_table WHERE  deleted = 0
        
        LIMIT 1";
         }
         else {

             $sql = "SELECT 
                         COUNT(*) AS count_purok,
                         SUM(CASE WHEN PENTAHIB3 = 'YES' THEN 1 ELSE 0 END) AS PENTAHIB3_count
                     FROM nip_table 
                     WHERE Barangay =  '".$_SESSION['health_center']."'    AND deleted = 0
                     LIMIT 1";
         }

        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {    
            echo $row['PENTAHIB3_count']; // Displaying the count of PENTAHIB1 = 'YES'
        } else {
            echo "0"; // If no records are found
        }
        ?>
    </th>
    <th class="black-text" style="text-align:start;">
        <?php
           if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
        $sql = "SELECT 
                    COUNT(*) AS count_purok,
                    SUM(CASE WHEN AMV19monthstobelow12months = 'YES' THEN 1 ELSE 0 END) AS AMV19monthstobelow12months_count
                FROM nip_table WHERE    deleted = 0
                
                LIMIT 1";
           }
           else {
            $sql = "SELECT 
            COUNT(*) AS count_purok,
            SUM(CASE WHEN AMV19monthstobelow12months = 'YES' THEN 1 ELSE 0 END) AS AMV19monthstobelow12months_count
        FROM nip_table 
        WHERE Barangay =  '".$_SESSION['health_center']."'    AND deleted = 0
        LIMIT 1";
           }
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {    
            echo $row['AMV19monthstobelow12months_count']; // Displaying the count of PENTAHIB1 = 'YES'
        } else {
            echo "0"; // If no records are found
        }
        ?>
    </th>
    <th class="black-text" style="text-align:start;">
        <?php
          if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') { 
            $sql = "SELECT 
            COUNT(*) AS count_purok,
            SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END) AS MCV2months_count
        FROM nip_table WHERE  deleted = 0
        
        LIMIT 1";
          }
          else {

          
        $sql = "SELECT 
                    COUNT(*) AS count_purok,
                    SUM(CASE WHEN MMR12MOSTO15MOS = 'YES' THEN 1 ELSE 0 END) AS MCV2months_count
                FROM nip_table 
                WHERE Barangay =  '".$_SESSION['health_center']."'    AND deleted = 0
                LIMIT 1";
          }
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {    
            echo $row['MCV2months_count']; // Displaying the count of PENTAHIB1 = 'YES'
        } else {
            echo "0"; // If no records are found
        }
        ?>
    </th>
</tr>

       
        </tfoot>
    </table>


    <table style="border:0 !important;">
    <tfoot style="border:0 !important; ">
            <tr style="border:0 !important;">
            <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">Key 	
	
</td>
<td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">
If Penta-1 is >95% ( Access is good)	

</td>
<td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">Category1 means: good access  and good utilization  <span class="red-text"> (Priority 4)</span></td>
            </tr>
            <tr >
                 <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">If Penta-1 is <95% ( Access is poor) </td>
                 <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">
                If Penta-1 to  MCV-2 DOR is >10% (Utilization is poor)
            </td>
            <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">
                Category2 means: good access  and poor utilization  <span class="red-text">(Priority 3)</span>
                </td>
            </tr>
           <tr>
              <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; "></td>
              <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; "></td>
              <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">
               Category3 means Poor access  and good utilization  <span class="red-text">(Priority 2)</span>
               </td>
              
            </tr>
           <tr style="text-align:start;border:0 !important; font-size:13.5px; ">
               <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; "></td>
               <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; "></td>
               <td class="black-text" style="text-align:start;border:0 !important; font-size:13.5px; ">
         
               Category4 means poor access and poor utilization   <span class="red-text">(Priority 1)</span>
               </td>
              
              
            </tr>

    </table>
</div>
</div>
</body>
</html>

<script>
        function exportToExcel() {
            let table = document.getElementById("mainTable");
            
            if (!table) {
                alert("Table not found!");
                return;
            }

            let wb = XLSX.utils.book_new();
            let ws = XLSX.utils.table_to_sheet(table);
            XLSX.utils.book_append_sheet(wb, ws, "Sheet1");

            XLSX.writeFile(wb, "<?php echo $_SESSION['health_center']; ?>_NIP.xlsx");
        }
    </script>

    