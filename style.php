 <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <!-- Compiled and minified CSS -->
 
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #37474f;
}
::selection {
  background: #90caf9;
}
 
    /* Enhanced Card Styles */
    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
 
    
 
    .card .card-content {
      padding: 24px;
    }
 
    /* Button Enhancements */
    .btn, .btn-large {
      border-radius: 4px;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
 
    .btn:hover, .btn-large:hover {
      box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
    }
 
    /* Form Styling */
    .input-field input:focus + label,
    .input-field textarea:focus + label {
      color: #009bd4 !important;
    }
 
    .input-field input:focus,
    .input-field textarea:focus {
      border-bottom: 1px solid #009bd4 !important;
      box-shadow: 0 1px 0 0 #009bd4 !important;
    }
 
    /* Select Dropdown Styling */
    .dropdown-content li > a, .dropdown-content li > span {
      color: #009bd4;
    }
 
    /* Table Enhancements */
    table.highlight > tbody > tr:hover {
      background-color: rgba(0, 155, 212, 0.05);
    }
 
    thead {
      background-color: #f5f5f5;
    }
 
    /* Navigation Improvements */
    nav {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16);
    }
 
    /* Container Spacing */
    .container {
      padding: 20px 15px;
    }
 
    /* Section Headers */
    .section-header {
      border-left: 4px solid #009bd4;
      padding-left: 15px;
      margin: 25px 0 15px;
      font-weight: 500;
    }
 
    /* Dashboard Stats */
    .stat-card {
      padding: 20px;
      border-radius: 8px;
      color: white;
      margin-bottom: 20px;
    }
 
    .stat-card.primary {
      background: linear-gradient(45deg, #009bd4, #00bcd4);
    }
 
    .stat-card.success {
      background: linear-gradient(45deg, #43a047, #66bb6a);
    }
 
    .stat-card.warning {
      background: linear-gradient(45deg, #ff9800, #ffb74d);
    }
 
    .stat-card.danger {
      background: linear-gradient(45deg, #e53935, #ef5350);
    }
 
    .stat-card .stat-value {
      font-size: 2.5rem;
      font-weight: 300;
    }
 
    .stat-card .stat-label {
      font-size: 1rem;
      opacity: 0.8;
    }
 
    /* Preloader */
    .preloader-wrapper.custom-spinner {
      width: 36px;
      height: 36px;
    }
 
    /* Toast Customization */
    .toast {
      border-radius: 4px;
    }
    </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #37474f;
}
::selection {
  background: #90caf9;
}
 
    /* Enhanced Card Styles */
    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
 
    .card:hover {
      
      box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }
 
    .card .card-content {
      padding: 24px;
    }
 
    /* Button Enhancements */
    .btn, .btn-large {
      border-radius: 4px;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
 
    .btn:hover, .btn-large:hover {
      box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
    }
 
    /* Form Styling */
    .input-field input:focus + label,
    .input-field textarea:focus + label {
      color: #009bd4 !important;
    }
 
    .input-field input:focus,
    .input-field textarea:focus {
      border-bottom: 1px solid #009bd4 !important;
      box-shadow: 0 1px 0 0 #009bd4 !important;
    }
 
    /* Select Dropdown Styling */
    .dropdown-content li > a, .dropdown-content li > span {
      color: #009bd4;
    }
 
    /* Table Enhancements */
    table.highlight > tbody > tr:hover {
      background-color: rgba(0, 155, 212, 0.05);
    }
 
    thead {
      background-color: #f5f5f5;
    }
 
    /* Navigation Improvements */
    nav {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16);
    }
 
    /* Container Spacing */
    .container {
      padding: 20px 15px;
    }
 
    /* Section Headers */
    .section-header {
      border-left: 4px solid #009bd4;
      padding-left: 15px;
      margin: 25px 0 15px;
      font-weight: 500;
    }
 
    /* Dashboard Stats */
    .stat-card {
      padding: 20px;
      border-radius: 8px;
      color: white;
      margin-bottom: 20px;
    }
 
    .stat-card.primary {
      background: linear-gradient(45deg, #009bd4, #00bcd4);
    }
 
    .stat-card.success {
      background: linear-gradient(45deg, #43a047, #66bb6a);
    }
 
    .stat-card.warning {
      background: linear-gradient(45deg, #ff9800, #ffb74d);
    }
 
    .stat-card.danger {
      background: linear-gradient(45deg, #e53935, #ef5350);
    }
 
    .stat-card .stat-value {
      font-size: 2.5rem;
      font-weight: 300;
    }
 
    .stat-card .stat-label {
      font-size: 1rem;
      opacity: 0.8;
    }
 
    /* Preloader */
    .preloader-wrapper.custom-spinner {
      width: 36px;
      height: 36px;
    }
 
    /* Toast Customization */
    .toast {
      border-radius: 4px;
    }
    </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #37474f;
}
::selection {
  background: #90caf9;
}
 
    /* Enhanced Card Styles */
    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
 
  
 
    .card .card-content {
      padding: 24px;
    }
 
    /* Button Enhancements */
    .btn, .btn-large {
      border-radius: 4px;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
 
    .btn:hover, .btn-large:hover {
      box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
    }
 
    /* Form Styling */
    .input-field input:focus + label,
    .input-field textarea:focus + label {
      color: #009bd4 !important;
    }
 
    .input-field input:focus,
    .input-field textarea:focus {
      border-bottom: 1px solid #009bd4 !important;
      box-shadow: 0 1px 0 0 #009bd4 !important;
    }
 
    /* Select Dropdown Styling */
    .dropdown-content li > a, .dropdown-content li > span {
      color: #009bd4;
    }
 
    /* Table Enhancements */
    table.highlight > tbody > tr:hover {
      background-color: rgba(0, 155, 212, 0.05);
    }
 
    thead {
      background-color: #f5f5f5;
    }
 
    /* Navigation Improvements */
    nav {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16);
    }
 
    /* Container Spacing */
    .container {
      padding: 20px 15px;
    }
 
    /* Section Headers */
    .section-header {
      border-left: 4px solid #009bd4;
      padding-left: 15px;
      margin: 25px 0 15px;
      font-weight: 500;
    }
 
    /* Dashboard Stats */
    .stat-card {
      padding: 20px;
      border-radius: 8px;
      color: white;
      margin-bottom: 20px;
    }
 
    .stat-card.primary {
      background: linear-gradient(45deg, #009bd4, #00bcd4);
    }
 
    .stat-card.success {
      background: linear-gradient(45deg, #43a047, #66bb6a);
    }
 
    .stat-card.warning {
      background: linear-gradient(45deg, #ff9800, #ffb74d);
    }
 
    .stat-card.danger {
      background: linear-gradient(45deg, #e53935, #ef5350);
    }
 
    .stat-card .stat-value {
      font-size: 2.5rem;
      font-weight: 300;
    }
 
    .stat-card .stat-label {
      font-size: 1rem;
      opacity: 0.8;
    }
 
    /* Preloader */
    .preloader-wrapper.custom-spinner {
      width: 36px;
      height: 36px;
    }
 
    /* Toast Customization */
    .toast {
      border-radius: 4px;
    }
    </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #37474f;
}
::selection {
  background: #90caf9;
}
 
    /* Enhanced Card Styles */
    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
 
 
    .card .card-content {
      padding: 24px;
    }
 
    /* Button Enhancements */
    .btn, .btn-large {
      border-radius: 4px;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
 
    .btn:hover, .btn-large:hover {
      box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
    }
 
    /* Form Styling */
    .input-field input:focus + label,
    .input-field textarea:focus + label {
      color: #009bd4 !important;
    }
 
    .input-field input:focus,
    .input-field textarea:focus {
      border-bottom: 1px solid #009bd4 !important;
      box-shadow: 0 1px 0 0 #009bd4 !important;
    }
 
    /* Select Dropdown Styling */
    .dropdown-content li > a, .dropdown-content li > span {
      color: #009bd4;
    }
 
    /* Table Enhancements */
    table.highlight > tbody > tr:hover {
      background-color: rgba(0, 155, 212, 0.05);
    }
 
    thead {
      background-color: #f5f5f5;
    }
 
    /* Navigation Improvements */
    nav {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16);
    }
 
    /* Container Spacing */
    .container {
      padding: 20px 15px;
    }
 
    /* Section Headers */
    .section-header {
      border-left: 4px solid #009bd4;
      padding-left: 15px;
      margin: 25px 0 15px;
      font-weight: 500;
    }
 
    /* Dashboard Stats */
    .stat-card {
      padding: 20px;
      border-radius: 8px;
      color: white;
      margin-bottom: 20px;
    }
 
    .stat-card.primary {
      background: linear-gradient(45deg, #009bd4, #00bcd4);
    }
 
    .stat-card.success {
      background: linear-gradient(45deg, #43a047, #66bb6a);
    }
 
    .stat-card.warning {
      background: linear-gradient(45deg, #ff9800, #ffb74d);
    }
 
    .stat-card.danger {
      background: linear-gradient(45deg, #e53935, #ef5350);
    }
 
    .stat-card .stat-value {
      font-size: 2.5rem;
      font-weight: 300;
    }
 
    .stat-card .stat-label {
      font-size: 1rem;
      opacity: 0.8;
    }
 
    /* Preloader */
    .preloader-wrapper.custom-spinner {
      width: 36px;
      height: 36px;
    }
 
    /* Toast Customization */
    .toast {
      border-radius: 4px;
    }
    </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #37474f;
}
::selection {
  background: #90caf9;
}
 
    /* Enhanced Card Styles */
    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
 
    
 
    .card .card-content {
      padding: 24px;
    }
 
    /* Button Enhancements */
    .btn, .btn-large {
      border-radius: 4px;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
 
    .btn:hover, .btn-large:hover {
      box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
    }
 
    /* Form Styling */
    .input-field input:focus + label,
    .input-field textarea:focus + label {
      color: #009bd4 !important;
    }
 
    .input-field input:focus,
    .input-field textarea:focus {
      border-bottom: 1px solid #009bd4 !important;
      box-shadow: 0 1px 0 0 #009bd4 !important;
    }
 
    /* Select Dropdown Styling */
    .dropdown-content li > a, .dropdown-content li > span {
      color: #009bd4;
    }
 
    /* Table Enhancements */
    table.highlight > tbody > tr:hover {
      background-color: rgba(0, 155, 212, 0.05);
    }
 
    thead {
      background-color: #f5f5f5;
    }
 
    /* Navigation Improvements */
    nav {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16);
    }
 
    /* Container Spacing */
    .container {
      padding: 20px 15px;
    }
 
    /* Section Headers */
    .section-header {
      border-left: 4px solid #009bd4;
      padding-left: 15px;
      margin: 25px 0 15px;
      font-weight: 500;
    }
 
    /* Dashboard Stats */
    .stat-card {
      padding: 20px;
      border-radius: 8px;
      color: white;
      margin-bottom: 20px;
    }
 
    .stat-card.primary {
      background: linear-gradient(45deg, #009bd4, #00bcd4);
    }
 
    .stat-card.success {
      background: linear-gradient(45deg, #43a047, #66bb6a);
    }
 
    .stat-card.warning {
      background: linear-gradient(45deg, #ff9800, #ffb74d);
    }
 
    .stat-card.danger {
      background: linear-gradient(45deg, #e53935, #ef5350);
    }
 
    .stat-card .stat-value {
      font-size: 2.5rem;
      font-weight: 300;
    }
 
    .stat-card .stat-label {
      font-size: 1rem;
      opacity: 0.8;
    }
 
    /* Preloader */
    .preloader-wrapper.custom-spinner {
      width: 36px;
      height: 36px;
    }
 
    /* Toast Customization */
    .toast {
      border-radius: 4px;
    }
    </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f7fa;
  color: #37474f;
}
::selection {
  background: #90caf9;
}
 
    /* Enhanced Card Styles */
    .card {
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0,0,0,0.05);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
 
    
 
    .card .card-content {
      padding: 24px;
    }
 
    /* Button Enhancements */
    .btn, .btn-large {
      border-radius: 4px;
      text-transform: uppercase;
      font-weight: 500;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
 
    .btn:hover, .btn-large:hover {
      box-shadow: 0 5px 11px 0 rgba(0,0,0,0.18), 0 4px 15px 0 rgba(0,0,0,0.15);
    }
 
    /* Form Styling */
    .input-field input:focus + label,
    .input-field textarea:focus + label {
      color: #009bd4 !important;
    }
 
    .input-field input:focus,
    .input-field textarea:focus {
      border-bottom: 1px solid #009bd4 !important;
      box-shadow: 0 1px 0 0 #009bd4 !important;
    }
 
    /* Select Dropdown Styling */
    .dropdown-content li > a, .dropdown-content li > span {
      color: #009bd4;
    }
 
    /* Table Enhancements */
    table.highlight > tbody > tr:hover {
      background-color: rgba(0, 155, 212, 0.05);
    }
 
    thead {
      background-color: #f5f5f5;
    }
 
    /* Navigation Improvements */
    nav {
      box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16);
    }
 
    /* Container Spacing */
    .container {
      padding: 20px 15px;
    }
 
    /* Section Headers */
    .section-header {
      border-left: 4px solid #009bd4;
      padding-left: 15px;
      margin: 25px 0 15px;
      font-weight: 500;
    }
 
    /* Dashboard Stats */
    .stat-card {
      padding: 20px;
      border-radius: 8px;
      color: white;
      margin-bottom: 20px;
    }
 
    .stat-card.primary {
      background: linear-gradient(45deg, #009bd4, #00bcd4);
    }
 
    .stat-card.success {
      background: linear-gradient(45deg, #43a047, #66bb6a);
    }
 
    .stat-card.warning {
      background: linear-gradient(45deg, #ff9800, #ffb74d);
    }
 
    .stat-card.danger {
      background: linear-gradient(45deg, #e53935, #ef5350);
    }
 
    .stat-card .stat-value {
      font-size: 2.5rem;
      font-weight: 300;
    }
 
    .stat-card .stat-label {
      font-size: 1rem;
      opacity: 0.8;
    }
 
    /* Preloader */
    .preloader-wrapper.custom-spinner {
      width: 36px;
      height: 36px;
    }
 
    /* Toast Customization */
    .toast {
      border-radius: 4px;
    }
    </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
  </style>
    <!-- Compiled and minified JavaScript -->
    <script src="style/js/materialize.js"></script>
    <script src="style/js/materialize.min.js"></script>
<link rel="stylesheet" href="style/css/cho.css">
    <link rel="stylesheet" href="style/css/cho.min.css">
    <style>
      @media only screen and (max-width: 600px) {
  #alaga {
   width:420px;
  }
  #logo-container {
   font-size:11.8px !important;
  }
}
body{
  font-family: 'Roboto', sans-serif;
}
::selection {
  background:#90caf9;
}
 
    </style>
 
