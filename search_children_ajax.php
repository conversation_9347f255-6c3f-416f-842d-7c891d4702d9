<?php
session_start();
header('Content-Type: application/json');

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// Get search parameters
$first_name = trim($_POST['first_name'] ?? '');
$last_name = trim($_POST['last_name'] ?? '');
$middle_name = trim($_POST['middle_name'] ?? '');
$birth_date_from = trim($_POST['birth_date_from'] ?? '');
$birth_date_to = trim($_POST['birth_date_to'] ?? '');
$immunization_status = trim($_POST['immunization_status'] ?? '');

// Build search query
$sql = "SELECT id, NameOfChild, middlename_of_child, LastNameOfChild, DateOfBirth, NameofMother, Barangay, IMMUNIZATIONSTATUS 
        FROM nip_table WHERE (deleted IS NULL OR deleted = 0)";

$params = [];
$types = "";

// Add search conditions
if (!empty($first_name)) {
    $sql .= " AND NameOfChild LIKE ?";
    $params[] = "%" . $first_name . "%";
    $types .= "s";
}

if (!empty($last_name)) {
    $sql .= " AND LastNameOfChild LIKE ?";
    $params[] = "%" . $last_name . "%";
    $types .= "s";
}

if (!empty($middle_name)) {
    $sql .= " AND middlename_of_child LIKE ?";
    $params[] = "%" . $middle_name . "%";
    $types .= "s";
}

if (!empty($birth_date_from)) {
    $sql .= " AND DateOfBirth >= ?";
    $params[] = $birth_date_from;
    $types .= "s";
}

if (!empty($birth_date_to)) {
    $sql .= " AND DateOfBirth <= ?";
    $params[] = $birth_date_to;
    $types .= "s";
}

if (!empty($immunization_status)) {
    $sql .= " AND IMMUNIZATIONSTATUS = ?";
    $params[] = $immunization_status;
    $types .= "s";
}

$sql .= " ORDER BY LastNameOfChild, NameOfChild LIMIT 50";

// Execute query
$stmt = mysqli_prepare($conn, $sql);
if (!empty($params)) {
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}

mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

$children = [];
while ($row = mysqli_fetch_assoc($result)) {
    $children[] = $row;
}

mysqli_stmt_close($stmt);
mysqli_close($conn);

echo json_encode($children);
?>
