<?php
/**
 * Print the earliest consultation date in <p> tags
 * Based on the specific consultation_dates array provided
 */

function printEarliestDateInP($data) {
    $consultation_dates = [
        'DateofConsultationBCG',
        'DateofConsultationPENTAHIB1',
        'DateofConsultationPENTAHIB2', 
        'DateofConsultationPENTAHIB3',
        'DateofConsultationOPV1v',
        'DateofConsultationOPV2',
        'DateofConsultationOPV3',
        'DateofConsultationIPV1',
        'DateofConsultationIPV2',
        'DateofConsultationPCV1',
        'DateofConsultationPCV2',
        'DateofConsultationPCV3',
        'DateofConsultationHEPAatBirth',
        'DateofConsultationHEPAB1',
        'DateofConsultationHEPAB2',
        'DateofConsultationHEPAB3',
        'DateofConsultationHEPAB4',
        'DateofConsultationAMV1',
        'DateofConsultationMMR',
        'DateofConsultationFIC',
        'DateofConsultationCIC',
        'DateofConsultationAMV2',
        'DATEOFCONSULTATIONEXCLUSIVEBF',
        'DATEOFCONSULTTT1',
        'DATEOFCONSULTTT2',
        'DATEOFCONSULTTT3',
        'DATEOFCONSULTTT4',
        'DATEOFCONSULTT5'
    ];
    
    $valid_dates = [];
    
    // Collect all valid dates
    foreach ($consultation_dates as $field) {
        if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== '0000-00-00') {
            $valid_dates[] = $data[$field];
        }
    }
    
    // Print earliest date in <p> tag
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        echo '<p>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p>No consultation date recorded</p>';
    }
}

// Alternative function that returns the HTML instead of printing
function getEarliestDateInP($data) {
    $consultation_dates = [
        'DateofConsultationBCG',
        'DateofConsultationPENTAHIB1',
        'DateofConsultationPENTAHIB2', 
        'DateofConsultationPENTAHIB3',
        'DateofConsultationOPV1v',
        'DateofConsultationOPV2',
        'DateofConsultationOPV3',
        'DateofConsultationIPV1',
        'DateofConsultationIPV2',
        'DateofConsultationPCV1',
        'DateofConsultationPCV2',
        'DateofConsultationPCV3',
        'DateofConsultationHEPAatBirth',
        'DateofConsultationHEPAB1',
        'DateofConsultationHEPAB2',
        'DateofConsultationHEPAB3',
        'DateofConsultationHEPAB4',
        'DateofConsultationAMV1',
        'DateofConsultationMMR',
        'DateofConsultationFIC',
        'DateofConsultationCIC',
        'DateofConsultationAMV2',
        'DATEOFCONSULTATIONEXCLUSIVEBF',
        'DATEOFCONSULTTT1',
        'DATEOFCONSULTTT2',
        'DATEOFCONSULTTT3',
        'DATEOFCONSULTTT4',
        'DATEOFCONSULTT5'
    ];
    
    $valid_dates = [];
    
    foreach ($consultation_dates as $field) {
        if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== '0000-00-00') {
            $valid_dates[] = $data[$field];
        }
    }
    
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        return '<p>' . htmlspecialchars($earliest) . '</p>';
    } else {
        return '<p>No consultation date recorded</p>';
    }
}

// Function with custom styling
function printEarliestDateInPStyled($data, $class = '', $style = '') {
    $consultation_dates = [
        'DateofConsultationBCG',
        'DateofConsultationPENTAHIB1',
        'DateofConsultationPENTAHIB2', 
        'DateofConsultationPENTAHIB3',
        'DateofConsultationOPV1v',
        'DateofConsultationOPV2',
        'DateofConsultationOPV3',
        'DateofConsultationIPV1',
        'DateofConsultationIPV2',
        'DateofConsultationPCV1',
        'DateofConsultationPCV2',
        'DateofConsultationPCV3',
        'DateofConsultationHEPAatBirth',
        'DateofConsultationHEPAB1',
        'DateofConsultationHEPAB2',
        'DateofConsultationHEPAB3',
        'DateofConsultationHEPAB4',
        'DateofConsultationAMV1',
        'DateofConsultationMMR',
        'DateofConsultationFIC',
        'DateofConsultationCIC',
        'DateofConsultationAMV2',
        'DATEOFCONSULTATIONEXCLUSIVEBF',
        'DATEOFCONSULTTT1',
        'DATEOFCONSULTTT2',
        'DATEOFCONSULTTT3',
        'DATEOFCONSULTTT4',
        'DATEOFCONSULTT5'
    ];
    
    $valid_dates = [];
    
    foreach ($consultation_dates as $field) {
        if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== '0000-00-00') {
            $valid_dates[] = $data[$field];
        }
    }
    
    $class_attr = $class ? ' class="' . htmlspecialchars($class) . '"' : '';
    $style_attr = $style ? ' style="' . htmlspecialchars($style) . '"' : '';
    
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        echo '<p' . $class_attr . $style_attr . '>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p' . $class_attr . $style_attr . '>No consultation date recorded</p>';
    }
}

// Example usage and testing
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    // Sample data for testing
    $sample_data = [
        'DateofConsultationBCG' => '2023-02-15',
        'DateofConsultationPENTAHIB1' => '2023-03-10',
        'DateofConsultationOPV1v' => '2023-02-10',
        'DateofConsultationPCV1' => '2023-04-05',
        'DateofConsultationMMR' => '2023-05-20',
        // Other fields can be empty or have dates
    ];
    
    echo '<div style="max-width: 600px; margin: 20px auto; font-family: Arial, sans-serif;">';
    echo '<h3>Earliest Consultation Date Test</h3>';
    
    echo '<h4>Method 1: Direct Print</h4>';
    printEarliestDateInP($sample_data);
    
    echo '<h4>Method 2: Get HTML</h4>';
    echo getEarliestDateInP($sample_data);
    
    echo '<h4>Method 3: With Styling</h4>';
    printEarliestDateInPStyled($sample_data, 'earliest-date', 'color: blue; font-weight: bold;');
    
    echo '<h4>Sample Data Used:</h4>';
    echo '<ul>';
    foreach ($sample_data as $field => $date) {
        echo '<li>' . $field . ': ' . $date . '</li>';
    }
    echo '</ul>';
    
    echo '<p><strong>Expected Result:</strong> 2023-02-10 (earliest from the dates above)</p>';
    echo '</div>';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Earliest Date in P Tag</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .earliest-date {
            color: blue;
            font-weight: bold;
        }
        
        .result-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-section">
        <h2>Print Earliest Consultation Date in &lt;p&gt; Tag</h2>
        <p>This function prints the earliest date from the specified consultation date fields in a paragraph tag.</p>
        
        <h3>Usage Examples:</h3>
        
        <h4>1. Simple Usage:</h4>
        <div class="code-block">
&lt;?php
include 'print_earliest_in_p.php';

// Print earliest date in &lt;p&gt; tag
printEarliestDateInP($row);
// Output: &lt;p&gt;2023-02-10&lt;/p&gt;
?&gt;
        </div>
        
        <h4>2. Get HTML String:</h4>
        <div class="code-block">
&lt;?php
$html = getEarliestDateInP($row);
echo $html;
// Output: &lt;p&gt;2023-02-10&lt;/p&gt;
?&gt;
        </div>
        
        <h4>3. With Custom Styling:</h4>
        <div class="code-block">
&lt;?php
printEarliestDateInPStyled($row, 'earliest-date', 'color: blue; font-weight: bold;');
// Output: &lt;p class="earliest-date" style="color: blue; font-weight: bold;"&gt;2023-02-10&lt;/p&gt;
?&gt;
        </div>
        
        <h3>Test the Function:</h3>
        <a href="?test=1" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Run Test</a>
    </div>

    <div class="demo-section">
        <h3>Integration in Your Forms:</h3>
        
        <h4>In any PHP file (like nip.php, print.php, etc.):</h4>
        <div class="code-block">
&lt;?php
// Include the function file
include 'print_earliest_in_p.php';

// In your form or display area
echo '&lt;label&gt;Earliest Consultation Date:&lt;/label&gt;';
printEarliestDateInP($row);
?&gt;
        </div>
        
        <h4>In HTML forms:</h4>
        <div class="code-block">
&lt;div class="form-group"&gt;
    &lt;label&gt;First Consultation Date:&lt;/label&gt;
    &lt;?php printEarliestDateInP($row); ?&gt;
&lt;/div&gt;
        </div>
        
        <h4>In tables:</h4>
        <div class="code-block">
&lt;td&gt;
    &lt;?php printEarliestDateInP($row); ?&gt;
&lt;/td&gt;
        </div>
    </div>

    <div class="demo-section">
        <h3>Features:</h3>
        <ul>
            <li>✅ Uses exact consultation_dates array you specified</li>
            <li>✅ Prints earliest date in &lt;p&gt; tag as requested</li>
            <li>✅ Handles empty/invalid dates gracefully</li>
            <li>✅ HTML escaping for security</li>
            <li>✅ Multiple usage options (print, return, styled)</li>
            <li>✅ Easy to integrate in existing forms</li>
        </ul>
    </div>
</body>
</html>
