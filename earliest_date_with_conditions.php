<?php
/**
 * Function to print earliest date and make conditions based on consultation date fields
 */

function printEarliestDateWithConditions($row) {
    // All consultation date fields
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Collect all valid dates with conditions
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
            $valid_dates[] = $date;
        }
    }
    
    // Print earliest date in <p> tag
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        echo '<p>' . htmlspecialchars($earliest) . '</p>';
        return $earliest;
    } else {
        echo '<p>No consultation date recorded</p>';
        return null;
    }
}

// Function with detailed conditions and analysis
function printEarliestDateWithDetailedConditions($row) {
    // Individual field conditions
    $bcg_date = $row['DateofConsultationBCG'] ?? '';
    $penta1_date = $row['DateofConsultationPENTAHIB1'] ?? '';
    $penta2_date = $row['DateofConsultationPENTAHIB2'] ?? '';
    $penta3_date = $row['DateofConsultationPENTAHIB3'] ?? '';
    $opv1_date = $row['DateofConsultationOPV1v'] ?? '';
    $opv2_date = $row['DateofConsultationOPV2'] ?? '';
    $opv3_date = $row['DateofConsultationOPV3'] ?? '';
    $ipv1_date = $row['DateofConsultationIPV1'] ?? '';
    $ipv2_date = $row['DateofConsultationIPV2'] ?? '';
    $pcv1_date = $row['DateofConsultationPCV1'] ?? '';
    $pcv2_date = $row['DateofConsultationPCV2'] ?? '';
    $pcv3_date = $row['DateofConsultationPCV3'] ?? '';
    $hepa_birth_date = $row['DateofConsultationHEPAatBirth'] ?? '';
    $hepa1_date = $row['DateofConsultationHEPAB1'] ?? '';
    $hepa2_date = $row['DateofConsultationHEPAB2'] ?? '';
    $hepa3_date = $row['DateofConsultationHEPAB3'] ?? '';
    $hepa4_date = $row['DateofConsultationHEPAB4'] ?? '';
    $amv1_date = $row['DateofConsultationAMV1'] ?? '';
    $mmr_date = $row['DateofConsultationMMR'] ?? '';
    $fic_date = $row['DateofConsultationFIC'] ?? '';
    $cic_date = $row['DateofConsultationCIC'] ?? '';
    $amv2_date = $row['DateofConsultationAMV2'] ?? '';
    $exclusive_bf_date = $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '';
    $tt1_date = $row['DATEOFCONSULTTT1'] ?? '';
    $tt2_date = $row['DATEOFCONSULTTT2'] ?? '';
    $tt3_date = $row['DATEOFCONSULTTT3'] ?? '';
    $tt4_date = $row['DATEOFCONSULTTT4'] ?? '';
    $tt5_date = $row['DATEOFCONSULTT5'] ?? '';
    
    // Collect all valid dates
    $all_dates = [
        'BCG' => $bcg_date,
        'PENTA-HIB1' => $penta1_date,
        'PENTA-HIB2' => $penta2_date,
        'PENTA-HIB3' => $penta3_date,
        'OPV1' => $opv1_date,
        'OPV2' => $opv2_date,
        'OPV3' => $opv3_date,
        'IPV1' => $ipv1_date,
        'IPV2' => $ipv2_date,
        'PCV1' => $pcv1_date,
        'PCV2' => $pcv2_date,
        'PCV3' => $pcv3_date,
        'HEPA at Birth' => $hepa_birth_date,
        'HEPA B1' => $hepa1_date,
        'HEPA B2' => $hepa2_date,
        'HEPA B3' => $hepa3_date,
        'HEPA B4' => $hepa4_date,
        'AMV1' => $amv1_date,
        'MMR' => $mmr_date,
        'FIC' => $fic_date,
        'CIC' => $cic_date,
        'AMV2' => $amv2_date,
        'Exclusive BF' => $exclusive_bf_date,
        'TT1' => $tt1_date,
        'TT2' => $tt2_date,
        'TT3' => $tt3_date,
        'TT4' => $tt4_date,
        'TT5' => $tt5_date
    ];
    
    $valid_dates = [];
    $valid_vaccines = [];
    
    // Apply conditions to each date
    foreach ($all_dates as $vaccine => $date) {
        if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
            $valid_dates[] = $date;
            $valid_vaccines[$vaccine] = $date;
        }
    }
    
    // Print results with conditions
    echo '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        $earliest_vaccine = array_search($earliest, $valid_vaccines);
        
        // Print earliest date in <p> tag
        echo '<p style="font-weight: bold; color: #1976d2;">Earliest Consultation Date: ' . htmlspecialchars($earliest) . '</p>';
        echo '<p>First consultation was for: <strong>' . htmlspecialchars($earliest_vaccine) . '</strong></p>';
        
        // Conditions based on earliest date
        if (!empty($row['DateOfBirth'])) {
            $birth_date = $row['DateOfBirth'];
            $days_after_birth = (strtotime($earliest) - strtotime($birth_date)) / (60*60*24);
            
            echo '<p>Consultation was <strong>' . round($days_after_birth) . ' days</strong> after birth</p>';
            
            // Condition: Early consultation (within 30 days)
            if ($days_after_birth <= 30) {
                echo '<p style="color: #4caf50;">✅ <strong>Early consultation</strong> - Within first month</p>';
            } elseif ($days_after_birth <= 60) {
                echo '<p style="color: #ff9800;">⚠️ <strong>Moderate delay</strong> - Within 2 months</p>';
            } else {
                echo '<p style="color: #f44336;">❌ <strong>Late consultation</strong> - After 2 months</p>';
            }
        }
        
        // Condition: Number of consultations
        $consultation_count = count($valid_vaccines);
        echo '<p>Total consultations recorded: <strong>' . $consultation_count . '</strong></p>';
        
        if ($consultation_count >= 10) {
            echo '<p style="color: #4caf50;">✅ <strong>Well documented</strong> - Many consultations</p>';
        } elseif ($consultation_count >= 5) {
            echo '<p style="color: #ff9800;">⚠️ <strong>Moderately documented</strong> - Some consultations</p>';
        } else {
            echo '<p style="color: #f44336;">❌ <strong>Poorly documented</strong> - Few consultations</p>';
        }
        
        // Condition: Check for specific important vaccines
        $important_vaccines = ['BCG', 'PENTA-HIB1', 'OPV1', 'HEPA at Birth'];
        $has_important = 0;
        foreach ($important_vaccines as $vaccine) {
            if (isset($valid_vaccines[$vaccine])) {
                $has_important++;
            }
        }
        
        if ($has_important >= 3) {
            echo '<p style="color: #4caf50;">✅ <strong>Good coverage</strong> - Has important vaccines</p>';
        } else {
            echo '<p style="color: #f44336;">❌ <strong>Missing important vaccines</strong></p>';
        }
        
    } else {
        echo '<p style="color: #f44336;">No consultation dates recorded</p>';
        echo '<p style="color: #f44336;">❌ <strong>No consultations</strong> - Child needs immediate attention</p>';
    }
    
    echo '</div>';
    
    return $valid_dates;
}

// Simple condition checker
function checkConsultationConditions($row) {
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Check each date with conditions
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
            $valid_dates[] = $date;
        }
    }
    
    // Return conditions result
    return [
        'has_consultations' => !empty($valid_dates),
        'earliest_date' => !empty($valid_dates) ? min($valid_dates) : null,
        'consultation_count' => count($valid_dates),
        'all_dates' => $valid_dates
    ];
}

// Example usage and testing
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    // Sample data for testing
    $sample_row = [
        'DateOfBirth' => '2023-01-15',
        'NameOfChild' => 'Juan',
        'LastNameOfChild' => 'Dela Cruz',
        'DateofConsultationBCG' => '2023-02-15',
        'DateofConsultationPENTAHIB1' => '2023-03-10',
        'DateofConsultationPENTAHIB2' => '2023-04-15',
        'DateofConsultationPENTAHIB3' => '2023-05-20',
        'DateofConsultationOPV1v' => '2023-02-10', // This will be earliest
        'DateofConsultationOPV2' => '2023-03-15',
        'DateofConsultationOPV3' => '2023-04-20',
        'DateofConsultationIPV1' => '2023-03-25',
        'DateofConsultationIPV2' => '2023-05-10',
        'DateofConsultationPCV1' => '2023-04-05',
        'DateofConsultationPCV2' => '2023-05-15',
        'DateofConsultationPCV3' => '2023-06-20',
        'DateofConsultationHEPAatBirth' => '2023-01-20', // Actually this would be earliest
        'DateofConsultationHEPAB1' => '',
        'DateofConsultationHEPAB2' => '',
        'DateofConsultationHEPAB3' => '',
        'DateofConsultationHEPAB4' => '',
        'DateofConsultationAMV1' => '2023-07-10',
        'DateofConsultationMMR' => '2023-08-15',
        'DateofConsultationFIC' => '',
        'DateofConsultationCIC' => '',
        'DateofConsultationAMV2' => '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => '',
        'DATEOFCONSULTTT1' => '',
        'DATEOFCONSULTTT2' => '',
        'DATEOFCONSULTTT3' => '',
        'DATEOFCONSULTTT4' => '',
        'DATEOFCONSULTT5' => ''
    ];
    
    echo '<div style="max-width: 800px; margin: 20px auto; font-family: Arial, sans-serif;">';
    echo '<h3>Earliest Date with Conditions - Test Results</h3>';
    
    echo '<h4>Method 1: Simple Print</h4>';
    printEarliestDateWithConditions($sample_row);
    
    echo '<h4>Method 2: Detailed Analysis</h4>';
    printEarliestDateWithDetailedConditions($sample_row);
    
    echo '<h4>Method 3: Condition Check</h4>';
    $conditions = checkConsultationConditions($sample_row);
    echo '<pre>' . print_r($conditions, true) . '</pre>';
    
    echo '</div>';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Earliest Date with Conditions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .result-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-section">
        <h2>Print Earliest Date with Conditions</h2>
        <p>Functions that print the earliest consultation date and apply conditions based on all consultation date fields.</p>
        
        <h3>Usage Examples:</h3>
        
        <h4>1. Simple Print with Conditions:</h4>
        <div class="code-block">
&lt;?php
include 'earliest_date_with_conditions.php';

// Print earliest date in &lt;p&gt; tag
printEarliestDateWithConditions($row);
// Output: &lt;p&gt;2023-01-20&lt;/p&gt;
?&gt;
        </div>
        
        <h4>2. Detailed Analysis with Conditions:</h4>
        <div class="code-block">
&lt;?php
// Print detailed analysis with conditions
printEarliestDateWithDetailedConditions($row);
// Shows earliest date, vaccine type, timing analysis, etc.
?&gt;
        </div>
        
        <h4>3. Condition Checking:</h4>
        <div class="code-block">
&lt;?php
$conditions = checkConsultationConditions($row);

if ($conditions['has_consultations']) {
    echo "Earliest consultation: " . $conditions['earliest_date'];
    echo "Total consultations: " . $conditions['consultation_count'];
} else {
    echo "No consultations recorded";
}
?&gt;
        </div>
        
        <h3>Test the Functions:</h3>
        <a href="?test=1" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Run Test</a>
    </div>

    <div class="demo-section">
        <h3>All Consultation Date Fields in Conditions:</h3>
        <div style="columns: 2; column-gap: 20px; font-size: 13px;">
            <ul>
                <li>$row['DateofConsultationBCG']</li>
                <li>$row['DateofConsultationPENTAHIB1']</li>
                <li>$row['DateofConsultationPENTAHIB2']</li>
                <li>$row['DateofConsultationPENTAHIB3']</li>
                <li>$row['DateofConsultationOPV1v']</li>
                <li>$row['DateofConsultationOPV2']</li>
                <li>$row['DateofConsultationOPV3']</li>
                <li>$row['DateofConsultationIPV1']</li>
                <li>$row['DateofConsultationIPV2']</li>
                <li>$row['DateofConsultationPCV1']</li>
                <li>$row['DateofConsultationPCV2']</li>
                <li>$row['DateofConsultationPCV3']</li>
                <li>$row['DateofConsultationHEPAatBirth']</li>
                <li>$row['DateofConsultationHEPAB1']</li>
                <li>$row['DateofConsultationHEPAB2']</li>
                <li>$row['DateofConsultationHEPAB3']</li>
                <li>$row['DateofConsultationHEPAB4']</li>
                <li>$row['DateofConsultationAMV1']</li>
                <li>$row['DateofConsultationMMR']</li>
                <li>$row['DateofConsultationFIC']</li>
                <li>$row['DateofConsultationCIC']</li>
                <li>$row['DateofConsultationAMV2']</li>
                <li>$row['DATEOFCONSULTATIONEXCLUSIVEBF']</li>
                <li>$row['DATEOFCONSULTTT1']</li>
                <li>$row['DATEOFCONSULTTT2']</li>
                <li>$row['DATEOFCONSULTTT3']</li>
                <li>$row['DATEOFCONSULTTT4']</li>
                <li>$row['DATEOFCONSULTT5']</li>
            </ul>
        </div>
    </div>
</body>
</html>
