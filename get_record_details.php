<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Check if ID is provided
if (!isset($_POST['id']) || empty($_POST['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Record ID is required']);
    exit;
}

include 'db.php';

$id = intval($_POST['id']);
$health_center = $_SESSION['health_center'];

// Prepare SQL query based on health center
if ($health_center == 'CITY HEALTH OFFICE') {
    $sql = "SELECT * FROM nip_table WHERE id = ? AND deleted = 0";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
} else {
    $sql = "SELECT * FROM nip_table WHERE id = ? AND Barangay = ? AND deleted = 0";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("is", $id, $health_center);
}

$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    
    // Format dates for better display
    if (!empty($row['DateOfRegistration'])) {
        $row['DateOfRegistration'] = date('F d, Y', strtotime($row['DateOfRegistration']));
    }
    if (!empty($row['DateOfBirth'])) {
        $row['DateOfBirth'] = date('F d, Y', strtotime($row['DateOfBirth']));
    }
    if (!empty($row['BirthdateofMother'])) {
        $row['BirthdateofMother'] = date('F d, Y', strtotime($row['BirthdateofMother']));
    }
    if (!empty($row['DateBCGwasgiven'])) {
        $row['DateBCGwasgiven'] = date('F d, Y', strtotime($row['DateBCGwasgiven']));
    }
    if (!empty($row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'])) {
        $row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'] = date('F d, Y', strtotime($row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED']));
    }
    
    // Return the record data as JSON
    header('Content-Type: application/json');
    echo json_encode($row);
} else {
    http_response_code(404);
    echo json_encode(['error' => 'Record not found']);
}

$stmt->close();
$conn->close();
?>
