<?php
session_start();
include 'db.php';

// Check if user is logged in and has admin privileges
if (!isset($_SESSION['health_center'])) {
    http_response_code(401);
    echo "Unauthorized access";
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $user_id = intval($_POST["user_id"]);
    
    // Validate input
    if (empty($user_id)) {
        echo "Invalid user ID";
        exit();
    }
    
    try {
        // Begin transaction
        $conn->begin_transaction();
        
        // Update user status to pending (2) and clear disapproval data
        $sql = "UPDATE health_facility SET
                approved = 2,
                disapproved_date = NULL,
                disapproval_reason = NULL,
                approved_date = NULL
                WHERE id = ? AND approved = 0";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        $stmt->bind_param("i", $user_id);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }
        
        if ($stmt->affected_rows === 0) {
            throw new Exception("No disapproved user found with the given ID or user is not disapproved");
        }
        
        // Log the action in audit table (if table exists)
        $audit_sql = "INSERT INTO user_action_logs (user_id, table_name, action_type, action_date, performed_by, reason, ip_address, user_agent) 
                      VALUES (?, 'health_facility', 'REACTIVATED', NOW(), ?, 'User reactivated from disapproved to pending status', ?, ?)";
        
        $audit_stmt = $conn->prepare($audit_sql);
        if ($audit_stmt) {
            $performed_by = $_SESSION['username'] ?? $_SESSION['health_center'] ?? 'Admin';
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
            
            $audit_stmt->bind_param("isss", $user_id, $performed_by, $ip_address, $user_agent);
            $audit_stmt->execute();
            $audit_stmt->close();
        }
        
        // Commit transaction
        $conn->commit();
        
        $stmt->close();
        echo "success";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        error_log("Reactivate user error: " . $e->getMessage());
        echo "Error: " . $e->getMessage();
    }
    
} else {
    echo "Invalid request method";
}

$conn->close();
?>
