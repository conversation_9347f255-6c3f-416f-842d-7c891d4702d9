<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
   
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>


    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
 
  
    <title>National Immunization Program</title>
   <?php include 'style.php'; ?>
   <style>
  
  .autocomplete-content {
       list-style-type: none;
       padding: 0;
       margin: 0;
       border: 1px solid #e0e0e0;
       max-height: 150px;
       overflow-y: auto;
       position: absolute;
       background-color: white;
       z-index: 1000;
       box-shadow: 0 2px 4px rgba(0,0,0,0.1);
   }

   .autocomplete-content li {
       padding: 8px 12px;
       cursor: pointer;
   }

   .autocomplete-content li:hover {
       background-color: #f5f5f5;
   }

   label {
  
       font-family:system-ui;
       font-size: 16.5px !important;
       color: black !important;
   }

   .tab a.active {
       background-color: transparent;
       color: #1976d2 !important;
       font-weight: 500;
   }

   .tabs .indicator {
       position: absolute;
       bottom: 0;
       height: 3px;
       background-color: #1976d2;
       will-change: left, right;
   }

   .tab a {
       color: #424242 !important;
   }

   .input-field label {
       
   }

   .input-field input[type=text]:focus,
   .input-field input[type=date]:focus,
   .input-field input[type=number]:focus {
       border-bottom: 1px solid #1976d2 !important;
       box-shadow: 0 1px 0 0 #1976d2 !important;
   }

   .input-field .prefix.active {
       color: #1976d2;
     
   }

   [type="radio"]:checked + span:after,
   [type="radio"].with-gap:checked + span:before,
   [type="radio"].with-gap:checked + span:after {
       border: 2px solid #1976d2;
   }

   [type="radio"]:checked + span:after,
   [type="radio"].with-gap:checked + span:after {
       background-color: #039be5;
   }

   .btn {
       background-color: #1976d2;
   }

   .btn:hover {
       background-color: #1565c0;
   }

   .chip {
       background-color: #1976d2;
   }

   .section-header {
       background-color: #1976d2;
   }

   .section-header h5 {
       color: white;
   }

   .z-depth-1 {
       box-shadow: 0 2px 5px rgba(0,0,0,0.1);
   }

   .blue-grey {
       background-color: #1976d2 !important;
   }

   .black-text {
       color: #424242 !important;
   }

   .white-text {
       color: white !important;
   }

   .alert {
       background-color: #e8f5e9;
       color: #2e7d32;
   }

   /* Error styling for required fields */
   .error {
       border-bottom: 1px solid #f44336 !important;
       box-shadow: 0 1px 0 0 #f44336 !important;
   }

   .error + label {
       color: #f44336 !important;
   }

   /* Error styling for select elements */
   .select-wrapper.error .select-dropdown {
       border-bottom: 2px solid #f44336 !important;
       box-shadow: 0 1px 0 0 #f44336 !important;
   }

   /* Submit button disabled state */
   #submit_btn:disabled {
       opacity: 0.6 !important;
       cursor: not-allowed !important;
       pointer-events: none !important;
   }
</style>
</head>
<body  style="background: #f8fafc;">
    <?php include 'nav.php'; ?>

    <script>
document.addEventListener('DOMContentLoaded', function() {
  // initialize Materialize select
  const elems = document.querySelectorAll('select');
  M.FormSelect.init(elems);

  // Age calculation on birthdate change
  const birthDateInput = document.getElementById('BirthdateofMother');
  const ageInput = document.getElementById('AgeofMother');
  const ageError = document.getElementById('ageError');

  birthDateInput.addEventListener('change', function() {
    const birthDate = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    ageInput.value = age;

    // Show error if age is 0-10
    if (age >= 0 && age <= 10) {
      ageError.style.display = 'block';
      ageError.textContent = 'Age must be greater than 10.';
    } else {
      ageError.style.display = 'none';
      ageError.textContent = '';
    }
  });

  // Enhanced toast notification function
  function showToast(message, type = 'info', duration = 4000) {
    let classes = 'blue darken-1'; // default
    let icon = 'info';

    switch(type) {
      case 'success':
        classes = 'green darken-1';
        icon = 'check_circle';
        break;
      case 'error':
        classes = 'red darken-1';
        icon = 'error';
        break;
      case 'warning':
        classes = 'orange darken-1';
        icon = 'warning';
        break;
      case 'loading':
        classes = 'blue darken-1';
        icon = 'hourglass_empty';
        break;
    }

    M.toast({
      html: `<i class="material-icons left">${icon}</i>${message}`,
      classes: classes,
      displayLength: duration
    });
  }

  // Function to show toast notification for required fields
  function showRequiredFieldToast(fieldName, element) {
    showToast(`${fieldName} is required`, 'error');

    // Add error styling
    if (element) {
      element.classList.add('error');
      element.focus();
    }
  }

  // Function to validate required fields
  function validateRequiredFields() {
    const requiredFields = [
      { name: 'DateOfBirth', label: 'Date of Birth', required: true },
      { name: 'NameOfChild', label: 'First Name of Child', required: true },
      { name: 'LastNameOfChild', label: 'Last Name of Child', required: true },
      { name: 'Sex', label: 'Sex', required: true },
      { name: 'NameofMother', label: 'Name of Mother', required: true },
      { name: 'Barangay', label: 'Barangay', required: true },
      { name: 'PlaceofDelivery', label: 'Place of Delivery', required: true },
      { name: 'Attendant', label: 'Attendant', required: true },
      { name: 'TypeofDelivery', label: 'Type of Delivery', required: true },
      { name: 'BirthWeightInGrams', label: 'Birth Weight', required: true }
    ];

    // Clear previous error styles
    document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));

    for (let field of requiredFields) {
      if (!field.required) continue;

      const element = document.querySelector(`[name="${field.name}"]`);

      if (!element) {
        console.warn(`Field ${field.name} not found in form`);
        continue;
      }

      // Check different input types
      if (element.type === 'radio') {
        const radioGroup = document.querySelectorAll(`[name="${field.name}"]`);
        const isChecked = Array.from(radioGroup).some(radio => radio.checked);
        if (!isChecked) {
          showRequiredFieldToast(field.label, radioGroup[0]);
          return false;
        }
      } else if (element.tagName === 'SELECT') {
        if (!element.value || element.value === '' || element.value === 'Choose your option') {
          showRequiredFieldToast(field.label, element);
          return false;
        }
      } else {
        if (!element.value || element.value.trim() === '') {
          showRequiredFieldToast(field.label, element);
          return false;
        }
      }
    }

    return true;
  }

  const form = document.getElementById('myForm');
  const submitButton = document.getElementById('submit_btn');

  // Ensure form and button exist
  if (!form) {
    console.error('Form with ID "myForm" not found');
    return;
  }

  if (!submitButton) {
    console.error('Submit button with ID "submit_btn" not found');
    return;
  }

  // Add event listener to the form submit event
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    handleFormSubmission();
  });

  // Add event listener to the submit button click event
  submitButton.addEventListener('click', function(e) {
    e.preventDefault();
    handleFormSubmission();
  });

  // Function to handle form submission logic
  function handleFormSubmission() {
    console.log('Form submission started');

    // Disable submit button to prevent double submission
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="material-icons right">hourglass_empty</i>Processing...';

    // Validate all required fields
    if (!validateRequiredFields()) {
      console.log('Validation failed');
      // Re-enable submit button if validation fails
      submitButton.disabled = false;
      submitButton.innerHTML = '<i class="material-icons right">send</i>Submit';
      return;
    }

    // Check age validation
    const ageInput = document.querySelector('[name="Age"]');
    const birthDateInput = document.querySelector('[name="BirthdateofMother"]');

    if (ageInput && birthDateInput) {
      const age = parseInt(ageInput.value, 10);
      if (!isNaN(age) && age >= 0 && age <= 10) {
        showToast('Mother\'s age must be greater than 10', 'error');
        birthDateInput.classList.add('error');
        birthDateInput.focus();

        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="material-icons right">send</i>Submit';
        return;
      }
    }

    console.log('Validation passed, submitting form');
    // If validation passes, submit the form via AJAX
    submitFormData();
  }

  // Function to submit form data via AJAX
  function submitFormData() {
    try {
      const formData = new FormData(form);

      // Add the submit button name to ensure PHP processes the form
      formData.append('submitBtn', 'Submit');

      // Add default values for immunization fields that might be empty
      const immunizationFields = [
        'BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3', 'OPV1', 'OPV2', 'OPV3',
        'IPV1','IPV2','PCV1','PCV2','PCV3', 
        'HEPAatBirth', 'HEPAB1', 'HEPAB2', 'HEPAB3', 'HEPAB4',
        'AMV19monthstobelow12months', 'MMR12MOSTO15MOS', 'FIC', 'CIC',
        'AMV2_16MOSTO5YRSOLD', 'IMMUNIZATIONSTATUS'
      ];

      immunizationFields.forEach(field => {
        if (!formData.get(field)) {
          formData.set(field, 'NO');
        }
      });

      // Add default values for other optional fields
      const optionalFields = [
        'FamilySerialNumber', 'ChildNumber', 'middlename_of_child',
        'PurokStreetSitio', 'HouseNo', 'Address', 'NameofFacility',
        'WastheChildReferredforNewbornScreening', 'TTStatusofMother',
        'WastheChildProtectedatBirth', 'PhoneNumber'
      ];

      optionalFields.forEach(field => {
        if (!formData.get(field)) {
          formData.set(field, '');
        }
      });

      // Show loading toast
      showToast('Submitting form...', 'loading', 2000);

      // Debug: Log form data being sent
      console.log('Form data being sent:');
      for (let [key, value] of formData.entries()) {
        console.log(key + ': ' + value);
      }

      // Submit via AJAX
      fetch('db.php', {
        method: 'POST',
        body: formData
      })
      .then(response => {
        console.log('Response status:', response.status);
        return response.text();
      })
      .then(data => {
        console.log('Server response:', data);

        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="material-icons right">send</i>Submit';

        // Check if submission was successful
        if (data.includes('Record Successfully Added') || data.includes('successfully')) {
          showToast('Record successfully added!', 'success');

          // Reset the form after successful submission
          form.reset();
          M.updateTextFields();
          M.FormSelect.init(document.querySelectorAll('select'));

          // Clear any error styles
          document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));

          // Show additional success info
          setTimeout(() => {
            showToast('Form has been reset for new entry', 'info', 3000);
          }, 1500);

        } else if (data.includes('Duplicate record found')) {
          showToast('Duplicate record found! This child is already registered.', 'warning');

        } else if (data.includes('required')) {
          // Extract the specific error message
          let errorMessage = 'Please check the required fields and try again.';

          if (data.includes('Date of Birth') && data.includes('required')) {
            errorMessage = 'Date of Birth is required';
          } else if (data.includes('Name Of Child') && data.includes('required')) {
            errorMessage = 'Name of Child is required';
          } else if (data.includes('LastNameOfChild') && data.includes('required')) {
            errorMessage = 'Last Name of Child is required';
          } else if (data.includes('Sex') && data.includes('required')) {
            errorMessage = 'Sex is required';
          } else if (data.includes('NameofMother') && data.includes('required')) {
            errorMessage = 'Name of Mother is required';
          } else if (data.includes('Barangay') && data.includes('required')) {
            errorMessage = 'Barangay is required';
          } else if (data.includes('PlaceofDelivery') && data.includes('required')) {
            errorMessage = 'Place of Delivery is required';
          } else if (data.includes('Attendant') && data.includes('required')) {
            errorMessage = 'Attendant is required';
          } else if (data.includes('TypeofDelivery') && data.includes('required')) {
            errorMessage = 'Type of Delivery is required';
          } else if (data.includes('BirthWeightInGrams') && data.includes('required')) {
            errorMessage = 'Birth Weight is required';
          }

          showToast(errorMessage, 'error');

        } else if (data.includes('Duplicate Child Found') || data.includes('already registered')) {
          showToast('Registration blocked - This child is already registered!', 'error', 8000);

          // Show additional guidance
          setTimeout(() => {
            showToast('Please check existing records or register a different child.', 'warning', 6000);
          }, 2000);

        } else if (data.includes('Duplicate Check Warning')) {
          showToast('Warning: Could not complete duplicate check. Please verify manually.', 'warning', 6000);

        } else if (data.includes('Error:') || data.includes('mysqli_error')) {
          showToast('Database error occurred. Please try again.', 'error');

        } else if (data.trim() === '') {
          showToast('No response from server. Please try again.', 'error');

        } else {
          showToast('Form submitted. Please check the results.', 'info');
        }
      })
      .catch(error => {
        console.error('Network error:', error);

        // Re-enable submit button on error
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="material-icons right">send</i>Submit';

        showToast('Network error. Please check your connection and try again.', 'error');
      });

    } catch (error) {
      console.error('Form submission error:', error);

      // Re-enable submit button on error
      submitButton.disabled = false;
      submitButton.innerHTML = '<i class="material-icons right">send</i>Submit';

      showToast('An error occurred while submitting the form. Please try again.', 'error');
    }
  }
});
</script>

   
<div class="center-align" style="position: relative; margin-top: 40px; margin-bottom: 20px;">
    <div style="height: 4px; background-color:#0d47a1; width: 50%; position: absolute; top: 24px; z-index: 1;"></div>
    
    <div style="display: inline-block; position: relative; z-index: 2;">
      <div style="background-color:#0d47a1  ; border-radius: 50%; width: 43px; height: 43px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
        <i class="material-icons white-text">description</i> <!-- form icon -->
      </div>
      <div style="color:#0d47a1 ; font-weight:  normal;font-size:16.6px; margin-top: 5px;">Data Entry</div>
    </div>
  </div>

  
      <div id="tab1" class=" ">
      
      <div class="row ">
        
      <div class="col s12 m10 offset-m1"  >
        <form action="" id="myForm" class="name white" method="post" class="col s12 z-depth-1">
            
         
          <?php include 'db.php'; ?>
          
          <div class="z-depth-1 ">
        
            <h5 class="  white-text blue-grey darken-3" style="border-radius:5px;padding:20px; font-size:15.5px;font-weight:normal !important;"><i class="material-icons left">note_add</i>Target Client List for Children Under 1 Year Old</h5>
            <div class="row  " style="padding:20.1px;margin-bottom:30px !important; "> 
          
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">Information of Child</h5>
        
       
         
           
            <div class="input-field col s12 m4  ">
              <input type="date"   name="DateOfBirth" id="DateOfBirth"     >
              <label for="DateOfBirth">Date of Birth</label>
            </div>
            <div class="input-field col s12 m4 ">
              <input  id="AgeofChild" placeholder="" class=""  name="AgeofChild"   type="number"  readonly>
              <label for="AgeofChild">Age of Child</label>
            </div>
            
            


            <div class="input-field col s12 m3">
              <input   id="LastNameOfChild" placeholder=""  name="LastNameOfChild"  type="text"  >
              <label for="LastNameOfChild">Last Name of Child</label>
            </div>
            <div class="input-field col s12 m3">
              <input   id="NameOfChild" placeholder=""  name="NameOfChild"  type="text"  >
              <label for="NameOfChild">First Name of Child</label>
            </div>
            <div class="input-field col s12 m3">
              <input   id="middlename_of_child" placeholder=""  name="middlename_of_child"  type="text"  >
              <label for="middlename_of_child">Middle Name of Child</label>
            </div>
             
            <div class="input-field col s12 m2">
              <select name="Sex" id="Sex" required>
                <option value=""></option>
                <option value="MALE">MALE</option>
                <option value="FEMALE">FEMALE</option>
              </select>
               
              <label for="Sex">Sex</label>
            </div>
            </div>
           
            <div class="row  " style="padding:20.1px; "> 
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">Information of Mother</h5>

            <div class="input-field col s12 m4">
              <input placeholder=""  id="NameofMother" name="NameofMother" type="text"  >
              <label for="NameofMother">Name of Mother</label>
            </div>
            <div class="input-field col s12 m2">
              <input  id="BirthdateofMother" name="BirthdateofMother" placeholder="" type="date"  >
              <label for="BirthdateofMother">Birthdate of Mother</label>
            </div>
            <div class="input-field col s12 m2">
              <input  id="AgeofMother" name="AgeeofMother" placeholder="" type="text"  readonly>
              <label for="AgeofMother">Age of Mother</label>
            </div>
            <?php if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') { ?>
              <div class="input-field col s12 m4">
              <select class="browser" id="Barangay" name="Barangay" placeholder=""  >
            <option value=""></option>
                <?php
                $sql = "SELECT * FROM facility_list ";
                $result = $conn->query($sql);
                    while ($row = $result->fetch_assoc()) {
                        echo '<option value="'.$row["facility_name"].'">'.$row["facility_name"].'</option>';
                    } 
                ?>  
        </select>
        <label for="Barangay">Barangay</label>
                  </div>
              <?php } 
                 else { ?>
            <div class="input-field col s12 m4">
              <input  id="Barangay" name="Barangay" value="<?php echo $_SESSION['health_center']; ?>" type="text" readonly>
              <label for="Barangay">Barangay</label>
            </div>
            <?php } ?>
            <div class="input-field col s12 m3">
              <input  id="HouseNo" name="HouseNo"   type="text" placeholder="" >
              <label for="HouseNo">House No.</label>
            </div>
            <div class="input-field col s12 m2">
              <input list="address" id="PurokStreetSitio" oninput="myFunction()"  name="PurokStreetSitio" type="text" placeholder="" >
              <label for="PurokStreetSitio">Purok/Sitio</label>
              <datalist id="address">
              <?php 
 if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {

   $sql = "SELECT street_Name FROM street";
 }
 else {
   $sql = "SELECT street_Name FROM street WHERE healthCenter = '".$_SESSION['health_center']."'";

 }
$stmt = $conn->prepare($sql);
 
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows > 0) {
while ($row = $result->fetch_assoc()) {
   echo '<option value="'.$row['street_Name'].'">';
}
}

?>
</datalist>
            </div>
            
            <div class="input-field col s12 m7">
              <input placeholder="" id="Address" name="Address" type="text"  autocomplete="off"  readonly>
              <label for="Address">Address</label>
              
            </div>
        </div>
      </div>



        </div>
         

        
        <div class="col s12 m10 offset-m1"  >
          <div class="z-depth-1 white">
          <div class="row  " style="padding:20.1px;"> 
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">Post Partum Information</h5>
            
            <div class="input-field col s12 m12">
              
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">Place of Delivery</h5>
                <p>
                  <label >
                    <input class="with-gap" name="PlaceofDelivery" value="HOSPITAL" type="radio"   />
                    <span>HOSPITAL</span>
                  </label>
                
                  <label>
                    <input class="with-gap" name="PlaceofDelivery" value="BIRTHING-HOME" type="radio" />
                    <span>BIRTHING-HOME</span>
                  </label>
              
                  <label>
                    <input class="with-gap" name="PlaceofDelivery"  value="HOME" type="radio"  />
                    <span>HOME</span>
                  </label>
               
                  <label>
                    <input class="with-gap" name="PlaceofDelivery" value="OTHER" type="radio"   />
                    <span>OTHER</span>
                  </label>
                </p>
      
             
            </div>
            <div class="input-field col s12 m12">
              <input  id="NameofFacility" placeholder="" name="NameofFacility"   autocomplete="off" type="text"  >
              <label for="NameofFacility">Name of Facility</label>
              <ul id="hospitalSuggestions" class="autocomplete-content  blue lighten-5 blue-text"></ul>
            </div>
            <div class="input-field col s12">
             
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">Attendant</h5>
              <p>
                <label >
                  <input class="with-gap" name="Attendant" value="DOCTOR" type="radio"   />
                  <span>DOCTOR</span>
                </label>
              
                <label>
                  <input class="with-gap" name="Attendant" value="NURSE" type="radio" />
                  <span>NURSE</span>
                </label>
            
                <label>
                  <input class="with-gap" name="Attendant" value="MIDWIFE"  type="radio"  />
                  <span>MIDWIFE</span>
                </label>
            
                <label>
                  <input class="with-gap" name="Attendant" value="HILOT" type="radio"   />
                  <span>HILOT</span>
                </label>
              
                <label>
                  <input class="with-gap" name="Attendant" value="OTHER" type="radio"   />
                  <span>OTHER</span>
                </label>
              </p>
            </div>
            <div class="input-field col s12">
            
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">Type of Delivery</h5>
              <p> 
                <label>
                  <input class="with-gap" name="TypeofDelivery" value="NSD" type="radio"   />
                  <span>NSD</span>
                </label>&nbsp&nbsp
                <label>
                  <input class="with-gap" name="TypeofDelivery" value="CS" type="radio"   />
                  <span>CS</span>
                </label>&nbsp&nbsp
                <label>
                  <input class="with-gap" name="TypeofDelivery" value="OTHER" type="radio"   />
                  <span>OTHER</span>
                </label>
              </p>
            </div>
            <div class="input-field col s12 m6">
              <input placeholder="" id="BirthWeightInGrams" name="BirthWeightInGrams" type="text"  oninput="classifyBirthWeight()" required>
              <label for="BirthWeightInGrams">Birth Weight(In Grams)</label>
            </div>
            <div class="input-field col s12 m6">
            <input placeholder="" id="BirthWeightClassification" name="BirthWeightClassification" type="text"  readonly>
              <label for="BirthWeightClassification">BirthWeight Classification</label>
            </div>
            <div class=" col s11 m10 alert teal lighten-5  teal-text" style="border-radius:3px;padding:5px;margin-left: 12px;border:1px solid #b2dfdb;">
              <small class="" style="font-size:13.8px;">&nbsp&nbsp <b>Note:</b>Birthweight <2000 Grams Should be Given HEPA B4</small>
            </div>
          </div>

          
    
          <div class="row" style="padding:20.1px;"> 
          <h5 class="white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">NB Screening</h5>
            <div class="input-field col s12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">Was the Child Referred for Newborn Screening?</h5>
              <p> 
                <label>
                  <input class="with-gap" name="WastheChildReferredforNewbornScreening" value="YES" type="radio"   />
                  <span>YES</span>
                </label>&nbsp 
                <label>
                  <input class="with-gap" name="WastheChildReferredforNewbornScreening" value="NO" type="radio"   />
                  <span>NO</span>
                </label> 
              </p>
            </div>
            <div class="input-field col s12 m12">
              <input  id="DateReferredforNewbornScreening"  name="DateReferredforNewbornScreening"    type="date"  >
              <label for="DateReferredforNewbornScreening">Date Referred for Newborn Screening</label>
            </div>
            <div class="input-field col s12 m12">
              <input  id="DateofConsultationNewbornScreening" name="DateofConsultationNewbornScreening" type="date"  >
              <label for="DateofConsultationNewbornScreening">Date of Consultation(Newborn Screening)</label>
            </div>
            
          </div>
          
          <div class="row  " style="padding:20.1px;"> 
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">Child Protected at Birth?</h5>
            <div class="input-field col s12 m12">
              
         
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT Status of Mother</h5>
<p> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox" onclick="showTTStatusResetBtn()" value="TT1" type="radio"/>
                    <span>TT1</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox2" value="TT2 LESS THAN 1 MONTH" type="radio"/>
                    <span>TT2 LESS THAN 1 MONTH</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox3" value="TT2 MORE THAN 1 MONTH" type="radio"/>
                    <span>TT2 MORE THAN 1 MONTH</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox4" value="TT3" type="radio"/>
                    <span>TT3</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox5" value="TT4" type="radio"/>
                    <span>TT4</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox6" value="TT5" type="radio"/>
                    <span>TT5</span>
                  </label> 
                  <label>
                    <input class="with-gap" name="TTStatusofMother" id="myCheckbox7" value="TT5 PLUS" type="radio"/>
                    <span>TT5 PLUS</span>
                  </label> 
                </p>
           
                <button id="resetWas" onclick="resetTTStatus()" type="button" class="small-btn btn red lighten-1" style="display: none;">Clear</button>
 
            </div>
            <div class="input-field col s12 m5">
              <input  id="Dateassessed"  name="Dateassessed"  type="date"  >
              <label for="Dateassessed">Date Assessed</label>
            </div>
            <div class="input-field col s12 m7">
              <input  id="WastheChildProtectedatBirth" placeholder="" class="blue-grey lighten-5"   name="WastheChildProtectedatBirth"  type="text" readonly >
              <label for="WastheChildProtectedatBirth">Was the Child Protected at Birth?</label>
            </div>
            <div class="input-field col s12 m5">
              <input  id="DateofConsultationCPAB"  name="DateofConsultationCPAB"    type="date"  >
              <label for="DateofConsultationCPAB">Date of Consultation(CPAB)</label>
            </div>
         
             
             
            
          </div>
       
        </div>
      </div>
      </div>


      </div>
     





<br>
      <div id="tab2"  >
        <div class="row ">
        
        <div class="col s12 m10 offset-m1"  >
            
             
              <div class="z-depth-1 white">
              <div class="row  " style="padding:20.1px;"> 
                
              <h5 class="white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">BCG Antigen</h5>
              <div class="input-field col s12 m12 ">
                <div class="input-field col s12 m12 ">
                  <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">BCG</h5>
                  <p >
                      <label>
                          <input onclick="isChecked()" class="with-gap" id="BCG_YES" name="BCG" value="YES" type="radio" />
                          <span>YES</span>
                      </label>&nbsp;&nbsp;
                      <label>
                          <input onclick="isChecked()" class="with-gap" id="BCG_NO" name="BCG" value="NO" type="radio" />
                          <span>NO</span>
                      </label>
                     <label for="">
                      <button id="resetBtn" onclick="resetBcg()" type="button" class="small-btn btn red lighten-1 with-gap" style="display: none;">Clear</button>
                     </label>

                      
                 
                  </p>
              </div>
              


        
              
              </div>
              
              <div class="input-field col s12 m6">
                <input type="date" name="DateBCGwasgiven" id="DateBCGwasgiven" class="datepicker"  >
                <label for="DateBCGwasgiven">Date BCG was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationBCG" name="DateofConsultationBCG" type="date"  >
                <label for="DateofConsultationBCG">Date of Consultation(BCG)</label>
              </div>





              <div class=" col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">PENTA-HIB Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">PENTA-HIB1</h5>
              <p> 
                <label>
                  <input class="with-gap" name="PENTAHIB1" value="YES" type="radio" onclick="isCheckedPenta()" />
                  <span>YES</span>
                </label>&nbsp;&nbsp;
                <label>
                  <input class="with-gap" name="PENTAHIB1" value="NO" type="radio" onclick="isCheckedPenta()" />
                  <span>NO</span>
                </label> 
              </p>
              
              <button id="resetPentaBtn" onclick="resetPenta()" type="button" class="small-btn btn red lighten-1 with-gap" style="display: none;">Clear</button>
              
              
              </div>
              <div class="input-field col s12 m6">
                <input  placeholder="" name="DatePenta1wasgiven" id="DatePenta1wasgiven" type="date"  >
                <label for="DatePenta1wasgiven">Date PENTA-HIB1 was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationPENTAHIB1" name="DateofConsultationPENTAHIB1" type="date"  >
                <label for="DateofConsultationPENTAHIB1">Date of Consultation (PENTA-HIB1)</label>
              </div>
              


              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">PENTA-HIB2</h5>
              <p> 
                <label>
                  <input class="with-gap" name="PENTAHIB2" value="YES" type="radio" onclick="isCheckedPenta2()" />
                  <span>YES</span>
                </label>&nbsp;&nbsp;
                <label>
                  <input class="with-gap" name="PENTAHIB2" value="NO" type="radio" onclick="isCheckedPenta2()" />
                  <span>NO</span>
                </label> 
              </p>
              
              <button id="resetPenta2Btn" onclick="resetPenta2()" type="button" class="small-btn btn red lighten-1 with-gap" style="display: none;">Clear</button>
              
                
              </div>
              <div class="input-field col s12 m6">
                <input   id="DatePentahib2wasgiven" name="DatePentahib2wasgiven" type="date"  >
                <label for="DatePentahib2wasgiven">Date Penta-HIB2 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input    id="DateofConsultationPENTAHIB2" name="DateofConsultationPENTAHIB2" type="date"  >
                <label for="DateofConsultationPENTAHIB2">Date of Consultation (PENTA-HIB2)</label>
              </div>

              <div class="input-field col s12 m12"> 
                <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">PENTA-HIB3</h5>
                
                <p> 
                  <label>
                    <input class="with-gap" name="PENTAHIB3" value="YES" onclick="isCheckedPenta3()" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PENTAHIB3" value="NO" onclick="isCheckedPenta3()" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <button id="resetPenta3Btn" onclick="resetPenta3()" type="button" class="small-btn btn red lighten-1 with-gap" style="display: none;">Clear</button>
              </div>
              <div class="input-field col s12 m6">
                <input    id="DatePentahib3wasgiven" name="DatePentahib3wasgiven" type="date"  >
                <label for="DatePentahib3wasgiven">Date PENTA-HIB3 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationPENTAHIB3" name="DateofConsultationPENTAHIB3" type="date"  >
                <label for="DateofConsultationPENTAHIB3">Date of Consultation (PENTA-HIB3)</label>
              </div>





              <div class="input-field col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">OPV Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12">
               
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">OPV1</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="OPV1" value="YES" onclick="isCheckedOPV1()" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="OPV1" value="NO" onclick="isCheckedOPV1()" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <button id="resetOPV1Btn" onclick="resetOPV1()" type="button" class="small-btn btn red lighten-1 with-gap" style="display: none;">Clear</button>

              </div>
              <div class="input-field col s12 m6">
                <input  placeholder="" id="DateOPV1wasgiven" name="DateOPV1wasgiven" type="date"  >
                <label for="DateOPV1wasgiven">Date OPV1 was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationOPV1" name="DateofConsultationOPV1v" type="date"  >
                <label for="DateofConsultationOPV1">Date of Consultation (OPV1)</label>
              </div>
              
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">OPV2</h5>
             
                <p> 
                  <label>
                    <input class="with-gap" onclick="isCheckedOPV2()" name="OPV2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" onclick="isCheckedOPV2()" name="OPV2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <button id="resetOPV2Btn" onclick="resetOPV2()" type="button" class="small-btn btn red lighten-1 with-gap" style="display: none;">Clear</button>

              </div>
              <div class="input-field col s12 m6">
                <input   id="DateOPV2wasgiven" name="DateOPV2wasgiven" type="date"  >
                <label for="DateOPV2wasgiven">Date OPV2 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input    id="DateofConsultationOPV2" name="DateofConsultationOPV2" type="date"  >
                <label for="DateofConsultationOPV2">Date of Consultation (OPV2)</label>
              </div>

              <div class="input-field col s12 m12"> 
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">OPV3</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="OPV3" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="OPV3" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s12 m6">
                <input    id="dateOPV3wasgiven" name="dateOPV3wasgiven" type="date"  >
                <label for="dateOPV3wasgiven">Date OPV3 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationOPV3" name="DateofConsultationOPV3" type="date"  >
                <label for="DateofConsultationOPV3">Date of Consultation (OPV3)</label>
              </div>
             
        








              <div class="input-field col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">IPV Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">IPV1</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="IPV1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="IPV1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <div class="input-field col s12 m6">
                <input    id="dateIPV1wasgiven" name="dateIPV1wasgiven" type="date"  >
                <label for="dateIPV1wasgiven">Date IPV1 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationIPV1" name="DateofConsultationIPV1" type="date"  >
                <label for="DateofConsultationIPV1">Date of Consultation (IPV1)</label>
              </div>
              </div>
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">IPV2</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="IPV2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="IPV2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <div class="input-field col s12 m6">
                <input    id="dateIPV2wasgiven" name="dateIPV2wasgiven" type="date"  >
                <label for="dateIPV2wasgiven">Date IPV2 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationIPV2" name="DateofConsultationIPV2" type="date"  >
                <label for="DateofConsultationIPV2">Date of Consultation (IPV2)</label>
              </div>
              </div>
              <div class="input-field col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">PCV Antigen</h5>
              </div>
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">PCV1</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="PCV1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PCV1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <div class="input-field col s12 m6">
                <input    id="datePCV1wasgiven" name="datePCV1wasgiven" type="date"  >
                <label for="datePCV1wasgiven">Date PCV1 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationPCV1" name="DateofConsultationPCV1" type="date"  >
                <label for="DateofConsultationPCV1">Date of Consultation (PCV1)</label>
              </div>
              </div>


              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">PCV2</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="PCV2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PCV2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <div class="input-field col s12 m6">
                <input    id="datePCV2wasgiven" name="datePCV2wasgiven" type="date"  >
                <label for="datePCV2wasgiven">Date PCV2 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationPCV2" name="DateofConsultationPCV2" type="date"  >
                <label for="DateofConsultationPCV2">Date of Consultation (PCV2)</label>
              </div>
              </div>
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">PCV3</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="PCV3" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="PCV3" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                <div class="input-field col s12 m6">
                <input    id="datePCV3wasgiven" name="datePCV3wasgiven" type="date"  >
                <label for="datePCV3wasgiven">Date PCV3 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input  placeholder="" id="DateofConsultationPCV3" name="DateofConsultationPCV3" type="date"  >
                <label for="DateofConsultationPCV3">Date of Consultation (PCV3)</label>
              </div>
              </div>
























              

              <div class="input-field col s12">
              <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">HEPATITIS Antigen</h5>
              </div>
              
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">HEPA at Birth</h5>
               
                <p> 
                  <label>
                    <input class="with-gap" name="HEPAatBirth" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="HEPAatBirth" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
               
              </div>
              <div class="input-field col s12 m12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">Timing of HEPA B Vaccine</h5>
                
                <p> 
                  <label>
                    <input class="with-gap" name="TimingHEPAatBirth" value="WITHIN 24 HOURS" type="radio"/>
                    <span>WITHIN 24 HOURS</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TimingHEPAatBirth" value="MORE THAN 24 HOURS" type="radio"/>
                    <span>MORE THAN 24 HOURS</span>
                  </label> 
                </p>
              </div>
              <div class="input-field col s3 s12 m6">
                <input   id="DateHEPAatBirthwasgiven" name="DateHEPAatBirthwasgiven" type="date"  >
                <label for="DateHEPAatBirthwasgiven">Date HEPA at Birth was given</label>
              </div>
              <div class="input-field col s12 m6">
                <input   id="DateofConsultationHEPAatBirth" name="DateofConsultationHEPAatBirth" type="date"  >
                <label for="DateofConsultationHEPAatBirth">Date of Consultation(HEPA at Birth)</label>
              </div>
              
              <div class="input-field col s12 m12">
              
                <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">HEPA B1</h5>
                <p> 
                  <label>
                    <input class="with-gap" name="HEPAB1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="HEPAB1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
                
              </div>
              <div class="input-field col s12 m6">
                <input   id="dateHEPA1wasgiven" name="dateHEPA1wasgiven" type="date"  >
                <label for="dateHEPA1wasgiven">Date HEPA1 was given</label>
              </div>
                <div class="input-field col s12 m6">
                <input    id="DateofConsultationHEPAB1" name="DateofConsultationHEPAB1" type="date"  >
                <label for="DateofConsultationHEPAB1">Date of Consultation (HEPA B1)</label>
              </div>

              <div class="input-field col s4" style="display:none;"> 
                <input placeholder="" id="HEPAB2" value="" name="HEPAB2" type="text"  >
                <label for="HEPAB2">HEPA B2</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input   id="dateHEPA2wasgiven" value="" name="dateHEPA2wasgiven" type="date"  >
                <label for="dateHEPA2wasgiven">Date HEPA2 was given</label>
              </div>
                <div class="input-field col s4" style="display:none;">
                <input  placeholder="" value="" id="DateofConsultationHEPAB2" name="DateofConsultationHEPAB2" type="date"  >
                <label for="DateofConsultationHEPAB2">Date of Consultation (HEPA B2)</label>
              </div>

<!---->
              <div class="input-field col s4" style="display:none;"> 
                <input placeholder="" value="" name="HEPAB3" id="HEPAB3" type="text"  >
                <label for="HEPAB3">HEPA B3</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input   id="dateHEPA3WASGIVEN" value="" name="dateHEPA3WASGIVEN" type="date">
                <label for="dateHEPA3WASGIVEN">DATE HEPA3 WAS GIVEN</label>
              </div>
                <div class="input-field col s4" style="display:none;">
                <input  placeholder="" value="" id="DateofConsultationHEPAB3" name="DateofConsultationHEPAB3" type="date"  >
                <label for="DateofConsultationHEPAB3">DATE OF CONSULTATION (HEPA B3)</label>
              </div>
<!---->
<div class="input-field col s4" style="display:none;">
                <input placeholder="" value=""  id="HEPAB4" name="HEPAB4" type="text"  >
                <label for="HEPAB4">HEPA B4</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input id="dateHEPA4WASGIVEN" value=""  name="dateHEPA4WASGIVEN" type="date"  >
                <label for="dateHEPA4WASGIVEN">DATE HEPA4 WAS GIVEN</label>
              </div>
              <div class="input-field col s4" style="display:none;">
                <input  placeholder="" value=""  id="DateofConsultationHEPAB4" name="DateofConsultationHEPAB4" type="date"  >
                <label for="DateofConsultationHEPAB4">DATE OF CONSULTATION (HEPA B4)</label>
              </div>
        


          <div class="input-field col s12">
          <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">MCV Antigen</h5>
          </div>
          
          <div class="input-field col s12 m12">
            
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">MCV 1 (at 9months)</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="AMV19monthstobelow12months" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="AMV19monthstobelow12months" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
          </div>
          
          <div class="input-field col s12 m6">
            <input   id="DateAMV1WASGIVEN" name="DateAMV1WASGIVEN" type="date"  >
            <label for="DateAMV1WASGIVEN">DATE MCV 1 WAS GIVEN</label>
          </div>
          <div class="input-field col s12 m6">
            <input  id="DateofConsultationAMV1" name="DateofConsultationAMV1" type="date"  >
            <label for="DateofConsultationAMV1">DATE OF CONSULTATON (MCV 1)</label>
          </div>
          
          <div class="input-field col s12 m12">
          <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">MCV 2(at 12 Months)</h5>
            
            <p> 
                  <label>
                    <input class="with-gap" name="MMR12MOSTO15MOS" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="MMR12MOSTO15MOS" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            
          </div>
          <div class="input-field col s12 m6">
            <input  id="dateMMRWASGIVEN" name="dateMMRWASGIVEN" type="date"  >
            <label for="dateMMRWASGIVEN">DATE MCV2 WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m6">
            <input    id="DateofConsultationMMR" name="DateofConsultationMMR" type="date"  >
            <label for="DateofConsultationMMR">DATE OF CONSULTATION (MCV2)</label>
          </div>



          <div class="input-field col s12 m12">
          <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">FIC</h5>
            
            <p> 
                  <label>
                    <input class="with-gap" name="FIC" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="FIC" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            
          </div>
          <div class="input-field col s12 m6">
            <input  id="dateFICWASGIVEN" name="dateFICWASGIVEN" type="date"  >
            <label for="dateFICWASGIVEN">DATE FIC WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m6">
            <input    id="DateofConsultationFIC" name="DateofConsultationFIC" type="date"  >
            <label for="DateofConsultationFIC">DATE OF CONSULTATION (FIC)</label>
          </div>






          <div class="input-field col s12 m12">
          <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">CIC</h5>
            
            <p> 
                  <label>
                    <input class="with-gap" name="CIC" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="CIC" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            
          </div>
          <div class="input-field col s12 m6">
            <input  id="dateCICWASGIVEN" name="dateCICWASGIVEN" type="date"  >
            <label for="dateCICWASGIVEN">DATE CIC WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m6">
            <input    id="DateofConsultationCIC" name="DateofConsultationCIC" type="date"  >
            <label for="DateofConsultationCIC">DATE OF CONSULTATION (CIC)</label>
          </div>







          

          <div class="input-field col s12 m12" style="display:none;"> 
           
            
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">AMV 2(16 MOS. TO 5 YRS. OLD)</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="AMAMV2_16MOSTO5YRSOLDV2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="AMAMV2_16MOSTO5YRSOLDV2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
          </div>
          <div class="input-field col s12 m5" style="display:none;">
            <input   id="dateAMV2WASGIVEN" name="dateAMV2WASGIVEN" type="date"  >
            <label for="dateAMV2WASGIVEN">DATE AMV2 WAS GIVEN</label>
          </div>
            <div class="input-field col s12 m7" style="display:none;">
            <input  placeholder="" id="DateofConsultationAMV2"  name="DateofConsultationAMV2" type="date"  >
            <label for="DateofConsultationAMV2">DATE OF CONSULTATION (AMV2)</label>
          </div>
            <div class="input-field col   s12 m4">
            <input  placeholder="" id="IMMUNIZATIONSTATUS" name="IMMUNIZATIONSTATUS" type="text"  >
            <label for="IMMUNIZATIONSTATUS">IMMUNIZATION STATUS</label>
          </div>
            <div class="input-field col  s12 m8">
            <input  placeholder="" id="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" name="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" type="date"  >
            <label for="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED">DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED</label>
          </div>

<!---->
          
 


      </div>

        </div>
        </div>
  
  
        
       
          </div>
        </div>
        </div>
    </div>



    <div id="tab3"  >
      <div class="row ">
      
      <div class="col s12 m10 offset-m1"  >
         
           
            <div class="z-depth-1 white">
            <div class="row  " style="padding:20.1px;"> 
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">Exclusive Breastfeeding</h5>
           
            <div class="input-field col s12">
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">CHILD WAS EXCLUSIVELY BREASTFED (PUT A CHECK)</h5>
              <p class="z-depth-1" style="padding:10px;"> 
                <label>
                  <input name="FIRSTMONTH" value="FIRST MONTH" type="checkbox" class="filled-in"   />
                  <span style="font-size:13.6px;font-weight:500;">FIRST MONTH</span>
                </label>&nbsp&nbsp&nbsp 
                <label>
                  <input   name="SECONDMONTH" value="SECOND MONTH" type="checkbox"  class="filled-in" />
                  <span style="font-size:13.6px;font-weight:500;">SECOND MONTH</span>
                </label> &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input   name="THIRDMONTH" value="THIRD MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">THIRD MONTH</span>
                </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp  
                <label>
                  <input name="FOURTHDMONTH" value="FOURTH MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">FOURTH MONTH</span>
                </label> &nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp  
                <label>
                  <input   name="FIFTMONTH" value="FIFTH MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">FIFTH MONTH</span>
                </label>&nbsp&nbsp&nbsp&nbsp&nbsp 
                <label>
                  <input   name="SIXTHMONTH" value="SIXTH MONTH" type="checkbox" class="filled-in"  />
                  <span style="font-size:13.6px;font-weight:500;">SIXTH MONTH</span>
                </label> 
              </p>
              
            </div>
            
            <div class="input-field col s12 m2">
              <input type="date" name="date6MONTHS" id="date6MONTHS" class="datepicker"  >
              <label for="date6MONTHS">DATE 6 MONTHS</label>
            </div>
            <div class="input-field col s12 m5">
              <input   id="WASTHECHILDEXCLUSIVELY" name="WASTHECHILDEXCLUSIVELY" placeholder="" type="text"  >
              <label for="WASTHECHILDEXCLUSIVELY">WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?</label>
            </div>
            <div class="input-field col s12 m5">
              <input   id="DATEOFCONSULTATIONEXCLUSIVEBF" name="DATEOFCONSULTATIONEXCLUSIVEBF" type="date"  >
              <label for="DATEOFCONSULTATIONEXCLUSIVEBF">DATE OF CONSULTATION (EXCLUSIVE)</label>
            </div>





            <div class="input-field col m12  s12">
            <h5 class="    white-text" style="padding:11px; font-size:14px; font-weight: bold; background:#42a5f5;">NON-PREGNANT GIVEN TETANUS TOXOID IMMUNIZATION</h5>
            </div>
            
            <div class="input-field col m12 s12">
              
              <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">TD1</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT1" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT1" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
            </div>
            <div class="input-field col m12 s12">
              <input  placeholder="" id="DATEOFCONSULTTT1" name="DATEOFCONSULTTT1" type="date"  >
              <label for="DATEOFCONSULTTT1">DATE OF CONSULT (TT1)</label>
            </div>

            
            <div class="input-field col m12 s12">
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT2</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT2" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT2" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m12 s12">
              <input  placeholder="" id="DATEOFCONSULTTT2" name="DATEOFCONSULTTT2" type="date"  >
              <label for="DATEOFCONSULTTT2">DATE OF CONSULT (TT2)</label>
            </div>
             
            <div class="input-field col m12 s12">
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT3</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT3" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT3" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m12 s12">
              <input  placeholder="" id="DATEOFCONSULTTT3" name="DATEOFCONSULTTT3" type="date"  >
              <label for="DATEOFCONSULTTT3">DATE OF CONSULT (TT3)</label>
            </div>
             
            <div class="input-field col m12 s12">
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT4</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT4" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT4" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m12 s12">
              <input  placeholder="" id="DATEOFCONSULTTT4" name="DATEOFCONSULTTT4" type="date"  >
              <label for="DATEOFCONSULTTT4">DATE OF CONSULT (TT4)</label>
            </div>

            <div class="input-field col m12 s12">
            <h5 class="blue-grey lighten-4 black-text" style="padding:9px; font-size:15px; font-weight: 500;">TT5</h5>
            <p> 
                  <label>
                    <input class="with-gap" name="TT5" value="YES" type="radio"/>
                    <span>YES</span>
                  </label>&nbsp&nbsp
                  <label>
                    <input class="with-gap" name="TT5" value="NO" type="radio"/>
                    <span>NO</span>
                  </label> 
                </p>
              
            </div>
            <div class="input-field col m8 s12">
              <input  placeholder="" id="DATEOFCONSULTT5" name="DATEOFCONSULTT5" type="date"  >
              <label for="DATEOFCONSULTT5">DATE OF CONSULT (TT5)</label>
            </div>
        

            <div class="input-field col m8 s12">
              <input  placeholder="" id="PhoneNumber" name="PhoneNumber" type="number"  >
              <label for="PhoneNumber">Phone Number</label>
            </div>
           
          
<!---->
 

      </div>
      </div>


    
      
        </div>
      </div>
      </div>
      <div class=" row center  s12 m12  " style="margin-right:9.1em;margin-bottom:0em;">
              <button type="submit" id="submit_btn"   name="submitBtn" class="right btn cyan darken-2 btn-small" style="font-weight: 500;"><i class="material-icons right">send</i>Submit</button><br><br>
            </div>
            
          </form> 
  </div> 


  <div class="row ">
        
        <div class="col s12 m10 offset-m1"  >
      <div class="row right ">
        <ul id="tabs-swipe-demo" class="tabs" >
          <li class="tab col "><a style="font-size:13px;" href="#tab1" class="active"> 
  <div class="chip   white-text" style="background:#42a5f5 ;">1</div>
   Personal Information</a> </li>
 
          <li class="tab col "><a style="font-size:11px;" href="#tab2">  <div class="chip   white-text" style="background:#42a5f5 ;">2</div>Antigens</a></li>
          <li class="tab col "><a style="font-size:11px;" href="#tab3">  <div class="chip   white-text" style="background:#42a5f5 ;">3</div>Breastfeeding</a></li>
      </ul>
      </div>
      
      </div>
      </div>
      
       
      
<script>
   $(document).ready(function(){
    $('.modal').modal();
  });
</script>
       
      <script>
  document.getElementById('bdate').addEventListener('change', function() {
    const birthDate = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    
    // If the current month is before the birth month or it's the birth month but the current day is before the birth day
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Add 0.1 to the calculated age
    age = (age + 0.1).toFixed(1); // Keeps one decimal place

    // Set the calculated age in the Age input
    document.getElementById('Age').value = age;
  });
</script>

<script>
  document.getElementById('BirthdateofMother').addEventListener('change', function() {
    const birthDate = new Date(this.value);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();

    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Set the calculated age
    document.getElementById('AgeofMother').value = age;

    // Show error message if age is 0 to 10
    const errorMsg = document.getElementById('ageError');
    if (age >= 0 && age <= 10) {
      errorMsg.style.display = 'block';
      errorMsg.textContent = 'Age must be greater than 10.';
    } else {
      errorMsg.style.display = 'none';
      errorMsg.textContent = '';
    }
  });
</script>



      <script>
        document.addEventListener('DOMContentLoaded', function() {
            var elems = document.querySelectorAll('.tabs');
            var instances = M.Tabs.init(elems);
        });
    </script>
      <script>
        document.getElementById('DateOfBirth').addEventListener('change', function() {
          const birthDate = new Date(this.value);
          const today = new Date();
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDifference = today.getMonth() - birthDate.getMonth();
          
          // If the current month is before the birth month or it's the birth month but the current day is before the birth day
          if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }
          
          // Set the calculated age in the Age input
          document.getElementById('AgeofChild').value = age;
        });
      </script>
      <script>
        document.getElementById('Philhealth').addEventListener('input', function (e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove all non-digit characters
            if (value.length > 2) {
                value = value.slice(0, 2) + '-' + value.slice(2); // Add first hyphen after 4 digits
            }
            if (value.length > 12) {
                value = value.slice(0, 12) + '-' + value.slice(12); // Add second hyphen after another 4 digits
            }
            e.target.value = value; // Update the input value with formatted string
        });
    </script>
</body>
</html>
<script>
  function forms() {
      var name = document.getElementById('name').innerHTML;
      var birthDate = document.getElementById('bdate').value;
  }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
      var elems = document.querySelectorAll('select');
      M.FormSelect.init(elems);
  });
</script>

 

<script>
    // Remove the warning notice pop-up and enhance backward compatibility
    (function() {
        if (window.history && window.history.replaceState) {
            window.addEventListener('load', function() {
                // Ensure the script runs only after the page fully loads
                window.history.replaceState(null, null, window.location.href);
            });
        }
    })();
</script>
<script>
  function classifyBirthWeight() {
    const weightInput = document.getElementById('BirthWeightInGrams');
    const classificationInput = document.getElementById('BirthWeightClassification');

    const weight = parseFloat(weightInput.value);

    if (!isNaN(weight)) {
      classificationInput.value =
        weight < 2000 ? 'LOW BIRTHWEIGHT (<2000 GMS)' : 'NORMAL';
    } else {
      classificationInput.value = '';
    }
  }
</script>

<script>
        const nameInput = document.getElementById('NameofFacility');
        const suggestionsList = document.getElementById('hospitalSuggestions');

        nameInput.addEventListener('input', async () => {
            const query = nameInput.value.trim();
            suggestionsList.innerHTML = ''; // Clear previous suggestions

            if (query.length > 0) {
                const response = await fetch(`autocomplete.php?q=${query}`);
                const hospitals = await response.json();

                hospitals.forEach(hospital => {
                    const suggestion = document.createElement('li');
                    suggestion.textContent = hospital;
                    suggestion.style.cursor = 'pointer';

                    suggestion.addEventListener('click', () => {
                        nameInput.value = hospital; // Set selected value
                        suggestionsList.innerHTML = ''; // Clear suggestions
                    });

                    suggestionsList.appendChild(suggestion);
                });
            }
        });

        // Close suggestions when clicking outside
        document.addEventListener('click', (event) => {
            if (!nameInput.contains(event.target)) {
                suggestionsList.innerHTML = '';
            }
        });
    </script>
 
 
 
 
 
 
 
 
 
 
 
 
 <script>
    document.getElementById('myCheckbox').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "NO";
      } else {
        input.value = "";
      }
    });
  </script>
 <script>
    document.getElementById('myCheckbox2').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "NO";
      } else {
        input.value = "";
      }
    });
  </script>
 <script>
    document.getElementById('myCheckbox3').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "YES";
      } else {
        input.value = "";
      }
    });
  </script>
  <script>
    document.getElementById('myCheckbox4').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "YES";
      } else {
        input.value = "";
      }
    });
  </script>
   <script>
    document.getElementById('myCheckbox5').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "YES";
      } else {
        input.value = "";
      }
    });
  </script>
   <script>
    document.getElementById('myCheckbox6').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "YES";
      } else {
        input.value = "";
      }
    });
  </script>
   <script>
    document.getElementById('myCheckbox7').addEventListener('change', function() {
      const input = document.getElementById('WastheChildProtectedatBirth');
      if (this.checked) {
        input.value = "YES";
      } else {
        input.value = "";
      }
    });
  </script>
 
 
 
 
 
 
 
 
 
 <script>
  function myFunction() {
    let PurokStreetSitio = document.getElementById("PurokStreetSitio").value;
    let HouseNo = document.getElementById("HouseNo").value;
    
 
    let healthCenter = <?php 
      $health_centers = [
        'BACLARAN HEALTH CENTER' => 'BACLARAN',
        'TAMBO HEALTH CENTER' => 'TAMBO',
        'VITALEZ HEALTH CENTER' => 'VITALEZ',
        'DON GALO HEALTH CENTER' => 'DON GALO',
        'STO.NIÑO HEALTH CENTER' => 'STO.NIÑO',
        'LA HUERTA HEALTH CENTER' => 'LA HUERTA',
        'MOONWALK HEALTH CENTER' => 'MOONWALK',
        'SAN DIONISIO HEALTH CENTER' => 'SAN DIONISIO',
        'SAN ISIDRO HEALTH CENTER' => 'SAN ISIDRO',
        'SAN ANTONIO HEALTH CENTER' => 'SAN ANTONIO',
        'DON BOSCO HEALTH CENTER' => 'DON BOSCO',
        'BF HEALTH CENTER' => 'BF',
        'MARCELO GREEN HEALTH CENTER' => 'MARCELO GREEN',
        'MERVILLE HEALTH CENTER' => 'MERVILLE',
        'SAN MARTIN DE PORRES HEALTH CENTER' => 'SAN MARTIN DE PORRES',
        'SUN VALLEY HEALTH CENTER' => 'SUN VALLEY'
      ];

      $current_center = $_SESSION['health_center'] ?? 'UNKNOWN'; 
      echo json_encode($health_centers[$current_center] ?? 'UNKNOWN');
    ?>;

    document.getElementById("Address").value = `${HouseNo} ${PurokStreetSitio}, ${healthCenter} PARAÑAQUE CITY`;
  }
</script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    var elems = document.querySelectorAll('.modal');
    var instances = M.Modal.init(elems, options);
  });
</script>

<script>
           
           function isChecked() {
             const yes = document.getElementById('BCG_YES').checked;
             const no = document.getElementById('BCG_NO').checked;
             document.getElementById('resetBtn').style.display = (yes || no) ? 'block' : 'none';
           }
         
           function resetBcg() {
             document.getElementById('BCG_YES').checked = false;
             document.getElementById('BCG_NO').checked = false;
             document.getElementById('resetBtn').style.display = 'none';
           }
         </script>

         <script>
  function isCheckedPenta() {
    const radios = document.getElementsByName('PENTAHIB1');
    let isAnyChecked = false;
    radios.forEach ? radios.forEach(r => { if (r.checked) isAnyChecked = true; }) :
    Array.from(radios).forEach(r => { if (r.checked) isAnyChecked = true; });

    document.getElementById('resetPentaBtn').style.display = isAnyChecked ? 'block' : 'none';
  }

  function resetPenta() {
    const radios = document.getElementsByName('PENTAHIB1');
    radios.forEach ? radios.forEach(r => r.checked = false) :
    Array.from(radios).forEach(r => r.checked = false);

    document.getElementById('resetPentaBtn').style.display = 'none';
  }
</script>
<script>
  function isCheckedPenta2() {
    const radios = document.getElementsByName('PENTAHIB2');
    let isChecked = Array.from(radios).some(r => r.checked);
    document.getElementById('resetPenta2Btn').style.display = isChecked ? 'block' : 'none';
  }

  function resetPenta2() {
    const radios = document.getElementsByName('PENTAHIB2');
    radios.forEach ? radios.forEach(r => r.checked = false) :
    Array.from(radios).forEach(r => r.checked = false);
    document.getElementById('resetPenta2Btn').style.display = 'none';
  }
</script>
<script>
  function isCheckedPenta3() {
    const radios = document.getElementsByName('PENTAHIB3');
    let isChecked = Array.from(radios).some(r => r.checked);
    document.getElementById('resetPenta3Btn').style.display = isChecked ? 'block' : 'none';
  }

  function resetPenta3() {
    const radios = document.getElementsByName('PENTAHIB3');
    radios.forEach ? radios.forEach(r => r.checked = false) :
    Array.from(radios).forEach(r => r.checked = false);
    document.getElementById('resetPenta3Btn').style.display = 'none';
  }
</script>
<script>
  function isCheckedOPV1() {
    const radios = document.getElementsByName('OPV1');
    let isChecked = Array.from(radios).some(r => r.checked);
    document.getElementById('resetOPV1Btn').style.display = isChecked ? 'block' : 'none';
  }

  function resetOPV1() {
    const radios = document.getElementsByName('OPV1');
    radios.forEach ? radios.forEach(r => r.checked = false) :
    Array.from(radios).forEach(r => r.checked = false);
    document.getElementById('resetOPV1Btn').style.display = 'none';
  }
</script>

<script>
  function isCheckedOPV2() {
    const radios = document.getElementsByName('OPV2');
    let isChecked = Array.from(radios).some(r => r.checked);
    document.getElementById('resetOPV2Btn').style.display = isChecked ? 'block' : 'none';
  }

  function resetOPV2() {
    const radios = document.getElementsByName('OPV2');
    radios.forEach ? radios.forEach(r => r.checked = false) :
    Array.from(radios).forEach(r => r.checked = false);
    document.getElementById('resetOPV2Btn').style.display = 'none';
  }
</script>





















<script>
  // Show/hide reset button based on radio selection
  function showTTStatusResetBtn() {
    const radios = document.getElementsByName('TTStatusofMother');
    let isChecked = Array.from(radios).some(r => r.checked);
    document.getElementById('resetWas').style.display = isChecked ? 'block' : 'none';
  }

  // Reset radio buttons and hide reset button
  function resetTTStatus() {
    const radios = document.getElementsByName('TTStatusofMother');
    Array.from(radios).forEach(r => r.checked = false);
    document.getElementById('resetWas').style.display = 'none';
    document.getElementById('WastheChildProtectedatBirth').value= '';
  }
</script>

 


