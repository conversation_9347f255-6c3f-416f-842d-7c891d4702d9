<?php
// Database Error Debug Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Database Error Debug</h2>";

// Test 1: Basic Connection
echo "<h3>1. Testing Database Connection</h3>";
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    echo "<p style='color: red;'>❌ Connection failed: " . mysqli_connect_error() . "</p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
}

// Test 2: Check if table exists
echo "<h3>2. Checking nip_table</h3>";
$result = mysqli_query($conn, "SHOW TABLES LIKE 'nip_table'");
if (mysqli_num_rows($result) > 0) {
    echo "<p style='color: green;'>✅ nip_table exists</p>";
} else {
    echo "<p style='color: red;'>❌ nip_table does not exist</p>";
    echo "<p>Creating nip_table...</p>";
    
    $create_table = "CREATE TABLE IF NOT EXISTS nip_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        DateOfRegistration DATE,
        DateOfBirth DATE,
        NameOfChild VARCHAR(255),
        LastNameOfChild VARCHAR(255),
        middlename_of_child VARCHAR(255),
        Sex VARCHAR(10),
        NameofMother VARCHAR(255),
        Barangay VARCHAR(255),
        Address TEXT,
        PhoneNumber VARCHAR(20),
        FamilySerialNumber VARCHAR(100),
        deleted TINYINT(1) DEFAULT 0
    )";
    
    if (mysqli_query($conn, $create_table)) {
        echo "<p style='color: green;'>✅ nip_table created successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create nip_table: " . mysqli_error($conn) . "</p>";
    }
}

// Test 3: Test the duplicate check function
echo "<h3>3. Testing Duplicate Check Function</h3>";

// Include the function from db.php
function checkDuplicateChild($conn, $firstName, $lastName, $birthDate) {
    $duplicateFound = false;
    $duplicateMessage = '';
    
    // Sanitize inputs
    $firstName = trim($firstName);
    $lastName = trim($lastName);
    $birthDate = trim($birthDate);
    
    if (empty($firstName) || empty($lastName) || empty($birthDate)) {
        return ['found' => false, 'message' => ''];
    }
    
    try {
        // Check for exact match on first name, last name, and birth date
        $checkStmt = $conn->prepare("SELECT id, NameOfChild, LastNameOfChild, DateOfBirth, FamilySerialNumber, DateOfRegistration 
                                     FROM nip_table 
                                     WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ? 
                                     AND (deleted IS NULL OR deleted = 0)");
        
        if ($checkStmt) {
            $checkStmt->bind_param("sss", $firstName, $lastName, $birthDate);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            
            if ($result->num_rows > 0) {
                $duplicateFound = true;
                $existingRecord = $result->fetch_assoc();
                $duplicateMessage = "Child '{$existingRecord['NameOfChild']} {$existingRecord['LastNameOfChild']}' with birth date '{$existingRecord['DateOfBirth']}' is already registered (ID: {$existingRecord['id']}, Family Serial: {$existingRecord['FamilySerialNumber']}, Registration Date: {$existingRecord['DateOfRegistration']})";
            }
            $checkStmt->close();
        }
        
    } catch (Exception $e) {
        error_log("Duplicate check error: " . $e->getMessage());
        return ['found' => false, 'message' => 'Warning: Could not verify if child is already registered. Please check manually.'];
    }
    
    return ['found' => $duplicateFound, 'message' => $duplicateMessage];
}

// Test the function
try {
    $testResult = checkDuplicateChild($conn, 'Test', 'Child', '2020-01-01');
    echo "<p style='color: green;'>✅ Duplicate check function works</p>";
    echo "<p>Test result: " . ($testResult['found'] ? 'Duplicate found' : 'No duplicate') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Duplicate check function error: " . $e->getMessage() . "</p>";
}

// Test 4: Simulate form submission
echo "<h3>4. Simulating Form Submission</h3>";

// Set up test POST data
$_POST['submitBtn'] = 'Submit';
$_POST['NameOfChild'] = 'Test';
$_POST['LastNameOfChild'] = 'Child';
$_POST['DateOfBirth'] = '2020-01-01';
$_POST['Sex'] = 'MALE';
$_POST['NameofMother'] = 'Test Mother';
$_POST['Barangay'] = 'Test Barangay';
$_POST['Address'] = 'Test Address';
$_POST['PhoneNumber'] = '09123456789';
$_POST['PlaceofDelivery'] = 'Hospital';
$_POST['Attendant'] = 'Doctor';
$_POST['TypeofDelivery'] = 'NSD';
$_POST['BirthWeightInGrams'] = '3000';

// Test variable assignment
$NameOfChild = $_POST['NameOfChild'];
$LastNameOfChild = $_POST['LastNameOfChild'];
$DateOfBirth = $_POST['DateOfBirth'];

echo "<p>Variables assigned:</p>";
echo "<ul>";
echo "<li>NameOfChild: " . htmlspecialchars($NameOfChild) . "</li>";
echo "<li>LastNameOfChild: " . htmlspecialchars($LastNameOfChild) . "</li>";
echo "<li>DateOfBirth: " . htmlspecialchars($DateOfBirth) . "</li>";
echo "</ul>";

// Test duplicate check with these variables
try {
    $duplicateCheck = checkDuplicateChild($conn, $NameOfChild, $LastNameOfChild, $DateOfBirth);
    echo "<p style='color: green;'>✅ Duplicate check with form variables works</p>";
    echo "<p>Result: " . ($duplicateCheck['found'] ? 'Duplicate found' : 'No duplicate') . "</p>";
    if (!empty($duplicateCheck['message'])) {
        echo "<p>Message: " . htmlspecialchars($duplicateCheck['message']) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Duplicate check with form variables failed: " . $e->getMessage() . "</p>";
}

// Test 5: Check PHP version and extensions
echo "<h3>5. PHP Environment Check</h3>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>MySQLi Extension:</strong> " . (extension_loaded('mysqli') ? '✅ Loaded' : '❌ Not loaded') . "</p>";
echo "<p><strong>Session Support:</strong> " . (function_exists('session_start') ? '✅ Available' : '❌ Not available') . "</p>";

// Test 6: Check database permissions
echo "<h3>6. Database Permissions Test</h3>";

// Test SELECT
$select_test = mysqli_query($conn, "SELECT 1");
echo "<p><strong>SELECT:</strong> " . ($select_test ? '✅ Works' : '❌ Failed - ' . mysqli_error($conn)) . "</p>";

// Test INSERT (into a test table)
$create_test_table = "CREATE TABLE IF NOT EXISTS test_permissions (id INT AUTO_INCREMENT PRIMARY KEY, test_data VARCHAR(50))";
$create_result = mysqli_query($conn, $create_test_table);
echo "<p><strong>CREATE TABLE:</strong> " . ($create_result ? '✅ Works' : '❌ Failed - ' . mysqli_error($conn)) . "</p>";

if ($create_result) {
    $insert_test = mysqli_query($conn, "INSERT INTO test_permissions (test_data) VALUES ('test')");
    echo "<p><strong>INSERT:</strong> " . ($insert_test ? '✅ Works' : '❌ Failed - ' . mysqli_error($conn)) . "</p>";
    
    if ($insert_test) {
        $delete_test = mysqli_query($conn, "DELETE FROM test_permissions WHERE test_data = 'test'");
        echo "<p><strong>DELETE:</strong> " . ($delete_test ? '✅ Works' : '❌ Failed - ' . mysqli_error($conn)) . "</p>";
    }
    
    // Clean up test table
    mysqli_query($conn, "DROP TABLE test_permissions");
}

// Test 7: Check for syntax errors in db.php
echo "<h3>7. Syntax Check</h3>";
$db_file = 'db.php';
if (file_exists($db_file)) {
    $syntax_check = shell_exec("php -l $db_file 2>&1");
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "<p style='color: green;'>✅ No syntax errors in db.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Syntax errors found in db.php:</p>";
        echo "<pre>" . htmlspecialchars($syntax_check) . "</pre>";
    }
} else {
    echo "<p style='color: red;'>❌ db.php file not found</p>";
}

// Summary
echo "<h3>8. Summary & Recommendations</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>Quick Fixes:</h4>";
echo "<ol>";
echo "<li><strong>If table missing:</strong> Run the table creation script above</li>";
echo "<li><strong>If connection fails:</strong> Check MySQL service is running</li>";
echo "<li><strong>If syntax errors:</strong> Fix the reported issues in db.php</li>";
echo "<li><strong>If permission errors:</strong> Check MySQL user permissions</li>";
echo "</ol>";

echo "<h4>Test Registration:</h4>";
echo "<p>After fixing any issues above, try:</p>";
echo "<ol>";
echo "<li>Visit <a href='nip.php'>nip.php</a> and fill out the form</li>";
echo "<li>Submit the form and check for errors</li>";
echo "<li>If errors persist, check the browser console for JavaScript errors</li>";
echo "</ol>";
echo "</div>";

// Clean up
unset($_POST);
mysqli_close($conn);

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4 {
    color: #343a40;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #dc3545;
}

ul, ol {
    line-height: 1.6;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
