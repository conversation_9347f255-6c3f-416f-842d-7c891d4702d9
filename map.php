<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Parañaque City NIP MAP</title>
      <!-- Leaflet JS -->
      <script src="leaflet/leaflet-global-src.js"></script>
      <script src="leaflet/leaflet-global-src.js.map"></script>
      <script src="leaflet/leaflet-global.js"></script>
      <script src="leaflet/leaflet-global.js.map"></script>
      <script src="leaflet/leaflet-src.js"></script>
      <script src="leaflet/leaflet.js"></script>
      <script src="leaflet/leaflet.js.map"></script>
      <link rel="stylesheet" href="leaflet/leaflet.css">
    <?php include 'style.php'; ?>
    <style>
        .leaflet-popup-content-wrapper {
 border-radius: 0;
    width: 23em !important; 
}
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #e3f2fd;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        h4 {
            font-weight: 500;
            color: #333;
        }

        #map {
            height: 600px;
            width: 100%;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .leaflet-bottom,
        .leaflet-right,
        .leaflet-control-attribution,
        .leaflet-control {
            display: none !important;
        }

        .chart-container {
            padding: 20px;
            background: #fff;
            border-radius: 1px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .chart-title {
            font-size: 1.6rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0px;
        }

        .chart-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 15px;
        }

        .value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1976d2;
        }

        .change {
            font-size: 1.1rem;
            font-weight: bold;
            color: green;
        }

        .indicator {
            font-size: 1rem;
            color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #b0bec5;
            padding: 6px;
            font-size:12.3px;
            text-align: left;
        }
        th {
           
            font-weight: bold;
            color:black;
        }
        tbody tr:nth-child(even) {
            background-color:white;
        }
        tbody tr:nth-child(odd) {
            background-color:#eef3f7;
        }

        /* Responsive adjustments */
        @media only screen and (max-width: 992px) {
            .col.m7 {
                width: 100%;
            }

            .col.m3 {
                width: 100%;
            }
        }
    </style>
</head>

<body>

    
    <?php include 'db.php'; ?>
    <?php include 'nav.php'; ?>

    <div class=" " style="background:#eceff1;margin-top:3px;">
        <div class="row">
            <div class=" col s12 m8">
                <h5 class="  white" style="color:#163a72;padding-top:10px;padding-bottom:10px;padding-left:10px; font-size:23.6px;font-weight:bold; border:1px solid #ddd;">City of Parañaque (National Immunization Program)


                <b>
                (N=<?php
                                     

                                     $sql = "SELECT COUNT(*) AS c FROM nip_table WHERE deleted = 0";
                                 
                                  
     $stmt = $conn->prepare($sql);
     $stmt->execute();
     $result = $stmt->get_result();
     
     while ($row = $result->fetch_assoc()) {
         echo number_format($row['c']).')'; // Format the number
     }
     
     $stmt->close();
     ?>
                </b>
               
                </h5>
                <div id="map"></div>
                <p>Source <a target="_" href="https://www.google.com/maps/place/Para%C3%B1aque,+Metro+Manila/@14.4970029,120.9852437,14z/data=!4m15!1m8!3m7!1s0x3397ce5df9175d09:0x2d041bbb0b2842e5!2sPara%C3%B1aque,+Metro+Manila!3b1!8m2!3d14.4793208!4d121.0197486!16zL20vMDE2dGQx!3m5!1s0x3397ce5df9175d09:0x2d041bbb0b2842e5!8m2!3d14.4793208!4d121.0197486!16zL20vMDE2dGQx?entry=ttu&g_ep=EgoyMDI1MDQwMS4wIKXMDSoASAFQAw%3D%3D">maps.google.com</a></p>
            </div>

            <div class="col s12 m4">
                <div class="chart-container ">
                    <h5 class="chart-title" style="margin-left:10px;color:#163a72;">Number of NIP Per Barangay

                    (N=<?php
                                     

                                     $sql = "SELECT COUNT(*) AS c FROM nip_table WHERE deleted = 0";
                                 
                                  
     $stmt = $conn->prepare($sql);
     $stmt->execute();
     $result = $stmt->get_result();
     
     while ($row = $result->fetch_assoc()) {
         echo number_format($row['c']).')'; // Format the number
     }
     
     $stmt->close();
     ?>
 </strong>
 <?php
 // Get the current date
 $currentDate = date('F d,Y');

 // Calculate the date 28 days ago
 $pastDate = date('F d,Y', strtotime('-95 days'));?>
                    </h5>
                    <div class="" style=" padding:10px;border-radius:1px;">

 
</div>
    
                        <p class="indicator">
                            <table class="responsive">
                                <thead>
                                    <tr>
                                        <th>Barangay</th>
                                        <th>Total</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody >
                                    <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT Barangay, COUNT(*) AS brgy FROM nip_table WHERE deleted = 0 GROUP BY Barangay ORDER BY brgy DESC";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();

                                  
                                    while ($row = $result->fetch_assoc()) { 
                                        $n = array(1,5,3,4,5);
                                       
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;
                                         
                                    ?>
                                    <?php  if($_SESSION['health_center'] ==  $row['Barangay']) { ?>
                                        <tr style="background:#a5d6a7;">
                                            <td><?php echo $row['Barangay']; ?></td>
                                            <td><?php echo $row['brgy']; ?></td>
                                            <td><?php echo $percentage; ?>%</td>
                                        </tr>
                                        <?php }
                                        else { 
                                        ?>
                                        <tr>
                                            <td><?php echo $row['Barangay']; ?></td>
                                            <td><?php echo $row['brgy']; ?></td>
                                            <td><?php echo $percentage; ?>%</td>
                                        </tr>
                                        <?php } ?>
                                    <?php }    $stmt->close(); ?>
                                    <tfoot>
                                        <th>Total</th>
                                        <th><?php echo number_format($total_count); ?></th>
                                        <th>100%</th>
                                    </tfoot>
                                </tbody>
                            </table>
                        </p>
                        <p style="font-size:13.8px;"><span style="background:#a5d6a7;padding:10px;">  Highlighted in green</span> = Your Health Center
                                        </p>











                                        
                    </div>

                </div>
            </div>
             

          <div class="row white">
            <div class="col s12 m6">
            <table class=" responsive ">
        <thead class="">
            <tr>
                <th>Barangay</th>
                <th>Street/Purok</th>
            </tr>
        </thead>
        <tbody>
            <tr>
            <td data-label="Services" >SAN MARTIN DE PORRES</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'SAN MARTIN DE PORRES HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                         <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    <?php } ?>
                </td>
            </tr>
            <tr>
            <td data-label="Services" >SAN ISIDRO</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'SAN ISIDRO HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services" >BF HOMES</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'BF HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
            <td data-label="Services" >SAN ANTONIO</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'SAN ANTONIO HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">DON BOSCO</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'DON BOSCO HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">SUN VALLEY</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'SUN VALLEY HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">STO.NIÑO</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'STO.NIÑO HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">VITALEZ</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'VITALEZ HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">BACLARAN</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'BACLARAN HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">LA HUERTA</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'LA HUERTA HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">DON GALO</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'DON GALO HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">MERVILLE</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'MERVILLE HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td data-label="Services">TAMBO</td>
                <td data-label="Products">
                <?php
                                    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
                                    $result_total = $conn->query($sql_total);
                                    $total_count = 0;
                                    if ($result_total->num_rows > 0) {
                                        $row_total = $result_total->fetch_assoc();
                                        $total_count = $row_total['total'];
                                    }
                                        $sql = "SELECT PurokStreetSitio  FROM nip_table WHERE Barangay = 'TAMBO HEALTH CENTER' And deleted = 0 GROUP BY PurokStreetSitio  ";
                                    $stmt = $conn->prepare($sql);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                   
                                    while ($row = $result->fetch_assoc()) {     
                                        $percentage = ($total_count > 0) ? round(($row['brgy'] / $total_count) * 100, 1) : 0;       
                                    ?>
                   
                   <li class=" "  style="line-height:5px;"><?php echo $row['PurokStreetSitio']; ?></li> 
                    <br>
                    
                    <?php } ?>
                </td>
            </tr>
        </tbody>
    </table>
            </div>
            <div class="col s12 m6">
            <table class="responsive">
    <thead>
        <tr>
            <th>Name of Facility</th>
            <th>No.</th>
            <th>Percentage</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
        $result_total = $conn->query($sql_total);
        $total_count = 0;
        if ($result_total->num_rows > 0) {
            $row_total = $result_total->fetch_assoc();
            $total_count = $row_total['total'];
        }

        $sql = "SELECT COUNT(*) AS totalFacility, NameOfFacility FROM nip_table WHERE deleted = 0 GROUP BY NameOfFacility ORDER BY totalFacility DESC";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $percentage = ($total_count > 0) ? round(($row['totalFacility'] / $total_count) * 100, 1) : 0;
        ?>
            <tr>
                <td><?php echo $row['NameOfFacility']; ?></td>
                <td><?php echo $row['totalFacility']; ?></td>
                <td><?php echo $percentage; ?>%</td>
            </tr>
        <?php } ?>
    </tbody>
</table>

                                     <p>The table shows per facility with total and percentage </p>
            </div>
          </div>
            


       




        </div>
    </div>
    

    <?php
    // Database connection

    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // SQL Query to count records per Barangay 
 
    $sql = "SELECT Barangay, COUNT(*) AS brgy FROM nip_table WHERE  deleted = 0 GROUP BY Barangay";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $result = $stmt->get_result();

    // Store data in an array for use in JavaScript
    $barangayData = [];
    while ($row = $result->fetch_assoc()) {
        $barangayData[$row['Barangay']] = $row['brgy'];
    }
    ?>
    <script>
        // Initialize the map centered at Parañaque City
        var map = L.map('map').setView([14.4843703,121.0025249], 13); // Sample coords


        // Add OpenStreetMap tiles
        L.tileLayer('http://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png', {
           
        }).addTo(map);

        // Health Centers with Coordinates
        var healthCenters = [
            { name: "VITALEZ HEALTH CENTER", coords: [14.508007,121.0060204] },
            { name: "SAN ISIDRO HEALTH CENTER", coords: [14.4693181,121.011065] },
            { name: "SAN ANTONIO HEALTH CENTER", coords: [14.4710524,121.0273762] },
            { name: "BACLARAN HEALTH CENTER", coords: [14.5293829,120.9958199] },
            { name: "SUN VALLEY HEALTH CENTER", coords: [14.4899553,121.029691] },
            { name: "SAN MARTIN DE PORRES HEALTH CENTER", coords: [14.4944448,121.042808] },
            { name: "LA HUERTA HEALTH CENTER", coords: [14.4999812,120.9908704] },
            { name: "STO.NIÑO HEALTH CENTER", coords: [14.501,120.9953121] },
            { name: "MARCELO GREEN HEALTH CENTER", coords: [14.4763622,121.0401454] },
            { name: "MERVILLE HEALTH CENTER", coords: [14.5001135,121.0262862] },
            { name: "DON BOSCO HEALTH CENTER", coords: [14.4818507,121.034941] },
            { name: "DON GALO HEALTH CENTER", coords: [14.5052473,120.99123] },
            { name: "TAMBO HEALTH CENTER", coords: [14.5199258,120.9942123] },
            { name: "BF HEALTH CENTER", coords: [14.4512616,121.0194087] },
            { name: "MOONWALK HEALTH CENTER", coords: [14.4920151,121.0120539] },
            { name: "SAN DIONISIO HEALTH CENTER", coords: [14.4945641,120.9895385] }
        ];
        var streets = [
            { name: "ABAD COMPOUND", coords: [14.4996147,121.0407909] } ,
            { name: "ANCOP MALUGAY ST", coords: [14.5038935,121.0380011] } ,
            { name: "GUIJO ST", coords: [14.5029324,121.0378351] } 
        ];
     
        var pqueBoundary = [
[14.50113,120.94698],
[14.52824,120.94027],
[14.53135,120.99282],
[14.53282,120.99267],
[14.53325,120.99656],
[14.53319,120.99798],
[14.53251,120.99874],
[14.53239,120.99871],
[14.53206,120.99872],
[14.53193,120.99878],
[14.53187,120.99937],
[14.53185,120.99940],
[14.53120,120.99924],
[14.53090,120.99929],
[14.52986,120.99983],
[14.52973,120.99977],
[14.52922,120.99975],
[14.52900,120.99972],
[14.52874,120.99960],
[14.52793,120.99916],
[14.52754,120.99836],
[14.52744,120.99833],
[14.52707,120.99841],
[14.52401,120.99887],
[14.52211,120.99884],
[14.52046,120.99906],
[14.51999,120.99935],
[14.51970,120.99943],
[14.51700,120.99805],
[14.51695,121.00047],
[14.51388,121.00110],
[14.51251,121.00101],
[14.51225,121.00114],
[14.51185,121.00112],
[14.51187,121.00089],
[14.51148,121.00076],
[14.51150,121.00032],
[14.51027,121.00020],
[14.51027,121.00016],
[14.50871,120.99978],
[14.50848,121.00051],
[14.50762,121.00147],
[14.50948,121.00216],
[14.50944,121.00239],
[14.50946,121.00251],
[14.50990,121.00334],
[14.50980,121.00380],
[14.50983,121.00392],
[14.50984,121.00395],
[14.51001,121.00403],
[14.51044,121.00415],
[14.51115,121.00472],
[14.51115,121.00478],
[14.51090,121.00498],
[14.51077,121.00513],
[14.51073,121.00540],
[14.51077,121.00565],
[14.51075,121.00604],
[14.51065,121.00618],
[14.51061,121.00629],
[14.51074,121.00717],
[14.51067,121.00767],
[14.51046,121.00783],
[14.51008,121.00783],
[14.50967,121.00788],
[14.50958,121.00794],
[14.50956,121.00803],
[14.50954,121.00849],
[14.50934,121.00883],
[14.50912,121.00899],
[14.50871,121.00909],
[14.50850,121.00945],
[14.50849,121.00974],
[14.50860,121.01018],
[14.50894,121.01049],
[14.50890,121.01115],
[14.50200,121.01572],
[14.50204,121.01645],
[14.50248,121.01686],
[14.50262,121.01751],
[14.50253,121.01776],
[14.50227,121.01799],
[14.50230,121.01824],
[14.50205,121.01912],
[14.50204,121.01947],
[14.50232,121.02029],
[14.50437,121.02211],
[14.50420,121.02227],
[14.50433,121.02250],
[14.50453,121.02256],
[14.50456,121.02265],
[14.50446,121.02298],
[14.50447,121.02297],
[14.50449,121.02311],
[14.50516,121.02346],
[14.50481,121.02407],
[14.50488,121.02426],
[14.50486,121.02443],
[14.50517,121.02500],
[14.50511,121.02575],
[14.50446,121.02615],
[14.50458,121.02631],
[14.50476,121.02641],
 
        ];
        
        // PHP Data for Barangay counts
        var barangayData = <?php echo json_encode($barangayData); ?>;

        // Function to generate popup content dynamically
        function generatePopup(name) {
            let total = barangayData[name] || 0;
            let male = Math.floor(total * 0.52); // Assuming ~52% male
            let female = total - male;
            return `<b>${name}</b><br>MALE: ${male}<br>FEMALE: ${female}<br>TOTAL: ${total}`;
        }

       
    const pquePolygon = L.polygon(pqueBoundary, {
        color: '#006064 ',
        fillColor: '#004d40    ',
        weight: 0,
        opacity: 0.1,
        fillOpacity: 0.5
}).addTo(map);


        // Add circles for each health center
    healthCenters.forEach(function(center) {
    const circle = L.circle(center.coords, {
        color: '#00695c  ',
        fillColor: '#00695c   ',
        fillOpacity: 0.6,
        opacity: 0.6,
        radius:  100
    }).addTo(map)
      .bindPopup(generatePopup(center.name))
      .on('mouseover', function () {
    this.openPopup();
});

});
    streets.forEach(function(center) {
    const circle = L.circle(center.coords, {
        color: 'blue  ',
        fillColor: 'blue',
        fillOpacity: 0.6,
        opacity: 0.6,
        radius:  20
    }).addTo(map)
      .bindPopup(generatePopup(center.name))
      .on('mouseover', function () {
    this.openPopup();
});

});
L.polyline([pqueBoundary], {
      dashArray: "8 14",
      color: "#00695c",
      weight: 2,

    }).addTo(map);

     






    
    </script>
</body>

</html>