<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Notification System Demo - NIP</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <script src="duplicate_notifications.js"></script>
    <style>
        .demo-container {
            margin-top: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .notification-preview {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .demo-button {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container demo-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align">
                            <i class="material-icons left">notifications</i>
                            Duplicate Notification System Demo
                        </span>
                        
                        <p class="center-align">
                            This demo showcases the enhanced web notification system for duplicate detection in the NIP system.
                        </p>
                        
                        <!-- Features Overview -->
                        <div class="demo-section">
                            <h5>🚀 Key Features</h5>
                            <div class="feature-grid">
                                <div class="feature-card">
                                    <i class="material-icons feature-icon blue-text">notifications</i>
                                    <h6>Multi-Channel Notifications</h6>
                                    <p>Toast, Browser, and Custom notifications</p>
                                </div>
                                <div class="feature-card">
                                    <i class="material-icons feature-icon orange-text">speed</i>
                                    <h6>Real-time Detection</h6>
                                    <p>Instant duplicate checking as you type</p>
                                </div>
                                <div class="feature-card">
                                    <i class="material-icons feature-icon green-text">security</i>
                                    <h6>Smart Prevention</h6>
                                    <p>Blocks exact duplicates automatically</p>
                                </div>
                                <div class="feature-card">
                                    <i class="material-icons feature-icon purple-text">analytics</i>
                                    <h6>Detailed Analysis</h6>
                                    <p>SOUNDEX matching and statistics</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notification Types Demo -->
                        <div class="demo-section">
                            <h5>📢 Notification Types</h5>
                            <p>Test different types of duplicate notifications:</p>
                            
                            <div class="row">
                                <div class="col s12 m6 l3">
                                    <button class="btn red demo-button waves-effect" onclick="showErrorDemo()">
                                        <i class="material-icons left">error</i>Error
                                    </button>
                                    <div class="notification-preview">
                                        <strong>Exact Duplicate Found!</strong><br>
                                        <small>Blocks registration completely</small>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <button class="btn orange demo-button waves-effect" onclick="showWarningDemo()">
                                        <i class="material-icons left">warning</i>Warning
                                    </button>
                                    <div class="notification-preview">
                                        <strong>Similar Record Found!</strong><br>
                                        <small>Potential duplicate detected</small>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <button class="btn blue demo-button waves-effect" onclick="showInfoDemo()">
                                        <i class="material-icons left">info</i>Info
                                    </button>
                                    <div class="notification-preview">
                                        <strong>Same Household</strong><br>
                                        <small>Another child from same family</small>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <button class="btn green demo-button waves-effect" onclick="showSuccessDemo()">
                                        <i class="material-icons left">check_circle</i>Success
                                    </button>
                                    <div class="notification-preview">
                                        <strong>No Duplicates!</strong><br>
                                        <small>Safe to proceed with registration</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Features -->
                        <div class="demo-section">
                            <h5>⚡ Advanced Features</h5>
                            <div class="row">
                                <div class="col s12 m6">
                                    <button class="btn purple demo-button waves-effect" onclick="showBrowserNotificationDemo()">
                                        <i class="material-icons left">desktop_windows</i>Browser Notification
                                    </button>
                                    <p><small>Shows system-level notifications (requires permission)</small></p>
                                </div>
                                
                                <div class="col s12 m6">
                                    <button class="btn teal demo-button waves-effect" onclick="showSoundDemo()">
                                        <i class="material-icons left">volume_up</i>Sound Alert
                                    </button>
                                    <p><small>Plays audio alert for critical duplicates</small></p>
                                </div>
                                
                                <div class="col s12 m6">
                                    <button class="btn indigo demo-button waves-effect" onclick="showCustomNotificationDemo()">
                                        <i class="material-icons left">widgets</i>Custom Notification
                                    </button>
                                    <p><small>Rich notifications with action buttons</small></p>
                                </div>
                                
                                <div class="col s12 m6">
                                    <button class="btn brown demo-button waves-effect" onclick="clearAllNotifications()">
                                        <i class="material-icons left">clear_all</i>Clear All
                                    </button>
                                    <p><small>Remove all active notifications</small></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Integration Examples -->
                        <div class="demo-section">
                            <h5>🔗 System Integration</h5>
                            <p>See how the notification system integrates with the NIP system:</p>
                            
                            <div class="row">
                                <div class="col s12 m4">
                                    <a href="realtime_duplicate_check.php" class="btn blue waves-effect">
                                        <i class="material-icons left">speed</i>Real-time Checker
                                    </a>
                                    <p><small>Live duplicate checking as you type</small></p>
                                </div>
                                
                                <div class="col s12 m4">
                                    <a href="duplicate_checker.php" class="btn green waves-effect">
                                        <i class="material-icons left">search</i>Duplicate Manager
                                    </a>
                                    <p><small>Find and manage existing duplicates</small></p>
                                </div>
                                
                                <div class="col s12 m4">
                                    <a href="test_duplicate_check.php" class="btn orange waves-effect">
                                        <i class="material-icons left">bug_report</i>System Test
                                    </a>
                                    <p><small>Test duplicate detection functions</small></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Statistics -->
                        <div class="demo-section">
                            <h5>📊 System Statistics</h5>
                            <div id="systemStats">
                                <p>Loading system statistics...</p>
                            </div>
                        </div>
                        
                        <div class="center-align" style="margin-top: 30px;">
                            <a href="db.php" class="btn large blue waves-effect">
                                <i class="material-icons left">add</i>Try Registration Form
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        // Demo functions
        function showErrorDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.showDuplicateError(
                    'Exact Duplicate Found!', 
                    'This child (Juan Dela Cruz, DOB: 2020-01-15) is already registered with ID: 12345',
                    [
                        { text: 'View Record', href: '#', icon: 'visibility' },
                        { text: 'Contact Admin', action: 'alert("Contact admin for assistance")', icon: 'support_agent' }
                    ]
                );
            }
        }
        
        function showWarningDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.showDuplicateWarning(
                    'Similar Record Found!', 
                    'Possible duplicate: Jon Dela Cruz (ID: 67890) with same birth date. Please verify.',
                    [
                        { text: 'Compare Records', href: '#', icon: 'compare' }
                    ]
                );
            }
        }
        
        function showInfoDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.showDuplicateInfo(
                    'Same Household Detected', 
                    'Another child from same household: Maria Dela Cruz (ID: 11111, DOB: 2018-05-20)',
                    [
                        { text: 'View Family', href: '#', icon: 'family_restroom' }
                    ]
                );
            }
        }
        
        function showSuccessDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.showNotification('success', 'No Duplicates Found!', 'This registration is safe to proceed. No similar records detected.');
            }
        }
        
        function showBrowserNotificationDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.showBrowserNotification(
                    'Duplicate Alert System', 
                    'This is a browser notification for duplicate detection', 
                    'warning'
                );
            }
        }
        
        function showSoundDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.playAlertSound();
                duplicateNotifier.showToast('info', 'Sound Alert', 'Audio alert played for duplicate detection');
            }
        }
        
        function showCustomNotificationDemo() {
            if (duplicateNotifier) {
                duplicateNotifier.showCustomNotification(
                    'warning',
                    'Custom Notification Demo',
                    'This is a rich custom notification with multiple action buttons and enhanced styling.',
                    [
                        { text: 'Primary Action', href: '#', icon: 'star' },
                        { text: 'Secondary Action', action: 'alert("Secondary action clicked")', icon: 'settings' },
                        { text: 'Learn More', href: 'https://example.com', icon: 'help' }
                    ]
                );
            }
        }
        
        function clearAllNotifications() {
            if (duplicateNotifier) {
                duplicateNotifier.clearAllNotifications();
                duplicateNotifier.showToast('success', 'Cleared', 'All notifications have been cleared');
            }
        }
        
        // Load system statistics
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Simulate loading statistics
            setTimeout(function() {
                document.getElementById('systemStats').innerHTML = `
                    <div class="row">
                        <div class="col s6 m3">
                            <div class="card blue lighten-4 center-align">
                                <div class="card-content">
                                    <h4>1,247</h4>
                                    <p>Total Records</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s6 m3">
                            <div class="card orange lighten-4 center-align">
                                <div class="card-content">
                                    <h4>23</h4>
                                    <p>Duplicates Found</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s6 m3">
                            <div class="card green lighten-4 center-align">
                                <div class="card-content">
                                    <h4>98.2%</h4>
                                    <p>Data Quality</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s6 m3">
                            <div class="card purple lighten-4 center-align">
                                <div class="card-content">
                                    <h4>156</h4>
                                    <p>Alerts Sent</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }, 1000);
            
            // Show welcome notification
            setTimeout(function() {
                if (duplicateNotifier) {
                    duplicateNotifier.showNotification('info', 'Welcome to Notification Demo!', 'Try the different notification types using the buttons above.');
                }
            }, 2000);
        });
    </script>
</body>
</html>
