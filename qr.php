<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Scanner - ChildNumber</title>

    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- QR Code Scanner Library -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

    <style>
        body {
           
            min-height: 100vh;
            font-family: 'Roboto', sans-serif;
        }

        .scanner-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            margin: 20px auto;
            max-width: 800px;
            overflow: hidden;
        }

        .scanner-header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .scanner-body {
            padding: 30px;
        }

        #qr-reader {
            border: 3px dashed #ddd;
            border-radius: 10px;
            margin: 20px 0;
            min-height: 300px;
            position: relative;
        }

        #qr-reader__dashboard_section_csr {
            display: none !important;
        }

        .scan-result {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }

        .scan-error {
            background: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }

        .camera-controls {
            text-align: center;
            margin: 20px 0;
        }

        .camera-select {
            margin: 10px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active {
            background-color: #4CAF50;
            animation: pulse 2s infinite;
        }

        .status-inactive {
            background-color: #f44336;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .child-info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .scan-history {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin: 20px 0;
        }

        .history-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .btn-floating {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .scanner-container {
                margin: 10px;
                border-radius: 10px;
            }

            .scanner-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="scanner-container">
            <!-- Header -->
            <div class="scanner-header">
                <h4><i class="material-icons left">qr_code_scanner</i>QR Code Scanner</h4>
                <p>Scan QR codes to retrieve ChildNumber information</p>
                <div class="scanner-status">
                    <span class="status-indicator status-inactive" id="statusIndicator"></span>
                    <span id="statusText">Scanner Inactive</span>
                </div>
            </div>

            <!-- Scanner Body -->
            <div class="scanner-body">
                <!-- Camera Controls -->
                <div class="camera-controls">
                    <div class="row">
                        <div class="col s12 m6">
                            <button id="startBtn" class="btn green waves-effect waves-light">
                                <i class="material-icons left">videocam</i>Start Scanner
                            </button>
                            <button id="stopBtn" class="btn red waves-effect waves-light" style="display: none;">
                                <i class="material-icons left">videocam_off</i>Stop Scanner
                            </button>
                        </div>
                        <div class="col s12 m6">
                            <select id="cameraSelect" class="browser-default camera-select" style="display: none;">
                                <option value="">Select Camera</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- QR Reader -->
                <div id="qr-reader"></div>

                <!-- Scan Results -->
                <div id="scanResult" class="scan-result">
                    <h6><i class="material-icons left">check_circle</i>Scan Successful!</h6>
                    <p><strong>Scanned Data:</strong> <span id="scannedData"></span></p>
                    <p><strong>Timestamp:</strong> <span id="scanTimestamp"></span></p>
                </div>

                <!-- Scan Error -->
                <div id="scanError" class="scan-error">
                    <h6><i class="material-icons left">error</i>Scan Error</h6>
                    <p id="errorMessage"></p>
                </div>

                <!-- Child Information Card -->
                <div id="childInfoCard" class="child-info-card">
                    <h5><i class="material-icons left">child_care</i>Child Information</h5>
                    <div id="childDetails">
                        <!-- Child details will be populated here -->
                    </div>
                </div>

                <!-- Scan History -->
                <div class="row">
                    <div class="col s12">
                        <h6><i class="material-icons left">history</i>Scan History</h6>
                        <div id="scanHistory" class="scan-history">
                            <p class="center-align grey-text">No scans yet</p>
                        </div>
                        <div class="center-align">
                            <button id="clearHistoryBtn" class="btn-small orange waves-effect waves-light">
                                <i class="material-icons left">clear_all</i>Clear History
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="row">
                    <div class="col s12">
                        <div class="card-panel blue lighten-5">
                            <h6><i class="material-icons left">info</i>Instructions</h6>
                            <ul>
                                <li>• Click "Start Scanner" to begin scanning</li>
                                <li>• Point your camera at a QR code containing ChildNumber</li>
                                <li>• The scanner will automatically detect and process the code</li>
                                <li>• Scanned data will appear below with child information</li>
                                <li>• Use the camera selector to switch between cameras</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <a class="btn-floating btn-large blue pulse" id="scanAgainBtn" style="display: none;">
        <i class="material-icons">qr_code_scanner</i>
    </a>

    <!-- Materialize JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>

    <script>
        class QRScanner {
            constructor() {
                this.html5QrCode = null;
                this.isScanning = false;
                this.cameras = [];
                this.selectedCameraId = null;
                this.scanHistory = JSON.parse(localStorage.getItem('qr_scan_history') || '[]');

                this.initializeElements();
                this.bindEvents();
                this.loadScanHistory();
                this.getCameras();
            }

            initializeElements() {
                this.startBtn = document.getElementById('startBtn');
                this.stopBtn = document.getElementById('stopBtn');
                this.cameraSelect = document.getElementById('cameraSelect');
                this.scanResult = document.getElementById('scanResult');
                this.scanError = document.getElementById('scanError');
                this.scannedData = document.getElementById('scannedData');
                this.scanTimestamp = document.getElementById('scanTimestamp');
                this.childInfoCard = document.getElementById('childInfoCard');
                this.childDetails = document.getElementById('childDetails');
                this.scanHistoryEl = document.getElementById('scanHistory');
                this.clearHistoryBtn = document.getElementById('clearHistoryBtn');
                this.statusIndicator = document.getElementById('statusIndicator');
                this.statusText = document.getElementById('statusText');
                this.scanAgainBtn = document.getElementById('scanAgainBtn');
            }

            bindEvents() {
                this.startBtn.addEventListener('click', () => this.startScanning());
                this.stopBtn.addEventListener('click', () => this.stopScanning());
                this.cameraSelect.addEventListener('change', (e) => this.switchCamera(e.target.value));
                this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
                this.scanAgainBtn.addEventListener('click', () => this.startScanning());
            }

            async getCameras() {
                try {
                    const devices = await Html5Qrcode.getCameras();
                    this.cameras = devices;

                    if (devices && devices.length > 0) {
                        this.populateCameraSelect(devices);
                        this.selectedCameraId = devices[0].id;
                    } else {
                        this.showError('No cameras found on this device');
                    }
                } catch (err) {
                    this.showError('Error accessing cameras: ' + err.message);
                }
            }

            populateCameraSelect(cameras) {
                this.cameraSelect.innerHTML = '<option value="">Select Camera</option>';
                cameras.forEach((camera, index) => {
                    const option = document.createElement('option');
                    option.value = camera.id;
                    option.textContent = camera.label || `Camera ${index + 1}`;
                    this.cameraSelect.appendChild(option);
                });
                this.cameraSelect.style.display = 'block';
            }

            async startScanning() {
                if (this.isScanning) return;

                try {
                    this.html5QrCode = new Html5Qrcode("qr-reader");

                    const config = {
                        fps: 10,
                        qrbox: { width: 250, height: 250 },
                        aspectRatio: 1.0
                    };

                    await this.html5QrCode.start(
                        this.selectedCameraId || { facingMode: "environment" },
                        config,
                        (decodedText, decodedResult) => this.onScanSuccess(decodedText, decodedResult),
                        (errorMessage) => this.onScanError(errorMessage)
                    );

                    this.isScanning = true;
                    this.updateUI();
                    this.updateStatus(true, 'Scanner Active - Point camera at QR code');

                } catch (err) {
                    this.showError('Failed to start scanner: ' + err.message);
                }
            }

            async stopScanning() {
                if (!this.isScanning || !this.html5QrCode) return;

                try {
                    await this.html5QrCode.stop();
                    this.html5QrCode.clear();
                    this.isScanning = false;
                    this.updateUI();
                    this.updateStatus(false, 'Scanner Stopped');
                } catch (err) {
                    this.showError('Failed to stop scanner: ' + err.message);
                }
            }

            async switchCamera(cameraId) {
                if (!cameraId) return;

                this.selectedCameraId = cameraId;

                if (this.isScanning) {
                    await this.stopScanning();
                    setTimeout(() => this.startScanning(), 500);
                }
            }

            onScanSuccess(decodedText, decodedResult) {
                console.log('QR Code scanned:', decodedText);

                // Process the scanned data
                this.processScannedData(decodedText);

                // Add to history
                this.addToHistory(decodedText);

                // Show success
                this.showSuccess(decodedText);

                // Auto-stop scanner after successful scan
                setTimeout(() => this.stopScanning(), 1000);
            }

            onScanError(errorMessage) {
                // Ignore frequent scan errors (normal when no QR code is visible)
                // Only log actual errors
                if (!errorMessage.includes('No QR code found')) {
                    console.warn('QR Scan error:', errorMessage);
                }
            }

            processScannedData(data) {
                // Try to extract ChildNumber from the scanned data
                let childNumber = null;

                try {
                    // Check if it's JSON
                    const jsonData = JSON.parse(data);
                    childNumber = jsonData.ChildNumber || jsonData.childNumber || jsonData.child_number;
                } catch (e) {
                    // Not JSON, check if it's a direct ChildNumber
                    if (data.match(/^\d+$/)) {
                        childNumber = data;
                    } else {
                        // Try to extract number from text
                        const match = data.match(/(?:ChildNumber|childNumber|child_number)[:=]\s*(\d+)/i);
                        if (match) {
                            childNumber = match[1];
                        }
                    }
                }

                if (childNumber) {
                    this.fetchChildInformation(childNumber);
                } else {
                    this.showError('No ChildNumber found in scanned QR code');
                }
            }

            async fetchChildInformation(childNumber) {
                try {
                    // Show loading state
                    this.childDetails.innerHTML = '<div class="center-align"><div class="preloader-wrapper small active"><div class="spinner-layer spinner-blue-only"><div class="circle-clipper left"><div class="circle"></div></div><div class="gap-patch"><div class="circle"></div></div><div class="circle-clipper right"><div class="circle"></div></div></div></div><p>Loading child information...</p></div>';
                    this.childInfoCard.style.display = 'block';

                    // Make AJAX request to fetch child information
                    const response = await fetch('get_child_info.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ childNumber: childNumber })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.displayChildInformation(result.data);
                    } else {
                        this.childDetails.innerHTML = `<p><i class="material-icons left">error</i>Child not found: ${result.message}</p>`;
                    }

                } catch (error) {
                    // Fallback: Display the ChildNumber even if backend is not available
                    this.displayChildInformation({
                        childNumber: childNumber,
                        name: 'Information not available',
                        age: 'N/A',
                        gender: 'N/A',
                        location: 'N/A',
                        lastUpdated: 'Backend service unavailable'
                    });
                }
            }

            displayChildInformation(childData) {
                this.childDetails.innerHTML = `
                    <div class="row">
                        <div class="col s12 m6">
                            <p><strong><i class="material-icons left">fingerprint</i>Child Number:</strong> ${childData.childNumber || 'N/A'}</p>
                            <p><strong><i class="material-icons left">person</i>Name:</strong> ${childData.name || 'N/A'}</p>
                            <p><strong><i class="material-icons left">cake</i>Age:</strong> ${childData.age || 'N/A'}</p>
                        </div>
                        <div class="col s12 m6">
                            <p><strong><i class="material-icons left">wc</i>Gender:</strong> ${childData.gender || 'N/A'}</p>
                            <p><strong><i class="material-icons left">location_on</i>Location:</strong> ${childData.location || 'N/A'}</p>
                            <p><strong><i class="material-icons left">access_time</i>Last Updated:</strong> ${childData.lastUpdated || 'N/A'}</p>
                        </div>
                    </div>
                `;
            }

            showSuccess(data) {
                this.hideMessages();
                this.scannedData.textContent = data;
                this.scanTimestamp.textContent = new Date().toLocaleString();
                this.scanResult.style.display = 'block';
                this.scanAgainBtn.style.display = 'block';

                // Show toast notification
                M.toast({
                    html: '<i class="material-icons left">check</i>QR Code scanned successfully!',
                    classes: 'green',
                    displayLength: 3000
                });
            }

            showError(message) {
                this.hideMessages();
                document.getElementById('errorMessage').textContent = message;
                this.scanError.style.display = 'block';

                // Show toast notification
                M.toast({
                    html: '<i class="material-icons left">error</i>' + message,
                    classes: 'red',
                    displayLength: 4000
                });
            }

            hideMessages() {
                this.scanResult.style.display = 'none';
                this.scanError.style.display = 'none';
                this.scanAgainBtn.style.display = 'none';
            }

            updateUI() {
                if (this.isScanning) {
                    this.startBtn.style.display = 'none';
                    this.stopBtn.style.display = 'inline-block';
                } else {
                    this.startBtn.style.display = 'inline-block';
                    this.stopBtn.style.display = 'none';
                }
            }

            updateStatus(active, text) {
                this.statusIndicator.className = `status-indicator ${active ? 'status-active' : 'status-inactive'}`;
                this.statusText.textContent = text;
            }

            addToHistory(data) {
                const historyItem = {
                    data: data,
                    timestamp: new Date().toISOString(),
                    id: Date.now()
                };

                this.scanHistory.unshift(historyItem);

                // Keep only last 50 scans
                if (this.scanHistory.length > 50) {
                    this.scanHistory = this.scanHistory.slice(0, 50);
                }

                localStorage.setItem('qr_scan_history', JSON.stringify(this.scanHistory));
                this.loadScanHistory();
            }

            loadScanHistory() {
                if (this.scanHistory.length === 0) {
                    this.scanHistoryEl.innerHTML = '<p class="center-align grey-text">No scans yet</p>';
                    return;
                }

                this.scanHistoryEl.innerHTML = this.scanHistory.map(item => `
                    <div class="history-item">
                        <div>
                            <strong>${item.data.substring(0, 50)}${item.data.length > 50 ? '...' : ''}</strong><br>
                            <small class="grey-text">${new Date(item.timestamp).toLocaleString()}</small>
                        </div>
                        <button class="btn-small blue waves-effect waves-light" onclick="scanner.processScannedData('${item.data.replace(/'/g, "\\'")}')">
                            <i class="material-icons">search</i>
                        </button>
                    </div>
                `).join('');
            }

            clearHistory() {
                this.scanHistory = [];
                localStorage.removeItem('qr_scan_history');
                this.loadScanHistory();

                M.toast({
                    html: '<i class="material-icons left">delete</i>Scan history cleared',
                    classes: 'orange',
                    displayLength: 2000
                });
            }
        }

        // Initialize scanner when page loads
        let scanner;
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Materialize components
            M.AutoInit();

            // Initialize QR Scanner
            scanner = new QRScanner();
        });

        // Handle page visibility change
        document.addEventListener('visibilitychange', function() {
            if (document.hidden && scanner && scanner.isScanning) {
                scanner.stopScanning();
            }
        });
    </script>
</body>
</html>
