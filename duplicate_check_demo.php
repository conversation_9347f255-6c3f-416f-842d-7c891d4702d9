<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Check Demo - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        .demo-container {
            margin-top: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .example-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .blocked-example {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .allowed-example {
            border-left-color: #4caf50;
            background: #e8f5e8;
        }
        .flow-step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #2196f3;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container demo-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align">
                            <i class="material-icons left">security</i>
                            Duplicate Check System Demo
                        </span>
                        
                        <p class="center-align">
                            Learn how the duplicate prevention system protects your NIP database using 
                            <strong>First Name + Last Name + Birth Date</strong> matching.
                        </p>
                        
                        <!-- How It Works -->
                        <div class="demo-section">
                            <h4>🔄 How the Duplicate Check Works</h4>
                            
                            <div class="flow-step">
                                <div class="step-number">1</div>
                                <div>
                                    <strong>User Submits Registration Form</strong><br>
                                    User fills out child registration form with first name, last name, birth date, and other details
                                </div>
                            </div>
                            
                            <div class="flow-step">
                                <div class="step-number">2</div>
                                <div>
                                    <strong>Duplicate Check Triggered</strong><br>
                                    System automatically checks database for existing records with same first name, last name, and birth date
                                </div>
                            </div>
                            
                            <div class="flow-step">
                                <div class="step-number">3</div>
                                <div>
                                    <strong>Database Search</strong><br>
                                    Searches nip_table for exact matches: <code>NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ?</code>
                                </div>
                            </div>
                            
                            <div class="flow-step">
                                <div class="step-number">4</div>
                                <div>
                                    <strong>Decision Point</strong><br>
                                    If duplicate found: Registration is blocked and user is notified<br>
                                    If no duplicate: Registration proceeds normally
                                </div>
                            </div>
                            
                            <div class="flow-step">
                                <div class="step-number">5</div>
                                <div>
                                    <strong>Logging & Response</strong><br>
                                    Blocked attempts are logged for audit trail, user receives clear feedback with next steps
                                </div>
                            </div>
                        </div>
                        
                        <!-- Examples -->
                        <div class="demo-section">
                            <h4>📝 Examples</h4>
                            
                            <h5>✅ Registration Allowed (No Duplicates)</h5>
                            <div class="example-card allowed-example">
                                <strong>Scenario:</strong> Registering a new child<br>
                                <strong>Input:</strong> Juan Santos, DOB: 2020-01-15<br>
                                <strong>Database Check:</strong> No existing record found<br>
                                <strong>Result:</strong> ✅ Registration proceeds successfully
                            </div>
                            
                            <div class="example-card allowed-example">
                                <strong>Scenario:</strong> Different child with similar name<br>
                                <strong>Input:</strong> Juan Garcia, DOB: 2020-01-15<br>
                                <strong>Database Check:</strong> Different last name, no match<br>
                                <strong>Result:</strong> ✅ Registration proceeds successfully
                            </div>
                            
                            <h5>❌ Registration Blocked (Duplicates Found)</h5>
                            <div class="example-card blocked-example">
                                <strong>Scenario:</strong> Attempting to register same child again<br>
                                <strong>Input:</strong> Juan Santos, DOB: 2020-01-15<br>
                                <strong>Database Check:</strong> Exact match found (ID: 123, Family Serial: CHO-20240101-ABC123)<br>
                                <strong>Result:</strong> ❌ Registration blocked, user notified
                            </div>
                            
                            <div class="example-card blocked-example">
                                <strong>Scenario:</strong> Identical twins registered separately<br>
                                <strong>Input:</strong> Maria Santos, DOB: 2020-01-15<br>
                                <strong>Database Check:</strong> Same name and birth date found<br>
                                <strong>Result:</strong> ❌ Registration blocked (prevents twin confusion)
                            </div>
                        </div>
                        
                        <!-- Technical Details -->
                        <div class="demo-section">
                            <h4>⚙️ Technical Implementation</h4>
                            
                            <h5>Database Query:</h5>
                            <div class="example-card">
                                <code>
                                SELECT id, NameOfChild, LastNameOfChild, DateOfBirth, FamilySerialNumber, DateOfRegistration<br>
                                FROM nip_table<br>
                                WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ?<br>
                                AND (deleted IS NULL OR deleted = 0)
                                </code>
                            </div>
                            
                            <h5>Key Features:</h5>
                            <ul>
                                <li><strong>Exact Matching:</strong> All three fields must match exactly</li>
                                <li><strong>Case Sensitive:</strong> "Juan" and "juan" are treated as different</li>
                                <li><strong>Active Records Only:</strong> Ignores soft-deleted records</li>
                                <li><strong>Prepared Statements:</strong> Prevents SQL injection attacks</li>
                                <li><strong>Error Handling:</strong> Graceful fallback if database issues occur</li>
                                <li><strong>Audit Trail:</strong> All blocked attempts are logged</li>
                            </ul>
                            
                            <h5>Error Handling:</h5>
                            <ul>
                                <li><strong>Database Connection Issues:</strong> Shows warning, allows registration</li>
                                <li><strong>Query Errors:</strong> Logs error, shows warning to user</li>
                                <li><strong>Missing Data:</strong> Skips check if required fields are empty</li>
                            </ul>
                        </div>
                        
                        <!-- Benefits -->
                        <div class="demo-section">
                            <h4>🎯 Benefits</h4>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <h5>Data Quality:</h5>
                                    <ul>
                                        <li>Prevents duplicate child records</li>
                                        <li>Maintains database integrity</li>
                                        <li>Reduces data cleanup needs</li>
                                        <li>Improves reporting accuracy</li>
                                    </ul>
                                </div>
                                
                                <div class="col s12 m6">
                                    <h5>User Experience:</h5>
                                    <ul>
                                        <li>Clear error messages</li>
                                        <li>Immediate feedback</li>
                                        <li>Actionable guidance</li>
                                        <li>No data loss</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <h5>Administrative:</h5>
                                    <ul>
                                        <li>Complete audit trail</li>
                                        <li>Blocked attempt logging</li>
                                        <li>System health monitoring</li>
                                        <li>Easy troubleshooting</li>
                                    </ul>
                                </div>
                                
                                <div class="col s12 m6">
                                    <h5>Healthcare:</h5>
                                    <ul>
                                        <li>Accurate immunization records</li>
                                        <li>No duplicate treatments</li>
                                        <li>Proper patient tracking</li>
                                        <li>Better health outcomes</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Live Demo -->
                        <div class="demo-section">
                            <h4>🎮 Try It Yourself</h4>
                            
                            <div class="row">
                                <div class="col s12 m6 l3">
                                    <a href="nip.php" class="btn blue waves-effect full-width">
                                        <i class="material-icons left">edit</i>Registration Form
                                    </a>
                                    <p><small>Try registering a child</small></p>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <a href="test_duplicate_check_simple.php" class="btn orange waves-effect full-width">
                                        <i class="material-icons left">science</i>Test System
                                    </a>
                                    <p><small>Automated testing</small></p>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <a href="duplicate_checker.php" class="btn green waves-effect full-width">
                                        <i class="material-icons left">search</i>View Duplicates
                                    </a>
                                    <p><small>Manage existing records</small></p>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <a href="database_health_check.php" class="btn purple waves-effect full-width">
                                        <i class="material-icons left">health_and_safety</i>Health Check
                                    </a>
                                    <p><small>System diagnostics</small></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Test Scenarios -->
                        <div class="demo-section">
                            <h4>🧪 Suggested Test Scenarios</h4>
                            
                            <div class="collection">
                                <div class="collection-item">
                                    <span class="badge green white-text">ALLOWED</span>
                                    <strong>New Child:</strong> Register a child with unique name and birth date
                                </div>
                                <div class="collection-item">
                                    <span class="badge red white-text">BLOCKED</span>
                                    <strong>Exact Duplicate:</strong> Try to register the same child again
                                </div>
                                <div class="collection-item">
                                    <span class="badge green white-text">ALLOWED</span>
                                    <strong>Different Name:</strong> Same birth date but different first or last name
                                </div>
                                <div class="collection-item">
                                    <span class="badge green white-text">ALLOWED</span>
                                    <strong>Different Date:</strong> Same name but different birth date
                                </div>
                                <div class="collection-item">
                                    <span class="badge red white-text">BLOCKED</span>
                                    <strong>Case Sensitivity:</strong> "Juan" vs "JUAN" (both blocked if same last name and DOB)
                                </div>
                            </div>
                        </div>
                        
                        <div class="center-align" style="margin-top: 30px;">
                            <a href="nip.php" class="btn large blue waves-effect">
                                <i class="material-icons left">play_arrow</i>Start Testing Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Add animation to flow steps
            const flowSteps = document.querySelectorAll('.flow-step');
            flowSteps.forEach((step, index) => {
                step.style.opacity = '0';
                step.style.transform = 'translateX(-20px)';
                
                setTimeout(() => {
                    step.style.transition = 'all 0.5s ease';
                    step.style.opacity = '1';
                    step.style.transform = 'translateX(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
