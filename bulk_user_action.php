<?php
session_start();
include 'db.php';

// Check if user is logged in and has admin privileges
if (!isset($_SESSION['health_center'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $action = $_POST["action"] ?? '';
    $user_ids = $_POST["user_ids"] ?? [];
    $reason = $_POST["reason"] ?? '';
    
    // Validate input
    if (empty($action) || empty($user_ids) || !is_array($user_ids)) {
        echo json_encode(['success' => false, 'message' => 'Invalid input data']);
        exit();
    }
    
    // Validate action type
    if (!in_array($action, ['approve', 'disapprove'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid action type']);
        exit();
    }
    
    // For disapprove action, reason is required
    if ($action === 'disapprove' && empty(trim($reason))) {
        echo json_encode(['success' => false, 'message' => 'Reason is required for disapproval']);
        exit();
    }
    
    try {
        // Begin transaction
        $conn->begin_transaction();
        
        $success_count = 0;
        $performed_by = $_SESSION['username'] ?? $_SESSION['health_center'] ?? 'Admin';
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        
        foreach ($user_ids as $user_id) {
            $user_id = intval($user_id);
            if ($user_id <= 0) continue;
            
            if ($action === 'approve') {
                // Approve user
                $approved_date = date('Y-m-d H:i:s');
                $sql = "UPDATE health_facility SET 
                        approved = 1, 
                        approved_date = ?, 
                        disapproved_date = NULL, 
                        disapproval_reason = NULL
                        WHERE id = ? AND (approved IS NULL OR approved = 2)";
                
                $stmt = $conn->prepare($sql);
                if ($stmt) {
                    $stmt->bind_param("si", $approved_date, $user_id);
                    if ($stmt->execute() && $stmt->affected_rows > 0) {
                        $success_count++;
                        
                        // Log the action
                        $audit_sql = "INSERT INTO user_action_logs (user_id, table_name, action_type, action_date, performed_by, reason, ip_address, user_agent) 
                                      VALUES (?, 'health_facility', 'BULK_APPROVED', NOW(), ?, 'Bulk approval action', ?, ?)";
                        $audit_stmt = $conn->prepare($audit_sql);
                        if ($audit_stmt) {
                            $audit_stmt->bind_param("isss", $user_id, $performed_by, $ip_address, $user_agent);
                            $audit_stmt->execute();
                            $audit_stmt->close();
                        }
                    }
                    $stmt->close();
                }
                
            } else if ($action === 'disapprove') {
                // Disapprove user
                $disapproved_date = date('Y-m-d H:i:s');
                $sanitized_reason = htmlspecialchars($reason, ENT_QUOTES, 'UTF-8');
                
                $sql = "UPDATE health_facility SET
                        approved = 0,
                        disapproved_date = ?,
                        disapproval_reason = ?,
                        approved_date = NULL
                        WHERE id = ? AND (approved IS NULL OR approved = 2)";
                
                $stmt = $conn->prepare($sql);
                if ($stmt) {
                    $stmt->bind_param("ssi", $disapproved_date, $sanitized_reason, $user_id);
                    if ($stmt->execute() && $stmt->affected_rows > 0) {
                        $success_count++;
                        
                        // Log the action
                        $audit_sql = "INSERT INTO user_action_logs (user_id, table_name, action_type, action_date, performed_by, reason, ip_address, user_agent) 
                                      VALUES (?, 'health_facility', 'BULK_DISAPPROVED', NOW(), ?, ?, ?, ?)";
                        $audit_stmt = $conn->prepare($audit_sql);
                        if ($audit_stmt) {
                            $audit_stmt->bind_param("issss", $user_id, $performed_by, $sanitized_reason, $ip_address, $user_agent);
                            $audit_stmt->execute();
                            $audit_stmt->close();
                        }
                    }
                    $stmt->close();
                }
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        if ($success_count > 0) {
            echo json_encode([
                'success' => true, 
                'count' => $success_count,
                'message' => "Successfully {$action}d {$success_count} users"
            ]);
        } else {
            echo json_encode([
                'success' => false, 
                'message' => "No users were {$action}d. They may not be in pending status."
            ]);
        }
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        error_log("Bulk user action error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}

$conn->close();
?>
