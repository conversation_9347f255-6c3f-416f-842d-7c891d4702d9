<?php

error_reporting(0);

// Include earliest consultation date function
include 'earliest_from_post.php';

$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Connection failed ' . mysqli_error($conn));
}
date_default_timezone_set('Asia/Manila');
 
if (isset($_POST['submitBtn'])) {

    $today = date("Ymd");
    $rand = strtoupper(substr(uniqid(sha1(time())),0,10));
     
     
    



    $DateOfRegistration = date('Y-m-d');
   // $UHCRead = $_POST['UHCRead'];								 
    $DateOfBirth = $_POST['DateOfBirth']; 
    $AgeofChild = $_POST['AgeofChild']; 
    $FamilySerialNumber =  'CHO-'.$today . $rand;

     
    $ChildNumber = rand(100, 999);  
     
    $LastNameOfChild = $_POST['LastNameOfChild'];
    $NameOfChild = $_POST['NameOfChild']; 
    $middlename_of_child = $_POST['middlename_of_child'];
    $Sex = $_POST['Sex']; 
    $NameofMother = $_POST['NameofMother']; 
    $BirthdateofMother = $_POST['BirthdateofMother']; 
    $AgeeofMother = $_POST['AgeeofMother']; 
    $Barangay = $_POST['Barangay']; 
    $PurokStreetSitio = $_POST['PurokStreetSitio']; 
    $HouseNo = $_POST['HouseNo']; 
    $Address = $_POST['Address']; 
    $PlaceofDelivery = $_POST['PlaceofDelivery'];

$NameofFacility = $_POST['NameofFacility'];
$Attendant = $_POST['Attendant'];
$TypeofDelivery = $_POST['TypeofDelivery'];
$BirthWeightInGrams = $_POST['BirthWeightInGrams'];
 
$BirthWeightClassification = $_POST['BirthWeightClassification'];
 
$WastheChildReferredforNewbornScreening = $_POST['WastheChildReferredforNewbornScreening'];
$DateReferredforNewbornScreening = $_POST['DateReferredforNewbornScreening'];
$DateofConsultationNewbornScreening = $_POST['DateofConsultationNewbornScreening'];
$TTStatusofMother = $_POST['TTStatusofMother'];
$Dateassessed = $_POST['Dateassessed'];
$WastheChildProtectedatBirth = $_POST['WastheChildProtectedatBirth'];
$DateofConsultationCPAB = $_POST['DateofConsultationCPAB'];									
$BCG = $_POST['BCG'];
$DateBCGwasgiven = $_POST['DateBCGwasgiven'];
$DateofConsultationBCG = $_POST['DateofConsultationBCG'];									
$PENTAHIB1 = $_POST['PENTAHIB1'];
$DatePenta1wasgiven = $_POST['DatePenta1wasgiven'];										
$DateofConsultationPENTAHIB1 = $_POST['DateofConsultationPENTAHIB1'];								
$PENTAHIB2 = $_POST['PENTAHIB2'];
$DatePentahib2wasgiven = $_POST['DatePentahib2wasgiven'];									
$DateofConsultationPENTAHIB2 = $_POST['DateofConsultationPENTAHIB2'];								
$PENTAHIB3 = $_POST['PENTAHIB3'];
$DatePentahib3wasgiven = $_POST['DatePentahib3wasgiven'];									
$DateofConsultationPENTAHIB3 = $_POST['DateofConsultationPENTAHIB3'];								
$OPV1 = $_POST['OPV1'];
$DateOPV1wasgiven = $_POST['DateOPV1wasgiven'];										
$DateofConsultationOPV1v = $_POST['DateofConsultationOPV1v'];									
$OPV2 = $_POST['OPV2'];
$DateOPV2wasgiven = $_POST['DateOPV2wasgiven'];										
$DateofConsultationOPV2 = $_POST['DateofConsultationOPV2'];									
$OPV3 = $_POST['OPV3'];

$dateOPV3wasgiven = $_POST['dateOPV3wasgiven'];										

/*new inserted fields*/
$IPV1 = $_POST['IPV1'];
$dateIPV1wasgiven = $_POST['dateIPV1wasgiven'];
$DateofConsultationIPV1 = $_POST['DateofConsultationIPV1'];
$IPV2 = $_POST['IPV2'];
$dateIPV2wasgiven = $_POST['dateIPV2wasgiven'];
$DateofConsultationIPV2 = $_POST['DateofConsultationIPV2'];
$PCV1 = $_POST['PCV1'];
$datePCV1wasgiven = $_POST['datePCV1wasgiven'];
$DateofConsultationPCV1 = $_POST['DateofConsultationPCV1'];
$PCV2 = $_POST['PCV2'];
$datePCV2wasgiven = $_POST['datePCV2wasgiven'];
$DateofConsultationPCV2 = $_POST['DateofConsultationPCV2'];
$PCV3 = $_POST['PCV3'];
$datePCV3wasgiven = $_POST['datePCV3wasgiven'];
$DateofConsultationPCV3 = $_POST['DateofConsultationPCV3'];
/*end*/
$DateofConsultationOPV3 = $_POST['DateofConsultationOPV3'];									
$HEPAatBirth = $_POST['HEPAatBirth'];
$TimingHEPAatBirth = $_POST['TimingHEPAatBirth'];										
$DateHEPAatBirthwasgiven = $_POST['DateHEPAatBirthwasgiven'];									
$DateofConsultationHEPAatBirth = $_POST['DateofConsultationHEPAatBirth'];							
$HEPAB1 = $_POST['HEPAB1'];
$dateHEPA1wasgiven = $_POST['dateHEPA1wasgiven'];										
$DateofConsultationHEPAB1 = $_POST['DateofConsultationHEPAB1'];								
$HEPAB2 = $_POST['HEPAB2'];
$dateHEPA2wasgiven = $_POST['dateHEPA2wasgiven'];										
$DateofConsultationHEPAB2 = $_POST['DateofConsultationHEPAB2'];								
$HEPAB3 = $_POST['HEPAB3'];
$dateHEPA3WASGIVEN = $_POST['dateHEPA3WASGIVEN'];										
$DateofConsultationHEPAB3 = $_POST['DateofConsultationHEPAB3'];								
$HEPAB4 = $_POST['HEPAB4'];
$dateHEPA4WASGIVEN = $_POST['dateHEPA4WASGIVEN'];										
$DateofConsultationHEPAB4 = $_POST['DateofConsultationHEPAB4'];								
$AMV19monthstobelow12months = $_POST['AMV19monthstobelow12months'];								
$DateAMV1WASGIVEN = $_POST['DateAMV1WASGIVEN'];										
$DateofConsultationAMV1 = $_POST['DateofConsultationAMV1'];									
$MMR12MOSTO15MOS = $_POST['MMR12MOSTO15MOS'];
$dateMMRWASGIVEN = $_POST['dateMMRWASGIVEN'];
$DateofConsultationMMR = $_POST['DateofConsultationMMR'];	

$FIC = $_POST['FIC'];
$dateFICWASGIVEN = $_POST['dateFICWASGIVEN'];
$DateofConsultationFIC = $_POST['DateofConsultationFIC'];
$CIC = $_POST['CIC'];
$dateCICWASGIVEN = $_POST['dateCICWASGIVEN'];
$DateofConsultationCIC = $_POST['DateofConsultationCIC'];

$AMV2_16MOSTO5YRSOLD = $_POST['AMV2_16MOSTO5YRSOLD'];										
$dateAMV2WASGIVEN = $_POST['dateAMV2WASGIVEN'];										
$DateofConsultationAMV2 = $_POST['DateofConsultationAMV2'];									
$IMMUNIZATIONSTATUS = $_POST['IMMUNIZATIONSTATUS'];									 	
$DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = $_POST['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'];		
$FIRSTMONTH = $_POST['FIRSTMONTH'];
$SECONDMONTH = $_POST['SECONDMONTH'];
$THIRDMONTH = $_POST['THIRDMONTH'];
$FOURTHDMONTH = $_POST['FOURTHDMONTH'];
$FIFTMONTH = $_POST['FIFTMONTH'];
$SIXTHMONTH = $_POST['SIXTHMONTH'];
$date6MONTHS = $_POST['date6MONTHS'];
$WASTHECHILDEXCLUSIVELY = $_POST['WASTHECHILDEXCLUSIVELY'];									
$DATEOFCONSULTATIONEXCLUSIVEBF = $_POST['DATEOFCONSULTATIONEXCLUSIVEBF'];							
$TT1 = $_POST['TT1'];
$DATEOFCONSULTTT1 = $_POST['DATEOFCONSULTTT1'];										
$TT2 = $_POST['TT2'];
$DATEOFCONSULTTT2 = $_POST['DATEOFCONSULTTT2'];										
$TT3 = $_POST['TT3'];
$DATEOFCONSULTTT3 = $_POST['DATEOFCONSULTTT3'];										
$TT4 = $_POST['TT4'];
$DATEOFCONSULTTT4 = $_POST['DATEOFCONSULTTT4'];										
$TT5 = $_POST['TT5'];
$DATEOFCONSULTT5 = $_POST['DATEOFCONSULTT5'];
$PhoneNumber = $_POST['PhoneNumber'];
$approvedBy = $_SESSION['fullname'];

$dateOfExpiration = date('Y-m-d', strtotime('+365 day'));

 












 if (empty($DateOfBirth)) {

        echo '
        <div class="  card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>Date of Birth </span>required</p>
		</div>
	</div>';
    } 
   else if (empty($NameOfChild)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>Name Of Child is </span>required</p>
		</div>
	</div>';
    } 
   else if (empty($LastNameOfChild)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>LastNameOfChild </span>required</p>
		</div>
	</div>';
    } 
   else if (empty($Attendant)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>Attendant </span>required</p>
		</div>
	</div>';
    } 
    else if (empty($PlaceofDelivery)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>PlaceofDelivery </span>required</p>
		</div>
	</div>';
    } 
    else if (empty($TypeofDelivery)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>TypeofDelivery </span>required</p>
		</div>
	</div>';
    } 
    else if (empty($BCG)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>BCG </span>required</p>
		</div>
	</div>';
    } 
    
    else {
        // Check if record already exists
        $check_sql = "SELECT id FROM nip_table
                      WHERE NameOfChild = ?
                      AND LastNameOfChild = ?
                      AND DateOfBirth = ?
                      AND NameofMother = ?
                      AND (deleted IS NULL OR deleted = 0)";

        $check_stmt = mysqli_prepare($conn, $check_sql);
        mysqli_stmt_bind_param($check_stmt, "ssss", $NameOfChild, $LastNameOfChild, $DateOfBirth, $NameofMother);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);

        if (mysqli_num_rows($check_result) > 0) {
            // Record already exists
            echo '<script>
                    if (typeof showToast === "function") {
                        showToast("This record is already exist", "error");
                    }
                  </script>';
            echo '<div class="row red lighten-5 red-text" style="padding:10px;">
                    <div class="col s12 red-text">
                      <i class="material-icons left">error</i>This record is already exist
                    </div>
                  </div>';
            mysqli_stmt_close($check_stmt);
            return; // Stop execution
        }
        mysqli_stmt_close($check_stmt);

$sql = "INSERT INTO nip_table (DateOfRegistration,  DateOfBirth,AgeofChild, FamilySerialNumber,ChildNumber,LastNameOfChild,NameOfChild,middlename_of_child,Sex,NameofMother,BirthdateofMother,Age,Barangay,PurokStreetSitio,HouseNo,Address,PlaceofDelivery,NameofFacility,Attendant,TypeofDelivery,BirthWeightInGrams,BirthWeightClassification,WastheChildReferredforNewbornScreening,DateReferredforNewbornScreening,DateofConsultationNewbornScreening,TTStatusofMother,Dateassessed,WastheChildProtectedatBirth,DateofConsultationCPAB,BCG,DateBCGwasgiven,DateofConsultationBCG,PENTAHIB1,DatePenta1wasgiven,DateofConsultationPENTAHIB1,PENTAHIB2,DatePentahib2wasgiven,DateofConsultationPENTAHIB2,PENTAHIB3,DatePentahib3wasgiven,DateofConsultationPENTAHIB3,OPV1,DateOPV1wasgiven,DateofConsultationOPV1v,OPV2,DateOPV2wasgiven,DateofConsultationOPV2,OPV3,dateOPV3wasgiven,DateofConsultationOPV3,

IPV1,
dateIPV1wasgiven,
DateofConsultationIPV1,
IPV2,
dateIPV2wasgiven,
DateofConsultationIPV2,
PCV1,
datePCV1wasgiven,
DateofConsultationPCV1,
PCV2,
datePCV2wasgiven,
DateofConsultationPCV2,
PCV3,
datePCV3wasgiven,
DateofConsultationPCV3,

HEPAatBirth,TimingHEPAatBirth,
DateHEPAatBirthwasgiven,									
DateofConsultationHEPAatBirth,							
HEPAB1,													
dateHEPA1wasgiven,										
DateofConsultationHEPAB1,								
HEPAB2,													
dateHEPA2wasgiven,										
DateofConsultationHEPAB2,								
HEPAB3,													
dateHEPA3WASGIVEN,										
DateofConsultationHEPAB3,								
HEPAB4,													
dateHEPA4WASGIVEN,										
DateofConsultationHEPAB4,								
AMV19monthstobelow12months,								
DateAMV1WASGIVEN,										
DateofConsultationAMV1,									
MMR12MOSTO15MOS,											
dateMMRWASGIVEN,											
DateofConsultationMMR,					
FIC,
dateFICWASGIVEN,
DateofConsultationFIC,
CIC,
dateCICWASGIVEN,
DateofConsultationCIC,				
AMV2_16MOSTO5YRSOLD,										
dateAMV2WASGIVEN,										
DateofConsultationAMV2,									
IMMUNIZATIONSTATUS,									 	
DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED,		
FIRSTMONTH,												
SECONDMONTH,												
THIRDMONTH,												
FOURTHDMONTH,											
FIFTMONTH,												
SIXTHMONTH,												
date6MONTHS,												
WASTHECHILDEXCLUSIVELY,									
DATEOFCONSULTATIONEXCLUSIVEBF,							
TT1,														
DATEOFCONSULTTT1,										
TT2,														
DATEOFCONSULTTT2,										
TT3,														
DATEOFCONSULTTT3,TT4,DATEOFCONSULTTT4,TT5,														
DATEOFCONSULTT5,PhoneNumber,approvedBy,dateOfExpiration

)
VALUES ('{$DateOfRegistration}', '{$DateOfBirth}','{$AgeofChild}', '{$FamilySerialNumber}','{$ChildNumber}','{$LastNameOfChild}','{$NameOfChild}','{$middlename_of_child}','{$Sex}','{$NameofMother}','{$BirthdateofMother}','{$AgeeofMother}','{$Barangay}','{$PurokStreetSitio}','{$HouseNo}','{$Address}','{$PlaceofDelivery}','{$NameofFacility}','{$Attendant}','{$TypeofDelivery}','{$BirthWeightInGrams}','{$BirthWeightClassification}','{$WastheChildReferredforNewbornScreening}','{$DateReferredforNewbornScreening}','{$DateofConsultationNewbornScreening}','{$TTStatusofMother}','{$Dateassessed}','{$WastheChildProtectedatBirth}','{$DateofConsultationCPAB}','{$BCG}','{$DateBCGwasgiven}','{$DateofConsultationBCG}','{$PENTAHIB1}','{$DatePenta1wasgiven}','{$DateofConsultationPENTAHIB1}','{$PENTAHIB2}','{$DatePentahib2wasgiven}','{$DateofConsultationPENTAHIB2}','{$PENTAHIB3}','{$DatePentahib3wasgiven}','{$DateofConsultationPENTAHIB3}','{$OPV1}','{$DateOPV1wasgiven}','{$DateofConsultationOPV1v}','{$OPV2}','{$DateOPV2wasgiven}','{$DateofConsultationOPV2}','{$OPV3}','{$dateOPV3wasgiven}',
 



'{$DateofConsultationOPV3}',
'{$IPV1}',
'{$dateIPV1wasgiven}',
'{$DateofConsultationIPV1}',
'{$IPV2}',
'{$dateIPV2wasgiven}',
'{$DateofConsultationIPV2}',
'{$PCV1}',
'{$datePCV1wasgiven}',
'{$DateofConsultationPCV1}',
'{$PCV2}',
'{$datePCV2wasgiven}',
'{$DateofConsultationPCV2}',
'{$PCV3}',
'{$datePCV3wasgiven}',
'{$DateofConsultationPCV3}',
'{$HEPAatBirth}','{$TimingHEPAatBirth}','{$DateHEPAatBirthwasgiven}','{$DateofConsultationHEPAatBirth}','{$HEPAB1}','{$dateHEPA1wasgiven}','{$DateofConsultationHEPAB1}','{$HEPAB2}','{$dateHEPA2wasgiven}','{$DateofConsultationHEPAB2}','{$HEPAB3}','{$dateHEPA3WASGIVEN}','{$DateofConsultationHEPAB3}','{$HEPAB4}','{$dateHEPA4WASGIVEN}','{$DateofConsultationHEPAB4}','{$AMV19monthstobelow12months}','{$DateAMV1WASGIVEN}','{$DateofConsultationAMV1}','{$MMR12MOSTO15MOS}','{$dateMMRWASGIVEN}','{$DateofConsultationMMR}','{$FIC}','{$dateFICWASGIVEN}','{$DateofConsultationFIC}','{$CIC}','{$dateCICWASGIVEN}','{$DateofConsultationCIC}','{$AMV2_16MOSTO5YRSOLD}','{$dateAMV2WASGIVEN}','{$DateofConsultationAMV2}','{$IMMUNIZATIONSTATUS}','{$DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED}','{$FIRSTMONTH}','{$SECONDMONTH}','{$THIRDMONTH}','{$FOURTHDMONTH}','{$FIFTMONTH}','{$SIXTHMONTH}','{$date6MONTHS}','{$WASTHECHILDEXCLUSIVELY}','{$DATEOFCONSULTATIONEXCLUSIVEBF}','{$TT1}','{$DATEOFCONSULTTT1}','{$TT2}','{$DATEOFCONSULTTT2}','{$TT3}','{$DATEOFCONSULTTT3}','{$TT4}','{$DATEOFCONSULTTT4}','{$TT5}','{$DATEOFCONSULTT5}','{$PhoneNumber}','{$approvedBy}','{$dateOfExpiration}')";
        






        
        if (mysqli_query($conn, $sql)) {
            // Success - show success toast
            echo '<script>
                    if (typeof showToast === "function") {
                        showToast("Record Successfully Added", "success");
                    }
                  </script>';
            echo '
            <div class="row green lighten-5 green-text" style="padding:10px;">
            <div class="col s12 green-text">
              <i class="material-icons left">check_circle</i>Record Successfully Added
            </div>
            </div>';

            // Display earliest consultation date
            echo '<div class="row blue lighten-5 blue-text" style="padding:10px; margin-top:10px;">
                    <div class="col s12 blue-text">
                      <i class="material-icons left">event</i><strong>Earliest Consultation Date: </strong>';
            printEarliestDateFromPostStyled('', 'display: inline; font-weight: bold;');
            echo '    </div>
                  </div>';


        } else {
            // Database error - show detailed error for debugging
            $error_message = mysqli_error($conn);
            echo '<script>
                    if (typeof showToast === "function") {
                        showToast("Database error: ' . addslashes($error_message) . '", "error");
                    }
                  </script>';
            echo '<div class="row red lighten-5 red-text" style="padding:10px;">
                    <div class="col s12 red-text">
                      <i class="material-icons left">error</i>Database Error: ' . htmlspecialchars($error_message) . '
                    </div>
                  </div>';

        }
       
        
        
    }
}

// Retrieve comments from database
$sql = "SELECT * FROM nip_table";
$result = mysqli_query($conn, $sql);
$comments = '<div id="display_area">'; 
while ($row = mysqli_fetch_array($result)) {
    $comments .= '<div class="comment_box">
                    <span class="delete" data-id="' . $row['id'] . '" >delete</span>
                    <span class="edit" data-id="' . $row['id'] . '">edit</span>
                    <div class="display_name">' . $row['DateOfRegistration'] . '</div>
                    <div class="comment_text">' . $row['DateOfBirth'] . '</div>
                </div>';
}
$comments .= '</div>';

?>
