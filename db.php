<?php 

error_reporting(0);

$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Connection failed ' . mysqli_error($conn));
}
date_default_timezone_set('Asia/Manila');

// Function to generate unique Family Serial Number
function generateUniqueFamilySerial($conn) {
    $attempts = 0;
    $maxAttempts = 10;

    do {
        $today = date("Ymd");
        $rand = strtoupper(substr(uniqid(sha1(time() + $attempts)),0,8));
        $familySerial = 'CHO-' . $today . '-' . $rand;

        // Check if this serial number already exists
        $checkStmt = $conn->prepare("SELECT id FROM nip_table WHERE FamilySerialNumber = ?");
        $checkStmt->bind_param("s", $familySerial);
        $checkStmt->execute();
        $checkStmt->store_result();
        $exists = $checkStmt->num_rows > 0;
        $checkStmt->close();

        $attempts++;
    } while ($exists && $attempts < $maxAttempts);

    if ($exists) {
        // Fallback with timestamp
        $familySerial = 'CHO-' . $today . '-' . time() . '-' . rand(100, 999);
    }

    return $familySerial;
}

// Enhanced function to check for duplicates including middle name
function checkForDuplicates($conn, $firstName, $lastName, $middleName, $dateOfBirth, $motherName, $phoneNumber, $address, $barangay) {
    $duplicateMessages = [];
    $duplicateFound = false;

    // 1. EXACT MATCH CHECK (First Name + Last Name + Middle Name + DOB)
    $exactCheckStmt = $conn->prepare("SELECT id, FamilySerialNumber, DateOfRegistration, NameOfChild, LastNameOfChild, middlename_of_child
                                      FROM nip_table
                                      WHERE NameOfChild = ? AND LastNameOfChild = ? AND middlename_of_child = ? AND DateOfBirth = ?
                                      AND (deleted IS NULL OR deleted = 0)");
    $exactCheckStmt->bind_param("ssss", $firstName, $lastName, $middleName, $dateOfBirth);
    $exactCheckStmt->execute();
    $exactResult = $exactCheckStmt->get_result();
    if ($exactResult->num_rows > 0) {
        $duplicateFound = true;
        $existingRecord = $exactResult->fetch_assoc();
        $fullName = trim($existingRecord['NameOfChild'] . ' ' . $existingRecord['middlename_of_child'] . ' ' . $existingRecord['LastNameOfChild']);
        $duplicateMessages[] = [
            'type' => 'error',
            'title' => 'Exact Duplicate Found!',
            'message' => "Child '{$fullName}' with the same birth date is already registered (ID: {$existingRecord['id']}, Family Serial: {$existingRecord['FamilySerialNumber']}, Registration Date: {$existingRecord['DateOfRegistration']})",
            'icon' => 'error'
        ];
    }
    $exactCheckStmt->close();

    // 2. PARTIAL NAME MATCH CHECK (First + Last + DOB, different middle name)
    if (!$duplicateFound) {
        $partialCheckStmt = $conn->prepare("SELECT id, FamilySerialNumber, NameOfChild, LastNameOfChild, middlename_of_child, NameofMother
                                           FROM nip_table
                                           WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ?
                                           AND middlename_of_child != ?
                                           AND (deleted IS NULL OR deleted = 0)");
        $partialCheckStmt->bind_param("ssss", $firstName, $lastName, $dateOfBirth, $middleName);
        $partialCheckStmt->execute();
        $partialResult = $partialCheckStmt->get_result();
        if ($partialResult->num_rows > 0) {
            while ($partialRecord = $partialResult->fetch_assoc()) {
                $existingFullName = trim($partialRecord['NameOfChild'] . ' ' . $partialRecord['middlename_of_child'] . ' ' . $partialRecord['LastNameOfChild']);
                $newFullName = trim($firstName . ' ' . $middleName . ' ' . $lastName);
                $duplicateMessages[] = [
                    'type' => 'warning',
                    'title' => 'Similar Record Found!',
                    'message' => "Possible duplicate: '{$existingFullName}' (ID: {$partialRecord['id']}) has same first/last name and birth date but different middle name. New entry: '{$newFullName}'. Mother: {$partialRecord['NameofMother']}",
                    'icon' => 'warning'
                ];
            }
        }
        $partialCheckStmt->close();
    }

    // 3. SOUNDEX MATCHING (Similar sounding names with same DOB)
    if (!$duplicateFound) {
        $soundexCheckStmt = $conn->prepare("SELECT id, NameOfChild, LastNameOfChild, middlename_of_child, DateOfBirth, NameofMother, FamilySerialNumber
                                           FROM nip_table
                                           WHERE (SOUNDEX(NameOfChild) = SOUNDEX(?) OR SOUNDEX(LastNameOfChild) = SOUNDEX(?) OR SOUNDEX(middlename_of_child) = SOUNDEX(?))
                                           AND DateOfBirth = ?
                                           AND (deleted IS NULL OR deleted = 0)");
        $soundexCheckStmt->bind_param("ssss", $firstName, $lastName, $middleName, $dateOfBirth);
        $soundexCheckStmt->execute();
        $soundexResult = $soundexCheckStmt->get_result();
        if ($soundexResult->num_rows > 0) {
            while ($soundexRecord = $soundexResult->fetch_assoc()) {
                $existingFullName = trim($soundexRecord['NameOfChild'] . ' ' . $soundexRecord['middlename_of_child'] . ' ' . $soundexRecord['LastNameOfChild']);
                $newFullName = trim($firstName . ' ' . $middleName . ' ' . $lastName);

                // Skip if it's the same record we already found
                if ($existingFullName !== $newFullName) {
                    $duplicateMessages[] = [
                        'type' => 'warning',
                        'title' => 'Similar Sounding Name Found!',
                        'message' => "Possible typo or similar name: '{$existingFullName}' (ID: {$soundexRecord['id']}) sounds similar to '{$newFullName}' with same birth date. Mother: {$soundexRecord['NameofMother']}",
                        'icon' => 'warning'
                    ];
                }
            }
        }
        $soundexCheckStmt->close();
    }

    // 4. MOTHER NAME + DOB CHECK (Same mother, same DOB, different child names)
    if (!empty($motherName)) {
        $motherCheckStmt = $conn->prepare("SELECT id, NameOfChild, LastNameOfChild, middlename_of_child, NameofMother
                                          FROM nip_table
                                          WHERE NameofMother = ? AND DateOfBirth = ?
                                          AND (NameOfChild != ? OR LastNameOfChild != ? OR middlename_of_child != ?)
                                          AND (deleted IS NULL OR deleted = 0)");
        $motherCheckStmt->bind_param("sssss", $motherName, $dateOfBirth, $firstName, $lastName, $middleName);
        $motherCheckStmt->execute();
        $motherResult = $motherCheckStmt->get_result();
        if ($motherResult->num_rows > 0) {
            while ($motherRecord = $motherResult->fetch_assoc()) {
                $existingFullName = trim($motherRecord['NameOfChild'] . ' ' . $motherRecord['middlename_of_child'] . ' ' . $motherRecord['LastNameOfChild']);
                $duplicateMessages[] = [
                    'type' => 'warning',
                    'title' => 'Same Mother & Birth Date!',
                    'message' => "Another child '{$existingFullName}' (ID: {$motherRecord['id']}) has the same mother '{$motherName}' and birth date '{$dateOfBirth}'. This could be twins or a data entry error.",
                    'icon' => 'warning'
                ];
            }
        }
        $motherCheckStmt->close();
    }

    // 5. PHONE NUMBER CHECK (if provided)
    if (!empty($phoneNumber)) {
        $phoneCheckStmt = $conn->prepare("SELECT id, NameOfChild, LastNameOfChild, middlename_of_child, PhoneNumber
                                         FROM nip_table
                                         WHERE PhoneNumber = ? AND (deleted IS NULL OR deleted = 0)");
        $phoneCheckStmt->bind_param("s", $phoneNumber);
        $phoneCheckStmt->execute();
        $phoneResult = $phoneCheckStmt->get_result();
        if ($phoneResult->num_rows > 0) {
            while ($phoneRecord = $phoneResult->fetch_assoc()) {
                $existingFullName = trim($phoneRecord['NameOfChild'] . ' ' . $phoneRecord['middlename_of_child'] . ' ' . $phoneRecord['LastNameOfChild']);
                $newFullName = trim($firstName . ' ' . $middleName . ' ' . $lastName);

                if ($existingFullName !== $newFullName) {
                    $duplicateMessages[] = [
                        'type' => 'warning',
                        'title' => 'Phone Number Already Used!',
                        'message' => "Phone number {$phoneNumber} is already registered for '{$existingFullName}' (ID: {$phoneRecord['id']})",
                        'icon' => 'phone'
                    ];
                }
            }
        }
        $phoneCheckStmt->close();
    }

    // 6. HOUSEHOLD CHECK (same address + mother, different child)
    if (!empty($address) && !empty($motherName) && !empty($barangay)) {
        $householdCheckStmt = $conn->prepare("SELECT id, NameOfChild, LastNameOfChild, middlename_of_child, DateOfBirth
                                             FROM nip_table
                                             WHERE Address = ? AND NameofMother = ? AND Barangay = ?
                                             AND (NameOfChild != ? OR LastNameOfChild != ? OR middlename_of_child != ?)
                                             AND (deleted IS NULL OR deleted = 0)");
        $householdCheckStmt->bind_param("ssssss", $address, $motherName, $barangay, $firstName, $lastName, $middleName);
        $householdCheckStmt->execute();
        $householdResult = $householdCheckStmt->get_result();
        if ($householdResult->num_rows > 0) {
            while ($householdRecord = $householdResult->fetch_assoc()) {
                $existingFullName = trim($householdRecord['NameOfChild'] . ' ' . $householdRecord['middlename_of_child'] . ' ' . $householdRecord['LastNameOfChild']);
                $duplicateMessages[] = [
                    'type' => 'info',
                    'title' => 'Same Household Detected',
                    'message' => "Another child '{$existingFullName}' (ID: {$householdRecord['id']}, DOB: {$householdRecord['DateOfBirth']}) from the same household is already registered",
                    'icon' => 'home'
                ];
            }
        }
        $householdCheckStmt->close();
    }

    return ['found' => $duplicateFound, 'messages' => $duplicateMessages];
}

if (isset($_POST['submitBtn'])) {

    $today = date("");
    $rand = strtoupper(substr(uniqid(sha1(time())),0,10));
     
     
    



    $DateOfRegistration = date('Y-m-d');
   // $UHCRead = $_POST['UHCRead'];								 
    $DateOfBirth = $_POST['DateOfBirth']; 
    $AgeofChild = $_POST['AgeofChild']; 
    $FamilySerialNumber = generateUniqueFamilySerial($conn);

     
    $ChildNumber = rand(100, 999);  
     
    $LastNameOfChild = $_POST['LastNameOfChild'];
    $NameOfChild = $_POST['NameOfChild']; 
    $middlename_of_child = $_POST['middlename_of_child'];
    $Sex = $_POST['Sex']; 
    $NameofMother = $_POST['NameofMother']; 
    $BirthdateofMother = $_POST['BirthdateofMother']; 
    $AgeeofMother = $_POST['AgeeofMother']; 
    $Barangay = $_POST['Barangay']; 
    $PurokStreetSitio = $_POST['PurokStreetSitio']; 
    $HouseNo = $_POST['HouseNo']; 
    $Address = $_POST['Address']; 
    $PlaceofDelivery = $_POST['PlaceofDelivery'];

$NameofFacility = $_POST['NameofFacility'];
$Attendant = $_POST['Attendant'];
$TypeofDelivery = $_POST['TypeofDelivery'];
$BirthWeightInGrams = $_POST['BirthWeightInGrams'];
 
$BirthWeightClassification = $_POST['BirthWeightClassification'];
 
$WastheChildReferredforNewbornScreening = $_POST['WastheChildReferredforNewbornScreening'];
$DateReferredforNewbornScreening = $_POST['DateReferredforNewbornScreening'];
$DateofConsultationNewbornScreening = $_POST['DateofConsultationNewbornScreening'];
$TTStatusofMother = $_POST['TTStatusofMother'];
$Dateassessed = $_POST['Dateassessed'];
$WastheChildProtectedatBirth = $_POST['WastheChildProtectedatBirth'];
$DateofConsultationCPAB = $_POST['DateofConsultationCPAB'];									
$BCG = $_POST['BCG'];
$DateBCGwasgiven = $_POST['DateBCGwasgiven'];
$DateofConsultationBCG = $_POST['DateofConsultationBCG'];									
$PENTAHIB1 = $_POST['PENTAHIB1'];
$DatePenta1wasgiven = $_POST['DatePenta1wasgiven'];										
$DateofConsultationPENTAHIB1 = $_POST['DateofConsultationPENTAHIB1'];								
$PENTAHIB2 = $_POST['PENTAHIB2'];
$DatePentahib2wasgiven = $_POST['DatePentahib2wasgiven'];									
$DateofConsultationPENTAHIB2 = $_POST['DateofConsultationPENTAHIB2'];								
$PENTAHIB3 = $_POST['PENTAHIB3'];
$DatePentahib3wasgiven = $_POST['DatePentahib3wasgiven'];									
$DateofConsultationPENTAHIB3 = $_POST['DateofConsultationPENTAHIB3'];								
$OPV1 = $_POST['OPV1'];
$DateOPV1wasgiven = $_POST['DateOPV1wasgiven'];										
$DateofConsultationOPV1v = $_POST['DateofConsultationOPV1v'];									
$OPV2 = $_POST['OPV2'];
$DateOPV2wasgiven = $_POST['DateOPV2wasgiven'];										
$DateofConsultationOPV2 = $_POST['DateofConsultationOPV2'];									
$OPV3 = $_POST['OPV3'];

$dateOPV3wasgiven = $_POST['dateOPV3wasgiven'];										

/*new inserted fields*/
$IPV1 = $_POST['IPV1'];
$dateIPV1wasgiven = $_POST['dateIPV1wasgiven'];
$DateofConsultationIPV1 = $_POST['DateofConsultationIPV1'];
$IPV2 = $_POST['IPV2'];
$dateIPV2wasgiven = $_POST['dateIPV2wasgiven'];
$DateofConsultationIPV2 = $_POST['DateofConsultationIPV2'];
$PCV1 = $_POST['PCV1'];
$datePCV1wasgiven = $_POST['datePCV1wasgiven'];
$DateofConsultationPCV1 = $_POST['DateofConsultationPCV1'];
$PCV2 = $_POST['PCV2'];
$datePCV2wasgiven = $_POST['datePCV2wasgiven'];
$DateofConsultationPCV2 = $_POST['DateofConsultationPCV2'];
$PCV3 = $_POST['PCV3'];
$datePCV3wasgiven = $_POST['datePCV3wasgiven'];
$DateofConsultationPCV3 = $_POST['DateofConsultationPCV3'];
/*end*/
$DateofConsultationOPV3 = $_POST['DateofConsultationOPV3'];									
$HEPAatBirth = $_POST['HEPAatBirth'];
$TimingHEPAatBirth = $_POST['TimingHEPAatBirth'];										
$DateHEPAatBirthwasgiven = $_POST['DateHEPAatBirthwasgiven'];									
$DateofConsultationHEPAatBirth = $_POST['DateofConsultationHEPAatBirth'];							
$HEPAB1 = $_POST['HEPAB1'];
$dateHEPA1wasgiven = $_POST['dateHEPA1wasgiven'];										
$DateofConsultationHEPAB1 = $_POST['DateofConsultationHEPAB1'];								
$HEPAB2 = $_POST['HEPAB2'];
$dateHEPA2wasgiven = $_POST['dateHEPA2wasgiven'];										
$DateofConsultationHEPAB2 = $_POST['DateofConsultationHEPAB2'];								
$HEPAB3 = $_POST['HEPAB3'];
$dateHEPA3WASGIVEN = $_POST['dateHEPA3WASGIVEN'];										
$DateofConsultationHEPAB3 = $_POST['DateofConsultationHEPAB3'];								
$HEPAB4 = $_POST['HEPAB4'];
$dateHEPA4WASGIVEN = $_POST['dateHEPA4WASGIVEN'];										
$DateofConsultationHEPAB4 = $_POST['DateofConsultationHEPAB4'];								
$AMV19monthstobelow12months = $_POST['AMV19monthstobelow12months'];								
$DateAMV1WASGIVEN = $_POST['DateAMV1WASGIVEN'];										
$DateofConsultationAMV1 = $_POST['DateofConsultationAMV1'];									
$MMR12MOSTO15MOS = $_POST['MMR12MOSTO15MOS'];
$dateMMRWASGIVEN = $_POST['dateMMRWASGIVEN'];
$DateofConsultationMMR = $_POST['DateofConsultationMMR'];	

$FIC = $_POST['FIC'];
$dateFICWASGIVEN = $_POST['dateFICWASGIVEN'];
$DateofConsultationFIC = $_POST['DateofConsultationFIC'];
$CIC = $_POST['CIC'];
$dateCICWASGIVEN = $_POST['dateCICWASGIVEN'];
$DateofConsultationCIC = $_POST['DateofConsultationCIC'];

$AMV2_16MOSTO5YRSOLD = $_POST['AMV2_16MOSTO5YRSOLD'];										
$dateAMV2WASGIVEN = $_POST['dateAMV2WASGIVEN'];										
$DateofConsultationAMV2 = $_POST['DateofConsultationAMV2'];									
$IMMUNIZATIONSTATUS = $_POST['IMMUNIZATIONSTATUS'];									 	
$DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = $_POST['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'];		
$FIRSTMONTH = $_POST['FIRSTMONTH'];
$SECONDMONTH = $_POST['SECONDMONTH'];
$THIRDMONTH = $_POST['THIRDMONTH'];
$FOURTHDMONTH = $_POST['FOURTHDMONTH'];
$FIFTMONTH = $_POST['FIFTMONTH'];
$SIXTHMONTH = $_POST['SIXTHMONTH'];
$date6MONTHS = $_POST['date6MONTHS'];
$WASTHECHILDEXCLUSIVELY = $_POST['WASTHECHILDEXCLUSIVELY'];									
$DATEOFCONSULTATIONEXCLUSIVEBF = $_POST['DATEOFCONSULTATIONEXCLUSIVEBF'];							
$TT1 = $_POST['TT1'];
$DATEOFCONSULTTT1 = $_POST['DATEOFCONSULTTT1'];										
$TT2 = $_POST['TT2'];
$DATEOFCONSULTTT2 = $_POST['DATEOFCONSULTTT2'];										
$TT3 = $_POST['TT3'];
$DATEOFCONSULTTT3 = $_POST['DATEOFCONSULTTT3'];										
$TT4 = $_POST['TT4'];
$DATEOFCONSULTTT4 = $_POST['DATEOFCONSULTTT4'];										
$TT5 = $_POST['TT5'];
$DATEOFCONSULTT5 = $_POST['DATEOFCONSULTT5'];
$PhoneNumber = $_POST['PhoneNumber'];
$approvedBy = $_SESSION['fullname'];

$dateOfExpiration = date('Y-m-d', strtotime('+365 day'));// ENHANCED DUPLICATE CHECK - Including First Name, Last Name, Middle Name, and Date of Birth
$duplicateCheck = checkForDuplicates(
    $conn,
    $NameOfChild,
    $LastNameOfChild,
    $middlename_of_child,
    $DateOfBirth,
    $NameofMother,
    $PhoneNumber,
    $Address,
    $Barangay
);

$duplicateFound = $duplicateCheck['found'];
$duplicateMessages = $duplicateCheck['messages'];

// Display duplicate check results with enhanced web notifications
if (!empty($duplicateMessages)) {
    foreach ($duplicateMessages as $msg) {
        $cardClass = '';
        $iconName = '';

        switch ($msg['type']) {
            case 'error':
                $cardClass = 'red lighten-4 red-text text-darken-4';
                $iconName = 'error';
                break;
            case 'warning':
                $cardClass = 'orange lighten-4 orange-text text-darken-4';
                $iconName = 'warning';
                break;
            case 'info':
                $cardClass = 'blue lighten-4 blue-text text-darken-4';
                $iconName = 'info';
                break;
        }

        // Enhanced card with action buttons
        echo "<div class='card {$cardClass}' id='duplicate-alert-{$msg['type']}'>
                <div class='card-content'>
                    <span class='card-title'>
                        <i class='material-icons left'>{$iconName}</i>{$msg['title']}
                        <a href='#' class='right btn-flat waves-effect' onclick='this.parentElement.parentElement.parentElement.style.display=\"none\"'>
                            <i class='material-icons'>close</i>
                        </a>
                    </span>
                    <p>{$msg['message']}</p>

                    <div class='card-action' style='padding-top: 10px;'>
                        <a href='duplicate_checker.php' class='btn-small blue waves-effect'>
                            <i class='material-icons left'>search</i>View All Duplicates
                        </a>
                        <a href='realtime_duplicate_check.php' class='btn-small orange waves-effect'>
                            <i class='material-icons left'>speed</i>Real-time Check
                        </a>
                    </div>
                </div>
              </div>";
    }
}

 if (empty($DateOfBirth)) {

        echo '
        <div class="  card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>Date of Birth </span>required</p>
		</div>
	</div>';
    } 
   else if (empty($NameOfChild)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>Name Of Child is </span>required</p>
		</div>
	</div>';
    } 
   else if (empty($LastNameOfChild)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>LastNameOfChild </span>required</p>
		</div>
	</div>';
    } 
   else if (empty($Attendant)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>Attendant </span>required</p>
		</div>
	</div>';
    } 
    else if (empty($PlaceofDelivery)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>PlaceofDelivery </span>required</p>
		</div>
	</div>';
    } 
    else if (empty($TypeofDelivery)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>TypeofDelivery </span>required</p>
		</div>
	</div>';
    } 
    else if (empty($BCG)) {
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
		<div class="card-content">
			<p><i class="material-icons left">report</i><span>BCG </span>required</p>
		</div>
	</div>';
    }
    // CRITICAL: Check for duplicates before proceeding with insertion
    else if ($duplicateFound) {
        // BLOCK INSERTION - Exact duplicate found
        echo '
        <div class="card red lighten-4 red-text text-darken-4">
            <div class="card-content">
                <span class="card-title">
                    <i class="material-icons left">block</i>Registration Blocked - Exact Duplicate Found!
                </span>
                <p><strong>This child is already registered in the system.</strong></p>
                <p>A child with the same first name, last name, middle name, and date of birth already exists.</p>
                <p>Registration has been prevented to maintain data integrity.</p>
                <div class="card-action">
                    <a href="duplicate_checker.php" class="btn blue waves-effect">
                        <i class="material-icons left">search</i>View Existing Records
                    </a>
                    <a href="realtime_duplicate_check.php" class="btn orange waves-effect">
                        <i class="material-icons left">speed</i>Check Details
                    </a>
                </div>
            </div>
        </div>';

        // Log the blocked attempt
        $blocked_reason = "Exact duplicate registration attempt blocked - Same first name, last name, middle name, and date of birth";
        $fullName = trim($NameOfChild . ' ' . $middlename_of_child . ' ' . $LastNameOfChild);

        // Create blocked attempts log table if it doesn't exist
        $conn->query("CREATE TABLE IF NOT EXISTS duplicate_block_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            child_name VARCHAR(255) NOT NULL,
            last_name VARCHAR(255) NOT NULL,
            middle_name VARCHAR(255),
            dob DATE NOT NULL,
            mother_name VARCHAR(255),
            blocked_reason TEXT NOT NULL,
            blocked_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            blocked_by VARCHAR(255),
            health_center VARCHAR(255),
            full_name VARCHAR(500),
            INDEX idx_blocked_date (blocked_date),
            INDEX idx_full_name (full_name),
            INDEX idx_dob (dob)
        )");

        $log_stmt = $conn->prepare("INSERT INTO duplicate_block_log (child_name, last_name, middle_name, dob, mother_name, blocked_reason, blocked_date, blocked_by, health_center, full_name) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)");
        $blocked_by = $_SESSION['fullname'] ?? 'Unknown User';
        $health_center = $_SESSION['health_center'] ?? 'Unknown';
        $log_stmt->bind_param("sssssssss", $NameOfChild, $LastNameOfChild, $middlename_of_child, $DateOfBirth, $NameofMother, $blocked_reason, $blocked_by, $health_center, $fullName);
        $log_stmt->execute();
        $log_stmt->close();

        // Show JavaScript notification
        echo "<script>
            document.addEventListener('DOMContentLoaded', function() {
                if (typeof M !== 'undefined' && M.toast) {
                    M.toast({
                        html: '<i class=\"material-icons left\">block</i>Registration Blocked - Exact Duplicate Found!',
                        classes: 'red white-text',
                        displayLength: 8000
                    });
                }

                // Scroll to the error message
                const errorElement = document.querySelector('.card.red');
                if (errorElement) {
                    errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        </script>";
    }
    else {
        // PROCEED WITH INSERTION - No exact duplicates found
        echo '
        <div class="card green lighten-4 green-text text-darken-4" style="margin-bottom: 15px;">
            <div class="card-content">
                <span class="card-title">
                    <i class="material-icons left">check_circle</i>Duplicate Check Passed
                </span>
                <p>No exact duplicates found. Proceeding with registration...</p>
            </div>
        </div>';

$sql = "INSERT INTO nip_table (DateOfRegistration,  DateOfBirth,AgeofChild, FamilySerialNumber,ChildNumber,LastNameOfChild,NameOfChild,middlename_of_child,Sex,NameofMother,BirthdateofMother,Age,Barangay,PurokStreetSitio,HouseNo,Address,PlaceofDelivery,NameofFacility,Attendant,TypeofDelivery,BirthWeightInGrams,BirthWeightClassification,WastheChildReferredforNewbornScreening,DateReferredforNewbornScreening,DateofConsultationNewbornScreening,TTStatusofMother,Dateassessed,WastheChildProtectedatBirth,DateofConsultationCPAB,BCG,DateBCGwasgiven,DateofConsultationBCG,PENTAHIB1,DatePenta1wasgiven,DateofConsultationPENTAHIB1,PENTAHIB2,DatePentahib2wasgiven,DateofConsultationPENTAHIB2,PENTAHIB3,DatePentahib3wasgiven,DateofConsultationPENTAHIB3,OPV1,DateOPV1wasgiven,DateofConsultationOPV1v,OPV2,DateOPV2wasgiven,DateofConsultationOPV2,OPV3,dateOPV3wasgiven,DateofConsultationOPV3,

IPV1,
dateIPV1wasgiven,
DateofConsultationIPV1,
IPV2,
dateIPV2wasgiven,
DateofConsultationIPV2,
PCV1,
datePCV1wasgiven,
DateofConsultationPCV1,
PCV2,
datePCV2wasgiven,
DateofConsultationPCV2,
PCV3,
datePCV3wasgiven,
DateofConsultationPCV3,

HEPAatBirth,TimingHEPAatBirth,
DateHEPAatBirthwasgiven,									
DateofConsultationHEPAatBirth,							
HEPAB1,													
dateHEPA1wasgiven,										
DateofConsultationHEPAB1,								
HEPAB2,													
dateHEPA2wasgiven,										
DateofConsultationHEPAB2,								
HEPAB3,													
dateHEPA3WASGIVEN,										
DateofConsultationHEPAB3,								
HEPAB4,													
dateHEPA4WASGIVEN,										
DateofConsultationHEPAB4,								
AMV19monthstobelow12months,								
DateAMV1WASGIVEN,										
DateofConsultationAMV1,									
MMR12MOSTO15MOS,											
dateMMRWASGIVEN,											
DateofConsultationMMR,					
FIC,
dateFICWASGIVEN,
DateofConsultationFIC,
CIC,
dateCICWASGIVEN,
DateofConsultationCIC,				
AMV2_16MOSTO5YRSOLD,										
dateAMV2WASGIVEN,										
DateofConsultationAMV2,									
IMMUNIZATIONSTATUS,									 	
DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED,		
FIRSTMONTH,												
SECONDMONTH,												
THIRDMONTH,												
FOURTHDMONTH,											
FIFTMONTH,												
SIXTHMONTH,												
date6MONTHS,												
WASTHECHILDEXCLUSIVELY,									
DATEOFCONSULTATIONEXCLUSIVEBF,							
TT1,														
DATEOFCONSULTTT1,										
TT2,														
DATEOFCONSULTTT2,										
TT3,														
DATEOFCONSULTTT3,TT4,DATEOFCONSULTTT4,TT5,														
DATEOFCONSULTT5,PhoneNumber,approvedBy,dateOfExpiration

)
VALUES ('{$DateOfRegistration}', '{$DateOfBirth}','{$AgeofChild}', '{$FamilySerialNumber}','{$ChildNumber}','{$LastNameOfChild}','{$NameOfChild}','{$middlename_of_child}','{$Sex}','{$NameofMother}','{$BirthdateofMother}','{$AgeeofMother}','{$Barangay}','{$PurokStreetSitio}','{$HouseNo}','{$Address}','{$PlaceofDelivery}','{$NameofFacility}','{$Attendant}','{$TypeofDelivery}','{$BirthWeightInGrams}','{$BirthWeightClassification}','{$WastheChildReferredforNewbornScreening}','{$DateReferredforNewbornScreening}','{$DateofConsultationNewbornScreening}','{$TTStatusofMother}','{$Dateassessed}','{$WastheChildProtectedatBirth}','{$DateofConsultationCPAB}','{$BCG}','{$DateBCGwasgiven}','{$DateofConsultationBCG}','{$PENTAHIB1}','{$DatePenta1wasgiven}','{$DateofConsultationPENTAHIB1}','{$PENTAHIB2}','{$DatePentahib2wasgiven}','{$DateofConsultationPENTAHIB2}','{$PENTAHIB3}','{$DatePentahib3wasgiven}','{$DateofConsultationPENTAHIB3}','{$OPV1}','{$DateOPV1wasgiven}','{$DateofConsultationOPV1v}','{$OPV2}','{$DateOPV2wasgiven}','{$DateofConsultationOPV2}','{$OPV3}','{$dateOPV3wasgiven}',
 



'{$DateofConsultationOPV3}',
'{$IPV1}',
'{$dateIPV1wasgiven}',
'{$DateofConsultationIPV1}',
'{$IPV2}',
'{$dateIPV2wasgiven}',
'{$DateofConsultationIPV2}',
'{$PCV1}',
'{$datePCV1wasgiven}',
'{$DateofConsultationPCV1}',
'{$PCV2}',
'{$datePCV2wasgiven}',
'{$DateofConsultationPCV2}',
'{$PCV3}',
'{$datePCV3wasgiven}',
'{$DateofConsultationPCV3}',
'{$HEPAatBirth}','{$TimingHEPAatBirth}','{$DateHEPAatBirthwasgiven}','{$DateofConsultationHEPAatBirth}','{$HEPAB1}','{$dateHEPA1wasgiven}','{$DateofConsultationHEPAB1}','{$HEPAB2}','{$dateHEPA2wasgiven}','{$DateofConsultationHEPAB2}','{$HEPAB3}','{$dateHEPA3WASGIVEN}','{$DateofConsultationHEPAB3}','{$HEPAB4}','{$dateHEPA4WASGIVEN}','{$DateofConsultationHEPAB4}','{$AMV19monthstobelow12months}','{$DateAMV1WASGIVEN}','{$DateofConsultationAMV1}','{$MMR12MOSTO15MOS}','{$dateMMRWASGIVEN}','{$DateofConsultationMMR}','{$FIC}','{$dateFICWASGIVEN}','{$DateofConsultationFIC}','{$CIC}','{$dateCICWASGIVEN}','{$DateofConsultationCIC}','{$AMV2_16MOSTO5YRSOLD}','{$dateAMV2WASGIVEN}','{$DateofConsultationAMV2}','{$IMMUNIZATIONSTATUS}','{$DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED}','{$FIRSTMONTH}','{$SECONDMONTH}','{$THIRDMONTH}','{$FOURTHDMONTH}','{$FIFTMONTH}','{$SIXTHMONTH}','{$date6MONTHS}','{$WASTHECHILDEXCLUSIVELY}','{$DATEOFCONSULTATIONEXCLUSIVEBF}','{$TT1}','{$DATEOFCONSULTTT1}','{$TT2}','{$DATEOFCONSULTTT2}','{$TT3}','{$DATEOFCONSULTTT3}','{$TT4}','{$DATEOFCONSULTTT4}','{$TT5}','{$DATEOFCONSULTT5}','{$PhoneNumber}','{$approvedBy}','{$dateOfExpiration}')";
        






        
        if (mysqli_query($conn, $sql)) {
           
           
          
             
            
            echo '
            <div class="row green lighten-5 green-text" style="padding:10px;">
            <div class="col s12 green-text">
              Record Successfully Added
            </div>
            </div>';
           
            
        } else {
            echo "Error: " . mysqli_error($conn);
            
        }
       


    } // End of else block for proceeding with insertion
} // End of main form submission block

// Retrieve comments from database
$sql = "SELECT * FROM nip_table";
$result = mysqli_query($conn, $sql);
$comments = '<div id="display_area">'; 
while ($row = mysqli_fetch_array($result)) {
    $comments .= '<div class="comment_box">
                    <span class="delete" data-id="' . $row['id'] . '" >delete</span>
                    <span class="edit" data-id="' . $row['id'] . '">edit</span>
                    <div class="display_name">' . $row['DateOfRegistration'] . '</div>
                    <div class="comment_text">' . $row['DateOfBirth'] . '</div>
                </div>';
}
$comments .= '</div>';

?>
