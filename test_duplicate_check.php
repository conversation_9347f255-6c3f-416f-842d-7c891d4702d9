<?php
include 'db.php';

echo "<h2>🧪 Duplicate Check System Test</h2>";

// Test the generateUniqueFamilySerial function
echo "<h3>1. Testing Unique Family Serial Generation:</h3>";
for ($i = 1; $i <= 5; $i++) {
    $serial = generateUniqueFamilySerial($conn);
    echo "Generated Serial $i: <strong>$serial</strong><br>";
}

// Test the checkForDuplicates function
echo "<br><h3>2. Testing Duplicate Detection:</h3>";

// Sample test data
$testData = [
    'NameOfChild' => 'Juan',
    'LastNameOfChild' => 'Dela Cruz',
    'DateOfBirth' => '2020-01-15',
    'NameofMother' => 'Maria Dela Cruz',
    'PhoneNumber' => '09123456789',
    'Address' => '123 Main Street',
    'Barangay' => 'Barangay 1'
];

echo "<h4>Test Data:</h4>";
echo "<ul>";
foreach ($testData as $key => $value) {
    echo "<li><strong>$key:</strong> $value</li>";
}
echo "</ul>";

$duplicateCheck = checkForDuplicates(
    $conn, 
    $testData['NameOfChild'], 
    $testData['LastNameOfChild'], 
    $testData['DateOfBirth'], 
    $testData['NameofMother'], 
    $testData['PhoneNumber'], 
    $testData['Address'], 
    $testData['Barangay']
);

echo "<h4>Duplicate Check Results:</h4>";
echo "<p><strong>Duplicates Found:</strong> " . ($duplicateCheck['found'] ? 'YES' : 'NO') . "</p>";
echo "<p><strong>Messages Count:</strong> " . count($duplicateCheck['messages']) . "</p>";

if (!empty($duplicateCheck['messages'])) {
    echo "<h5>Messages:</h5>";
    foreach ($duplicateCheck['messages'] as $msg) {
        $color = '';
        switch ($msg['type']) {
            case 'error': $color = 'red'; break;
            case 'warning': $color = 'orange'; break;
            case 'info': $color = 'blue'; break;
        }
        echo "<div style='background: #{$color}20; padding: 10px; margin: 5px 0; border-left: 4px solid $color;'>";
        echo "<strong>{$msg['title']}</strong><br>";
        echo "{$msg['message']}";
        echo "</div>";
    }
} else {
    echo "<p style='color: green;'>✅ No duplicates detected for test data!</p>";
}

// Test database statistics
echo "<br><h3>3. Database Statistics:</h3>";

try {
    // Total records
    $total_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE deleted = 0");
    $total_row = $total_result->fetch_assoc();
    echo "<p><strong>Total Active Records:</strong> {$total_row['total']}</p>";
    
    // Records with phone numbers
    $phone_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE PhoneNumber IS NOT NULL AND PhoneNumber != '' AND deleted = 0");
    $phone_row = $phone_result->fetch_assoc();
    echo "<p><strong>Records with Phone Numbers:</strong> {$phone_row['total']}</p>";
    
    // Potential exact duplicates
    $dup_result = $conn->query("SELECT COUNT(*) as groups, SUM(cnt-1) as duplicates FROM (
        SELECT COUNT(*) as cnt 
        FROM nip_table 
        WHERE deleted = 0 
        GROUP BY NameOfChild, LastNameOfChild, DateOfBirth, NameofMother 
        HAVING COUNT(*) > 1
    ) as dup_groups");
    $dup_row = $dup_result->fetch_assoc();
    echo "<p><strong>Duplicate Groups:</strong> {$dup_row['groups']}</p>";
    echo "<p><strong>Total Duplicate Records:</strong> {$dup_row['duplicates']}</p>";
    
    // Family serial number conflicts
    $serial_result = $conn->query("SELECT COUNT(*) as conflicts FROM (
        SELECT FamilySerialNumber 
        FROM nip_table 
        WHERE FamilySerialNumber IS NOT NULL AND FamilySerialNumber != '' AND deleted = 0 
        GROUP BY FamilySerialNumber 
        HAVING COUNT(*) > 1
    ) as serial_conflicts");
    $serial_row = $serial_result->fetch_assoc();
    echo "<p><strong>Family Serial Conflicts:</strong> {$serial_row['conflicts']}</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting statistics: " . $e->getMessage() . "</p>";
}

// Test SOUNDEX functionality
echo "<br><h3>4. Testing SOUNDEX (Similar Names):</h3>";
$test_names = [
    ['John', 'Jon'],
    ['Maria', 'Marie'],
    ['Jose', 'Joseph'],
    ['Ana', 'Anna'],
    ['Cruz', 'Cruzz']
];

echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Name 1</th><th>Name 2</th><th>SOUNDEX 1</th><th>SOUNDEX 2</th><th>Match</th></tr>";

foreach ($test_names as $names) {
    $soundex1 = soundex($names[0]);
    $soundex2 = soundex($names[1]);
    $match = ($soundex1 === $soundex2) ? '✅ YES' : '❌ NO';
    
    echo "<tr>";
    echo "<td>{$names[0]}</td>";
    echo "<td>{$names[1]}</td>";
    echo "<td>$soundex1</td>";
    echo "<td>$soundex2</td>";
    echo "<td>$match</td>";
    echo "</tr>";
}
echo "</table>";

// Test recent registrations
echo "<br><h3>5. Recent Registrations (Last 10):</h3>";
try {
    $recent_result = $conn->query("SELECT id, NameOfChild, LastNameOfChild, DateOfBirth, DateOfRegistration, FamilySerialNumber 
                                   FROM nip_table 
                                   WHERE deleted = 0 
                                   ORDER BY DateOfRegistration DESC, id DESC 
                                   LIMIT 10");
    
    if ($recent_result && $recent_result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Birth Date</th><th>Registration Date</th><th>Family Serial</th></tr>";
        
        while ($row = $recent_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['NameOfChild']} {$row['LastNameOfChild']}</td>";
            echo "<td>{$row['DateOfBirth']}</td>";
            echo "<td>{$row['DateOfRegistration']}</td>";
            echo "<td>{$row['FamilySerialNumber']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No recent registrations found.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting recent registrations: " . $e->getMessage() . "</p>";
}

echo "<br><hr>";
echo "<h3>🛠️ Tools:</h3>";
echo "<p>";
echo "<a href='duplicate_checker.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Duplicate Checker</a>";
echo "<a href='db.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='user_settings.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>👥 User Management</a>";
echo "</p>";

echo "<br><div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin-top: 20px;'>";
echo "<h4>✅ Duplicate Check Features Implemented:</h4>";
echo "<ul>";
echo "<li><strong>Exact Duplicate Detection:</strong> Same name, birth date, and mother's name</li>";
echo "<li><strong>Similar Name Detection:</strong> Uses SOUNDEX to find potential typos</li>";
echo "<li><strong>Phone Number Conflicts:</strong> Detects when same phone is used multiple times</li>";
echo "<li><strong>Family Serial Uniqueness:</strong> Ensures no duplicate family serial numbers</li>";
echo "<li><strong>Household Detection:</strong> Identifies multiple children from same household</li>";
echo "<li><strong>Automatic Prevention:</strong> Stops exact duplicates from being inserted</li>";
echo "<li><strong>Warning System:</strong> Shows warnings for potential duplicates</li>";
echo "<li><strong>Unique Serial Generation:</strong> Automatically generates unique family serials</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-collapse: collapse;
}

th {
    background-color: #007bff;
    color: white;
    font-weight: bold;
    text-align: left;
    padding: 8px;
}

td {
    padding: 8px;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3, h4, h5 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
}

a:hover {
    opacity: 0.8;
}
</style>
