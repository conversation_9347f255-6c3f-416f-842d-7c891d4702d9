<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Check Implementation - Demo</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .success-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        
        .error-box {
            background: #ffebee;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #f44336;
            margin: 15px 0;
        }
        
        .warning-box {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left red-text">error</i>
                Duplicate Check Implementation - Complete!
            </h3>
            <p class="center-align">Prevents adding records that already exist in the database</p>
        </div>

        <div class="demo-section">
            <h4>✅ What Was Implemented</h4>
            
            <div class="success-box">
                <h6>Duplicate Check Features:</h6>
                <ul>
                    <li>✅ <strong>Pre-Insert Validation:</strong> Checks for duplicates before adding records</li>
                    <li>✅ <strong>Multi-Field Matching:</strong> Uses 5 key fields to identify duplicates</li>
                    <li>✅ <strong>User-Friendly Error:</strong> Clear message when duplicate is found</li>
                    <li>✅ <strong>Detailed Information:</strong> Shows which record already exists</li>
                    <li>✅ <strong>Prevents Data Corruption:</strong> Maintains database integrity</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔍 Duplicate Detection Criteria</h4>
            
            <div class="warning-box">
                <h6>A record is considered duplicate if ALL these fields match:</h6>
                <ol>
                    <li><strong>Child's First Name</strong> (NameOfChild)</li>
                    <li><strong>Child's Last Name</strong> (LastNameOfChild)</li>
                    <li><strong>Mother's Name</strong> (NameofMother)</li>
                    <li><strong>Date of Birth</strong> (DateOfBirth)</li>
                    <li><strong>Barangay</strong> (Barangay)</li>
                </ol>
                <p><strong>Note:</strong> Only checks active records (not deleted ones)</p>
            </div>
        </div>

        <div class="demo-section">
            <h4>💻 Code Implementation</h4>
            
            <h5>1. Duplicate Check Query:</h5>
            <div class="code-block">
// Duplicate check - Check if record already exists
$duplicate_check_sql = "SELECT id FROM nip_table WHERE 
    NameOfChild = ? AND 
    LastNameOfChild = ? AND 
    NameofMother = ? AND 
    DateOfBirth = ? AND 
    Barangay = ? AND 
    (deleted IS NULL OR deleted = 0)";

$stmt = mysqli_prepare($conn, $duplicate_check_sql);
mysqli_stmt_bind_param($stmt, "sssss", $NameOfChild, $LastNameOfChild, $NameofMother, $DateOfBirth, $Barangay);
mysqli_stmt_execute($stmt);
$duplicate_result = mysqli_stmt_get_result($stmt);
            </div>
            
            <h5>2. Duplicate Found - Error Message:</h5>
            <div class="code-block">
if (mysqli_num_rows($duplicate_result) > 0) {
    // Record already exists - show error message
    echo '
    &lt;div class="row red lighten-4 red-text text-darken-4" style="padding:10px;"&gt;
        &lt;div class="col s12 red-text"&gt;
            &lt;i class="material-icons left"&gt;error&lt;/i&gt;
            &lt;strong&gt;Duplicate Record Found!&lt;/strong&gt;&lt;br&gt;
            This record already exists in the database.&lt;br&gt;
            Child: &lt;strong&gt;' . htmlspecialchars($NameOfChild . ' ' . $LastNameOfChild) . '&lt;/strong&gt;&lt;br&gt;
            Mother: &lt;strong&gt;' . htmlspecialchars($NameofMother) . '&lt;/strong&gt;&lt;br&gt;
            Birth Date: &lt;strong&gt;' . htmlspecialchars($DateOfBirth) . '&lt;/strong&gt;&lt;br&gt;
            Barangay: &lt;strong&gt;' . htmlspecialchars($Barangay) . '&lt;/strong&gt;
        &lt;/div&gt;
    &lt;/div&gt;';
} else {
    // No duplicate found - proceed with insertion
    // ... INSERT query here ...
}
            </div>
            
            <h5>3. Success Message with Earliest Date:</h5>
            <div class="code-block">
if (mysqli_query($conn, $sql)) {
    echo '
    &lt;div class="row green lighten-5 green-text" style="padding:10px;"&gt;
        &lt;div class="col s12 green-text"&gt;
            &lt;i class="material-icons left"&gt;check_circle&lt;/i&gt;Record Successfully Added
        &lt;/div&gt;
    &lt;/div&gt;';
    
    // Display earliest consultation date
    echo '&lt;div class="row blue lighten-5 blue-text" style="padding:10px; margin-top:10px;"&gt;
            &lt;div class="col s12 blue-text"&gt;
                &lt;i class="material-icons left"&gt;event&lt;/i&gt;&lt;strong&gt;Earliest Consultation Date: &lt;/strong&gt;';
    printEarliestDateWithConditions($new_record);
    echo '    &lt;/div&gt;
          &lt;/div&gt;';
}
            </div>
        </div>

        <div class="demo-section">
            <h4>🎯 User Experience</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">✅ New Record</span>
                            <p><strong>When no duplicate exists:</strong></p>
                            <ul>
                                <li>Record is successfully added</li>
                                <li>Green success message appears</li>
                                <li>Earliest consultation date is shown</li>
                                <li>User can continue adding records</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card red lighten-4">
                        <div class="card-content">
                            <span class="card-title">❌ Duplicate Found</span>
                            <p><strong>When duplicate exists:</strong></p>
                            <ul>
                                <li>Record is NOT added to database</li>
                                <li>Red error message appears</li>
                                <li>Shows details of existing record</li>
                                <li>User can modify data and try again</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔧 How It Works</h4>
            
            <div class="row">
                <div class="col s12 m4">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 1: Check</span>
                            <p>Before inserting, query database for existing records with same:</p>
                            <ul style="font-size: 13px;">
                                <li>Child name</li>
                                <li>Mother name</li>
                                <li>Birth date</li>
                                <li>Barangay</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m4">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 2: Decide</span>
                            <p>Based on check result:</p>
                            <ul style="font-size: 13px;">
                                <li><strong>Found:</strong> Show error message</li>
                                <li><strong>Not Found:</strong> Proceed with insert</li>
                                <li><strong>Deleted:</strong> Ignore deleted records</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m4">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 3: Result</span>
                            <p>User gets clear feedback:</p>
                            <ul style="font-size: 13px;">
                                <li><strong>Success:</strong> Record added + earliest date</li>
                                <li><strong>Error:</strong> Duplicate details shown</li>
                                <li><strong>Action:</strong> User can modify and retry</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Example Scenarios</h4>
            
            <h5>Scenario 1: Attempting to Add Duplicate</h5>
            <div class="error-box">
                <h6>Input Data:</h6>
                <ul>
                    <li>Child: Juan Dela Cruz</li>
                    <li>Mother: Maria Dela Cruz</li>
                    <li>Birth Date: 2023-01-15</li>
                    <li>Barangay: Poblacion</li>
                </ul>
                <h6>Result:</h6>
                <p style="color: #d32f2f; font-weight: bold;">
                    <i class="material-icons" style="vertical-align: middle;">error</i>
                    Duplicate Record Found! This record already exists in the database.
                </p>
            </div>
            
            <h5>Scenario 2: Adding New Record</h5>
            <div class="success-box">
                <h6>Input Data:</h6>
                <ul>
                    <li>Child: Maria Santos</li>
                    <li>Mother: Ana Santos</li>
                    <li>Birth Date: 2023-02-20</li>
                    <li>Barangay: San Jose</li>
                </ul>
                <h6>Result:</h6>
                <p style="color: #388e3c; font-weight: bold;">
                    <i class="material-icons" style="vertical-align: middle;">check_circle</i>
                    Record Successfully Added + Earliest Consultation Date shown
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>🛡️ Security Features</h4>
            
            <div class="warning-box">
                <h6>Security Measures Implemented:</h6>
                <ul>
                    <li>✅ <strong>Prepared Statements:</strong> Prevents SQL injection attacks</li>
                    <li>✅ <strong>Parameter Binding:</strong> Safe data handling</li>
                    <li>✅ <strong>HTML Escaping:</strong> Prevents XSS in error messages</li>
                    <li>✅ <strong>Input Validation:</strong> Checks data before processing</li>
                    <li>✅ <strong>Deleted Records:</strong> Excludes soft-deleted records from check</li>
                </ul>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>The duplicate check is now active in your registration form!</p>
            
            <div class="row">
                <div class="col s12 m6">
                    <a href="nip.php" class="btn large blue waves-effect">
                        <i class="material-icons left">add</i>Test Registration Form
                    </a>
                    <p><small>Try adding a duplicate record</small></p>
                </div>
                
                <div class="col s12 m6">
                    <a href="records.php" class="btn large green waves-effect">
                        <i class="material-icons left">table_chart</i>View Existing Records
                    </a>
                    <p><small>See what records already exist</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>✅ Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>Duplicate Check Implementation - Complete!</h6>
                    <ul>
                        <li><strong>Detection:</strong> Checks 5 key fields for duplicates</li>
                        <li><strong>Prevention:</strong> Stops duplicate records from being added</li>
                        <li><strong>User Feedback:</strong> Clear error messages with details</li>
                        <li><strong>Security:</strong> Uses prepared statements for safety</li>
                        <li><strong>Integration:</strong> Works with existing earliest date functionality</li>
                        <li><strong>Status:</strong> ✅ Active and ready to use!</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
