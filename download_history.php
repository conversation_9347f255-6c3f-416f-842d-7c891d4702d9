<?php
session_start();
include 'db.php';
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Create download_history table if it doesn't exist
$create_table_sql = "
CREATE TABLE IF NOT EXISTS download_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    health_center VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    download_type VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    record_count INT NOT NULL,
    file_size_kb DECIMAL(10,2) NOT NULL,
    download_time DATETIME NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    date_range_from DATE NULL,
    date_range_to DATE NULL,
    status VARCHAR(50) DEFAULT 'completed',
    processing_time_ms INT DEFAULT 0,
    security_verified BOOLEAN DEFAULT TRUE,
    INDEX idx_health_center (health_center),
    INDEX idx_download_time (download_time),
    INDEX idx_status (status)
)";

$conn->query($create_table_sql);

// Function to log download
function logDownload($conn, $data) {
    $stmt = $conn->prepare("
        INSERT INTO download_history 
        (health_center, user_name, download_type, file_name, record_count, file_size_kb, 
         download_time, ip_address, user_agent, date_range_from, date_range_to, 
         status, processing_time_ms, security_verified) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->bind_param("ssssidssssssii", 
        $data['health_center'], $data['user_name'], $data['download_type'], 
        $data['file_name'], $data['record_count'], $data['file_size_kb'],
        $data['download_time'], $data['ip_address'], $data['user_agent'],
        $data['date_range_from'], $data['date_range_to'], $data['status'],
        $data['processing_time_ms'], $data['security_verified']
    );
    
    $result = $stmt->execute();
    $stmt->close();
    return $result;
}

// Function to get download history
function getDownloadHistory($conn, $health_center, $limit = 50, $offset = 0) {
    $stmt = $conn->prepare("
        SELECT * FROM download_history 
        WHERE health_center = ? 
        ORDER BY download_time DESC 
        LIMIT ? OFFSET ?
    ");
    
    $stmt->bind_param("sii", $health_center, $limit, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $history = [];
    while ($row = $result->fetch_assoc()) {
        $history[] = $row;
    }
    
    $stmt->close();
    return $history;
}

// Function to get download statistics
function getDownloadStats($conn, $health_center) {
    // Total downloads
    $total_stmt = $conn->prepare("SELECT COUNT(*) as count FROM download_history WHERE health_center = ?");
    $total_stmt->bind_param("s", $health_center);
    $total_stmt->execute();
    $total_count = $total_stmt->get_result()->fetch_assoc()['count'];
    $total_stmt->close();
    
    // Today's downloads
    $today_stmt = $conn->prepare("SELECT COUNT(*) as count FROM download_history WHERE health_center = ? AND DATE(download_time) = CURDATE()");
    $today_stmt->bind_param("s", $health_center);
    $today_stmt->execute();
    $today_count = $today_stmt->get_result()->fetch_assoc()['count'];
    $today_stmt->close();
    
    // This week's downloads
    $week_stmt = $conn->prepare("SELECT COUNT(*) as count FROM download_history WHERE health_center = ? AND download_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $week_stmt->bind_param("s", $health_center);
    $week_stmt->execute();
    $week_count = $week_stmt->get_result()->fetch_assoc()['count'];
    $week_stmt->close();
    
    // Average file size
    $size_stmt = $conn->prepare("SELECT AVG(file_size_kb) as avg_size FROM download_history WHERE health_center = ?");
    $size_stmt->bind_param("s", $health_center);
    $size_stmt->execute();
    $avg_size = $size_stmt->get_result()->fetch_assoc()['avg_size'];
    $size_stmt->close();
    
    // Most active day
    $active_day_stmt = $conn->prepare("
        SELECT DATE(download_time) as download_date, COUNT(*) as count 
        FROM download_history 
        WHERE health_center = ? 
        GROUP BY DATE(download_time) 
        ORDER BY count DESC 
        LIMIT 1
    ");
    $active_day_stmt->bind_param("s", $health_center);
    $active_day_stmt->execute();
    $active_day_result = $active_day_stmt->get_result();
    $most_active_day = $active_day_result->num_rows > 0 ? $active_day_result->fetch_assoc() : null;
    $active_day_stmt->close();
    
    return [
        'total_downloads' => $total_count,
        'today_downloads' => $today_count,
        'week_downloads' => $week_count,
        'avg_file_size_kb' => round($avg_size, 2),
        'most_active_day' => $most_active_day
    ];
}

try {
    $health_center = $_SESSION['health_center'];
    $action = $_GET['action'] ?? 'get_history';
    
    switch ($action) {
        case 'log_download':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);
                
                $download_data = [
                    'health_center' => $health_center,
                    'user_name' => $_SESSION['fullname'] ?? 'Unknown',
                    'download_type' => $input['download_type'] ?? 'manual',
                    'file_name' => $input['file_name'] ?? 'export.xlsx',
                    'record_count' => $input['record_count'] ?? 0,
                    'file_size_kb' => $input['file_size_kb'] ?? 0,
                    'download_time' => date('Y-m-d H:i:s'),
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                    'date_range_from' => $input['date_range_from'] ?? null,
                    'date_range_to' => $input['date_range_to'] ?? null,
                    'status' => $input['status'] ?? 'completed',
                    'processing_time_ms' => $input['processing_time_ms'] ?? 0,
                    'security_verified' => $input['security_verified'] ?? true
                ];
                
                $result = logDownload($conn, $download_data);
                
                echo json_encode([
                    'success' => $result,
                    'message' => $result ? 'Download logged successfully' : 'Failed to log download'
                ]);
            }
            break;
            
        case 'get_history':
            $limit = intval($_GET['limit'] ?? 50);
            $offset = intval($_GET['offset'] ?? 0);
            
            $history = getDownloadHistory($conn, $health_center, $limit, $offset);
            
            echo json_encode([
                'success' => true,
                'history' => $history,
                'count' => count($history)
            ]);
            break;
            
        case 'get_stats':
            $stats = getDownloadStats($conn, $health_center);
            
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
            break;
            
        case 'export_history':
            $history = getDownloadHistory($conn, $health_center, 1000, 0);
            
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="download_history_' . date('Y-m-d') . '.xls"');
            header('Pragma: no-cache');
            header('Expires: 0');
            
            echo "Download ID\tHealth Center\tUser Name\tDownload Type\tFile Name\tRecord Count\tFile Size (KB)\tDownload Time\tIP Address\tStatus\n";
            
            foreach ($history as $record) {
                echo implode("\t", [
                    $record['id'],
                    $record['health_center'],
                    $record['user_name'],
                    $record['download_type'],
                    $record['file_name'],
                    $record['record_count'],
                    $record['file_size_kb'],
                    $record['download_time'],
                    $record['ip_address'],
                    $record['status']
                ]) . "\n";
            }
            exit;
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
