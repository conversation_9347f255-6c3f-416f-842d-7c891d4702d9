<?php session_start(); ?>

<?php
// Check if session exists, if not check for remember me cookie
if (!isset($_SESSION['health_center'])) {
    // Check if remember me cookie exists
    if (isset($_COOKIE['remember_user']) && isset($_COOKIE['remember_token'])) {
        // Include database connection
        include 'db.php';

        $remember_token = $_COOKIE['remember_token'];
        $health_center = $_COOKIE['remember_user'];

        // Verify the remember token in database
        $stmt = $conn->prepare("SELECT * FROM health_facility WHERE health_center = ? AND remember_token = ?");
        $stmt->bind_param("ss", $health_center, $remember_token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Valid remember token, restore session
            $user = $result->fetch_assoc();
            $_SESSION['health_center'] = $user['health_center'];
            $_SESSION['fullname'] = $user['fullname'];
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['AddressofFacility'] = $user['AddressofFacility'];
            $_SESSION['mobile_number'] = $user['mobile_number'];

            // Generate new remember token for security
            $new_token = bin2hex(random_bytes(32));
            $update_stmt = $conn->prepare("UPDATE health_facility SET remember_token = ? WHERE id = ?");
            $update_stmt->bind_param("si", $new_token, $user['id']);
            $update_stmt->execute();

            // Update cookie with new token
            setcookie('remember_token', $new_token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 days, httponly

            $stmt->close();
            $update_stmt->close();
            $conn->close();
        } else {
            // Invalid or expired token, clear cookies and redirect
            setcookie('remember_user', '', time() - 3600, '/');
            setcookie('remember_token', '', time() - 3600, '/');
            header('Location: login.php');
            exit;
        }
    } else {
        // No session and no valid remember me cookie
        header('Location: login.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta charset="UTF-8">
    <title>National Immunization Program</title>
    <?php include 'style.php'; ?>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
      

        .container {
            background: #ffffff;
            padding: 40px;
            
            margin: 2em auto;
           
            
            color: #1a1a1a;
            
        }

        h4 {
            font-weight: 700;
            font-size: 18.2px;
            margin: 25px 0;
            
            text-align: center;
            letter-spacing: -0.5px;
        }

        .blue-text {
            
            
            font-size: 20px;
            display: block;
            
        }

        p {
            font-size: 17.5px;
            color: #4a5568;
          
            text-align: center;
            margin: 20px 0;
        }

        

        img {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 1em;
            }

            h4 {
                font-size: 24px;
            }

            .black-text {
                font-size: 22px;
            }

            .blue-text {
                font-size: 20px;
            }

            p {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <?php include 'nav.php'; ?>

    <div class=" container center"  >
        <img src="alaga.png" alt="Health Management Image">
        <h4 class="">Welcome, <span class=" "><br><?php echo $_SESSION['health_center']; ?></span></h4>
        <p > Electronic Target Client List - ETCL<br><span style="font-size:22.6px; font-weight:500;" class="blue-text">National Immunization Program - Health Management Information System</span></p>
    </div>
</body>
</html>
