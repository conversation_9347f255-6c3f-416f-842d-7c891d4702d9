<?php
session_start();
include 'db.php';

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}

// Function to verify user's health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

$current_health_center = $_SESSION['health_center'];
$access_granted = verifyHealthCenterAccess($conn, $current_health_center);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Security Test - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <style>
        .security-card {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
        }
        .access-granted {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-left: 5px solid #4caf50;
        }
        .access-denied {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            border-left: 5px solid #f44336;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
        }
    </style>
</head>
<body class="grey lighten-4">
    <nav class="blue darken-2">
        <div class="nav-wrapper container">
            <a href="#" class="brand-logo">
                <i class="material-icons left">security</i>Excel Security Test
            </a>
            <ul class="right">
                <li><a href="index.php"><i class="material-icons left">home</i>Dashboard</a></li>
                <li><a href="logout.php"><i class="material-icons left">exit_to_app</i>Logout</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <div class="row">
            <div class="col s12">
                <h4 class="blue-grey-text text-darken-2">
                    <i class="material-icons left">verified_user</i>
                    Excel Download Security Test
                </h4>
                
                <!-- Current Session Info -->
                <div class="security-card <?php echo $access_granted ? 'access-granted' : 'access-denied'; ?>">
                    <h5>
                        <i class="material-icons left"><?php echo $access_granted ? 'check_circle' : 'error'; ?></i>
                        Current Session Status
                    </h5>
                    <p><strong>Health Center:</strong> <?php echo htmlspecialchars($current_health_center); ?></p>
                    <p><strong>Access Status:</strong> 
                        <?php if ($access_granted): ?>
                            <span class="green-text"><i class="material-icons tiny">check</i> GRANTED - You can download Excel data</span>
                        <?php else: ?>
                            <span class="red-text"><i class="material-icons tiny">block</i> BLOCKED - Excel download blocked. Data will not proceed.</span>
                        <?php endif; ?>
                    </p>
                </div>

                <!-- Security Test Results -->
                <div class="test-section">
                    <h5><i class="material-icons left">assessment</i>Security Validation Results</h5>
                    
                    <div class="row">
                        <div class="col s12 m6">
                            <div class="card">
                                <div class="card-content">
                                    <span class="card-title">
                                        <i class="material-icons left">account_circle</i>Session Check
                                    </span>
                                    <p>
                                        <?php if (isset($_SESSION['health_center'])): ?>
                                            <span class="green-text"><i class="material-icons tiny">check</i> Session Active</span><br>
                                            <small>Health Center: <?php echo htmlspecialchars($_SESSION['health_center']); ?></small>
                                        <?php else: ?>
                                            <span class="red-text"><i class="material-icons tiny">error</i> No Session</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col s12 m6">
                            <div class="card">
                                <div class="card-content">
                                    <span class="card-title">
                                        <i class="material-icons left">verified</i>Database Verification
                                    </span>
                                    <p>
                                        <?php if ($access_granted): ?>
                                            <span class="green-text"><i class="material-icons tiny">check</i> Health Center Verified</span><br>
                                            <small>Found in health_facility table</small>
                                        <?php else: ?>
                                            <span class="red-text"><i class="material-icons tiny">error</i> Health Center Not Found</span><br>
                                            <small>Not registered in health_facility table</small>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Excel Download -->
                <div class="test-section">
                    <h5><i class="material-icons left">file_download</i>Excel Download Options</h5>

                    <?php if ($access_granted): ?>
                        <div class="green lighten-4" style="padding: 15px; border-radius: 5px; margin: 10px 0;">
                            <p><i class="material-icons left green-text">check_circle</i>
                                <strong>Excel Download Available</strong> - Your health center is verified and you can download Excel data.
                            </p>

                            <div class="row">
                                <div class="col s12 m4">
                                    <a href="sheetjs.php" class="btn green darken-1 waves-effect waves-light" style="width: 100%;">
                                        <i class="material-icons left">file_download</i>Advanced Excel Export
                                    </a>
                                </div>
                                <div class="col s12 m4">
                                    <button onclick="testQuickDownload()" class="btn blue darken-1 waves-effect waves-light" style="width: 100%;">
                                        <i class="material-icons left">flash_on</i>Quick Download Test
                                    </button>
                                </div>
                                <div class="col s12 m4">
                                    <button onclick="showDataPreview()" class="btn orange darken-1 waves-effect waves-light" style="width: 100%;">
                                        <i class="material-icons left">preview</i>Data Preview
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="red lighten-4" style="padding: 15px; border-radius: 5px; margin: 10px 0;">
                            <p><i class="material-icons left red-text">block</i>
                                <strong>Wrong password - Excel download blocked. Data will not proceed.</strong><br>
                                You can only download data for your registered health center.
                            </p>
                            <div class="row">
                                <div class="col s12 m6">
                                    <button class="btn red darken-1 disabled" style="width: 100%;">
                                        <i class="material-icons left">block</i>Excel Download Blocked
                                    </button>
                                </div>
                                <div class="col s12 m6">
                                    <button onclick="showAccessHelp()" class="btn grey darken-1 waves-effect waves-light" style="width: 100%;">
                                        <i class="material-icons left">help</i>Get Access Help
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Data Statistics -->
                <div class="test-section">
                    <h5><i class="material-icons left">analytics</i>Data Statistics</h5>
                    <div class="row">
                        <div class="col s12 m3">
                            <div class="card blue darken-1 white-text">
                                <div class="card-content center-align">
                                    <i class="material-icons large">people</i>
                                    <h4 id="totalRecords">-</h4>
                                    <p>Total Records</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card green darken-1 white-text">
                                <div class="card-content center-align">
                                    <i class="material-icons large">today</i>
                                    <h4 id="todayRecords">-</h4>
                                    <p>Today's Records</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card orange darken-1 white-text">
                                <div class="card-content center-align">
                                    <i class="material-icons large">date_range</i>
                                    <h4 id="monthRecords">-</h4>
                                    <p>This Month</p>
                                </div>
                            </div>
                        </div>
                        <div class="col s12 m3">
                            <div class="card purple darken-1 white-text">
                                <div class="card-content center-align">
                                    <i class="material-icons large">file_download</i>
                                    <h4 id="downloadCount">-</h4>
                                    <p>Downloads Today</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Real-time Security Monitor -->
                <div class="test-section">
                    <h5><i class="material-icons left">monitor</i>Real-time Security Monitor</h5>
                    <div class="card">
                        <div class="card-content">
                            <div class="row">
                                <div class="col s12 m6">
                                    <h6><i class="material-icons left">security</i>Security Status</h6>
                                    <div id="securityStatus" class="security-monitor">
                                        <div class="preloader-wrapper small active">
                                            <div class="spinner-layer spinner-blue-only">
                                                <div class="circle-clipper left">
                                                    <div class="circle"></div>
                                                </div>
                                                <div class="gap-patch">
                                                    <div class="circle"></div>
                                                </div>
                                                <div class="circle-clipper right">
                                                    <div class="circle"></div>
                                                </div>
                                            </div>
                                        </div>
                                        Checking security status...
                                    </div>
                                </div>
                                <div class="col s12 m6">
                                    <h6><i class="material-icons left">access_time</i>Session Info</h6>
                                    <div id="sessionInfo">
                                        <p><strong>Login Time:</strong> <span id="loginTime">-</span></p>
                                        <p><strong>Session Duration:</strong> <span id="sessionDuration">-</span></p>
                                        <p><strong>Last Activity:</strong> <span id="lastActivity">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Features -->
                <div class="test-section">
                    <h5><i class="material-icons left">security</i>Security Features Implemented</h5>
                    
                    <ul class="collection">
                        <li class="collection-item">
                            <i class="material-icons left green-text">check</i>
                            <strong>Session Validation:</strong> Users must be logged in to access Excel downloads
                        </li>
                        <li class="collection-item">
                            <i class="material-icons left green-text">check</i>
                            <strong>Health Center Verification:</strong> Session health_center must match health_facility table
                        </li>
                        <li class="collection-item">
                            <i class="material-icons left green-text">check</i>
                            <strong>Password Protection:</strong> Additional password verification required for Excel downloads
                        </li>
                        <li class="collection-item">
                            <i class="material-icons left green-text">check</i>
                            <strong>Data Filtering:</strong> Users can only download data for their own health center
                        </li>
                        <li class="collection-item">
                            <i class="material-icons left green-text">check</i>
                            <strong>Access Logging:</strong> All download attempts are logged for security audit
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        let securityCheckInterval;
        let sessionStartTime = new Date();

        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();

            // Initialize features
            loadDataStatistics();
            startSecurityMonitor();
            updateSessionInfo();

            // Update session info every minute
            setInterval(updateSessionInfo, 60000);

            // Show toast based on access status
            <?php if ($access_granted): ?>
                M.toast({
                    html: '<i class="material-icons left">check_circle</i>Excel download access granted for <?php echo htmlspecialchars($current_health_center); ?>',
                    classes: 'green rounded',
                    displayLength: 4000
                });
            <?php else: ?>
                M.toast({
                    html: '<i class="material-icons left">block</i>Wrong password - Excel download blocked. Data will not proceed.',
                    classes: 'red rounded',
                    displayLength: 6000
                });
            <?php endif; ?>
        });

        // Load data statistics
        async function loadDataStatistics() {
            try {
                const response = await fetch('get_statistics.php');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('totalRecords').textContent = data.total || '0';
                    document.getElementById('todayRecords').textContent = data.today || '0';
                    document.getElementById('monthRecords').textContent = data.month || '0';
                    document.getElementById('downloadCount').textContent = data.downloads || '0';
                } else {
                    // Show placeholder data if access denied
                    document.getElementById('totalRecords').textContent = '---';
                    document.getElementById('todayRecords').textContent = '---';
                    document.getElementById('monthRecords').textContent = '---';
                    document.getElementById('downloadCount').textContent = '---';
                }
            } catch (error) {
                console.error('Error loading statistics:', error);
                document.getElementById('totalRecords').textContent = 'Error';
                document.getElementById('todayRecords').textContent = 'Error';
                document.getElementById('monthRecords').textContent = 'Error';
                document.getElementById('downloadCount').textContent = 'Error';
            }
        }

        // Start security monitoring
        function startSecurityMonitor() {
            checkSecurityStatus();
            securityCheckInterval = setInterval(checkSecurityStatus, 30000); // Check every 30 seconds
        }

        // Check security status
        async function checkSecurityStatus() {
            try {
                const response = await fetch('check_security_status.php');
                const data = await response.json();

                const statusDiv = document.getElementById('securityStatus');

                if (data.success) {
                    statusDiv.innerHTML = `
                        <div class="green-text">
                            <i class="material-icons left">verified_user</i>
                            <strong>SECURE</strong> - All security checks passed
                        </div>
                        <small>Last checked: ${new Date().toLocaleTimeString()}</small>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div class="red-text">
                            <i class="material-icons left">warning</i>
                            <strong>SECURITY ISSUE</strong> - ${data.message}
                        </div>
                        <small>Last checked: ${new Date().toLocaleTimeString()}</small>
                    `;
                }
            } catch (error) {
                document.getElementById('securityStatus').innerHTML = `
                    <div class="orange-text">
                        <i class="material-icons left">error</i>
                        <strong>CONNECTION ERROR</strong> - Unable to verify security status
                    </div>
                    <small>Last checked: ${new Date().toLocaleTimeString()}</small>
                `;
            }
        }

        // Update session information
        function updateSessionInfo() {
            const now = new Date();
            const duration = Math.floor((now - sessionStartTime) / 1000 / 60); // minutes

            document.getElementById('loginTime').textContent = sessionStartTime.toLocaleString();
            document.getElementById('sessionDuration').textContent = `${duration} minutes`;
            document.getElementById('lastActivity').textContent = now.toLocaleTimeString();
        }

        // Test quick download
        async function testQuickDownload() {
            M.toast({
                html: '<i class="material-icons left">hourglass_empty</i>Testing quick download...',
                classes: 'blue rounded',
                displayLength: 2000
            });

            try {
                const response = await fetch('quick_download_test.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ test: true })
                });

                const result = await response.json();

                if (result.success) {
                    M.toast({
                        html: '<i class="material-icons left">check_circle</i>Quick download test successful!',
                        classes: 'green rounded',
                        displayLength: 3000
                    });
                } else {
                    M.toast({
                        html: '<i class="material-icons left">block</i>Quick download blocked: ' + result.message,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                }
            } catch (error) {
                M.toast({
                    html: '<i class="material-icons left">error</i>Quick download test failed',
                    classes: 'red rounded',
                    displayLength: 3000
                });
            }
        }

        // Show data preview
        async function showDataPreview() {
            M.toast({
                html: '<i class="material-icons left">search</i>Loading data preview...',
                classes: 'blue rounded',
                displayLength: 2000
            });

            try {
                const response = await fetch('get_nip_data.php?count=true&preview=5');
                const data = await response.json();

                if (data.error) {
                    M.toast({
                        html: '<i class="material-icons left">block</i>Data preview blocked: ' + data.error,
                        classes: 'red rounded',
                        displayLength: 4000
                    });
                } else {
                    // Show modal with preview data
                    showPreviewModal(data);
                }
            } catch (error) {
                M.toast({
                    html: '<i class="material-icons left">error</i>Failed to load data preview',
                    classes: 'red rounded',
                    displayLength: 3000
                });
            }
        }

        // Show access help
        function showAccessHelp() {
            M.toast({
                html: '<i class="material-icons left">help</i>Contact your administrator to verify your health center registration',
                classes: 'orange rounded',
                displayLength: 6000
            });

            // Show help modal
            const helpModal = `
                <div id="helpModal" class="modal">
                    <div class="modal-content">
                        <h4><i class="material-icons left">help</i>Access Help</h4>
                        <p><strong>Why is my Excel download blocked?</strong></p>
                        <ul class="collection">
                            <li class="collection-item">Your health center must be registered in the system</li>
                            <li class="collection-item">Your session must match your registered health center</li>
                            <li class="collection-item">You can only download data for your own health center</li>
                        </ul>
                        <p><strong>How to get access:</strong></p>
                        <ol>
                            <li>Contact your system administrator</li>
                            <li>Verify your health center registration</li>
                            <li>Ensure you're logged in with the correct account</li>
                            <li>Try logging out and logging back in</li>
                        </ol>
                    </div>
                    <div class="modal-footer">
                        <a href="#!" class="modal-close waves-effect waves-green btn-flat">Close</a>
                        <a href="mailto:<EMAIL>" class="waves-effect waves-green btn blue">Contact Admin</a>
                    </div>
                </div>
            `;

            // Add modal to page if it doesn't exist
            if (!document.getElementById('helpModal')) {
                document.body.insertAdjacentHTML('beforeend', helpModal);
                M.Modal.init(document.getElementById('helpModal'));
            }

            // Open modal
            const modalInstance = M.Modal.getInstance(document.getElementById('helpModal'));
            modalInstance.open();
        }

        // Show preview modal
        function showPreviewModal(data) {
            const previewModal = `
                <div id="previewModal" class="modal modal-fixed-footer" style="max-height: 80%;">
                    <div class="modal-content">
                        <h4><i class="material-icons left">preview</i>Data Preview</h4>
                        <p>Showing sample records from your health center:</p>
                        <div class="row">
                            <div class="col s12">
                                <table class="striped responsive-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Registration Date</th>
                                            <th>Child Name</th>
                                            <th>Mother's Name</th>
                                            <th>Barangay</th>
                                        </tr>
                                    </thead>
                                    <tbody id="previewTableBody">
                                        <!-- Data will be inserted here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <p class="grey-text">Total records available: <strong>${data.count || 0}</strong></p>
                    </div>
                    <div class="modal-footer">
                        <a href="#!" class="modal-close waves-effect waves-green btn-flat">Close</a>
                        <a href="sheetjs.php" class="waves-effect waves-green btn green">Download Full Excel</a>
                    </div>
                </div>
            `;

            // Add modal to page if it doesn't exist
            if (!document.getElementById('previewModal')) {
                document.body.insertAdjacentHTML('beforeend', previewModal);
                M.Modal.init(document.getElementById('previewModal'));
            }

            // Populate table with preview data
            const tbody = document.getElementById('previewTableBody');
            if (data.preview && data.preview.length > 0) {
                tbody.innerHTML = data.preview.map(row => `
                    <tr>
                        <td>${row.id || '-'}</td>
                        <td>${row.DateOfRegistration || '-'}</td>
                        <td>${row.NameOfChild || '-'} ${row.lastnameOfChild || ''}</td>
                        <td>${row.NameofMother || '-'}</td>
                        <td>${row.Barangay || '-'}</td>
                    </tr>
                `).join('');
            } else {
                tbody.innerHTML = '<tr><td colspan="5" class="center-align">No data available</td></tr>';
            }

            // Open modal
            const modalInstance = M.Modal.getInstance(document.getElementById('previewModal'));
            modalInstance.open();
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (securityCheckInterval) {
                clearInterval(securityCheckInterval);
            }
        });
    </script>
</body>
</html>
