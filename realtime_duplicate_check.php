<?php
header('Content-Type: application/json');
include 'db.php';

// Real-time duplicate checking API
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $response = [
        'duplicates_found' => false,
        'messages' => [],
        'severity' => 'none',
        'recommendations' => []
    ];
    
    try {
        // Extract data from input
        $nameOfChild = $input['NameOfChild'] ?? '';
        $lastNameOfChild = $input['LastNameOfChild'] ?? '';
        $dateOfBirth = $input['DateOfBirth'] ?? '';
        $nameofMother = $input['NameofMother'] ?? '';
        $phoneNumber = $input['PhoneNumber'] ?? '';
        $address = $input['Address'] ?? '';
        $barangay = $input['Barangay'] ?? '';
        
        // Only check if we have minimum required data
        if (empty($nameOfChild) || empty($lastNameOfChild) || empty($dateOfBirth)) {
            echo json_encode($response);
            exit;
        }
        
        // Use the existing duplicate check function
        $duplicateCheck = checkForDuplicates(
            $conn, 
            $nameOfChild, 
            $lastNameOfChild, 
            $dateOfBirth, 
            $nameofMother, 
            $phoneNumber, 
            $address, 
            $barangay
        );
        
        $response['duplicates_found'] = $duplicateCheck['found'];
        $response['messages'] = $duplicateCheck['messages'];
        
        // Determine severity level
        $errorCount = 0;
        $warningCount = 0;
        
        foreach ($duplicateCheck['messages'] as $msg) {
            if ($msg['type'] === 'error') {
                $errorCount++;
            } elseif ($msg['type'] === 'warning') {
                $warningCount++;
            }
        }
        
        if ($errorCount > 0) {
            $response['severity'] = 'error';
        } elseif ($warningCount > 0) {
            $response['severity'] = 'warning';
        } elseif (!empty($duplicateCheck['messages'])) {
            $response['severity'] = 'info';
        }
        
        // Add recommendations based on findings
        if ($duplicateCheck['found']) {
            $response['recommendations'][] = [
                'action' => 'stop_registration',
                'message' => 'Registration blocked due to exact duplicate found',
                'icon' => 'block'
            ];
        } elseif ($warningCount > 0) {
            $response['recommendations'][] = [
                'action' => 'review_carefully',
                'message' => 'Please review the warnings before proceeding',
                'icon' => 'warning'
            ];
            $response['recommendations'][] = [
                'action' => 'check_existing',
                'message' => 'Check existing records to avoid duplicates',
                'icon' => 'search'
            ];
        }
        
        // Add quick stats
        $stats_query = "SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN NameOfChild LIKE ? OR LastNameOfChild LIKE ? THEN 1 END) as similar_names,
            COUNT(CASE WHEN DateOfBirth = ? THEN 1 END) as same_dob
            FROM nip_table WHERE deleted = 0";
        
        $stats_stmt = $conn->prepare($stats_query);
        $name_pattern = "%{$nameOfChild}%";
        $lastname_pattern = "%{$lastNameOfChild}%";
        $stats_stmt->bind_param("sss", $name_pattern, $lastname_pattern, $dateOfBirth);
        $stats_stmt->execute();
        $stats_result = $stats_stmt->get_result();
        $stats = $stats_result->fetch_assoc();
        $stats_stmt->close();
        
        $response['statistics'] = $stats;
        
    } catch (Exception $e) {
        $response['error'] = 'Database error: ' . $e->getMessage();
    }
    
    echo json_encode($response);
    exit;
}

// If GET request, show the test interface
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Duplicate Checker - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <script src="duplicate_notifications.js"></script>
    <style>
        .realtime-checker {
            margin-top: 30px;
        }
        .duplicate-status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ddd;
        }
        .duplicate-status.clean {
            background: #e8f5e8;
            border-left-color: #4caf50;
            color: #2e7d32;
        }
        .duplicate-status.warning {
            background: #fff3e0;
            border-left-color: #ff9800;
            color: #ef6c00;
        }
        .duplicate-status.error {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        .checking {
            opacity: 0.7;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .stat-card {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container realtime-checker">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">search</i>
                            Real-time Duplicate Checker
                        </span>
                        
                        <p>Enter child information to check for duplicates in real-time:</p>
                        
                        <form id="duplicateCheckForm">
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <input id="NameOfChild" name="NameOfChild" type="text" required>
                                    <label for="NameOfChild">First Name</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input id="LastNameOfChild" name="LastNameOfChild" type="text" required>
                                    <label for="LastNameOfChild">Last Name</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <input id="DateOfBirth" name="DateOfBirth" type="date" required>
                                    <label for="DateOfBirth">Date of Birth</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input id="NameofMother" name="NameofMother" type="text">
                                    <label for="NameofMother">Mother's Name</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <input id="PhoneNumber" name="PhoneNumber" type="tel">
                                    <label for="PhoneNumber">Phone Number</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input id="Barangay" name="Barangay" type="text">
                                    <label for="Barangay">Barangay</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12">
                                    <input id="Address" name="Address" type="text">
                                    <label for="Address">Complete Address</label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn blue waves-effect">
                                <i class="material-icons left">search</i>Check for Duplicates
                            </button>
                            
                            <button type="button" id="clearForm" class="btn grey waves-effect">
                                <i class="material-icons left">clear</i>Clear Form
                            </button>
                        </form>
                        
                        <!-- Status Display -->
                        <div id="duplicateStatus" class="duplicate-status" style="display: none;">
                            <div id="statusContent"></div>
                        </div>
                        
                        <!-- Statistics -->
                        <div id="statisticsSection" style="display: none;">
                            <h6>Database Statistics</h6>
                            <div class="stats-grid" id="statsGrid"></div>
                        </div>
                        
                        <!-- Messages -->
                        <div id="messagesSection" style="display: none;">
                            <h6>Duplicate Check Results</h6>
                            <div id="messagesList"></div>
                        </div>
                        
                        <!-- Recommendations -->
                        <div id="recommendationsSection" style="display: none;">
                            <h6>Recommendations</h6>
                            <div id="recommendationsList"></div>
                        </div>
                    </div>
                </div>
                
                <div class="center-align">
                    <a href="duplicate_checker.php" class="btn green waves-effect">
                        <i class="material-icons left">list</i>View All Duplicates
                    </a>
                    <a href="db.php" class="btn blue waves-effect">
                        <i class="material-icons left">add</i>Registration Form
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            const form = document.getElementById('duplicateCheckForm');
            const statusDiv = document.getElementById('duplicateStatus');
            const statusContent = document.getElementById('statusContent');
            const statsSection = document.getElementById('statisticsSection');
            const statsGrid = document.getElementById('statsGrid');
            const messagesSection = document.getElementById('messagesSection');
            const messagesList = document.getElementById('messagesList');
            const recommendationsSection = document.getElementById('recommendationsSection');
            const recommendationsList = document.getElementById('recommendationsList');
            
            // Real-time checking on input change
            const inputs = form.querySelectorAll('input');
            let checkTimeout;
            
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    clearTimeout(checkTimeout);
                    checkTimeout = setTimeout(checkForDuplicates, 1000); // Debounce
                });
            });
            
            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                checkForDuplicates();
            });
            
            // Clear form
            document.getElementById('clearForm').addEventListener('click', function() {
                form.reset();
                hideAllSections();
                M.updateTextFields();
            });
            
            function checkForDuplicates() {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                
                // Show checking status
                showStatus('checking', '<i class="material-icons left">hourglass_empty</i>Checking for duplicates...');
                
                fetch('realtime_duplicate_check.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    displayResults(result);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showStatus('error', '<i class="material-icons left">error</i>Error checking for duplicates');
                });
            }
            
            function displayResults(result) {
                if (result.error) {
                    showStatus('error', `<i class="material-icons left">error</i>Error: ${result.error}`);
                    return;
                }
                
                // Show status
                if (result.severity === 'error') {
                    showStatus('error', '<i class="material-icons left">error</i>Exact duplicate found! Registration should be blocked.');
                    if (duplicateNotifier) {
                        duplicateNotifier.showDuplicateError('Exact Duplicate Found', 'This child is already registered in the system');
                    }
                } else if (result.severity === 'warning') {
                    showStatus('warning', '<i class="material-icons left">warning</i>Potential duplicates detected. Please review carefully.');
                    if (duplicateNotifier) {
                        duplicateNotifier.showDuplicateWarning('Potential Duplicates', 'Similar records found in the system');
                    }
                } else if (result.severity === 'info') {
                    showStatus('clean', '<i class="material-icons left">info</i>Some information found. Please review.');
                } else {
                    showStatus('clean', '<i class="material-icons left">check_circle</i>No duplicates found. Safe to proceed.');
                }
                
                // Show statistics
                if (result.statistics) {
                    displayStatistics(result.statistics);
                }
                
                // Show messages
                if (result.messages && result.messages.length > 0) {
                    displayMessages(result.messages);
                }
                
                // Show recommendations
                if (result.recommendations && result.recommendations.length > 0) {
                    displayRecommendations(result.recommendations);
                }
            }
            
            function showStatus(type, content) {
                statusDiv.className = `duplicate-status ${type}`;
                statusContent.innerHTML = content;
                statusDiv.style.display = 'block';
            }
            
            function displayStatistics(stats) {
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${stats.total_records}</div>
                        <div class="stat-label">Total Records</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.similar_names}</div>
                        <div class="stat-label">Similar Names</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${stats.same_dob}</div>
                        <div class="stat-label">Same Birth Date</div>
                    </div>
                `;
                statsSection.style.display = 'block';
            }
            
            function displayMessages(messages) {
                messagesList.innerHTML = messages.map(msg => {
                    const iconClass = msg.type === 'error' ? 'error' : msg.type === 'warning' ? 'warning' : 'info';
                    const cardClass = msg.type === 'error' ? 'red lighten-4' : msg.type === 'warning' ? 'orange lighten-4' : 'blue lighten-4';
                    
                    return `
                        <div class="card ${cardClass}">
                            <div class="card-content">
                                <span class="card-title">
                                    <i class="material-icons left">${iconClass}</i>${msg.title}
                                </span>
                                <p>${msg.message}</p>
                            </div>
                        </div>
                    `;
                }).join('');
                messagesSection.style.display = 'block';
            }
            
            function displayRecommendations(recommendations) {
                recommendationsList.innerHTML = recommendations.map(rec => `
                    <div class="chip">
                        <i class="material-icons left">${rec.icon}</i>
                        ${rec.message}
                    </div>
                `).join('');
                recommendationsSection.style.display = 'block';
            }
            
            function hideAllSections() {
                statusDiv.style.display = 'none';
                statsSection.style.display = 'none';
                messagesSection.style.display = 'none';
                recommendationsSection.style.display = 'none';
            }
        });
    </script>
</body>
</html>
