<?php
include 'db.php'; // Include database connection

$id = $_GET['id'];

$sql = "SELECT * FROM nip_table WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $record = $result->fetch_assoc();
    echo json_encode(['success' => true, 'record' => $record]);
} else {
    echo json_encode(['success' => false, 'message' => 'Record not found.']);
}

$stmt->close();
$conn->close();
?>
