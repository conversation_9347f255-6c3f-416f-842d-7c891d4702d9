<?php
// Simple Database Test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Simple Database Test</h2>";

// Test 1: Connection
echo "<h3>1. Database Connection</h3>";
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    echo "<p style='color: red;'>Connection failed: " . mysqli_connect_error() . "</p>";
    exit;
}
echo "<p style='color: green;'>✅ Connected successfully</p>";

// Test 2: Table check
echo "<h3>2. Table Check</h3>";
$result = mysqli_query($conn, "SHOW TABLES LIKE 'nip_table'");
if (mysqli_num_rows($result) == 0) {
    echo "<p style='color: orange;'>⚠️ nip_table doesn't exist. Creating it...</p>";
    
    $sql = "CREATE TABLE nip_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        DateOfRegistration DATE,
        DateOfBirth DATE,
        NameOfChild VARCHAR(255),
        LastNameOfChild VARCHAR(255),
        middlename_of_child VARCHAR(255),
        Sex VARCHAR(10),
        NameofMother VARCHAR(255),
        Barangay VARCHAR(255),
        Address TEXT,
        PhoneNumber VARCHAR(20),
        FamilySerialNumber VARCHAR(100),
        deleted TINYINT(1) DEFAULT 0
    )";
    
    if (mysqli_query($conn, $sql)) {
        echo "<p style='color: green;'>✅ Table created successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Error creating table: " . mysqli_error($conn) . "</p>";
    }
} else {
    echo "<p style='color: green;'>✅ nip_table exists</p>";
}

// Test 3: Simple query
echo "<h3>3. Simple Query Test</h3>";
$count_result = mysqli_query($conn, "SELECT COUNT(*) as total FROM nip_table");
if ($count_result) {
    $row = mysqli_fetch_assoc($count_result);
    echo "<p style='color: green;'>✅ Query successful. Total records: " . $row['total'] . "</p>";
} else {
    echo "<p style='color: red;'>❌ Query failed: " . mysqli_error($conn) . "</p>";
}

// Test 4: Test prepared statement
echo "<h3>4. Prepared Statement Test</h3>";
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM nip_table WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ?");
if ($stmt) {
    $test_name = "Test";
    $test_lastname = "Child";
    $test_dob = "2020-01-01";
    
    $stmt->bind_param("sss", $test_name, $test_lastname, $test_dob);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    echo "<p style='color: green;'>✅ Prepared statement works. Found " . $row['total'] . " matching records</p>";
    $stmt->close();
} else {
    echo "<p style='color: red;'>❌ Prepared statement failed: " . $conn->error . "</p>";
}

// Test 5: Test the actual duplicate check function
echo "<h3>5. Duplicate Check Function Test</h3>";

function testCheckDuplicateChild($conn, $firstName, $lastName, $birthDate) {
    $duplicateFound = false;
    $duplicateMessage = '';
    
    $firstName = trim($firstName);
    $lastName = trim($lastName);
    $birthDate = trim($birthDate);
    
    if (empty($firstName) || empty($lastName) || empty($birthDate)) {
        return ['found' => false, 'message' => 'Empty fields'];
    }
    
    try {
        $checkStmt = $conn->prepare("SELECT id, NameOfChild, LastNameOfChild, DateOfBirth, FamilySerialNumber, DateOfRegistration 
                                     FROM nip_table 
                                     WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ? 
                                     AND (deleted IS NULL OR deleted = 0)");
        
        if ($checkStmt) {
            $checkStmt->bind_param("sss", $firstName, $lastName, $birthDate);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            
            if ($result->num_rows > 0) {
                $duplicateFound = true;
                $existingRecord = $result->fetch_assoc();
                $duplicateMessage = "Child '{$existingRecord['NameOfChild']} {$existingRecord['LastNameOfChild']}' with birth date '{$existingRecord['DateOfBirth']}' is already registered";
            }
            $checkStmt->close();
        } else {
            return ['found' => false, 'message' => 'Prepare failed: ' . $conn->error];
        }
        
    } catch (Exception $e) {
        return ['found' => false, 'message' => 'Exception: ' . $e->getMessage()];
    }
    
    return ['found' => $duplicateFound, 'message' => $duplicateMessage];
}

$test_result = testCheckDuplicateChild($conn, 'Test', 'Child', '2020-01-01');
echo "<p style='color: green;'>✅ Function executed successfully</p>";
echo "<p>Result: " . ($test_result['found'] ? 'Duplicate found' : 'No duplicate') . "</p>";
if (!empty($test_result['message'])) {
    echo "<p>Message: " . htmlspecialchars($test_result['message']) . "</p>";
}

// Test 6: Test form submission simulation
echo "<h3>6. Form Submission Simulation</h3>";

// Simulate POST data
$_POST['submitBtn'] = 'Submit';
$_POST['NameOfChild'] = 'TestChild';
$_POST['LastNameOfChild'] = 'TestLastName';
$_POST['DateOfBirth'] = '2020-01-01';

// Extract variables like in db.php
$NameOfChild = $_POST['NameOfChild'];
$LastNameOfChild = $_POST['LastNameOfChild'];
$DateOfBirth = $_POST['DateOfBirth'];

echo "<p>Extracted variables:</p>";
echo "<ul>";
echo "<li>NameOfChild: " . htmlspecialchars($NameOfChild) . "</li>";
echo "<li>LastNameOfChild: " . htmlspecialchars($LastNameOfChild) . "</li>";
echo "<li>DateOfBirth: " . htmlspecialchars($DateOfBirth) . "</li>";
echo "</ul>";

// Test duplicate check with extracted variables
$duplicate_check = testCheckDuplicateChild($conn, $NameOfChild, $LastNameOfChild, $DateOfBirth);
echo "<p style='color: green;'>✅ Duplicate check with form variables successful</p>";
echo "<p>Found duplicate: " . ($duplicate_check['found'] ? 'Yes' : 'No') . "</p>";

// Test 7: Insert a test record
echo "<h3>7. Test Record Insertion</h3>";

$test_insert = "INSERT INTO nip_table (DateOfRegistration, NameOfChild, LastNameOfChild, DateOfBirth, Sex, NameofMother, Barangay, Address, FamilySerialNumber) 
                VALUES (NOW(), 'TestChild', 'TestLastName', '2020-01-01', 'MALE', 'TestMother', 'TestBarangay', 'TestAddress', 'TEST-123')";

if (mysqli_query($conn, $test_insert)) {
    $inserted_id = mysqli_insert_id($conn);
    echo "<p style='color: green;'>✅ Test record inserted successfully (ID: $inserted_id)</p>";
    
    // Now test duplicate check again
    $duplicate_check2 = testCheckDuplicateChild($conn, $NameOfChild, $LastNameOfChild, $DateOfBirth);
    echo "<p>Duplicate check after insert: " . ($duplicate_check2['found'] ? 'Found duplicate' : 'No duplicate') . "</p>";
    
    // Clean up - delete the test record
    mysqli_query($conn, "DELETE FROM nip_table WHERE id = $inserted_id");
    echo "<p>Test record cleaned up</p>";
    
} else {
    echo "<p style='color: red;'>❌ Insert failed: " . mysqli_error($conn) . "</p>";
}

echo "<h3>8. Summary</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>All tests completed successfully!</strong></p>";
echo "<p>If you're still getting database errors, the issue might be:</p>";
echo "<ul>";
echo "<li>JavaScript errors in the browser (check browser console)</li>";
echo "<li>AJAX request issues</li>";
echo "<li>Session problems</li>";
echo "<li>File permissions</li>";
echo "</ul>";
echo "</div>";

echo "<h3>Next Steps</h3>";
echo "<p><a href='nip.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Registration Form</a></p>";

mysqli_close($conn);
unset($_POST);
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
h2, h3 { color: #343a40; }
ul { line-height: 1.6; }
</style>
