<?php
/**
 * Database Migration to Add Remember Token Functionality
 * Run this script once to add remember_token column to health_facility table
 */

include 'db.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Adding Remember Token Functionality</h2>";
echo "<p>Starting migration...</p>";

try {
    // Check if remember_token column already exists
    $check_column = "SHOW COLUMNS FROM health_facility LIKE 'remember_token'";
    $result = $conn->query($check_column);
    
    if ($result->num_rows == 0) {
        // Add remember_token column
        $add_column_sql = "ALTER TABLE health_facility ADD COLUMN remember_token VARCHAR(64) NULL DEFAULT NULL";
        
        if ($conn->query($add_column_sql) === TRUE) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "✓ Successfully added remember_token column to health_facility table";
            echo "</div>";
        } else {
            throw new Exception("Error adding remember_token column: " . $conn->error);
        }
        
        // Add index for better performance
        $add_index_sql = "CREATE INDEX idx_remember_token ON health_facility(remember_token)";
        
        if ($conn->query($add_index_sql) === TRUE) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "✓ Successfully added index for remember_token column";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "⚠ Warning: Could not add index for remember_token: " . $conn->error;
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #cce5ff; border: 1px solid #99ccff; color: #004085; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "ℹ remember_token column already exists in health_facility table";
        echo "</div>";
    }
    
    // Verify the column was added successfully
    $verify_column = "SHOW COLUMNS FROM health_facility LIKE 'remember_token'";
    $verify_result = $conn->query($verify_column);
    
    if ($verify_result->num_rows > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✓ Migration Completed Successfully!</h4>";
        echo "<p>The remember_token functionality has been added to your database.</p>";
        echo "<p><strong>Features added:</strong></p>";
        echo "<ul>";
        echo "<li>remember_token column in health_facility table</li>";
        echo "<li>Database index for performance optimization</li>";
        echo "<li>Support for persistent login sessions</li>";
        echo "</ul>";
        echo "<p><strong>Users can now stay logged in even after closing the browser!</strong></p>";
        echo "</div>";
    } else {
        throw new Exception("Migration verification failed - remember_token column not found");
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✗ Migration Failed</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
    echo "</div>";
}

$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Remember Token Migration</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #333; }
        p { color: #666; }
    </style>
</head>
<body>
    <hr>
    <p><strong>Next Steps:</strong></p>
    <ol>
        <li>The remember token functionality is now available</li>
        <li>Users will automatically stay logged in when they close and reopen the browser</li>
        <li>The login system will use secure tokens to maintain sessions</li>
        <li>You can delete this migration file after running it successfully</li>
    </ol>
    
    <p><a href="profile.php">← Back to Profile</a> | <a href="login.php">Go to Login</a></p>
</body>
</html>
