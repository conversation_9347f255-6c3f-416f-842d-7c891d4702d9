<?php
error_reporting(0);
try {
    $server = '*************';
    $database = 'PCHIMS';
    $user = 'sa';
    $pass = 'angelo';
    $mssqldriver = '{SQL Server Native Client 11.0}'; 
    $db = new PDO("odbc:Driver=$mssqldriver;Server=$server;Database=$database;charset=UTF-8", $user, $pass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
   // echo 'NIP';
} catch (PDOException $e) {
    echo "<div class='alert alert-warning' role='alert'>Failed to connect to server: " . htmlspecialchars($e->getMessage()) . "</div>";
    exit;
}
 
 
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $ekonsultaRegIDNo = $_POST['ekonsultaRegIDNo'];

    try {
        $stmt = $db->prepare("EXEC [SP_UHC_eKonsultaReg_CheckFEP_PHP]  ?");
        $stmt->execute([$ekonsultaRegIDNo]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            echo json_encode([
                "success" => true,
                "ekonsultaRegIDNo" => $row['ekonsultaRegIDNo'],
                "UHCID" => $row['UHCID'],
                "LName" => $row['LName'],
                "FName" => $row['FName'],
                "MName" => $row['MName'],
                "BirthDate" => $row['BirthDate'],
                "Age" => $row['Age'],
                "Sex" => $row['Sex'],
                "ContactNo" => $row['ContactNo']
            ]);
        }
        
        else {
            echo json_encode(["success" => false]);
        }
    } catch (PDOException $e) {
        echo json_encode(["success" => false, "error" => $e->getMessage()]);
    }
}
?>
