<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filter & Print Demo - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        .demo-container {
            margin-top: 30px;
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .step-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
        .screenshot-placeholder {
            background: #f5f5f5;
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container demo-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align">
                            <i class="material-icons left">search</i>
                            Filter & Print System Demo
                        </span>
                        
                        <p class="center-align">
                            Learn how to use the new Filter & Print functionality to search and print child records.
                        </p>
                        
                        <!-- Features Overview -->
                        <div class="demo-section">
                            <h4>🎯 Key Features</h4>
                            
                            <div class="feature-card">
                                <h5><i class="material-icons left">search</i>Advanced Search</h5>
                                <p>Search by first name, last name, middle name, and birth date with flexible filtering options.</p>
                            </div>
                            
                            <div class="feature-card">
                                <h5><i class="material-icons left">table_chart</i>Results Table</h5>
                                <p>View search results in a clean, organized table with all essential child information.</p>
                            </div>
                            
                            <div class="feature-card">
                                <h5><i class="material-icons left">print</i>Print Options</h5>
                                <p>Print individual records or bulk print all search results with professional formatting.</p>
                            </div>
                            
                            <div class="feature-card">
                                <h5><i class="material-icons left">speed</i>Real-time Search</h5>
                                <p>Instant search results as you type with partial matching and flexible criteria.</p>
                            </div>
                        </div>
                        
                        <!-- How to Use -->
                        <div class="demo-section">
                            <h4>📋 How to Use</h4>
                            
                            <div class="step-card">
                                <h5>Step 1: Access Filter Page</h5>
                                <p>Navigate to <strong>Filter & Print</strong> from the main menu or click the button below.</p>
                                <a href="filter_records.php" class="btn blue waves-effect">
                                    <i class="material-icons left">search</i>Open Filter Page
                                </a>
                            </div>
                            
                            <div class="step-card">
                                <h5>Step 2: Enter Search Criteria</h5>
                                <p>Fill in any combination of the following fields:</p>
                                <ul>
                                    <li><strong>First Name:</strong> Child's first name (partial matching supported)</li>
                                    <li><strong>Last Name:</strong> Child's last name (partial matching supported)</li>
                                    <li><strong>Middle Name:</strong> Child's middle name (partial matching supported)</li>
                                    <li><strong>Birth Date:</strong> Exact birth date (YYYY-MM-DD format)</li>
                                </ul>
                                <p><em>Note: You can search using any combination of fields. Leave fields empty to search all records.</em></p>
                            </div>
                            
                            <div class="step-card">
                                <h5>Step 3: Click Search</h5>
                                <p>Click the <strong>"Search Records"</strong> button to find matching children.</p>
                            </div>
                            
                            <div class="step-card">
                                <h5>Step 4: Review Results</h5>
                                <p>Browse the results table showing:</p>
                                <ul>
                                    <li>Record ID and Registration Date</li>
                                    <li>Full Name (First + Middle + Last)</li>
                                    <li>Birth Date and Sex</li>
                                    <li>Mother's Name</li>
                                    <li>Barangay and Contact Information</li>
                                    <li>Family Serial Number</li>
                                </ul>
                            </div>
                            
                            <div class="step-card">
                                <h5>Step 5: Print Records</h5>
                                <p>Choose your printing option:</p>
                                <ul>
                                    <li><strong>Print All Results:</strong> Click "Print Results" to print the entire search results table</li>
                                    <li><strong>Print Individual Record:</strong> Click the blue print icon next to any record for detailed individual printing</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Search Examples -->
                        <div class="demo-section">
                            <h4>🔍 Search Examples</h4>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <div class="card green lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">Example 1: Find by Name</span>
                                            <p><strong>First Name:</strong> Juan</p>
                                            <p><strong>Last Name:</strong> Dela Cruz</p>
                                            <p><strong>Result:</strong> All children named Juan Dela Cruz</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6">
                                    <div class="card blue lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">Example 2: Find by Birth Date</span>
                                            <p><strong>Birth Date:</strong> 2020-01-15</p>
                                            <p><strong>Result:</strong> All children born on January 15, 2020</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <div class="card orange lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">Example 3: Partial Name Search</span>
                                            <p><strong>First Name:</strong> Mar</p>
                                            <p><strong>Result:</strong> Maria, Mario, Mark, etc.</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6">
                                    <div class="card purple lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">Example 4: Combined Search</span>
                                            <p><strong>Last Name:</strong> Santos</p>
                                            <p><strong>Birth Date:</strong> 2020-01-15</p>
                                            <p><strong>Result:</strong> All Santos children born on that date</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Print Features -->
                        <div class="demo-section">
                            <h4>🖨️ Print Features</h4>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title">Bulk Print</span>
                                            <p>Print all search results in a table format including:</p>
                                            <ul>
                                                <li>Professional header with NIP branding</li>
                                                <li>Generation timestamp</li>
                                                <li>Total record count</li>
                                                <li>Clean table layout</li>
                                                <li>Print-optimized formatting</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6">
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title">Individual Print</span>
                                            <p>Print detailed individual records including:</p>
                                            <ul>
                                                <li>Complete child information</li>
                                                <li>Family details</li>
                                                <li>Birth information</li>
                                                <li>Immunization status</li>
                                                <li>Official formatting</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tips & Best Practices -->
                        <div class="demo-section">
                            <h4>💡 Tips & Best Practices</h4>
                            
                            <div class="collection">
                                <div class="collection-item">
                                    <span class="badge green white-text">TIP</span>
                                    <strong>Partial Matching:</strong> You don't need to type the complete name. "Mar" will find "Maria", "Mario", etc.
                                </div>
                                <div class="collection-item">
                                    <span class="badge blue white-text">TIP</span>
                                    <strong>Date Format:</strong> Use YYYY-MM-DD format for birth dates (e.g., 2020-01-15)
                                </div>
                                <div class="collection-item">
                                    <span class="badge orange white-text">TIP</span>
                                    <strong>Empty Fields:</strong> Leave fields empty to search all records (useful for getting complete lists)
                                </div>
                                <div class="collection-item">
                                    <span class="badge purple white-text">TIP</span>
                                    <strong>Print Preview:</strong> Use your browser's print preview to check formatting before printing
                                </div>
                                <div class="collection-item">
                                    <span class="badge red white-text">TIP</span>
                                    <strong>Large Results:</strong> For performance, searches are limited to 100 results. Use more specific criteria for large datasets.
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="demo-section">
                            <h4>🚀 Quick Actions</h4>
                            
                            <div class="row">
                                <div class="col s12 m6 l3">
                                    <a href="filter_records.php" class="btn blue waves-effect full-width">
                                        <i class="material-icons left">search</i>Start Filtering
                                    </a>
                                    <p><small>Open the filter page</small></p>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <a href="nip.php" class="btn green waves-effect full-width">
                                        <i class="material-icons left">add</i>New Registration
                                    </a>
                                    <p><small>Register a new child</small></p>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <a href="records.php" class="btn orange waves-effect full-width">
                                        <i class="material-icons left">list</i>All Records
                                    </a>
                                    <p><small>View all records</small></p>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <a href="/nip/" class="btn purple waves-effect full-width">
                                        <i class="material-icons left">home</i>Dashboard
                                    </a>
                                    <p><small>Return to main dashboard</small></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="center-align" style="margin-top: 30px;">
                            <a href="filter_records.php" class="btn large blue waves-effect">
                                <i class="material-icons left">play_arrow</i>Try Filter & Print Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
