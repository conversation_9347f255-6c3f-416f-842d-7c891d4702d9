<?php
/**
 * Database Migration Runner for User Approval System
 * Run this script once to update your database schema
 */

include 'db.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Migration for Enhanced User Approval System</h2>";
echo "<p>Starting migration...</p>";

try {
    // Read the migration SQL file
    $migration_sql = file_get_contents('database_migration_user_approval.sql');
    
    if ($migration_sql === false) {
        throw new Exception("Could not read migration file");
    }
    
    // Split the SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $migration_sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $success_count = 0;
    $error_count = 0;
    
    echo "<h3>Executing Migration Statements:</h3>";
    echo "<ul>";
    
    foreach ($statements as $statement) {
        if (trim($statement)) {
            try {
                $result = $conn->query($statement);
                if ($result) {
                    $success_count++;
                    echo "<li style='color: green;'>✓ Statement executed successfully</li>";
                } else {
                    $error_count++;
                    echo "<li style='color: red;'>✗ Error: " . $conn->error . "</li>";
                }
            } catch (Exception $e) {
                $error_count++;
                echo "<li style='color: red;'>✗ Exception: " . $e->getMessage() . "</li>";
            }
        }
    }
    
    echo "</ul>";
    
    echo "<h3>Migration Summary:</h3>";
    echo "<p><strong>Successful statements:</strong> $success_count</p>";
    echo "<p><strong>Failed statements:</strong> $error_count</p>";
    
    if ($error_count == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✓ Migration Completed Successfully!</h4>";
        echo "<p>Your database has been updated with the following enhancements:</p>";
        echo "<ul>";
        echo "<li>Added approval/disapproval date tracking</li>";
        echo "<li>Added disapproval reason field</li>";
        echo "<li>Created audit log table for tracking actions</li>";
        echo "<li>Added performance indexes</li>";
        echo "<li>Created reporting view</li>";
        echo "</ul>";
        echo "<p><strong>You can now use the enhanced approve/disapprove functionality!</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠ Migration Completed with Errors</h4>";
        echo "<p>Some statements failed to execute. Please check the errors above and run the migration again if needed.</p>";
        echo "</div>";
    }
    
    // Test the new functionality
    echo "<h3>Testing New Functionality:</h3>";
    
    // Check if new columns exist
    $test_queries = [
        "SHOW COLUMNS FROM users LIKE 'approved_date'" => "users.approved_date column",
        "SHOW COLUMNS FROM users LIKE 'disapproved_date'" => "users.disapproved_date column",
        "SHOW COLUMNS FROM users LIKE 'disapproval_reason'" => "users.disapproval_reason column",
        "SHOW COLUMNS FROM health_facility LIKE 'approved_date'" => "health_facility.approved_date column",
        "SHOW COLUMNS FROM health_facility LIKE 'disapproved_date'" => "health_facility.disapproved_date column",
        "SHOW COLUMNS FROM health_facility LIKE 'disapproval_reason'" => "health_facility.disapproval_reason column",
        "SHOW TABLES LIKE 'user_action_logs'" => "user_action_logs table",
        "SHOW TABLES LIKE 'user_approval_status'" => "user_approval_status view"
    ];
    
    echo "<ul>";
    foreach ($test_queries as $query => $description) {
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            echo "<li style='color: green;'>✓ $description exists</li>";
        } else {
            echo "<li style='color: red;'>✗ $description missing</li>";
        }
    }
    echo "</ul>";
    
    // Show current user counts by status
    echo "<h3>Current User Status Summary:</h3>";
    
    $status_queries = [
        "SELECT COUNT(*) as count FROM users WHERE approved = 1" => "Approved Users",
        "SELECT COUNT(*) as count FROM users WHERE approved = 0" => "Pending Users", 
        "SELECT COUNT(*) as count FROM users WHERE approved = -1" => "Disapproved Users",
        "SELECT COUNT(*) as count FROM health_facility WHERE approved = 1" => "Approved Health Facilities",
        "SELECT COUNT(*) as count FROM health_facility WHERE approved = 0" => "Pending Health Facilities",
        "SELECT COUNT(*) as count FROM health_facility WHERE approved = -1" => "Disapproved Health Facilities"
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th style='padding: 8px; background: #f8f9fa;'>Category</th><th style='padding: 8px; background: #f8f9fa;'>Count</th></tr>";
    
    foreach ($status_queries as $query => $label) {
        $result = $conn->query($query);
        if ($result) {
            $row = $result->fetch_assoc();
            $count = $row['count'];
            echo "<tr><td style='padding: 8px;'>$label</td><td style='padding: 8px; text-align: center;'>$count</td></tr>";
        }
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Migration Failed</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

$conn->close();

echo "<hr>";
echo "<p><strong>Note:</strong> You can safely delete this migration script (run_migration.php) and the SQL file (database_migration_user_approval.sql) after successful migration.</p>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test the approve/disapprove functionality in user_settings.php</li>";
echo "<li>Check the audit logs in the user_action_logs table</li>";
echo "<li>Review the user_approval_status view for reporting</li>";
echo "</ol>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

ul, ol {
    padding-left: 20px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}
</style>
