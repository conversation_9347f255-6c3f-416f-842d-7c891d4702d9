<?php
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "national_immunization_program";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$query = $_GET['q'] ?? '';

$sql = "SELECT hospital_name FROM hospital WHERE hospital_name LIKE ?";
$stmt = $conn->prepare($sql);
$search = "%" . $query . "%";
$stmt->bind_param("s", $search);
$stmt->execute();
$result = $stmt->get_result();

$hospitals = [];
while ($row = $result->fetch_assoc()) {
    $hospitals[] = $row['hospital_name'];
}

header('Content-Type: application/json');
echo json_encode($hospitals);

$conn->close();
?>