<?php
session_start();

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Connection failed ' . mysqli_error($conn));
}
date_default_timezone_set('Asia/Manila');

// Set default session if not logged in (for testing)
if (!isset($_SESSION['fullname'])) {
    $_SESSION['fullname'] = 'System Admin';
}

$message = '';
$messageType = '';
$import_errors = [];

// Check if PhpSpreadsheet is available
$phpspreadsheet_available = false;
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    $phpspreadsheet_available = true;
}

// Handle file upload
if (isset($_POST['import_excel'])) {
    if (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] == 0) {
        $allowed_extensions = ['xlsx', 'xls', 'csv'];
        $file_extension = strtolower(pathinfo($_FILES['excel_file']['name'], PATHINFO_EXTENSION));
        
        if (in_array($file_extension, $allowed_extensions)) {
            $upload_dir = 'uploads/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_path = $upload_dir . time() . '_' . $_FILES['excel_file']['name'];
            
            if (move_uploaded_file($_FILES['excel_file']['tmp_name'], $file_path)) {
                if ($file_extension == 'csv') {
                    $result = importCSV($file_path, $conn);
                } elseif ($phpspreadsheet_available && in_array($file_extension, ['xlsx', 'xls'])) {
                    $result = importExcel($file_path, $conn);
                } else {
                    $result = ['count' => 0, 'errors' => ['PhpSpreadsheet library not installed. Please use CSV format or install PhpSpreadsheet via Composer.']];
                }
                
                $imported_count = $result['count'];
                $import_errors = $result['errors'];
                
                if ($imported_count > 0) {
                    $message = "Successfully imported $imported_count records.";
                    if (!empty($import_errors)) {
                        $message .= " " . count($import_errors) . " errors occurred.";
                    }
                    $messageType = 'success';
                } else {
                    $message = "No records were imported. Please check your file format and data.";
                    $messageType = 'error';
                }
                
                // Clean up uploaded file
                unlink($file_path);
            } else {
                $message = "Failed to upload file.";
                $messageType = 'error';
            }
        } else {
            $message = "Invalid file format. Please upload Excel (.xlsx, .xls) or CSV files only.";
            $messageType = 'error';
        }
    } else {
        $message = "Please select a file to upload.";
        $messageType = 'error';
    }
}

function importExcel($file_path, $conn) {
    $imported_count = 0;
    $errors = [];
    
    try {
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();
        
        // Skip header row
        array_shift($rows);
        
        foreach ($rows as $row_index => $row) {
            $row_number = $row_index + 2; // +2 because we skipped header and arrays are 0-indexed
            
            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }
            
            $result = processRow($row, $row_number, $conn);
            if ($result['success']) {
                $imported_count++;
            } else {
                $errors[] = $result['error'];
            }
        }
    } catch (Exception $e) {
        $errors[] = "Error reading Excel file: " . $e->getMessage();
    }
    
    return ['count' => $imported_count, 'errors' => $errors];
}

function importCSV($file_path, $conn) {
    $imported_count = 0;
    $errors = [];
    
    if (($handle = fopen($file_path, "r")) !== FALSE) {
        $header = fgetcsv($handle); // Read header row
        $row_number = 1;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $row_number++;
            
            // Skip empty rows
            if (empty(array_filter($data))) {
                continue;
            }
            
            $result = processRow($data, $row_number, $conn);
            if ($result['success']) {
                $imported_count++;
            } else {
                $errors[] = $result['error'];
            }
        }
        fclose($handle);
    }
    
    return ['count' => $imported_count, 'errors' => $errors];
}

function processRow($data, $row_number, $conn) {
    // Map CSV/Excel columns to database fields
    $record = [
        'DateOfBirth' => isset($data[0]) ? trim($data[0]) : '',
        'NameOfChild' => isset($data[1]) ? trim($data[1]) : '',
        'lastnameOfChild' => isset($data[2]) ? trim($data[2]) : '',
        'middlename_of_child' => isset($data[3]) ? trim($data[3]) : '',
        'Sex' => isset($data[4]) ? trim($data[4]) : '',
        'NameofMother' => isset($data[5]) ? trim($data[5]) : '',
        'BirthdateofMother' => isset($data[6]) ? trim($data[6]) : '',
        'AgeeofMother' => isset($data[7]) ? trim($data[7]) : '',
        'Barangay' => isset($data[8]) ? trim($data[8]) : '',
        'PurokStreetSitio' => isset($data[9]) ? trim($data[9]) : '',
        'HouseNo' => isset($data[10]) ? trim($data[10]) : '',
        'Address' => isset($data[11]) ? trim($data[11]) : '',
        'PlaceofDelivery' => isset($data[12]) ? trim($data[12]) : '',
        'NameofFacility' => isset($data[13]) ? trim($data[13]) : '',
        'Attendant' => isset($data[14]) ? trim($data[14]) : '',
        'TypeofDelivery' => isset($data[15]) ? trim($data[15]) : '',
        'BirthWeightInGrams' => isset($data[16]) ? trim($data[16]) : '',
        'BCG' => isset($data[17]) ? trim($data[17]) : '',
        'PhoneNumber' => isset($data[18]) ? trim($data[18]) : ''
    ];
    
    // Validate required fields
    $required_fields = ['DateOfBirth', 'NameOfChild', 'lastnameOfChild', 'Attendant', 'PlaceofDelivery', 'TypeofDelivery', 'BCG'];
    foreach ($required_fields as $field) {
        if (empty($record[$field])) {
            return ['success' => false, 'error' => "Row $row_number: Missing required field '$field'"];
        }
    }
    
    // Validate and convert date format
    $convertedDate = convertDate($record['DateOfBirth']);
    if (!$convertedDate) {
        return ['success' => false, 'error' => "Row $row_number: Invalid date format for DateOfBirth. Accepted formats: YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, MM-DD-YYYY, DD-MM-YYYY"];
    }
    $record['DateOfBirth'] = $convertedDate;
    
    // Check for duplicates
    $checkStmt = $conn->prepare("SELECT id FROM nip_table WHERE NameOfChild = ? AND lastnameOfChild = ? AND DateOfBirth = ?");
    $checkStmt->bind_param("sss", $record['NameOfChild'], $record['lastnameOfChild'], $record['DateOfBirth']);
    $checkStmt->execute();
    $checkStmt->store_result();
    
    if ($checkStmt->num_rows > 0) {
        return ['success' => false, 'error' => "Row $row_number: Duplicate record found for " . $record['NameOfChild'] . " " . $record['lastnameOfChild']];
    }
    
    // Generate required fields
    $today = date("Ymd");
    $rand = strtoupper(substr(uniqid(sha1(time())),0,10));
    $DateOfRegistration = date('Y-m-d');
    $FamilySerialNumber = 'CHO-'.$today . $rand;
    $ChildNumber = rand(100, 999);
    $AgeofChild = calculateAge($record['DateOfBirth']);
    $approvedBy = $_SESSION['fullname'];
    $dateOfExpiration = date('Y-m-d', strtotime('+365 day'));
    
    // Insert record with all available fields
    $sql = "INSERT INTO nip_table (
        DateOfRegistration, DateOfBirth, AgeofChild, FamilySerialNumber, ChildNumber,
        lastnameOfChild, NameOfChild, middlename_of_child, Sex, NameofMother,
        BirthdateofMother, Age, Barangay, PurokStreetSitio, HouseNo, Address, 
        PlaceofDelivery, NameofFacility, Attendant, TypeofDelivery, BirthWeightInGrams,
        BCG, PhoneNumber, approvedBy, dateOfExpiration
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssisssssssssssssssssssss", 
        $DateOfRegistration, $record['DateOfBirth'], $AgeofChild, $FamilySerialNumber, $ChildNumber,
        $record['lastnameOfChild'], $record['NameOfChild'], $record['middlename_of_child'], 
        $record['Sex'], $record['NameofMother'], $record['BirthdateofMother'], $record['AgeeofMother'],
        $record['Barangay'], $record['PurokStreetSitio'], $record['HouseNo'], $record['Address'],
        $record['PlaceofDelivery'], $record['NameofFacility'], $record['Attendant'], 
        $record['TypeofDelivery'], $record['BirthWeightInGrams'], $record['BCG'], 
        $record['PhoneNumber'], $approvedBy, $dateOfExpiration
    );
    
    if ($stmt->execute()) {
        return ['success' => true, 'error' => ''];
    } else {
        return ['success' => false, 'error' => "Row $row_number: Database error - " . $stmt->error];
    }
}

function convertDate($dateString) {
    // Remove any extra whitespace
    $dateString = trim($dateString);
    
    if (empty($dateString)) {
        return false;
    }
    
    // List of common date formats to try
    $formats = [
        'Y-m-d',        // 2023-01-15
        'Y/m/d',        // 2023/01/15
        'm/d/Y',        // 01/15/2023
        'd/m/Y',        // 15/01/2023
        'm-d-Y',        // 01-15-2023
        'd-m-Y',        // 15-01-2023
        'Y.m.d',        // 2023.01.15
        'm.d.Y',        // 01.15.2023
        'd.m.Y',        // 15.01.2023
        'Y m d',        // 2023 01 15
        'm d Y',        // 01 15 2023
        'd m Y'         // 15 01 2023
    ];
    
    foreach ($formats as $format) {
        $date = DateTime::createFromFormat($format, $dateString);
        if ($date && $date->format($format) === $dateString) {
            // Return in MySQL format
            return $date->format('Y-m-d');
        }
    }
    
    // Try to parse with strtotime as fallback
    $timestamp = strtotime($dateString);
    if ($timestamp !== false) {
        return date('Y-m-d', $timestamp);
    }
    
    return false;
}

function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

function calculateAge($birthdate) {
    $birth = new DateTime($birthdate);
    $today = new DateTime();
    $age = $birth->diff($today);
    return $age->y;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Excel - National Immunization Program</title>
    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        .container {
            margin-top: 20px;
        }
        .file-field .btn {
            background-color: #26a69a;
        }
        .file-field .btn:hover {
            background-color: #2bbbad;
        }
        .sample-table {
            font-size: 11px;
        }
        .sample-table th, .sample-table td {
            padding: 4px;
            border: 1px solid #ddd;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-available {
            background-color: #4caf50;
        }
        .status-unavailable {
            background-color: #f44336;
        }
    </style>
</head>
<body>
    <nav class="teal">
        <div class="nav-wrapper">
            <a href="#" class="brand-logo center">Import Excel Data - Advanced</a>
            <ul id="nav-mobile" class="left hide-on-med-and-down">
                <li><a href="nip.php"><i class="material-icons left">arrow_back</i>Back to Main</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <!-- System Status -->
        <div class="card">
            <div class="card-content">
                <span class="card-title">
                    <i class="material-icons left">settings</i>System Status
                </span>
                <p>
                    <span class="status-indicator <?php echo $phpspreadsheet_available ? 'status-available' : 'status-unavailable'; ?>"></span>
                    PhpSpreadsheet Library: <?php echo $phpspreadsheet_available ? 'Available' : 'Not Available'; ?>
                </p>
                <?php if (!$phpspreadsheet_available): ?>
                    <div class="card-panel orange lighten-4 orange-text text-darken-2">
                        <i class="material-icons left">warning</i>
                        <strong>Note:</strong> To import Excel files (.xlsx, .xls), install PhpSpreadsheet by running: 
                        <code>composer install</code> in your project directory.
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Display Messages -->
        <?php if (!empty($message)): ?>
            <div class="card-panel <?php echo $messageType == 'success' ? 'green lighten-4 green-text text-darken-2' : 
                                                ($messageType == 'error' ? 'red lighten-4 red-text text-darken-2' : 
                                                'orange lighten-4 orange-text text-darken-2'); ?>">
                <i class="material-icons left"><?php echo $messageType == 'success' ? 'check_circle' : 
                                                      ($messageType == 'error' ? 'error' : 'warning'); ?></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Display Import Errors -->
        <?php if (!empty($import_errors)): ?>
            <div class="card">
                <div class="card-content">
                    <span class="card-title red-text">
                        <i class="material-icons left">error</i>Import Errors (<?php echo count($import_errors); ?>)
                    </span>
                    <div class="error-list">
                        <ul class="collection">
                            <?php foreach ($import_errors as $error): ?>
                                <li class="collection-item red-text text-lighten-1">
                                    <i class="material-icons left tiny">error_outline</i>
                                    <?php echo htmlspecialchars($error); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">cloud_upload</i>
                            Import Excel/CSV File
                        </span>
                        
                        <!-- Instructions -->
                        <div class="instructions">
                            <h6><i class="material-icons left">info</i>Instructions:</h6>
                            <ol>
                                <li>Prepare your Excel/CSV file with the required columns (see sample format below)</li>
                                <li>Make sure all required fields are filled</li>
                                <li>Date formats accepted: YYYY-MM-DD, MM/DD/YYYY, DD/MM/YYYY, MM-DD-YYYY, DD-MM-YYYY</li>
                                <li>Upload the file using the form below</li>
                            </ol>
                            
                            <h6><i class="material-icons left">warning</i>Required Fields:</h6>
                            <p><strong>Date of Birth, Name of Child, Last Name of Child, Attendant, Place of Delivery, Type of Delivery, BCG</strong></p>
                        </div>

                        <!-- Upload Form -->
                        <form method="POST" enctype="multipart/form-data">
                            <div class="file-field input-field">
                                <div class="btn">
                                    <span><i class="material-icons left">attach_file</i>Choose File</span>
                                    <input type="file" name="excel_file" accept=".xlsx,.xls,.csv" required>
                                </div>
                                <div class="file-path-wrapper">
                                    <input class="file-path validate" type="text" placeholder="Select Excel or CSV file">
                                </div>
                            </div>
                            
                            <button type="submit" name="import_excel" class="btn waves-effect waves-light teal">
                                <i class="material-icons left">cloud_upload</i>Import Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Extended Sample CSV Format -->
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">table_chart</i>
                            Complete CSV Format Template
                        </span>
                        <p>Your CSV file should have the following column structure (19 columns):</p>
                        
                        <div style="overflow-x: auto;">
                            <table class="striped sample-table">
                                <thead>
                                    <tr>
                                        <th>DateOfBirth*</th>
                                        <th>NameOfChild*</th>
                                        <th>lastnameOfChild*</th>
                                        <th>MiddleNameOfChild</th>
                                        <th>Sex</th>
                                        <th>NameofMother</th>
                                        <th>BirthdateofMother</th>
                                        <th>AgeofMother</th>
                                        <th>Barangay</th>
                                        <th>PurokStreetSitio</th>
                                        <th>HouseNo</th>
                                        <th>Address</th>
                                        <th>PlaceofDelivery*</th>
                                        <th>NameofFacility</th>
                                        <th>Attendant*</th>
                                        <th>TypeofDelivery*</th>
                                        <th>BirthWeightInGrams</th>
                                        <th>BCG*</th>
                                        <th>PhoneNumber</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2023-01-15</td>
                                        <td>Juan</td>
                                        <td>Dela Cruz</td>
                                        <td>Santos</td>
                                        <td>Male</td>
                                        <td>Maria Dela Cruz</td>
                                        <td>1995-05-20</td>
                                        <td>28</td>
                                        <td>Barangay 1</td>
                                        <td>Purok 1</td>
                                        <td>123</td>
                                        <td>123 Main St</td>
                                        <td>Hospital</td>
                                        <td>City Hospital</td>
                                        <td>Doctor</td>
                                        <td>Normal</td>
                                        <td>3200</td>
                                        <td>Given</td>
                                        <td>09123456789</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <p><small>* Required fields</small></p>
                        
                        <div class="card-panel blue lighten-5 blue-text text-darken-2" style="margin-top: 20px;">
                            <i class="material-icons left">download</i>
                            <strong>Download Complete CSV Template:</strong> 
                            <a href="#" onclick="downloadCompleteCSV()" class="btn-small waves-effect waves-light blue">
                                Download Template
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Imports -->
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">history</i>
                            Recent Records (Last 10)
                        </span>
                        
                        <table class="striped responsive-table">
                            <thead>
                                <tr>
                                    <th>Registration Date</th>
                                    <th>Child Name</th>
                                    <th>Date of Birth</th>
                                    <th>Mother's Name</th>
                                    <th>Barangay</th>
                                    <th>Approved By</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $recent_sql = "SELECT * FROM nip_table ORDER BY DateOfRegistration DESC LIMIT 10";
                                $recent_result = mysqli_query($conn, $recent_sql);
                                
                                if (mysqli_num_rows($recent_result) > 0) {
                                    while ($row = mysqli_fetch_assoc($recent_result)) {
                                        echo "<tr>";
                                        echo "<td>" . date('M d, Y', strtotime($row['DateOfRegistration'])) . "</td>";
                                        echo "<td>" . $row['NameOfChild'] . " " . $row['lastnameOfChild'] . "</td>";
                                        echo "<td>" . date('M d, Y', strtotime($row['DateOfBirth'])) . "</td>";
                                        echo "<td>" . $row['NameofMother'] . "</td>";
                                        echo "<td>" . $row['Barangay'] . "</td>";
                                        echo "<td>" . $row['approvedBy'] . "</td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='6' class='center-align'>No records found</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Materialize JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    
    <script>
        // Initialize Materialize components
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });

        // Function to download complete CSV template
        function downloadCompleteCSV() {
            const csvContent = `DateOfBirth,NameOfChild,lastnameOfChild,MiddleNameOfChild,Sex,NameofMother,BirthdateofMother,AgeofMother,Barangay,PurokStreetSitio,HouseNo,Address,PlaceofDelivery,NameofFacility,Attendant,TypeofDelivery,BirthWeightInGrams,BCG,PhoneNumber
2023-01-15,Juan,Dela Cruz,Santos,Male,Maria Dela Cruz,1995-05-20,28,Barangay 1,Purok 1,123,123 Main St,Hospital,City Hospital,Doctor,Normal,3200,Given,09123456789
2023-02-10,Maria,Garcia,Lopez,Female,Ana Garcia,1990-08-15,33,Barangay 2,Purok 2,456,456 Oak Ave,Clinic,Health Center,Midwife,Cesarean,2800,Given,09987654321`;
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.setAttribute('hidden', '');
            a.setAttribute('href', url);
            a.setAttribute('download', 'nip_complete_template.csv');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('.card-panel');
            messages.forEach(function(message) {
                if (message.classList.contains('green') || message.classList.contains('red') || message.classList.contains('orange')) {
                    message.style.transition = 'opacity 0.5s';
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 500);
                }
            });
        }, 8000);
    </script>
</body>
</html>