<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Records.php - Earliest Date Implementation</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .success-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        
        .table-demo {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left green-text">check_circle</i>
                Records.php - Earliest Date Successfully Added!
            </h3>
            <p class="center-align">The earliest consultation date is now displayed in the records table</p>
        </div>

        <div class="demo-section">
            <h4>✅ What Was Implemented</h4>
            
            <div class="success-box">
                <h6>Successfully Added to records.php:</h6>
                <ul>
                    <li>✅ <strong>Function Include:</strong> Added earliest date function</li>
                    <li>✅ <strong>Table Header:</strong> Added "Date of Consultation" column</li>
                    <li>✅ <strong>Table Data:</strong> Prints earliest date in each row</li>
                    <li>✅ <strong>All Conditions:</strong> Uses all 28 consultation date fields</li>
                    <li>✅ <strong>Styled Output:</strong> Blue text with bold formatting</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔧 Code Changes Made</h4>
            
            <h5>1. Added Function Include (Line 4):</h5>
            <div class="code-block">
&lt;?php session_start(); ?&gt;

&lt;?php
// Include earliest consultation date function
include 'simple_earliest_with_conditions.php';

if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
?&gt;
            </div>
            
            <h5>2. Added Table Header (Line 293):</h5>
            <div class="code-block">
&lt;th&gt;DATE OF CONSULT (TT5)&lt;/th&gt;
&lt;th&gt;Phone Number&lt;/th&gt;
&lt;th&gt;Date of Consultation&lt;/th&gt;

&lt;/tr&gt;
            </div>
            
            <h5>3. Added Table Data (Line 533):</h5>
            <div class="code-block">
&lt;td style="text-transform: uppercase !important;"&gt;&lt;?php echo $row['PhoneNumber']; ?&gt;&lt;/td&gt;
&lt;td style="font-weight: bold; color: #1976d2;"&gt;&lt;?php printEarliestDateWithConditions($row); ?&gt;&lt;/td&gt;

&lt;/tr&gt;
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Table Structure</h4>
            
            <div class="table-demo">
                <h6>Records Table Now Shows:</h6>
                <table class="striped">
                    <thead>
                        <tr>
                            <th>...</th>
                            <th>Phone Number</th>
                            <th style="color: #1976d2; font-weight: bold;">Date of Consultation</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>...</td>
                            <td>09123456789</td>
                            <td style="color: #1976d2; font-weight: bold;">&lt;p&gt;2023-01-20&lt;/p&gt;</td>
                        </tr>
                        <tr>
                            <td>...</td>
                            <td>09987654321</td>
                            <td style="color: #1976d2; font-weight: bold;">&lt;p&gt;2023-02-15&lt;/p&gt;</td>
                        </tr>
                        <tr>
                            <td>...</td>
                            <td>09555123456</td>
                            <td style="color: #1976d2; font-weight: bold;">&lt;p&gt;No consultation date recorded&lt;/p&gt;</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="demo-section">
            <h4>🎯 How It Works</h4>
            
            <h5>Function Called for Each Row:</h5>
            <div class="code-block">
&lt;?php printEarliestDateWithConditions($row); ?&gt;
            </div>
            
            <h5>Checks All Consultation Date Fields:</h5>
            <div class="code-block">
$consultation_dates = [
    $row['DateofConsultationBCG'] ?? '',
    $row['DateofConsultationPENTAHIB1'] ?? '',
    $row['DateofConsultationPENTAHIB2'] ?? '',
    $row['DateofConsultationPENTAHIB3'] ?? '',
    $row['DateofConsultationOPV1v'] ?? '',
    $row['DateofConsultationOPV2'] ?? '',
    $row['DateofConsultationOPV3'] ?? '',
    $row['DateofConsultationIPV1'] ?? '',
    $row['DateofConsultationIPV2'] ?? '',
    $row['DateofConsultationPCV1'] ?? '',
    $row['DateofConsultationPCV2'] ?? '',
    $row['DateofConsultationPCV3'] ?? '',
    $row['DateofConsultationHEPAatBirth'] ?? '',
    $row['DateofConsultationHEPAB1'] ?? '',
    $row['DateofConsultationHEPAB2'] ?? '',
    $row['DateofConsultationHEPAB3'] ?? '',
    $row['DateofConsultationHEPAB4'] ?? '',
    $row['DateofConsultationAMV1'] ?? '',
    $row['DateofConsultationMMR'] ?? '',
    $row['DateofConsultationFIC'] ?? '',
    $row['DateofConsultationCIC'] ?? '',
    $row['DateofConsultationAMV2'] ?? '',
    $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
    $row['DATEOFCONSULTTT1'] ?? '',
    $row['DATEOFCONSULTTT2'] ?? '',
    $row['DATEOFCONSULTTT3'] ?? '',
    $row['DATEOFCONSULTTT4'] ?? '',
    $row['DATEOFCONSULTT5'] ?? ''
];
            </div>
            
            <h5>Applies Conditions and Prints Result:</h5>
            <div class="code-block">
foreach ($consultation_dates as $date) {
    if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
        $valid_dates[] = $date;
    }
}

if (!empty($valid_dates)) {
    $earliest = min($valid_dates);
    echo '&lt;p&gt;' . htmlspecialchars($earliest) . '&lt;/p&gt;';
} else {
    echo '&lt;p&gt;No consultation date recorded&lt;/p&gt;';
}
            </div>
        </div>

        <div class="demo-section">
            <h4>🎨 Styling Applied</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">CSS Styling</span>
                            <div class="code-block" style="font-size: 11px;">
&lt;td style="font-weight: bold; color: #1976d2;"&gt;
    &lt;?php printEarliestDateWithConditions($row); ?&gt;
&lt;/td&gt;
                            </div>
                            <p><strong>Result:</strong> Blue, bold text for earliest dates</p>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Output Format</span>
                            <div class="code-block" style="font-size: 11px;">
&lt;p&gt;2023-01-20&lt;/p&gt;
&lt;p&gt;No consultation date recorded&lt;/p&gt;
                            </div>
                            <p><strong>Format:</strong> Clean paragraph tags with date or message</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📋 Benefits</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">For Users</span>
                            <ul>
                                <li>✅ Quick view of first consultation</li>
                                <li>✅ Easy identification of children without consultations</li>
                                <li>✅ Better record overview</li>
                                <li>✅ Improved data visibility</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">For System</span>
                            <ul>
                                <li>✅ Automatic calculation</li>
                                <li>✅ Real-time data processing</li>
                                <li>✅ Consistent formatting</li>
                                <li>✅ Error handling for missing data</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>The earliest consultation date is now working in records.php!</p>
            
            <div class="row">
                <div class="col s12 m6">
                    <a href="records.php" class="btn large blue waves-effect">
                        <i class="material-icons left">table_chart</i>View Records Table
                    </a>
                    <p><small>See the new column in action</small></p>
                </div>
                
                <div class="col s12 m6">
                    <a href="simple_earliest_with_conditions.php" class="btn large green waves-effect">
                        <i class="material-icons left">code</i>View Function File
                    </a>
                    <p><small>See the function code</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>✅ Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>Records.php - Earliest Date Implementation Complete!</h6>
                    <ul>
                        <li><strong>Location:</strong> New column after "Phone Number"</li>
                        <li><strong>Header:</strong> "Date of Consultation"</li>
                        <li><strong>Function:</strong> <code>printEarliestDateWithConditions($row)</code></li>
                        <li><strong>Output:</strong> <code>&lt;p&gt;2023-01-20&lt;/p&gt;</code> or <code>&lt;p&gt;No consultation date recorded&lt;/p&gt;</code></li>
                        <li><strong>Styling:</strong> Blue, bold text for visibility</li>
                        <li><strong>Data Source:</strong> All 28 consultation date fields with conditions</li>
                        <li><strong>Status:</strong> ✅ Working and ready to use!</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
