<?php
session_start();
include 'db.php';

// Admin password for security (change this to your preferred admin password)
$ADMIN_PASSWORD = 'admin2024';

$error_message = '';
$success_message = '';
$users = [];
$show_users = false;

// Handle admin authentication
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['admin_auth'])) {
        $admin_pass = $_POST['admin_password'];
        if ($admin_pass === $ADMIN_PASSWORD) {
            $_SESSION['admin_authenticated'] = true;
            $success_message = 'Admin authenticated successfully!';
        } else {
            $error_message = 'Invalid admin password!';
        }
    }
    
    // Handle password reset
    if (isset($_POST['reset_password']) && isset($_SESSION['admin_authenticated'])) {
        $user_id = $_POST['user_id'];
        $new_password = $_POST['new_password'];
        
        if (!empty($user_id) && !empty($new_password)) {
            $stmt = $conn->prepare("UPDATE health_facility SET password = ? WHERE id = ?");
            $stmt->bind_param("si", $new_password, $user_id);
            
            if ($stmt->execute()) {
                $success_message = "Password updated successfully for user ID: $user_id";
                
                // Log the password reset
                $log_stmt = $conn->prepare("INSERT INTO user_action_logs (user_id, table_name, action_type, performed_by, reason) VALUES (?, 'health_facility', 'PASSWORD_RESET', 'ADMIN', 'Password reset by admin')");
                $log_stmt->bind_param("i", $user_id);
                $log_stmt->execute();
                $log_stmt->close();
            } else {
                $error_message = "Failed to update password: " . $stmt->error;
            }
            $stmt->close();
        } else {
            $error_message = "Please provide both user ID and new password.";
        }
    }
    
    // Handle show users request
    if (isset($_POST['show_users']) && isset($_SESSION['admin_authenticated'])) {
        $show_users = true;
    }
}

// Fetch users if authenticated and requested
if ($show_users && isset($_SESSION['admin_authenticated'])) {
    $result = $conn->query("SELECT id, username, password, email, health_center, fullname, mobile_number, approved FROM health_facility ORDER BY health_center, username");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $users[] = $row;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Recovery Tool - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Roboto', sans-serif;
        }
        .recovery-container {
            margin-top: 50px;
            max-width: 1200px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .card-title {
            color: #1976d2;
            font-weight: 600;
        }
        .password-display {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            border-left: 4px solid #2196f3;
        }
        .user-card {
            margin-bottom: 15px;
            border-left: 4px solid #4caf50;
        }
        .status-approved { border-left-color: #4caf50; }
        .status-pending { border-left-color: #ff9800; }
        .status-disapproved { border-left-color: #f44336; }
        .admin-section {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container recovery-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align">
                            <i class="material-icons left">lock_open</i>
                            Password Recovery Tool
                        </span>
                        
                        <div class="warning-box">
                            <strong><i class="material-icons tiny">warning</i> Security Notice:</strong>
                            This tool is for administrative use only. Passwords in this system are stored in plain text.
                            For security reasons, consider implementing password hashing in the future.
                        </div>

                        <?php if ($error_message): ?>
                            <div class="card red lighten-4">
                                <div class="card-content red-text">
                                    <i class="material-icons left">error</i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($success_message): ?>
                            <div class="card green lighten-4">
                                <div class="card-content green-text">
                                    <i class="material-icons left">check_circle</i>
                                    <?php echo htmlspecialchars($success_message); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!isset($_SESSION['admin_authenticated'])): ?>
                            <!-- Admin Authentication -->
                            <div class="card admin-section">
                                <div class="card-content">
                                    <span class="card-title white-text">
                                        <i class="material-icons left">admin_panel_settings</i>
                                        Admin Authentication Required
                                    </span>
                                    <form method="POST">
                                        <div class="input-field">
                                            <i class="material-icons prefix white-text">lock</i>
                                            <input id="admin_password" name="admin_password" type="password" class="white-text" required>
                                            <label for="admin_password" class="white-text">Admin Password</label>
                                        </div>
                                        <button type="submit" name="admin_auth" class="btn white blue-text waves-effect">
                                            <i class="material-icons left">login</i>Authenticate
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Admin Panel -->
                            <div class="row">
                                <div class="col s12 m6">
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title">
                                                <i class="material-icons left">visibility</i>
                                                View All Users
                                            </span>
                                            <p>Display all users with their current passwords</p>
                                            <form method="POST">
                                                <button type="submit" name="show_users" class="btn blue waves-effect">
                                                    <i class="material-icons left">list</i>Show Users
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6">
                                    <div class="card">
                                        <div class="card-content">
                                            <span class="card-title">
                                                <i class="material-icons left">edit</i>
                                                Reset Password
                                            </span>
                                            <form method="POST">
                                                <div class="input-field">
                                                    <i class="material-icons prefix">person</i>
                                                    <input id="user_id" name="user_id" type="number" required>
                                                    <label for="user_id">User ID</label>
                                                </div>
                                                <div class="input-field">
                                                    <i class="material-icons prefix">lock</i>
                                                    <input id="new_password" name="new_password" type="text" required>
                                                    <label for="new_password">New Password</label>
                                                </div>
                                                <button type="submit" name="reset_password" class="btn orange waves-effect">
                                                    <i class="material-icons left">refresh</i>Reset Password
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($users)): ?>
                                <div class="card">
                                    <div class="card-content">
                                        <span class="card-title">
                                            <i class="material-icons left">people</i>
                                            All Users (<?php echo count($users); ?> total)
                                        </span>
                                        
                                        <div class="row">
                                            <?php foreach ($users as $user): ?>
                                                <?php 
                                                $status_class = '';
                                                $status_text = '';
                                                if ($user['approved'] == 1) {
                                                    $status_class = 'status-approved';
                                                    $status_text = 'Approved';
                                                } elseif ($user['approved'] == 0) {
                                                    $status_class = 'status-disapproved';
                                                    $status_text = 'Disapproved';
                                                } else {
                                                    $status_class = 'status-pending';
                                                    $status_text = 'Pending';
                                                }
                                                ?>
                                                <div class="col s12 m6 l4">
                                                    <div class="card user-card <?php echo $status_class; ?>">
                                                        <div class="card-content">
                                                            <div class="card-title" style="font-size: 18px;">
                                                                <strong>ID: <?php echo $user['id']; ?></strong>
                                                                <span class="right chip <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                            </div>
                                                            <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                                                            <p><strong>Full Name:</strong> <?php echo htmlspecialchars($user['fullname']); ?></p>
                                                            <p><strong>Health Center:</strong> <?php echo htmlspecialchars($user['health_center']); ?></p>
                                                            <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                                                            <p><strong>Mobile:</strong> <?php echo htmlspecialchars($user['mobile_number']); ?></p>
                                                            
                                                            <div class="password-display">
                                                                <strong>Password:</strong> 
                                                                <span class="password-text" style="display: none;"><?php echo htmlspecialchars($user['password']); ?></span>
                                                                <span class="password-hidden">••••••••</span>
                                                                <a href="#" class="toggle-password blue-text" style="margin-left: 10px;">
                                                                    <i class="material-icons tiny">visibility</i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="center-align" style="margin-top: 30px;">
                                <a href="login.php" class="btn green waves-effect">
                                    <i class="material-icons left">arrow_back</i>Back to Login
                                </a>
                                <a href="?logout=1" class="btn red waves-effect">
                                    <i class="material-icons left">logout</i>Logout Admin
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Toggle password visibility
            document.querySelectorAll('.toggle-password').forEach(function(toggle) {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const card = this.closest('.card-content');
                    const passwordText = card.querySelector('.password-text');
                    const passwordHidden = card.querySelector('.password-hidden');
                    const icon = this.querySelector('i');
                    
                    if (passwordText.style.display === 'none') {
                        passwordText.style.display = 'inline';
                        passwordHidden.style.display = 'none';
                        icon.textContent = 'visibility_off';
                    } else {
                        passwordText.style.display = 'none';
                        passwordHidden.style.display = 'inline';
                        icon.textContent = 'visibility';
                    }
                });
            });
        });
    </script>

    <?php
    // Handle logout
    if (isset($_GET['logout'])) {
        unset($_SESSION['admin_authenticated']);
        header('Location: password_recovery.php');
        exit;
    }
    ?>
</body>
</html>
