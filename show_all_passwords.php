<?php
// Simple script to display all user passwords
// WARNING: This is for emergency password recovery only!

include 'db.php';

echo "<h2>🔓 Emergency Password Recovery</h2>";
echo "<p><strong>⚠️ WARNING:</strong> This page shows all user passwords in plain text!</p>";

try {
    $sql = "SELECT id, username, password, fullname, health_center, email, mobile_number, approved, date_created FROM health_facility ORDER BY health_center, username";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        echo "<h3>📊 Total Users: " . $result->num_rows . "</h3>";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%; font-family: Arial, sans-serif;'>";
        echo "<thead style='background-color: #f0f0f0;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Username</th>";
        echo "<th>🔑 Password</th>";
        echo "<th>Full Name</th>";
        echo "<th>Health Center</th>";
        echo "<th>Email</th>";
        echo "<th>Mobile</th>";
        echo "<th>Status</th>";
        echo "<th>Date Created</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        while ($row = $result->fetch_assoc()) {
            $status_color = '';
            $status_text = '';
            
            if ($row['approved'] == 1) {
                $status_color = 'background-color: #d4edda; color: #155724;';
                $status_text = '✅ Approved';
            } elseif ($row['approved'] == 0) {
                $status_color = 'background-color: #f8d7da; color: #721c24;';
                $status_text = '❌ Disapproved';
            } else {
                $status_color = 'background-color: #fff3cd; color: #856404;';
                $status_text = '⏳ Pending';
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['username']) . "</strong></td>";
            echo "<td style='background-color: #ffe6e6; font-family: monospace; font-weight: bold; color: #d63384;'>" . htmlspecialchars($row['password']) . "</td>";
            echo "<td>" . htmlspecialchars($row['fullname']) . "</td>";
            echo "<td>" . htmlspecialchars($row['health_center']) . "</td>";
            echo "<td>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td>" . htmlspecialchars($row['mobile_number']) . "</td>";
            echo "<td style='$status_color'>" . $status_text . "</td>";
            echo "<td>" . htmlspecialchars($row['date_created']) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        
        // Summary by health center
        echo "<br><h3>📈 Summary by Health Center:</h3>";
        $summary_sql = "SELECT health_center, COUNT(*) as user_count, 
                        SUM(CASE WHEN approved = 1 THEN 1 ELSE 0 END) as approved_count,
                        SUM(CASE WHEN approved = 0 THEN 1 ELSE 0 END) as disapproved_count,
                        SUM(CASE WHEN approved NOT IN (0,1) THEN 1 ELSE 0 END) as pending_count
                        FROM health_facility 
                        GROUP BY health_center 
                        ORDER BY health_center";
        
        $summary_result = $conn->query($summary_sql);
        
        if ($summary_result && $summary_result->num_rows > 0) {
            echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; margin-top: 10px;'>";
            echo "<thead style='background-color: #e9ecef;'>";
            echo "<tr>";
            echo "<th>Health Center</th>";
            echo "<th>Total Users</th>";
            echo "<th>✅ Approved</th>";
            echo "<th>❌ Disapproved</th>";
            echo "<th>⏳ Pending</th>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            
            while ($summary_row = $summary_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td><strong>" . htmlspecialchars($summary_row['health_center']) . "</strong></td>";
                echo "<td>" . $summary_row['user_count'] . "</td>";
                echo "<td style='color: #155724;'>" . $summary_row['approved_count'] . "</td>";
                echo "<td style='color: #721c24;'>" . $summary_row['disapproved_count'] . "</td>";
                echo "<td style='color: #856404;'>" . $summary_row['pending_count'] . "</td>";
                echo "</tr>";
            }
            
            echo "</tbody>";
            echo "</table>";
        }
        
    } else {
        echo "<p>❌ No users found in the database.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<br><hr>";
echo "<h3>🛠️ Quick Actions:</h3>";
echo "<p>";
echo "<a href='password_recovery.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Full Recovery Tool</a>";
echo "<a href='quick_password_lookup.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Search Tool</a>";
echo "<a href='login.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🏠 Back to Login</a>";
echo "</p>";

echo "<br><div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin-top: 20px;'>";
echo "<h4>🔒 Security Recommendations:</h4>";
echo "<ul>";
echo "<li><strong>Change Default Passwords:</strong> All users should change their passwords after recovery</li>";
echo "<li><strong>Implement Password Hashing:</strong> Consider upgrading to hashed passwords (bcrypt/Argon2)</li>";
echo "<li><strong>Delete This File:</strong> Remove this file after password recovery is complete</li>";
echo "<li><strong>Enable 2FA:</strong> Consider implementing two-factor authentication</li>";
echo "<li><strong>Regular Backups:</strong> Ensure regular database backups are in place</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    font-weight: bold;
    text-align: left;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
}

a:hover {
    opacity: 0.8;
}
</style>
