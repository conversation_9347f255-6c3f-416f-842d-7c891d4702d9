<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>Immunization Certificate</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.4.0/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script type="text/javascript" src="https://unpkg.com/qr-code-styling/lib/qr-code-styling.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <?php session_start(); ?>
    <style>
        @media print {
            #printButton {
                display: none;
            }
            body {
                background: white;
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .cert-container {
                box-shadow: none;
                border: none;
                margin: 0;
                padding: 1.5em;
                max-width: 100%;
                width: 100%;
                page-break-after: avoid;
            }
            .header-logo {
                gap: 1.5rem;
                margin-bottom: 1rem;
                padding: 1rem 0;
            }
            .header-logo img {
                max-height: 60px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .header-text h5 {
                font-size: 12px;
                font-family:'Times New Roman', Times, serif !important;
                margin: 0.2rem 0;
            }
            .header-text p {
                
                font-size: 12px;
                margin: 0.1rem 0;
            }
            .cert-title {
                font-size: 12px;
                padding: 0.3rem;
            }
            .cert-content {
                font-size: 11px;
                line-height: 1.6;
                margin: 1rem 0;
                padding: 0 0.5rem;
            }
            table {
                margin: 1rem 0;
                page-break-inside: avoid;
            }
            th, td {
                padding: 2px;
                font-size: 11px;
            }
            .cert-note {
                font-size: 11px;
                padding: 0.8rem;
                margin: 1rem 0;
            }
            .cert-footer {
                margin-top: 1rem;
            }
            .signature-text {
                font-size: 11px;
            }
            .qr-container {
                padding: 0.9rem;
            }
            #canvas {
                width: 100px !important;
                height: 100px !important;
            }
            @page {
                size: A4;
                margin: 0;
            }
        }

        body {
          
            color: #333;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .cert-container {
            margin: 0 auto;
            padding: 2.5em;
            max-width: 900px;
            background: #ffffff;
            border-radius: 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            border: 1px solid #e0e0e0;
        }

        .header-logo {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0rem;
            padding: 1.5rem 0;
           
        }

        .header-logo img {
            max-height: 75px;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .header-text {
            text-align: center;
            margin-bottom: 1.5rem;
           
        }

        .header-text h5 {
         
           
           
            font-size: 13px;
            margin: 0.3rem 0;
          
            
        }

        .header-text p {
            font-size: 14px;
            color: #555;
            margin: 0.2rem 0;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family:'Times New Roman', Times, serif !important;
            
        }

        .cert-title {
            background:rgba(0, 137, 123, 0.89);
            color: white;
            padding: 0.4rem;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
            
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
           
        }

        .cert-content {
            font-size: 12px;
            line-height: 1.8;
            color: #333;
            margin: 1.5rem 0;
            padding: 0 1rem;
        }

        .cert-content u {
            text-decoration: none;
            border-bottom: 1px solid #000;
            padding-bottom: 1px;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
            background: white;
            border: 1px solid #000;
        }

        th, td {
            border: 1px solid #000;
            padding: 8px;
            font-size: 14px;
            text-align: left;
        }

        th {
            background-color: #eceff1; /* Light gray */
            font-weight: bold;
        }
      

        tr:hover td {
            background-color: #f5f5f5;
        }

        .cert-note {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            padding: 1.2rem;
            margin: 1.5rem 0;
            font-size: 14px;
            color: #333;
        }

        .cert-footer {
            margin-top: 2.2rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding-top: 1rem;
            
        }

        .signature {
            width: 40%;
            text-align: center;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            width: 80%;
            margin: 0 auto;
            height: 1px;
        }

        .signature-text {
            font-size: 12px;
            color: #666;
            margin-top: 0.5rem;
        }

        .qr-container {
            background: white;
            padding: 0.9rem;
         
        }

        #printButton {
             
            background: #00897b;
            color: white;
            text-transform: uppercase;
            font-weight: 500;
            border-radius: 0;
            padding: 0 2rem;
            transition: all 0.3s ease;
        }

        #printButton:hover {
            
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        @page {
            size: A4;
            margin: 0;
        }

        @media print {
            @page {
                size: A4;
                margin: 0;
            }
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }

        .contact-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin: 0.8rem 0;
            padding: 0.8rem;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
        }

      

        .contact-info span {
            font-weight: 500;
            color: #333;
        }

        .official-seal {
            position: absolute;
            
            right: 20px;
            width: 100px;
            height: 100px;
            border: 2px solid #00897b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          
            font-size: 12px;
            
            text-align: center;
            padding: 10px;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
      
            font-size: 115px;
            color: rgba(26, 34, 126, 0.05);
            pointer-events: none;
            white-space: nowrap;
        }
    </style>
</head>
<body id="capture">
    <?php 
        include 'db.php'; 
        $id = isset($_GET['id']) ? $_GET['id'] : '';
        session_start();

        if ($id) {
            if($deleted == 1) {
                $sql = "SELECT * FROM nip_table WHERE id = ? AND deleted = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ss", $id,$deleted);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                $stmt->close();
                echo 'Record Deleted <a href="records.php">Back</a> <div class="cert-container" style="display:none;">';
            }
            else {

                $sql = "SELECT * FROM nip_table WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("s", $id);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                $stmt->close();
            }
        } else {
            
            echo "Invalid parameters. Please provide valid 'id' value.";
            exit;
        }
    ?>

    <?php
    $dob = strtotime($row['DateOfBirth']);
    $currentDate = time();
    $ageInYears = ($currentDate - $dob) / (365 * 24 * 60 * 60);
    ?>

    <div class="cert-container">
       
        <div class="watermark">
            OFFICIAL DOCUMENT
        </div>

        <div class="header-logo">
            <img src="alaga.png" alt="ALAGA Logo">
            <img src="img/log.png" alt="Logo">
            <img src="img/bago.jpg" alt="BAGO Logo">
        </div>

        <div class="header-text">
            <h5>Republic of the Philippines</h5>
            <h5>OFFICE OF THE CITY HEALTH OFFICER</h5>
            <h5>City of Parañaque</h5>
            <h5 style="color: #00897b;"><?php echo htmlspecialchars($row['Barangay']); ?></h5>
            <p>
              
                <?php echo $_SESSION['AddressofFacility']; ?><br>
                <?php echo $_SESSION['mobile_number']; ?>
            </p>
          
        </div>

        <div class="cert-title">Certification</div>

        <div class="cert-content">
            This is to certify that 
            <u><b>
                <?php 
                    echo htmlspecialchars($row['lastnameOfChild']) . ', ' . 
                         htmlspecialchars($row['NameOfChild']) . ' ' . 
                         htmlspecialchars($row['middlename_of_child']) . ', ';

                    if (round($ageInYears, 1) < 0.9) {  
                        echo round($ageInYears * 12, 1) . ' Months old, ';
                    } else {
                        echo round($ageInYears, 1) . ' Years old, ';
                    }

                    echo htmlspecialchars($row['Sex']);
                ?>
            </b></u>, born on 
            <u><b><?php echo htmlspecialchars(date('F d,Y',strtotime($row['DateOfBirth']))); ?></b></u>, residing at 
            <u><b><?php echo htmlspecialchars($row['Address']); ?></b></u>, had been given the following routine immunization at 
            <u><b><?php echo htmlspecialchars($row['Barangay']); ?></b></u> as listed below:
        </div>

        <?php
        // Check if BCG to TT5 vaccines have valid values
        $bcgToTt5Vaccines = [
            'BCG' => 'DateBCGwasgiven',
                    'PENTA-HIB1' => 'DatePenta1wasgiven',
                    'PENTA-HIB2' => 'DatePentahib2wasgiven',
                    'PENTA-HIB3' => 'DatePentahib3wasgiven',
                    'OPV1' => 'DateOPV1wasgiven',
                    'OPV2' => 'DateOPV2wasgiven',
                    'OPV3' => 'dateOPV3wasgiven',
                    'HEPA at Birth' => 'DateHEPAatBirthwasgiven',
                    'HEPA B1' => 'dateHEPA1wasgiven',
                    'MCV 1 (9months to below 12 months)' => 'DateAMV1WASGIVEN',
                    'FIC' => 'FIC',
                    'CIC' => 'CIC',
                    'MCV 2(12 MOS. TO 15 MOS.)' => 'dateMMRWASGIVEN',
                    'AMV 2(16 MOS. TO 5 YRS. OLD)' => 'dateAMV2WASGIVEN',
                    'TD1' => 'DATEOFCONSULTTT1',
                    'TT2' => 'DATEOFCONSULTTT2',
                    'TT3' => 'DATEOFCONSULTTT3',
                    'TT4' => 'DATEOFCONSULTTT4',
                    'TT5' => 'DATEOFCONSULTT5'
        ];
        
        $hasValidBcgToTt5 = false;
        foreach ($bcgToTt5Vaccines as $name => $field) {
            $value = trim(strtoupper($row[$field]));
            if (!empty($value) && $value !== 'NO') {
                $hasValidBcgToTt5 = true;
                break;
            }
        }
        
        if ($hasValidBcgToTt5): ?>
        <table>
            <thead>
                <tr>
                    <th>Vaccine/Antigen</th>
                    <th>Date Given</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $vaccines = [
                    'BCG' => 'DateBCGwasgiven',
                    'PENTA-HIB1' => 'DatePenta1wasgiven',
                    'PENTA-HIB2' => 'DatePentahib2wasgiven',
                    'PENTA-HIB3' => 'DatePentahib3wasgiven',
                    'OPV1' => 'DateOPV1wasgiven',
                    'OPV2' => 'DateOPV2wasgiven',
                    'OPV3' => 'dateOPV3wasgiven',
                    'HEPA at Birth' => 'DateHEPAatBirthwasgiven',
                    'HEPA B1' => 'dateHEPA1wasgiven',
                    'MCV 1 (9months to below 12 months)' => 'DateAMV1WASGIVEN',
                    'FIC' => 'FIC',
                    'CIC' => 'CIC',
                    'MCV 2(12 MOS. TO 15 MOS.)' => 'dateMMRWASGIVEN',
                    'AMV 2(16 MOS. TO 5 YRS. OLD)' => 'dateAMV2WASGIVEN',
                    'TD1' => 'DATEOFCONSULTTT1',
                    'TT2' => 'DATEOFCONSULTTT2',
                    'TT3' => 'DATEOFCONSULTTT3',
                    'TT4' => 'DATEOFCONSULTTT4',
                    'TT5' => 'DATEOFCONSULTT5'
                ];

                foreach ($vaccines as $name => $field) {
                    $value = trim(strtoupper($row[$field]));
                    if (!empty($value) && $value !== 'NO') {
                        echo "<tr>";
                        echo "<th>$name</th>";
                        echo "<th><u>" . htmlspecialchars(date('m/d/Y',strtotime($row[$field]))) . "</u></th>";
                        echo "</tr>";
                    }
                }
                ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="cert-note" style="background: #ffebee; border: 1px solid #f44336; color: #c62828;">
            <center><strong>No valid vaccination records found for BCG to TT5 vaccines.</strong></center>
        </div>
        <?php endif; ?>

        <div class="cert-note cyan lighten-5">
            This certification is being issued on <u><b><?php
                date_default_timezone_set('Asia/Manila');
                echo date('F d,Y ');  

                $approvedBy = $row['approvedBy'];
                $nameParts = explode(' ', $approvedBy);
                $lastName = implode(' ', array_slice($nameParts,-1,2));
            ?></b></u> from 
            <u><b><?php echo htmlspecialchars($row['Barangay']); ?></b></u>, upon the request of 
            <u><b><?php echo htmlspecialchars($row['NameofMother']); ?></b></u> for legal purposes only <b>except medico legal</b>./<b><?php echo (htmlspecialchars($lastName)); ?></b>
        </div>

        <div class="cert-footer">
            <div class="signature">
                <div class="signature-line"></div>
                <div class="signature-text">Authorized Signature/Medical Officer</div>
            </div>
            
            <div class="qr-container">
                <div id="canvas"></div>
            </div>
        </div>
<br>
        <center>
            <div class="row container">
            <div class="col s12 m12">

                <button id="printButton" type="submit" class=" btn waves-effect waves-light">
                    <i class="material-icons left">print</i>
                    Print Certificate
                </button>
            </div>
            </div>
        </center>
    </div>

    <script>
        const qrCode = new QRCodeStyling({
            width: 140,
            height: 140,
            data: "<?php echo $row['FamilySerialNumber']; ?>",
            dotsOptions: {
                color: "#00897b",
                type: "circle"
            },
            backgroundOptions: {
                color: "#FFFFFF",
            }
        });
        qrCode.append(document.getElementById("canvas"));

        document.getElementById('printButton').addEventListener('click', function () {
            window.print();
        });
    </script>
</body>
</html>
