<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Connection failed: ' . mysqli_connect_error());
}

// Get table structure
$sql = "DESCRIBE nip_table";
$result = mysqli_query($conn, $sql);

if ($result) {
    echo "nip_table structure:\n";
    echo "Column Count: " . mysqli_num_rows($result) . "\n\n";
    
    $columns = [];
    $i = 1;
    while ($row = mysqli_fetch_assoc($result)) {
        echo "$i. " . $row['Field'] . " (" . $row['Type'] . ")\n";
        $columns[] = $row['Field'];
        $i++;
    }
    
    echo "\n\nColumns for INSERT statement:\n";
    echo implode(', ', $columns);
    
    echo "\n\nBind param string (all strings):\n";
    echo '"' . str_repeat('s', count($columns)) . '"';
    
    echo "\n\nPlaceholders:\n";
    echo str_repeat('?, ', count($columns) - 1) . '?';
    
} else {
    echo "Error: " . mysqli_error($conn);
}

mysqli_close($conn);
?>