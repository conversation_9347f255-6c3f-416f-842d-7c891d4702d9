<?php
/**
 * Simple function to print earliest date and apply conditions
 * Based on all consultation date fields
 */

function printEarliestDateWithConditions($row) {
    // All consultation date fields with conditions
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Apply conditions to each date
    foreach ($consultation_dates as $date) {
        // Condition: Check if date is not empty, not null, and not 0000-00-00
        if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
            $valid_dates[] = $date;
        }
    }
    
    // Print earliest date in <p> tag with conditions
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        echo '<p>' . htmlspecialchars($earliest) . '</p>';
        return $earliest;
    } else {
        echo '<p>No consultation date recorded</p>';
        return null;
    }
}

// Function to get earliest date with conditions (returns value instead of printing)
function getEarliestDateWithConditions($row) {
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Apply conditions
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
            $valid_dates[] = $date;
        }
    }
    
    return !empty($valid_dates) ? min($valid_dates) : null;
}

// Function with additional conditions and analysis
function printEarliestDateWithAnalysis($row) {
    $earliest = getEarliestDateWithConditions($row);
    
    if ($earliest) {
        // Print earliest date in <p> tag
        echo '<p style="font-weight: bold; color: #1976d2;">' . htmlspecialchars($earliest) . '</p>';
        
        // Additional conditions based on birth date
        if (!empty($row['DateOfBirth'])) {
            $days_after_birth = (strtotime($earliest) - strtotime($row['DateOfBirth'])) / (60*60*24);
            
            // Condition: Early consultation
            if ($days_after_birth <= 30) {
                echo '<p style="color: #4caf50;">✅ Early consultation (within 30 days)</p>';
            } elseif ($days_after_birth <= 60) {
                echo '<p style="color: #ff9800;">⚠️ Moderate delay (within 60 days)</p>';
            } else {
                echo '<p style="color: #f44336;">❌ Late consultation (after 60 days)</p>';
            }
        }
    } else {
        echo '<p style="color: #f44336;">No consultation date recorded</p>';
    }
}

// Function to check specific conditions
function checkConsultationConditions($row) {
    $earliest = getEarliestDateWithConditions($row);
    
    // Count total consultations
    $consultation_dates = [
        $row['DateofConsultationBCG'] ?? '',
        $row['DateofConsultationPENTAHIB1'] ?? '',
        $row['DateofConsultationPENTAHIB2'] ?? '',
        $row['DateofConsultationPENTAHIB3'] ?? '',
        $row['DateofConsultationOPV1v'] ?? '',
        $row['DateofConsultationOPV2'] ?? '',
        $row['DateofConsultationOPV3'] ?? '',
        $row['DateofConsultationIPV1'] ?? '',
        $row['DateofConsultationIPV2'] ?? '',
        $row['DateofConsultationPCV1'] ?? '',
        $row['DateofConsultationPCV2'] ?? '',
        $row['DateofConsultationPCV3'] ?? '',
        $row['DateofConsultationHEPAatBirth'] ?? '',
        $row['DateofConsultationHEPAB1'] ?? '',
        $row['DateofConsultationHEPAB2'] ?? '',
        $row['DateofConsultationHEPAB3'] ?? '',
        $row['DateofConsultationHEPAB4'] ?? '',
        $row['DateofConsultationAMV1'] ?? '',
        $row['DateofConsultationMMR'] ?? '',
        $row['DateofConsultationFIC'] ?? '',
        $row['DateofConsultationCIC'] ?? '',
        $row['DateofConsultationAMV2'] ?? '',
        $row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        $row['DATEOFCONSULTTT1'] ?? '',
        $row['DATEOFCONSULTTT2'] ?? '',
        $row['DATEOFCONSULTTT3'] ?? '',
        $row['DATEOFCONSULTTT4'] ?? '',
        $row['DATEOFCONSULTT5'] ?? ''
    ];
    
    $count = 0;
    foreach ($consultation_dates as $date) {
        if (!empty($date) && $date !== '0000-00-00' && $date !== null) {
            $count++;
        }
    }
    
    return [
        'has_consultations' => $earliest !== null,
        'earliest_date' => $earliest,
        'consultation_count' => $count,
        'is_early' => $earliest && !empty($row['DateOfBirth']) ? 
            ((strtotime($earliest) - strtotime($row['DateOfBirth'])) / (60*60*24)) <= 30 : false,
        'is_well_documented' => $count >= 5
    ];
}

/*
USAGE EXAMPLES:

// Method 1: Simple print in <p> tag
printEarliestDateWithConditions($row);

// Method 2: Get value for processing
$earliest = getEarliestDateWithConditions($row);
if ($earliest) {
    echo "First consultation: " . $earliest;
}

// Method 3: Print with analysis
printEarliestDateWithAnalysis($row);

// Method 4: Check conditions
$conditions = checkConsultationConditions($row);
if ($conditions['has_consultations']) {
    echo "Has consultations: " . $conditions['consultation_count'];
    if ($conditions['is_early']) {
        echo " - Early consultation ✅";
    }
}

// Method 5: In database loops
while ($row = mysqli_fetch_assoc($result)) {
    echo "Child: " . $row['NameOfChild'];
    echo " | Earliest consultation: ";
    printEarliestDateWithConditions($row);
}
*/
?>
