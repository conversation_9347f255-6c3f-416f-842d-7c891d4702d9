<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Database connection failed: ' . mysqli_connect_error());
}

// Initialize variables
$row = [];
$search_performed = false;
$child_found = false;
$search_results = [];

// Handle search
if (isset($_POST['search']) || isset($_GET['id'])) {
    $search_performed = true;

    if (isset($_GET['id'])) {
        // Direct access with ID
        $child_id = intval($_GET['id']);
        $sql = "SELECT * FROM nip_table WHERE id = ? AND (deleted IS NULL OR deleted = 0)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $child_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($result) > 0) {
            $row = mysqli_fetch_assoc($result);
            $child_found = true;
        }
        mysqli_stmt_close($stmt);
    } else {
        // Search by name
        $first_name = trim($_POST['first_name']);
        $last_name = trim($_POST['last_name']);
        $middle_name = trim($_POST['middle_name']);

        if (!empty($first_name) || !empty($last_name) || !empty($middle_name)) {
            $sql = "SELECT id, NameOfChild, middlename_of_child, LastNameOfChild, DateOfBirth, NameofMother, Barangay
                    FROM nip_table WHERE (deleted IS NULL OR deleted = 0)";
            $params = [];
            $types = "";

            if (!empty($first_name)) {
                $sql .= " AND NameOfChild LIKE ?";
                $params[] = "%" . $first_name . "%";
                $types .= "s";
            }

            if (!empty($last_name)) {
                $sql .= " AND LastNameOfChild LIKE ?";
                $params[] = "%" . $last_name . "%";
                $types .= "s";
            }

            if (!empty($middle_name)) {
                $sql .= " AND middlename_of_child LIKE ?";
                $params[] = "%" . $middle_name . "%";
                $types .= "s";
            }

            $sql .= " ORDER BY LastNameOfChild, NameOfChild";

            $stmt = mysqli_prepare($conn, $sql);
            if (!empty($params)) {
                mysqli_stmt_bind_param($stmt, $types, ...$params);
            }
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            while ($search_row = mysqli_fetch_assoc($result)) {
                $search_results[] = $search_row;
            }
            mysqli_stmt_close($stmt);
        }
    }
}

// Handle antigen update
if (isset($_POST['update_antigens'])) {
    $child_id = intval($_POST['child_id']);

    // Build update query for all antigen fields
    $update_sql = "UPDATE nip_table SET
                   BCG = ?, DateBCGwasgiven = ?, DateofConsultationBCG = ?,
                   PENTAHIB1 = ?, DatePenta1wasgiven = ?, DateofConsultationPENTAHIB1 = ?,
                   PENTAHIB2 = ?, DatePentahib2wasgiven = ?, DateofConsultationPENTAHIB2 = ?,
                   PENTAHIB3 = ?, DatePentahib3wasgiven = ?, DateofConsultationPENTAHIB3 = ?,
                   OPV1 = ?, DateOPV1wasgiven = ?, DateofConsultationOPV1v = ?,
                   OPV2 = ?, DateOPV2wasgiven = ?, DateofConsultationOPV2 = ?,
                   OPV3 = ?, dateOPV3wasgiven = ?, DateofConsultationOPV3 = ?,
                   IPV1 = ?, dateIPV1wasgiven = ?, DateofConsultationIPV1 = ?,
                   IPV2 = ?, dateIPV2wasgiven = ?, DateofConsultationIPV2 = ?,
                   PCV1 = ?, datePCV1wasgiven = ?, DateofConsultationPCV1 = ?,
                   PCV2 = ?, datePCV2wasgiven = ?, DateofConsultationPCV2 = ?,
                   PCV3 = ?, datePCV3wasgiven = ?, DateofConsultationPCV3 = ?,
                   HEPAatBirth = ?, TimingHEPAatBirth = ?, DateHEPAatBirthwasgiven = ?, DateofConsultationHEPAatBirth = ?,
                   HEPAB1 = ?, dateHEPA1wasgiven = ?, DateofConsultationHEPAB1 = ?,
                   AMV19monthstobelow12months = ?, DateAMV1WASGIVEN = ?, DateofConsultationAMV1 = ?,
                   MMR12MOSTO15MOS = ?, dateMMRWASGIVEN = ?, DateofConsultationMMR = ?,
                   FIC = ?, dateFICWASGIVEN = ?, DateofConsultationFIC = ?,
                   IMMUNIZATIONSTATUS = ?, DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED = ?
                   WHERE id = ?";

    $stmt = mysqli_prepare($conn, $update_sql);

    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "ssssssssssssssssssssssssssssssssssssssssssssssssi",
            $_POST['BCG'], $_POST['DateBCGwasgiven'], $_POST['DateofConsultationBCG'],
            $_POST['PENTAHIB1'], $_POST['DatePenta1wasgiven'], $_POST['DateofConsultationPENTAHIB1'],
            $_POST['PENTAHIB2'], $_POST['DatePentahib2wasgiven'], $_POST['DateofConsultationPENTAHIB2'],
            $_POST['PENTAHIB3'], $_POST['DatePentahib3wasgiven'], $_POST['DateofConsultationPENTAHIB3'],
            $_POST['OPV1'], $_POST['DateOPV1wasgiven'], $_POST['DateofConsultationOPV1v'],
            $_POST['OPV2'], $_POST['DateOPV2wasgiven'], $_POST['DateofConsultationOPV2'],
            $_POST['OPV3'], $_POST['dateOPV3wasgiven'], $_POST['DateofConsultationOPV3'],
            $_POST['IPV1'], $_POST['dateIPV1wasgiven'], $_POST['DateofConsultationIPV1'],
            $_POST['IPV2'], $_POST['dateIPV2wasgiven'], $_POST['DateofConsultationIPV2'],
            $_POST['PCV1'], $_POST['datePCV1wasgiven'], $_POST['DateofConsultationPCV1'],
            $_POST['PCV2'], $_POST['datePCV2wasgiven'], $_POST['DateofConsultationPCV2'],
            $_POST['PCV3'], $_POST['datePCV3wasgiven'], $_POST['DateofConsultationPCV3'],
            $_POST['HEPAatBirth'], $_POST['TimingHEPAatBirth'], $_POST['DateHEPAatBirthwasgiven'], $_POST['DateofConsultationHEPAatBirth'],
            $_POST['HEPAB1'], $_POST['dateHEPA1wasgiven'], $_POST['DateofConsultationHEPAB1'],
            $_POST['AMV19monthstobelow12months'], $_POST['DateAMV1WASGIVEN'], $_POST['DateofConsultationAMV1'],
            $_POST['MMR12MOSTO15MOS'], $_POST['dateMMRWASGIVEN'], $_POST['DateofConsultationMMR'],
            $_POST['FIC'], $_POST['dateFICWASGIVEN'], $_POST['DateofConsultationFIC'],
            $_POST['IMMUNIZATIONSTATUS'], $_POST['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'],
            $child_id
        );

        if (mysqli_stmt_execute($stmt)) {
            echo '<script>alert("Antigens updated successfully!");</script>';
            // Refresh the data
            $sql = "SELECT * FROM nip_table WHERE id = ?";
            $refresh_stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($refresh_stmt, "i", $child_id);
            mysqli_stmt_execute($refresh_stmt);
            $refresh_result = mysqli_stmt_get_result($refresh_stmt);
            $row = mysqli_fetch_assoc($refresh_result);
            $child_found = true;
            mysqli_stmt_close($refresh_stmt);
        } else {
            echo '<script>alert("Error updating antigens: ' . mysqli_error($conn) . '");</script>';
        }
        mysqli_stmt_close($stmt);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Antigens - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
        }
        .search-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .child-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            cursor: pointer;
            transition: all 0.3s;
        }
        .child-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .antigen-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Search Section -->
        <div class="search-container">
            <h4><i class="material-icons left">search</i>Search Child for Antigen Update</h4>
            <form method="POST" action="">
                <div class="row">
                    <div class="input-field col s12 m4">
                        <input type="text" id="first_name" name="first_name" value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                        <label for="first_name">First Name</label>
                    </div>
                    <div class="input-field col s12 m4">
                        <input type="text" id="last_name" name="last_name" value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                        <label for="last_name">Last Name</label>
                    </div>
                    <div class="input-field col s12 m4">
                        <input type="text" id="middle_name" name="middle_name" value="<?php echo isset($_POST['middle_name']) ? htmlspecialchars($_POST['middle_name']) : ''; ?>">
                        <label for="middle_name">Middle Name</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col s12">
                        <button type="submit" name="search" class="btn blue waves-effect">
                            <i class="material-icons left">search</i>Search Children
                        </button>
                        <a href="update_antigen.php" class="btn grey waves-effect">
                            <i class="material-icons left">clear</i>Clear Search
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search Results -->
        <?php if ($search_performed && !empty($search_results)): ?>
        <div class="search-container">
            <h5><i class="material-icons left">list</i>Search Results (<?php echo count($search_results); ?> found)</h5>
            <?php foreach ($search_results as $child): ?>
                <div class="child-card" onclick="selectChild(<?php echo $child['id']; ?>)">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s12 m8">
                            <h6 style="margin: 0; color: #2196f3;">
                                <?php echo htmlspecialchars($child['NameOfChild'] . ' ' . $child['middlename_of_child'] . ' ' . $child['LastNameOfChild']); ?>
                            </h6>
                            <p style="margin: 5px 0;">
                                <strong>Birth Date:</strong> <?php echo htmlspecialchars($child['DateOfBirth']); ?> |
                                <strong>Mother:</strong> <?php echo htmlspecialchars($child['NameofMother']); ?>
                            </p>
                            <p style="margin: 0; color: #666;">
                                <strong>Barangay:</strong> <?php echo htmlspecialchars($child['Barangay']); ?>
                            </p>
                        </div>
                        <div class="col s12 m4 right-align">
                            <span class="btn-small blue">Select Child</span>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php elseif ($search_performed && empty($search_results) && !$child_found): ?>
        <div class="search-container">
            <div class="card orange lighten-4 orange-text text-darken-2">
                <div class="card-content">
                    <p><i class="material-icons left">info</i>No children found matching your search criteria.</p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Antigen Update Form -->
        <?php if ($child_found): ?>
        <div class="antigen-form">
            <h4><i class="material-icons left">medical_services</i>Update Antigens</h4>
            <div class="card blue lighten-4">
                <div class="card-content">
                    <h6>Selected Child:</h6>
                    <p><strong><?php echo htmlspecialchars($row['NameOfChild'] . ' ' . $row['middlename_of_child'] . ' ' . $row['lastnameOfChild']); ?></strong></p>
                    <p>Birth Date: <?php echo htmlspecialchars($row['DateOfBirth']); ?> | Mother: <?php echo htmlspecialchars($row['NameofMother']); ?></p>
                </div>
            </div>

            <form method="POST" action="">
                <input type="hidden" name="child_id" value="<?php echo $row['id']; ?>">

<div id="test1" class="col s12">

          <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">Antigens</h6>
          <div class="row " >
            
            <div class="input-field col s12 m12">
              
          <div class="input-field  col s12 m12 z-depth-1 ">
          <label for="" class="" style="font-weight: 500 !important;">BCG</label><br>
          <input type="hidden" name="BCG" value="NO"> 

          <p >
      <label>
        <input name="BCG" id="BCGyes" value="YES" type="radio" <?php echo ($row['BCG'] == 'YES') ? 'checked' : ''; ?> />
        <span style="font-weight: 500;">YES</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="BCG" id="BCGno" value="NO" type="radio" <?php echo ($row['BCG'] == 'NO') ? 'checked' : ''; ?> />
        <span style="font-weight: 500;">NO</span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
    </p>
        </div>
        </div>

        <div class="input-field col s12 m6">
          <input placeholder="" name="DateBCGwasgiven" id="DateBCGwasgiven" type="date" value="<?php echo htmlspecialchars($row['DateBCGwasgiven']); ?>"   class="  ">
          <label for="DateBCGwasgiven">Date BCG was given</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" name="DateofConsultationBCG" id="DateofConsultationBCG" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationBCG']); ?>"   class="  ">
          <label for="DateofConsultationBCG">Date of Consultation(BCG)</label>
        </div>

        </div>
        <hr>


         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">PENTA-HIB Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" name="PENTAHIB1" id="PENTAHIB1" type="text" value="<?php echo htmlspecialchars($row['PENTAHIB1']); ?>"   class="  ">
          <label for="PENTAHIB1">PENTA-HIB1</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DatePenta1wasgiven" id="DatePenta1wasgiven" type="date" value="<?php echo htmlspecialchars($row['DatePenta1wasgiven']); ?>"   class="  ">
          <label for="DatePenta1wasgiven">Date PENTA-HIB1 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input   placeholder="" name="DateofConsultationPENTAHIB1" id="DateofConsultationPENTAHIB1" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPENTAHIB1']); ?>"   class="   ">
          <label for="DateofConsultationPENTAHIB1">Date of Consultation (PENTA-HIB1)</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" name="PENTAHIB2" id="PENTAHIB2" type="text" value="<?php echo htmlspecialchars($row['PENTAHIB2']); ?>"   class="  ">
          <label for="PENTAHIB2">PENTA-HIB2</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DatePentahib2wasgiven" id="DatePentahib2wasgiven" type="date" value="<?php echo htmlspecialchars($row['DatePentahib2wasgiven']); ?>"   class="  ">
          <label for="DatePentahib2wasgiven">Date Penta-HIB2 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationPENTAHIB2" id="DateofConsultationPENTAHIB2" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPENTAHIB2']); ?>"   class="  ">
          <label for="DateofConsultationPENTAHIB2">Date of Consultation (PENTA-HIB2)</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" name="PENTAHIB3" id="PENTAHIB3" type="text" value="<?php echo htmlspecialchars($row['PENTAHIB3']); ?>"   class="  ">
          <label for="PENTAHIB3">PENTA-HIB3</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DatePentahib3wasgiven" id="DatePentahib3wasgiven" type="date" value="<?php echo htmlspecialchars($row['DatePentahib3wasgiven']); ?>"   class="  ">
          <label for="DatePentahib3wasgiven">Date PENTA-HIB3 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationPENTAHIB3" id="DateofConsultationPENTAHIB3" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPENTAHIB3']); ?>"   class="  ">
          <label for="DateofConsultationPENTAHIB3">Date of Consultation (PENTA-HIB3)</label>
        </div>












         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">OPV Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" name="OPV1" id="OPV1" type="text" value="<?php echo htmlspecialchars($row['OPV1']); ?>"   class="  ">
          <label for="OPV1">OPV1</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateOPV1wasgiven" id="DateOPV1wasgiven" type="date" value="<?php echo htmlspecialchars($row['DateOPV1wasgiven']); ?>"   class="  ">
          <label for="DateOPV1wasgiven">Date OPV1 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationOPV1v" id="DateofConsultationOPV1v" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationOPV1v']); ?>"   class="  ">
          <label for="DateofConsultationOPV1v">Date of Consultation (OPV1)</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" name="OPV2" id="OPV2" type="text" value="<?php echo htmlspecialchars($row['OPV2']); ?>"   class="  ">
          <label for="OPV2">OPV2</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateOPV2wasgiven" id="DateOPV2wasgiven" type="date" value="<?php echo htmlspecialchars($row['DateOPV2wasgiven']); ?>"   class="  ">
          <label for="DateOPV2wasgiven">Date OPV2 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationOPV2" id="DateofConsultationOPV2" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationOPV2']); ?>"   class="  ">
          <label for="DateofConsultationOPV2">Date of Consultation (OPV2)</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" name="OPV3" id="OPV3" type="text" value="<?php echo htmlspecialchars($row['OPV3']); ?>"   class="  ">
          <label for="OPV3">OPV3</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="dateOPV3wasgiven" id="dateOPV3wasgiven" type="date" value="<?php echo htmlspecialchars($row['dateOPV3wasgiven']); ?>"   class="  ">
          <label for="dateOPV3wasgiven">Date OPV3 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationOPV3" id="DateofConsultationOPV3" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationOPV3']); ?>"   class="  ">
          <label for="DateofConsultationOPV3">Date of Consultation (OPV3)</label>
        </div>






         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">IPV Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" name="IPV1" id="IPV1" type="text" value="<?php echo htmlspecialchars($row['IPV1']); ?>"   class="  ">
          <label for="IPV1">IPV1</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="dateIPV1wasgiven" id="dateIPV1wasgiven" type="date" value="<?php echo htmlspecialchars($row['dateIPV1wasgiven']); ?>"   class="  ">
          <label for="dateIPV1wasgiven">Date IPV1 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationIPV1" id="DateofConsultationIPV1" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationIPV1']); ?>"   class="  ">
          <label for="DateofConsultationIPV1">Date of Consultation (IPV1)</label>
        </div>
  <div class="input-field col s12 m2">
          <input placeholder="" name="IPV2" id="IPV2" type="text" value="<?php echo htmlspecialchars($row['IPV2']); ?>"   class="  ">
          <label for="IPV2">IPV2</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="dateIPV2wasgiven" id="dateIPV2wasgiven" type="date" value="<?php echo htmlspecialchars($row['dateIPV2wasgiven']); ?>"   class="  ">
          <label for="dateIPV2wasgiven">Date IPV2 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationIPV2" id="DateofConsultationIPV2" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationIPV2']); ?>"   class="  ">
          <label for="DateofConsultationIPV2">Date of Consultation (IPV2)</label>
        </div>
        <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">PCV Antigen</h6>

      

        
        <div class="input-field col s12 m2">
          <input placeholder="" name="PCV1" id="PCV1" type="text" value="<?php echo htmlspecialchars($row['PCV1']); ?>"   class="  ">
          <label for="PCV1">PCV1</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="datePCV1wasgiven" id="datePCV1wasgiven" type="date" value="<?php echo htmlspecialchars($row['datePCV1wasgiven']); ?>"   class="  ">
          <label for="datePCV1wasgiven">Date PCV1 was given</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationPCV1" id="DateofConsultationPCV1" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPCV1']); ?>"   class="  ">
          <label for="DateofConsultationPCV1">Dateof Consultation PCV1</label>
        </div>

        <div class="input-field col s12 m2">
          <input placeholder="" name="PCV2" id="PCV2" type="text" value="<?php echo htmlspecialchars($row['PCV2']); ?>"   class="  ">
          <label for="PCV2">PCV2</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="datePCV2wasgiven" id="datePCV2wasgiven" type="date" value="<?php echo htmlspecialchars($row['datePCV2wasgiven']); ?>"   class="  ">
          <label for="datePCV2wasgiven">date PCV2 wasgiven</label>
        </div>
       <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationPCV2" id="DateofConsultationPCV2" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPCV2']); ?>"   class="  ">
          <label for="DateofConsultationPCV2">Date of Consultation PCV2</label>
        </div>


        <div class="input-field col s12 m2">
          <input placeholder="" name="PCV3" id="PCV3" type="text" value="<?php echo htmlspecialchars($row['PCV3']); ?>"   class="  ">
          <label for="PCV3">PCV3</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="datePCV3wasgiven" id="datePCV3wasgiven" type="date" value="<?php echo htmlspecialchars($row['datePCV3wasgiven']); ?>"   class="  ">
          <label for="datePCV3wasgiven">date PCV3 wasgiven</label>
        </div>
       <div class="input-field col s12 m5">
          <input placeholder="" name="DateofConsultationPCV3" id="DateofConsultationPCV3" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationPCV3']); ?>"   class="  ">
          <label for="DateofConsultationPCV3">Date of Consultation PCV3</label>
        </div>

        
   
<!-- ----------------------------------- -->
         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">HEPATITIS Antigen</h6>
        <div class="input-field col s12 m2">
          <input placeholder="" name="HEPAatBirth" id="HEPAatBirth" type="text" value="<?php echo htmlspecialchars($row['HEPAatBirth']); ?>"   class="  ">
          <label for="HEPAatBirth">HEPA at Birth</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" name="TimingHEPAatBirth" id="TimingHEPAatBirth" type="text" value="<?php echo htmlspecialchars($row['TimingHEPAatBirth']); ?>"   class="  ">
          <label for="TimingHEPAatBirth">Timing HEPA at Birth</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" name="DateHEPAatBirthwasgiven" id="DateHEPAatBirthwasgiven" type="date" value="<?php echo htmlspecialchars($row['DateHEPAatBirthwasgiven']); ?>"   class="  ">
          <label for="DateHEPAatBirthwasgiven">Date HEPA at Birth was given</label>
        </div>


        <div class="input-field col s12 m4">
          <input placeholder="" name="DateofConsultationHEPAatBirth" id="DateofConsultationHEPAatBirth" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationHEPAatBirth']); ?>"   class="  ">
          <label for="DateofConsultationHEPAatBirth">Date of Consultation(HEPA at Birth)</label>
        </div>
        <div class="input-field col s12 m5">
          <input placeholder="" name="HEPAB1" id="HEPAB1" type="text" value="<?php echo htmlspecialchars($row['HEPAB1']); ?>"   class="  ">
          <label for="HEPAB1">HEPA B1</label>
        </div>
        <div class="input-field col s12 m3">
          <input placeholder="" name="dateHEPA1wasgiven" id="dateHEPA1wasgiven" type="date" value="<?php echo htmlspecialchars($row['dateHEPA1wasgiven']); ?>"   class="  ">
          <label for="dateHEPA1wasgiven">Date HEPA1 was given</label>
        </div>


        <div class="input-field col s12 m4">
          <input placeholder="" name="DateofConsultationHEPAB1" id="DateofConsultationHEPAB1" type="date" value="<?php echo htmlspecialchars($row['DateofConsultationHEPAB1']); ?>"   class="  ">
          <label for="DateofConsultationHEPAB1">Date of Consultation (HEPA B1)</label>
        </div>
        
       
          <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">MCV Antigen</h6>
          
          <div class="input-field col s12 m4">
          <input placeholder="" id="AMV19monthstobelow12months" type="text" value="<?php echo htmlspecialchars($row['AMV19monthstobelow12months']); ?>"   class="  ">
          <label for="AMV19monthstobelow12months">MCV 1 (9months to below 12 months)</label>
        </div>

        <div class="input-field col s12 m4">
          <input   id="DateAMV1WASGIVEN" name="DateAMV1WASGIVEN" value="<?php echo htmlspecialchars($row['DateAMV1WASGIVEN']); ?>" type="date"  >
          <label for="DateAMV1WASGIVEN">DATE MCV 1 WAS GIVEN</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="DateofConsultationAMV1" name="DateofConsultationAMV1" value="<?php echo htmlspecialchars($row['DateofConsultationAMV1']); ?>" type="date"  >
          <label for="DateofConsultationAMV1">DATE OF CONSULTATON (MCV 1)</label>
        </div> 
        <div class="input-field col s12 m4">
          <input placeholder=""   id="MMR12MOSTO15MOS" name="MMR12MOSTO15MOS" value="<?php echo htmlspecialchars($row['MMR12MOSTO15MOS']); ?>" type="text"  >
          <label for="MMR12MOSTO15MOS">MCV 2(at 12 Months)</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="dateMMRWASGIVEN" name="dateMMRWASGIVEN" value="<?php echo htmlspecialchars($row['dateMMRWASGIVEN']); ?>" type="date"  >
          <label for="dateMMRWASGIVEN">DATE MCV2 WAS GIVEN</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="DateofConsultationMMR" name="DateofConsultationMMR" value="<?php echo htmlspecialchars($row['DateofConsultationMMR']); ?>" type="date"  >
          <label for="DateofConsultationMMR">DATE OF CONSULTATION (MCV2)</label>
        </div> 
        
        <div class="input-field col s12 m4">
          <input  placeholder="" id="FIC" name="FIC" value="<?php echo htmlspecialchars($row['FIC']); ?>" type="text"  >
          <label for="FIC">FIC</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="dateFICWASGIVEN" name="dateFICWASGIVEN" value="<?php echo htmlspecialchars($row['dateFICWASGIVEN']); ?>" type="date"  >
          <label for="dateFICWASGIVEN">DATE FIC WAS GIVEN</label>
        </div> 
        <div class="input-field col s12 m4">
          <input   id="DateofConsultationFIC" name="DateofConsultationFIC" value="<?php echo htmlspecialchars($row['DateofConsultationFIC']); ?>" type="date"  >
          <label for="DateofConsultationFIC">DATE OF CONSULTATION (FIC)</label>
        </div> 
       
        <div class="input-field col s12 m4">
          <input placeholder=""  id="IMMUNIZATIONSTATUS" name="IMMUNIZATIONSTATUS" value="<?php echo htmlspecialchars($row['IMMUNIZATIONSTATUS']); ?>" type="text"  >
          <label for="IMMUNIZATIONSTATUS">IMMUNIZATION STATUS</label>
        </div> 
        <div class="input-field col s12 m8">
          <input   id="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" name="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED" value="<?php echo htmlspecialchars($row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED']); ?>" type="date"  >
          <label  for="DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED">DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED</label>
        </div> 

      </div>
      <div >

      </div>



 
        <div id="test3" class="col s12"> 
          
          <div class="row " >
            
            <div class="input-field col s12 m12">
              
              <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">Exclusive Breastfeeding</h6>
          <div class="input-field  col s12 m12 z-depth-1 ">
            <label for="" class="" style="font-weight: 500 !important;">CHILD WAS EXCLUSIVELY BREASTFED (PUT A CHECK)</label><br>
            <p>
              <label>
                <input id="CHILD1" name="FIRSTMONTH" value="FIRST MONTH" type="checkbox" class="filled-in" 
                <?php echo ($row['FIRSTMONTH'] == 'FIRST MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">FIRST MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD2" name="SECONDMONTH" value="SECOND MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['SECONDMONTH'] == 'SECOND MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">SECOND MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD3" name="THIRDMONTH" value="THIRD MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['THIRDMONTH'] == 'THIRD MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">THIRD MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD4" name="FOURTHMONTH" value="FOURTH MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['FOURTHDMONTH'] == 'FOURTH MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">FOURTH MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD5" name="FIFTHMONTH" value="FIFTH MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['FIFTMONTH'] == 'FIFTH MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">FIFTH MONTH</span>
              </label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            
              <label>
                <input id="CHILD6" name="SIXTHMONTH" value="SIXTH MONTH" type="checkbox" class="filled-in" 
                       <?php echo ($row['SIXTHMONTH'] == 'SIXTH MONTH') ? 'checked' : ''; ?> />
                <span style="font-size:13.6px;font-weight:500;">SIXTH MONTH</span>
              </label>
            </p>
            
        </div>
        </div>
        
        <div class="input-field col s12 m5">
          <input placeholder="" id="date6MONTHS" type="date" value="<?php echo htmlspecialchars($row['date6MONTHS']); ?>"   class="  ">
          <label for="date6MONTHS">DATE 6 MONTHS</label>
        </div>
        <div class="input-field col s12 m7">
          <input placeholder="" id="WASTHECHILDEXCLUSIVELY" type="text" value="<?php echo htmlspecialchars($row['WASTHECHILDEXCLUSIVELY']); ?>"   class="  ">
          <label for="WASTHECHILDEXCLUSIVELY">WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?</label>
        </div>
        <div class="input-field col s12 m12">
          <input placeholder="" id="DATEOFCONSULTATIONEXCLUSIVEBF" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTATIONEXCLUSIVEBF']); ?>"   class="  ">
          <label for="DATEOFCONSULTATIONEXCLUSIVEBF">DATE OF CONSULTATION (EXCLUSIVE)</label>
        </div>

        </div>
        <hr>
        
         <h6 class="black-text" style="padding-left:0px;padding-bottom:14px;padding-top:12px;font-size:19.2px;font-weight:500;">NON-PREGNANT GIVEN TETANUS TOXOID IMMUNIZATION</h6>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT1" type="text" value="<?php echo htmlspecialchars($row['TT1']); ?>"   class="  ">
          <label for="TT1">TD1</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT1" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT1']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT1">DATE OF CONSULT (TT1)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT2" type="text" value="<?php echo htmlspecialchars($row['TT2']); ?>"   class="  ">
          <label for="TT2">TT2</label>
        </div>


        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT2" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT2']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT2">DATE OF CONSULT (TT2)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT3" type="text" value="<?php echo htmlspecialchars($row['TT3']); ?>"   class="  ">
          <label for="TT3">TT3</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT3" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT3']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT3">DATE OF CONSULT (TT3)</label>
        </div>

        
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT4" type="text" value="<?php echo htmlspecialchars($row['TT4']); ?>"   class="  ">
          <label for="TT4">TT4</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTTT4" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTTT4']); ?>"   class="  ">
          <label for="DATEOFCONSULTTT4">DATE OF CONSULT (TT4)</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="TT5" type="text" value="<?php echo htmlspecialchars($row['TT5']); ?>"   class="  ">
          <label for="TT5">TT5</label>
        </div>
        <div class="input-field col s12 m6">
          <input placeholder="" id="DATEOFCONSULTT5" type="date" value="<?php echo htmlspecialchars($row['DATEOFCONSULTT5']); ?>"   class="  ">
          <label for="DATEOFCONSULTT5">DATE OF CONSULT (TT5)</label>
        </div>
        <div class="input-field col s12 m12">
          <input placeholder="" id="PhoneNumber" name="PhoneNumber" type="text" value="<?php echo htmlspecialchars($row['PhoneNumber']); ?>"   class="  ">
          <label for="PhoneNumber">Phone Number</label>
        </div>

                <!-- Update Button -->
                <div class="row">
                    <div class="col s12 center-align">
                        <button type="submit" name="update_antigens" class="btn large green waves-effect">
                            <i class="material-icons left">save</i>Update Antigens
                        </button>
                        <a href="update_antigen.php" class="btn grey waves-effect">
                            <i class="material-icons left">arrow_back</i>Back to Search
                        </a>
                    </div>
                </div>
            </form>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });

        function selectChild(childId) {
            window.location.href = 'update_antigen.php?id=' + childId;
        }
    </script>
</body>
</html>