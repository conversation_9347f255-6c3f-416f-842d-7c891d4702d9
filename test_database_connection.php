<?php
// Simple Database Connection Test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔌 Database Connection Test</h2>";

// Test the database connection using the same method as db.php
include_once 'db.php';

echo "<h3>1. 🧪 Testing Basic Connection</h3>";

try {
    // Test basic query
    $result = $conn->query("SELECT 1 as test");
    if ($result) {
        echo "<p style='color: green;'>✅ Basic database query successful</p>";
    } else {
        throw new Exception("Query failed: " . $conn->error);
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Basic query failed: " . $e->getMessage() . "</p>";
}

echo "<h3>2. 🧪 Testing Table Access</h3>";

try {
    // Test nip_table access
    $result = $conn->query("SELECT COUNT(*) as total FROM nip_table");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ nip_table accessible - Total records: {$row['total']}</p>";
    } else {
        throw new Exception("Table query failed: " . $conn->error);
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Table access failed: " . $e->getMessage() . "</p>";
}

echo "<h3>3. 🧪 Testing Duplicate Check Functions</h3>";

try {
    // Test generateUniqueFamilySerial
    if (function_exists('generateUniqueFamilySerial')) {
        $serial = generateUniqueFamilySerial($conn);
        echo "<p style='color: green;'>✅ generateUniqueFamilySerial() works: $serial</p>";
    } else {
        echo "<p style='color: red;'>❌ generateUniqueFamilySerial() function not found</p>";
    }
    
    // Test checkForDuplicates
    if (function_exists('checkForDuplicates')) {
        $check = checkForDuplicates($conn, 'Test', 'Child', 'Middle', '2020-01-01', 'Test Mother', '', '', '');
        echo "<p style='color: green;'>✅ checkForDuplicates() works - Messages: " . count($check['messages']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ checkForDuplicates() function not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Function test failed: " . $e->getMessage() . "</p>";
}

echo "<h3>4. 🧪 Testing Form Submission Simulation</h3>";

try {
    // Simulate a form submission
    $_POST['submitBtn'] = 'Submit';
    $_POST['NameOfChild'] = 'TEST_CHILD_' . time();
    $_POST['LastNameOfChild'] = 'TEST_LAST';
    $_POST['middlename_of_child'] = 'TEST_MIDDLE';
    $_POST['DateOfBirth'] = '2020-01-01';
    $_POST['Sex'] = 'MALE';
    $_POST['NameofMother'] = 'TEST_MOTHER';
    $_POST['Barangay'] = 'TEST_BARANGAY';
    $_POST['Address'] = 'TEST_ADDRESS';
    $_POST['PhoneNumber'] = '09123456789';
    $_POST['PlaceofDelivery'] = 'HOSPITAL';
    $_POST['Attendant'] = 'DOCTOR';
    $_POST['TypeofDelivery'] = 'NSD';
    $_POST['BCG'] = 'YES';
    $_POST['BirthWeightInGrams'] = '3000';
    
    // Test duplicate checking with the form data
    $duplicateCheck = checkForDuplicates(
        $conn, 
        $_POST['NameOfChild'], 
        $_POST['LastNameOfChild'], 
        $_POST['middlename_of_child'], 
        $_POST['DateOfBirth'], 
        $_POST['NameofMother'], 
        $_POST['PhoneNumber'], 
        $_POST['Address'], 
        $_POST['Barangay']
    );
    
    echo "<p style='color: green;'>✅ Form submission simulation successful</p>";
    echo "<p><strong>Duplicate Found:</strong> " . ($duplicateCheck['found'] ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Messages:</strong> " . count($duplicateCheck['messages']) . "</p>";
    
    if (!empty($duplicateCheck['messages'])) {
        foreach ($duplicateCheck['messages'] as $msg) {
            echo "<div style='background: #fff3e0; padding: 10px; margin: 5px 0; border-left: 4px solid orange;'>";
            echo "<strong>{$msg['title']}</strong><br>";
            echo "{$msg['message']}";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Form simulation failed: " . $e->getMessage() . "</p>";
}

echo "<h3>5. 🧪 Testing Error Handling</h3>";

try {
    // Test with invalid data to see error handling
    $invalidCheck = checkForDuplicates($conn, '', '', '', '', '', '', '', '');
    echo "<p style='color: green;'>✅ Error handling works - Empty data handled gracefully</p>";
    
    // Test with malformed date
    $malformedCheck = checkForDuplicates($conn, 'Test', 'Child', 'Middle', 'invalid-date', 'Mother', '', '', '');
    echo "<p style='color: green;'>✅ Error handling works - Invalid date handled gracefully</p>";
    
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠️ Error handling test: " . $e->getMessage() . "</p>";
}

echo "<h3>6. 📊 Database Statistics</h3>";

try {
    // Get database statistics
    $stats = [];
    
    $result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE (deleted IS NULL OR deleted = 0)");
    $stats['total'] = $result->fetch_assoc()['total'];
    
    $result = $conn->query("SELECT COUNT(*) as with_middle FROM nip_table WHERE middlename_of_child IS NOT NULL AND middlename_of_child != '' AND (deleted IS NULL OR deleted = 0)");
    $stats['with_middle'] = $result->fetch_assoc()['with_middle'];
    
    $result = $conn->query("SELECT COUNT(*) as with_phone FROM nip_table WHERE PhoneNumber IS NOT NULL AND PhoneNumber != '' AND (deleted IS NULL OR deleted = 0)");
    $stats['with_phone'] = $result->fetch_assoc()['with_phone'];
    
    // Check if support tables exist
    $result = $conn->query("SHOW TABLES LIKE 'duplicate_block_log'");
    $stats['block_log_exists'] = $result->num_rows > 0;
    
    if ($stats['block_log_exists']) {
        $result = $conn->query("SELECT COUNT(*) as blocked FROM duplicate_block_log");
        $stats['blocked_attempts'] = $result->fetch_assoc()['blocked'];
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
    echo "<h4>📈 Current Statistics</h4>";
    echo "<ul>";
    echo "<li><strong>Total Active Records:</strong> {$stats['total']}</li>";
    echo "<li><strong>Records with Middle Names:</strong> {$stats['with_middle']}</li>";
    echo "<li><strong>Records with Phone Numbers:</strong> {$stats['with_phone']}</li>";
    echo "<li><strong>Duplicate Block Log:</strong> " . ($stats['block_log_exists'] ? 'Available' : 'Not Available') . "</li>";
    if ($stats['block_log_exists']) {
        echo "<li><strong>Blocked Attempts:</strong> {$stats['blocked_attempts']}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Statistics failed: " . $e->getMessage() . "</p>";
}

echo "<h3>7. 🎯 Test Results Summary</h3>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3;'>";
echo "<h4>✅ Database Connection Test Complete</h4>";
echo "<p>If you see mostly green checkmarks above, your database is working correctly.</p>";
echo "<p>If you see red X marks, please run the database fix script.</p>";
echo "</div>";

echo "<h3>🔗 Quick Actions</h3>";
echo "<p>";
echo "<a href='fix_database_errors.php' style='background: #f44336; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Fix Database Errors</a>";
echo "<a href='database_health_check.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Full Health Check</a>";
echo "<a href='nip.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Try Registration</a>";
echo "<a href='test_middle_name_duplicate_check.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🧪 Test Duplicates</a>";
echo "</p>";

// Clean up test POST data
unset($_POST);

echo "<hr>";
echo "<p><em>Connection test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}

ul {
    line-height: 1.6;
}
</style>
