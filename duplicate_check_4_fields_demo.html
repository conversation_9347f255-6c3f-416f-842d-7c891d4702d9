<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Check - 4 Fields Implementation</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .success-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        
        .field-box {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left red-text">error</i>
                Duplicate Check - 4 Fields Implementation Complete!
            </h3>
            <p class="center-align">Using Date of Birth, Last Name, First Name, and Middle Name</p>
        </div>

        <div class="demo-section">
            <h4>✅ Problem Solved!</h4>
            
            <div class="success-box">
                <h6>Duplicate Check Implementation:</h6>
                <ul>
                    <li>✅ <strong>4-Field Matching:</strong> Uses the exact fields you requested</li>
                    <li>✅ <strong>Date of Birth:</strong> DateOfBirth field</li>
                    <li>✅ <strong>Last Name:</strong> LastNameOfChild field</li>
                    <li>✅ <strong>First Name:</strong> NameOfChild field</li>
                    <li>✅ <strong>Middle Name:</strong> middlename_of_child field</li>
                    <li>✅ <strong>Error Message:</strong> "This record is already exist"</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔍 Duplicate Detection Fields</h4>
            
            <div class="field-box">
                <h6>The system checks for duplicates using these 4 fields:</h6>
                <div class="row">
                    <div class="col s12 m6">
                        <div class="card blue lighten-4">
                            <div class="card-content">
                                <span class="card-title">Field 1: Date of Birth</span>
                                <p><strong>Database Field:</strong> DateOfBirth</p>
                                <p><strong>Example:</strong> 2023-01-15</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col s12 m6">
                        <div class="card green lighten-4">
                            <div class="card-content">
                                <span class="card-title">Field 2: Last Name</span>
                                <p><strong>Database Field:</strong> LastNameOfChild</p>
                                <p><strong>Example:</strong> Dela Cruz</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col s12 m6">
                        <div class="card orange lighten-4">
                            <div class="card-content">
                                <span class="card-title">Field 3: First Name</span>
                                <p><strong>Database Field:</strong> NameOfChild</p>
                                <p><strong>Example:</strong> Juan</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col s12 m6">
                        <div class="card purple lighten-4">
                            <div class="card-content">
                                <span class="card-title">Field 4: Middle Name</span>
                                <p><strong>Database Field:</strong> middlename_of_child</p>
                                <p><strong>Example:</strong> Santos</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>💻 Code Implementation</h4>
            
            <h5>Duplicate Check Query:</h5>
            <div class="code-block">
// Duplicate check using Date of Birth, Last Name, First Name, and Middle Name
$duplicate_check_sql = "SELECT id FROM nip_table WHERE 
    DateOfBirth = ? AND 
    LastNameOfChild = ? AND 
    NameOfChild = ? AND 
    middlename_of_child = ? AND 
    (deleted IS NULL OR deleted = 0)";

$stmt = mysqli_prepare($conn, $duplicate_check_sql);
mysqli_stmt_bind_param($stmt, "ssss", $DateOfBirth, $LastNameOfChild, $NameOfChild, $middlename_of_child);
mysqli_stmt_execute($stmt);
$duplicate_result = mysqli_stmt_get_result($stmt);
            </div>
            
            <h5>Error Message When Duplicate Found:</h5>
            <div class="code-block">
if (mysqli_num_rows($duplicate_result) > 0) {
    // Duplicate found - show error message
    echo '
    &lt;div class="card red lighten-4 red-text text-darken-4"&gt;
        &lt;div class="card-content"&gt;
            &lt;p&gt;&lt;i class="material-icons left"&gt;error&lt;/i&gt;This record is already exist&lt;/p&gt;
        &lt;/div&gt;
    &lt;/div&gt;';
} else {
    // No duplicate found - proceed with insertion
    $sql = "INSERT INTO nip_table ...";
}
            </div>
        </div>

        <div class="demo-section">
            <h4>🎯 How It Works</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card teal lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 1: Check</span>
                            <p>Before inserting, search for existing records with the same:</p>
                            <ul style="font-size: 13px;">
                                <li><strong>Date of Birth</strong> (exact match)</li>
                                <li><strong>Last Name</strong> (exact match)</li>
                                <li><strong>First Name</strong> (exact match)</li>
                                <li><strong>Middle Name</strong> (exact match)</li>
                            </ul>
                            <p><small>All 4 fields must match for duplicate detection</small></p>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card pink lighten-4">
                        <div class="card-content">
                            <span class="card-title">Step 2: Result</span>
                            <p>Based on search result:</p>
                            <ul style="font-size: 13px;">
                                <li><strong>All 4 fields match:</strong> Show "This record is already exist"</li>
                                <li><strong>Any field different:</strong> Allow insertion</li>
                                <li><strong>Deleted records:</strong> Ignored in check</li>
                            </ul>
                            <p><small>Record is only blocked if ALL 4 fields match exactly</small></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Example Scenarios</h4>
            
            <h5>Scenario 1: Exact Duplicate (BLOCKED)</h5>
            <div class="card red lighten-4">
                <div class="card-content">
                    <h6>Existing Record:</h6>
                    <ul>
                        <li>Date of Birth: 2023-01-15</li>
                        <li>Last Name: Dela Cruz</li>
                        <li>First Name: Juan</li>
                        <li>Middle Name: Santos</li>
                    </ul>
                    <h6>New Record (Same):</h6>
                    <ul>
                        <li>Date of Birth: 2023-01-15</li>
                        <li>Last Name: Dela Cruz</li>
                        <li>First Name: Juan</li>
                        <li>Middle Name: Santos</li>
                    </ul>
                    <p style="color: #d32f2f; font-weight: bold;">
                        <i class="material-icons">error</i> Result: "This record is already exist"
                    </p>
                </div>
            </div>
            
            <h5>Scenario 2: Different Middle Name (ALLOWED)</h5>
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>Existing Record:</h6>
                    <ul>
                        <li>Date of Birth: 2023-01-15</li>
                        <li>Last Name: Dela Cruz</li>
                        <li>First Name: Juan</li>
                        <li>Middle Name: Santos</li>
                    </ul>
                    <h6>New Record (Different Middle Name):</h6>
                    <ul>
                        <li>Date of Birth: 2023-01-15</li>
                        <li>Last Name: Dela Cruz</li>
                        <li>First Name: Juan</li>
                        <li>Middle Name: <strong>Garcia</strong></li>
                    </ul>
                    <p style="color: #388e3c; font-weight: bold;">
                        <i class="material-icons">check_circle</i> Result: Record Successfully Added
                    </p>
                </div>
            </div>
            
            <h5>Scenario 3: Different Birth Date (ALLOWED)</h5>
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>Existing Record:</h6>
                    <ul>
                        <li>Date of Birth: 2023-01-15</li>
                        <li>Last Name: Dela Cruz</li>
                        <li>First Name: Juan</li>
                        <li>Middle Name: Santos</li>
                    </ul>
                    <h6>New Record (Different Birth Date):</h6>
                    <ul>
                        <li>Date of Birth: <strong>2023-02-20</strong></li>
                        <li>Last Name: Dela Cruz</li>
                        <li>First Name: Juan</li>
                        <li>Middle Name: Santos</li>
                    </ul>
                    <p style="color: #388e3c; font-weight: bold;">
                        <i class="material-icons">check_circle</i> Result: Record Successfully Added
                    </p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🧪 Testing Instructions</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test Duplicate Detection</span>
                            <ol style="font-size: 13px;">
                                <li>Go to registration form</li>
                                <li>Fill in child information</li>
                                <li>Submit the form</li>
                                <li>Try submitting with exact same:
                                    <ul>
                                        <li>Date of Birth</li>
                                        <li>Last Name</li>
                                        <li>First Name</li>
                                        <li>Middle Name</li>
                                    </ul>
                                </li>
                                <li>Should see "This record is already exist"</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test Allowed Records</span>
                            <ol style="font-size: 13px;">
                                <li>Use same child name</li>
                                <li>Change any one of these:
                                    <ul>
                                        <li>Different birth date</li>
                                        <li>Different last name</li>
                                        <li>Different first name</li>
                                        <li>Different middle name</li>
                                    </ul>
                                </li>
                                <li>Should be allowed to submit</li>
                                <li>Should see "Record Successfully Added"</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>The 4-field duplicate check is now active!</p>
            
            <div class="row">
                <div class="col s12 m6">
                    <a href="nip.php" class="btn large blue waves-effect">
                        <i class="material-icons left">add</i>Test Registration Form
                    </a>
                    <p><small>Try adding duplicate records</small></p>
                </div>
                
                <div class="col s12 m6">
                    <a href="records.php" class="btn large green waves-effect">
                        <i class="material-icons left">table_chart</i>View Records
                    </a>
                    <p><small>See existing records</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>✅ Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>4-Field Duplicate Check - Problem Solved!</h6>
                    <ul>
                        <li><strong>Fields Used:</strong> Date of Birth + Last Name + First Name + Middle Name</li>
                        <li><strong>Detection:</strong> All 4 fields must match exactly for duplicate</li>
                        <li><strong>Message:</strong> "This record is already exist"</li>
                        <li><strong>Security:</strong> Uses prepared statements</li>
                        <li><strong>Flexibility:</strong> Allows records with any field difference</li>
                        <li><strong>Status:</strong> ✅ Active and working!</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
