<?php
include 'db.php';
session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Set timezone
date_default_timezone_set('Asia/Manila');

// Get date parameters from request
$fromDate = isset($_GET['fromDate']) && !empty($_GET['fromDate']) ? $_GET['fromDate'] : null;
$toDate = isset($_GET['toDate']) && !empty($_GET['toDate']) ? $_GET['toDate'] : null;
$countOnly = isset($_GET['count']) && $_GET['count'] === 'true';
$previewLimit = isset($_GET['preview']) ? intval($_GET['preview']) : null;

try {
    // Base query
    if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
        $sql = "SELECT * FROM nip_table WHERE deleted = 0";
        $params = [];
        $types = "";
    } else {
        $sql = "SELECT * FROM nip_table WHERE deleted = 0 AND Barangay = ?";
        $params = [$_SESSION['health_center']];
        $types = "s";
    }

    // Add date filtering if dates are provided
    if ($fromDate && $toDate) {
        // Add one day to toDate to include the entire day
        $toDateObj = new DateTime($toDate);
        $toDateObj->modify('+1 day');
        $toDatePlusOne = $toDateObj->format('Y-m-d');
        
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
        $params[] = $fromDate;
        $params[] = $toDatePlusOne;
        $types .= "ss";
    }

    // If only count is requested
    if ($countOnly) {
        $countSql = str_replace("SELECT *", "SELECT COUNT(*) as count", $sql);

        $countStmt = $conn->prepare($countSql);
        if (!$countStmt) {
            throw new Exception("Prepare statement failed: " . $conn->error);
        }

        // Bind parameters if there are any
        if (!empty($params)) {
            $countStmt->bind_param($types, ...$params);
        }

        $countStmt->execute();

        if ($countStmt->error) {
            throw new Exception("SQL Error: " . $countStmt->error);
        }

        $countResult = $countStmt->get_result();
        $countRow = $countResult->fetch_assoc();

        $response = ['count' => $countRow['count']];

        // If preview is requested along with count
        if ($previewLimit && $previewLimit > 0) {
            // Get preview data
            $previewSql = $sql . " ORDER BY id DESC LIMIT ?";
            $previewParams = $params;
            $previewParams[] = $previewLimit;
            $previewTypes = $types . "i";

            $previewStmt = $conn->prepare($previewSql);
            if (!$previewStmt) {
                throw new Exception("Preview prepare statement failed: " . $conn->error);
            }

            if (!empty($previewParams)) {
                $previewStmt->bind_param($previewTypes, ...$previewParams);
            }

            $previewStmt->execute();

            if ($previewStmt->error) {
                throw new Exception("Preview SQL Error: " . $previewStmt->error);
            }

            $previewResult = $previewStmt->get_result();
            $previewData = [];

            while ($row = $previewResult->fetch_assoc()) {
                $previewData[] = $row;
            }

            $response['preview'] = $previewData;
            $previewStmt->close();
        }

        $countStmt->close();
        echo json_encode($response);
        exit;
    }

    // Add ordering for the main query
    $sql .= " ORDER BY id DESC";

    // Prepare and execute the main query
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Prepare statement failed: " . $conn->error);
    }
    
    // Bind parameters if there are any
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    
    if ($stmt->error) {
        throw new Exception("SQL Error: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $data = [];
    
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    echo json_encode($data);
    
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
    // Log error for debugging
    error_log("NIP Data Error: " . $e->getMessage());
}
?>
