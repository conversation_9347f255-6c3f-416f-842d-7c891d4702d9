<?php session_start(); ?>

<?php
// Check if session exists, if not check for remember me cookie
if (!isset($_SESSION['health_center'])) {
    // Check if remember me cookie exists
    if (isset($_COOKIE['remember_user']) && isset($_COOKIE['remember_token'])) {
        // Include database connection
        include 'db.php';

        $remember_token = $_COOKIE['remember_token'];
        $health_center = $_COOKIE['remember_user'];

        // Verify the remember token in database
        $stmt = $conn->prepare("SELECT * FROM health_facility WHERE health_center = ? AND remember_token = ?");
        $stmt->bind_param("ss", $health_center, $remember_token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            // Valid remember token, restore session
            $user = $result->fetch_assoc();
            $_SESSION['health_center'] = $user['health_center'];
            $_SESSION['fullname'] = $user['fullname'];
            $_SESSION['user_id'] = $user['id'];

            // Generate new remember token for security
            $new_token = bin2hex(random_bytes(32));
            $update_stmt = $conn->prepare("UPDATE health_facility SET remember_token = ? WHERE id = ?");
            $update_stmt->bind_param("si", $new_token, $user['id']);
            $update_stmt->execute();

            // Update cookie with new token
            setcookie('remember_token', $new_token, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 days, httponly

            $stmt->close();
            $update_stmt->close();
            $conn->close();
        } else {
            // Invalid or expired token, clear cookies and redirect
            setcookie('remember_user', '', time() - 3600, '/');
            setcookie('remember_token', '', time() - 3600, '/');
            header('Location: login.php');
            exit;
        }
    } else {
        // No session and no valid remember me cookie
        header('Location: login.php');
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <?php include 'style.php'; ?>
     <style>
        /* Material Design - Clean and Static */
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: #212121;
        }

        /* Material Design Card Panel */
        .card-panel {
            background-color: #ffffff;
            padding: 24px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }

        /* Material Design Profile Header */
        .profile-header {
            background-color: #1976d2;
            color: #ffffff;
            padding: 24px;
            border-radius: 4px 4px 0 0;
            margin-bottom: 0;
        }

        /* Material Design Section Headers */
        .section-header {
            background-color: #ffffff;
            color: #1976d2;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 500;
            border-bottom: 1px solid #e0e0e0;
            margin: 0;
            display: flex;
          
        }

        .section-header .material-icons {
            margin-right: 8px;
            font-size: 20px;
        }

        /* Material Design Input Fields */
        .input-field {
            margin-bottom: 16px;
        }

        .input-field input[type=text],
        .input-field input[type=password],
        .input-field input[type=email] {
            border-bottom: 1px solid #9e9e9e;
            background-color: transparent;
            font-size: 16px;
            padding: 8px 0;
        }

        .input-field input[type=text]:focus,
        .input-field input[type=password]:focus,
        .input-field input[type=email]:focus {
            border-bottom: 2px solid #1976d2;
            box-shadow: 0 1px 0 0 #1976d2;
        }

        .input-field label {
            color: #757575;
            
            font-weight: 400;
        }

        .input-field label.active {
            color: #1976d2;
      
            font-weight: 500;
        }

        /* Material Design Account Icon */
        .account-icon {
            font-size: 120px;
            color: #1976d2;
            margin-bottom: 16px;
        }

        /* Material Design Verification Badge */
        .verification-badge {
            background-color: #4caf50;
            color: #ffffff;
            border-radius: 50%;
            padding: 4px;
            font-size: 12px;
            position: absolute;
            margin-left: -24px;
            margin-top: 8px;
        }

        /* Material Design Buttons */
        .btn-material {
            background-color: #1976d2;
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .btn-secondary {
            background-color: #757575;
        }

        /* Material Design Checkbox */
        [type="checkbox"].filled-in:checked + span:not(.lever):after {
            background-color: #1976d2;
            border-color: #1976d2;
        }

        /* Material Design Typography */
        .profile-name {
            font-size: 20px;
            font-weight: 500;
            color: #212121;
            margin-bottom: 8px;
        }

        .profile-subtitle {
            font-size: 14px;
            color: #757575;
            margin-bottom: 4px;
        }

        .profile-detail {
            font-size: 14px;
            color: #424242;
            margin-bottom: 16px;
        }

        /* Material Design Container */
        .material-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        /* Material Design Grid */
        .material-row {
            display: flex;
            flex-wrap: wrap;
            margin: -8px;
        }

        .material-col {
            padding: 8px;
            flex: 1;
            min-width: 200px;
        }

        /* Material Design Responsive */
        @media (max-width: 768px) {
            .account-icon {
                font-size: 80px;
            }

            .material-container {
                padding: 16px;
            }

            .card-panel {
                padding: 16px;
            }

            .section-header {
                padding: 12px 16px;
                font-size: 14px;
            }

            .material-col {
                min-width: 100%;
            }

            .profile-name {
                font-size: 18px;
            }

            .btn-material {
                width: 100%;
                margin-bottom: 8px;
            }
        }

        @media (max-width: 480px) {
            .material-container {
                padding: 8px;
            }

            .card-panel {
                padding: 12px;
            }

            .account-icon {
                font-size: 60px;
            }
        }

        /* Material Design Layout */
        .row {
            margin-bottom: 0;
        }

        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

      
        /* Material Design Elevation */
        .elevation-1 {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .elevation-2 {
            box-shadow: 0 4px 8px rgba(0,0,0,0.12), 0 2px 4px rgba(0,0,0,0.08);
        }

        .elevation-3 {
            box-shadow: 0 8px 16px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1);
        }

        /* Modal Styles */
        .modal {
            max-width: 500px;
        }

        .modal .modal-content h5 {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .modal .input-field input:focus + label {
            color: #1976d2 !important;
        }

        .modal .input-field input:focus {
            border-bottom: 1px solid #1976d2 !important;
            box-shadow: 0 1px 0 0 #1976d2 !important;
        }

        .modal .helper-text {
            color: #757575;
            font-size: 12px;
        }

        .modal-footer .btn {
            margin-left: 8px;
        }
     </style>
</head>
<body style="background:#FCF7FF;">
<?php include 'nav.php'; ?>

<title><?php echo $_SESSION['health_center']; ?> | NIP</title>


<?php
include 'db.php';
?>
 
<?php
 
// Assuming the session holds the health center's name or id
$health_center = $_SESSION['health_center'];

$sql = "SELECT * FROM health_facility WHERE health_center='$health_center'";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    // Output data for each row
    $row = $result->fetch_assoc();
} else {
    echo "No profile found for the specified health center.";
}
?>

 
  
  
        
  
  <div class="material-container">
    <div class="row">
      <div class="col s12">
        <!-- Material Design Profile Header -->
        <div class="profile-header elevation-2">
          <h5 style="margin: 0; font-size: 20px; font-weight: 500;">
            <i class="material-icons left" style="font-size: 24px;">account_circle</i>
            Profile
          </h5>
        </div>

        <!-- Material Design Profile Card -->
        <div class="card-panel elevation-2">
          <div class="row valign-wrapper">
            <div class="col s12  ">
              <div style="position: relative; display: inline-block;">
                <i class="material-icons account-icon">account_circle</i>
                <i class="material-icons verification-badge tooltipped"
                   data-tooltip="This Account is Verified by City Health Office"
                   data-position="bottom">check</i>
              </div>

              <div class="profile-name"><?php echo $row['fullname'] . ' - ' . $row['item']; ?></div>

              <div class="profile-detail">
                <i class="material-icons left" style="font-size: 16px; margin-right: 4px;">home</i>
                <strong><?php echo $row['health_center']; ?></strong>
                <br>
                <span class="profile-subtitle"><?php echo $row['AddressofFacility']; ?></span>
              </div>

              <div class="profile-detail">
                <i class="material-icons left" style="font-size: 16px; margin-right: 4px;">perm_contact_calendar</i>
                <strong>Date of Registration</strong>
                <br>
                <span class="profile-subtitle"><?php echo date('F d, Y', strtotime($row['date_created'])); ?></span>
              </div>
            </div>
          </div>
        </div>
  
    
      

 
      <div class="col s12 m12 offset-m0 z-depth-1  white"  >
       
      

        <div class="row "  >
        <h5 class="  col s12   " style="padding:0.7em;font-size:18.4px; border-bottom:1px solid #ddd;padding-top:0px !important;color:#00ACB1;"><i class="material-icons left">account_circle</i>Personal Information</h5>
    <div class="input-field col s12 m1">
      <input value="<?php echo $row['id']; ?>"   id="Facility" type="text"    readonly >
      <label class="active" for="Facility">Facility ID</label>
    </div>
     
    <div class="input-field col s12 m6">
      <input value="<?php echo $row['username']; ?>"   id="username" type="text" readonly>
      <label class="active" for="username">username</label>
    </div>
    <div class="input-field col s12 m3">
      <input value="<?php echo $row['password']; ?>" placeholder="" onclick="myFunction()" id="pw"  type="password"  readonly>
      <label class="active" for="password">Password</label>
     
    </div>
    <div class="input-field col s5 m2">
    <p>
      <label>
        <input type="checkbox" class="filled-in" onclick="myFunction()">
        <span><small>Show Password</small></span>
      </label>
    </p>
     
    </div>
    
    <div class="input-field col s12 m4">
      <input value="<?php echo date('F d,Y',strtotime($row['birthdate'])); ?>" placeholder="" id="item" type="text"  readonly>
      <label class="active" for="birthdate">Birthdate</label>
    </div>
    <div class="input-field col s12 m2">
      <input value="<?php echo $row['age']; ?>" placeholder="" id="age" type="text"  >
      <label class="active" for="age">Age</label>
    </div>
   
    <div class="input-field col s12 m6">
      <input value="<?php echo $row['email']; ?>" placeholder="" id="email" type="text"  readonly>
      <label class="active" for="email">Email</label>
    </div>
    <div class="input-field col s12 m12">
      <input value="<?php echo $row['health_center']; ?>" placeholder="" id="health_center" type="text"  readonly>
      <label class="active" for="health_center">Health Center</label>
    </div>

    <div class="input-field col s12 m12">
      <input value="<?php echo $row['AddressofFacility']; ?>" placeholder="" id="AddressofFacility" type="text"  readonly>
      <label class="active" for="AddressofFacility">Address of Facility</label>
    </div>
    <div class="input-field col s12 m5">
      <input value="<?php echo $row['fullname']; ?>" placeholder="" id="fullname" type="text"  readonly>
      <label class="active" for="fullname">Full Name</label>
    </div>
    <div class="input-field col s12 m3">
      <input value="<?php echo $row['item']; ?>" placeholder="" id="item" type="text"  readonly>
      <label class="active" for="item">Item</label>
    </div>
    <div class="input-field col s12 m4">
      <input value="<?php echo $row['mobile_number']; ?>" placeholder="" id="mobile_number" type="text"  readonly>
      <label class="active" for="mobile_number">Mobile Number</label>
    </div>
    <div class="input-field col s12">
      <button class="  btn-small  blue accent-3 white-text btn z-depth-1 " style="font-weight:500;" onclick="history.back()">Back</button>
      <button class=" cyan darken-4 white-text btn z-depth-1 right btn-small modal-trigger" data-target="changePasswordModal" style="font-weight:500;">Change Password</button>
    </div>
    
  </div>
  
  
</div>




</div>
</div>
</div>
 
    
    
 <script>
  function myFunction() {
  var x = document.getElementById("pw");
  if (x.type === "password") {
    x.type = "text";
  } else {
    x.type = "password";
  }
}
 </script>    
 <script>
    $(document).ready(function(){
    $('.tooltipped').tooltip();
    $('.modal').modal();

    // Show/Hide passwords functionality
    $('#showPasswords').change(function() {
      const passwordFields = ['currentPassword', 'newPassword', 'confirmPassword'];
      const showPasswords = $(this).is(':checked');

      passwordFields.forEach(function(fieldId) {
        const field = document.getElementById(fieldId);
        field.type = showPasswords ? 'text' : 'password';
      });
    });

    // Change password functionality
    $('#changePasswordBtn').click(function() {
      const currentPassword = $('#currentPassword').val();
      const newPassword = $('#newPassword').val();
      const confirmPassword = $('#confirmPassword').val();

      // Clear previous messages
      $('#passwordChangeMessage').hide().removeClass('green lighten-4 green-text red lighten-4 red-text');

      // Validation
      if (!currentPassword || !newPassword || !confirmPassword) {
        showMessage('All fields are required', 'error');
        return;
      }

      if (newPassword.length < 6) {
        showMessage('New password must be at least 6 characters long', 'error');
        return;
      }

      if (newPassword !== confirmPassword) {
        showMessage('New passwords do not match', 'error');
        return;
      }

      if (currentPassword === newPassword) {
        showMessage('New password must be different from current password', 'error');
        return;
      }

      // Disable button and show loading
      $(this).prop('disabled', true).html('<i class="material-icons left">hourglass_empty</i>Changing...');

      // Send AJAX request
      $.ajax({
        url: 'change_password.php',
        type: 'POST',
        data: {
          currentPassword: currentPassword,
          newPassword: newPassword
        },
        dataType: 'json',
        success: function(response) {
          if (response.success) {
            showMessage(response.message, 'success');
            $('#changePasswordForm')[0].reset();
            setTimeout(function() {
              $('#changePasswordModal').modal('close');
            }, 2000);
          } else {
            showMessage(response.message, 'error');
          }
        },
        error: function() {
          showMessage('An error occurred. Please try again.', 'error');
        },
        complete: function() {
          $('#changePasswordBtn').prop('disabled', false).html('<i class="material-icons left">save</i>Change Password');
        }
      });
    });

    function showMessage(message, type) {
      const messageDiv = $('#passwordChangeMessage');
      messageDiv.removeClass('green lighten-4 green-text red lighten-4 red-text');

      if (type === 'success') {
        messageDiv.addClass('green lighten-4 green-text');
      } else {
        messageDiv.addClass('red lighten-4 red-text');
      }

      messageDiv.html('<div style="padding: 10px; border-radius: 4px;"><i class="material-icons left">' +
        (type === 'success' ? 'check_circle' : 'error') + '</i>' + message + '</div>').show();
    }
  });
 </script>

<!-- Change Password Modal -->
<div id="changePasswordModal" class="modal">
  <div class="modal-content">
    <h5 style="color: #1976d2; margin-bottom: 20px;">
      <i class="material-icons left">lock</i>Change Password
    </h5>

    <div id="passwordChangeMessage" style="display: none; margin-bottom: 15px;"></div>

    <form id="changePasswordForm">
      <div class="row">
        <div class="input-field col s12">
          <input id="currentPassword" name="currentPassword" type="password" required>
          <label for="currentPassword">Current Password</label>
        </div>

        <div class="input-field col s12">
          <input id="newPassword" name="newPassword" type="password" required minlength="6">
          <label for="newPassword">New Password</label>
          <span class="helper-text">Password must be at least 6 characters long</span>
        </div>

        <div class="input-field col s12">
          <input id="confirmPassword" name="confirmPassword" type="password" required>
          <label for="confirmPassword">Confirm New Password</label>
        </div>

        <div class="col s12">
          <p>
            <label>
              <input type="checkbox" id="showPasswords" />
              <span>Show Passwords</span>
            </label>
          </p>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button" class="modal-close waves-effect waves-light btn-flat">Cancel</button>
    <button type="button" id="changePasswordBtn" class="waves-effect waves-light btn cyan darken-4">
      <i class="material-icons left">save</i>Change Password
    </button>
  </div>
</div>

</body>
</html>
<?php
$conn->close();
?>
