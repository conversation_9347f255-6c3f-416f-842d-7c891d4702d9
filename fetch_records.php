<?php
session_start();
include 'db.php'; // Include your DB connection

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $from = $_POST['from'];
    $to = $_POST['to'];
    $health_center = $_SESSION['health_center'];

    if ($health_center === 'CITY HEALTH OFFICE') {
        $sql = "SELECT * FROM nip_table WHERE DateOfRegistration BETWEEN ? AND ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $from, $to);
    } else {
        $sql = "SELECT * FROM nip_table WHERE Barangay = ? AND DateOfRegistration BETWEEN ? AND ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sss", $health_center, $from, $to);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo '<thead>
    <tr>
      <!-- Define your table headers here -->
      <th>ID</th>
      <th>Date of Registration</th>
      <th>Date of Birth</th>
      <th>Age of Child</th>
      <!-- Add other headers -->
    </tr>
  </thead><tr>';
            echo '<td>' . htmlspecialchars($row['id']) . '</td>';
            echo '<td>' . htmlspecialchars($row['DateOfRegistration']) . '</td>';
            echo '<td>' . htmlspecialchars($row['DateOfBirth']) . '</td>';
            echo '<td>' . htmlspecialchars($row['AgeofChild']) . '</td>';
            // Add other columns as needed
            echo '<td>
                <a target="_blank" href="print.php?id=' . urlencode($row['id']) . '&from=' . urlencode($from) . '&to=' . urlencode($to) . '" class="btn btn-small black darken-4">
                    <i class="material-icons center">print</i>
                </a>
                <a target="_blank" href="edit.php?id=' . urlencode($row['id']) . '&from=' . urlencode($from) . '&to=' . urlencode($to) . '" class="btn btn-small blue darken-4 modal-trigger">
                    <i class="material-icons center">edit</i>
                </a>
                <button type="button" class="btn btn-small teal darken-4 modal-trigger"><i class="material-icons center">remove_red_eye</i></button>
                <button type="button" class="btn btn-small red darken-4 modal-trigger"><i class="material-icons center">delete_forever</i></button>
              </td>';
            echo '</tr>';
        }
    } else {
        echo '<tr><td colspan="5">No records found.</td></tr>';
    }

    $stmt->close();
}
?>
