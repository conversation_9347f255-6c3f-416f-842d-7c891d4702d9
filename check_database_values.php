<?php
include 'db.php';

echo "<h2>Database Field Check for health_facility Table</h2>";

// Check table structure
echo "<h3>Table Structure:</h3>";
$structure_sql = "DESCRIBE health_facility";
$structure_result = $conn->query($structure_sql);

if ($structure_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $structure_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check current data and approved values
echo "<h3>Current Data Sample (First 10 Records):</h3>";
$data_sql = "SELECT id, username, email, fullname, health_center, approved, approved_date, disapproved_date, disapproval_reason FROM health_facility LIMIT 10";
$data_result = $conn->query($data_sql);

if ($data_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Fullname</th><th>Health Center</th><th>Approved</th><th>Approved Date</th><th>Disapproved Date</th><th>Disapproval Reason</th></tr>";
    while ($row = $data_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $row['fullname'] . "</td>";
        echo "<td>" . $row['health_center'] . "</td>";
        echo "<td style='font-weight: bold; color: " . 
             ($row['approved'] == 1 ? 'green' : ($row['approved'] == 0 ? 'red' : 'orange')) . ";'>" . 
             ($row['approved'] === null ? 'NULL' : $row['approved']) . "</td>";
        echo "<td>" . $row['approved_date'] . "</td>";
        echo "<td>" . $row['disapproved_date'] . "</td>";
        echo "<td>" . substr($row['disapproval_reason'], 0, 50) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check approved value distribution
echo "<h3>Approved Value Distribution:</h3>";
$stats_sql = "SELECT approved, COUNT(*) as count FROM health_facility GROUP BY approved ORDER BY approved";
$stats_result = $conn->query($stats_sql);

if ($stats_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Approved Value</th><th>Count</th><th>Status</th></tr>";
    while ($row = $stats_result->fetch_assoc()) {
        $status = '';
        $color = '';
        if ($row['approved'] == 1) {
            $status = 'Approved';
            $color = 'green';
        } elseif ($row['approved'] == 0) {
            $status = 'Disapproved';
            $color = 'red';
        } else {
            $status = 'Pending';
            $color = 'orange';
        }
        
        echo "<tr>";
        echo "<td style='font-weight: bold;'>" . ($row['approved'] === null ? 'NULL' : $row['approved']) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "<td style='color: $color; font-weight: bold;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>Field Name Verification:</h3>";
echo "<p><strong>Checking if fields exist:</strong></p>";
$check_fields = ['email', 'fullname', 'date_created', 'approved_date', 'disapproved_date', 'disapproval_reason'];
foreach ($check_fields as $field) {
    $check_sql = "SHOW COLUMNS FROM health_facility LIKE '$field'";
    $check_result = $conn->query($check_sql);
    $exists = $check_result && $check_result->num_rows > 0;
    echo "<p>$field: " . ($exists ? "<span style='color: green;'>✓ EXISTS</span>" : "<span style='color: red;'>✗ MISSING</span>") . "</p>";
}

$conn->close();
?>
