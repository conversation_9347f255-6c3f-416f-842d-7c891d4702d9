<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>Document</title>
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    <?php include 'style.php';?>
    <style>
   
    .pagination li.active a {
    color: #fff;
    font-weight: 500;
    background: #42a5f5;
}
    @media only screen and (max-width : 492px) {
      header, main, footer {
        padding-left: 0;
      }
    }
    th {
        text-align: start !important;
        border-radius:0px;
        
      text-align:start !important;
        font-weight:600 !important;
        font-size:13.2px !important;
        font-family: 'Roboto', sans-serif !important;

    }
    td { 
        text-align:start !important;
        color:#37474f   !important;
        font-family: 'Roboto', sans-serif !important;
        font-size: 13.5px !important;
        border-bottom:1px solid #ddd !important;
        padding-top:4px !important;
        padding-bottom:4px !important;
         
    }
     
    label {
        color:black !important;
    }
  
    
    .dataTables_paginate  {
        background-color: #039be5  !important;
        padding:2px;
    }
   
    .pagination li.disabled a {
    cursor: default;
    color: white !important;
}
 .dt-button   {
    background:#5e9834 !important;
    border:none !important;
    color:white !important;
    font-weight:500 !important;
    padding-top:0.4em !important;
}
 
nav { 
    color: #fff;
     background-color: white; 
    width: 100%;
    /* height: 56px; */
    color:black !important;
    
} 
.dt-paging-button ,.current {
    background:white !important;
    color:black !important;
}


  
   th {
        
        border-radius:0px;
        color:#37474f   !important;
      text-align:start !important;
        font-weight:600 !important;
        font-size:14px !important;
         
    }
    td { 
        text-align:start !important;
   
        font-weight:normal;
        font-size: 14px;
        
    }
    
  
  
    [type="checkbox"]:checked + span:after, [type="checkbox"].with-gap:checked + span:after {
    background-color: #039be5    !important;
    border:2px solid #039be5   !important;
}
 


    </style>
 

</head>
<body class="blue-grey lighten-5">
  
<?php include 'nav.php'; ?>
<?php include'db.php'; ?>
<br>
<div class="row  white" style="padding:10px; margin:30px; ">
<h5 class="start" style="font-weight:normal;margin-left:5px;">Account Settings</h5>
 
<table class="  display  " id="example" style="width:100%" id="example">
    <thead class="  " >
        <tr  class=" ">
            <th>Action</th>
            <th class="">#</th>
            <th class="">Username</th>
            <th class="">Email</th>
            
            <th class="">Health Center</th>
            <th class="">Fullname</th>
            <th class="">Mobile Number</th>
          
            <th class=" ">Status</th>
            <th class=" ">Action</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $sql = "SELECT * FROM health_facility";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
           
            ?>
        
            <tr id="row_<?php echo $row['id']; ?>">
                <td>
                <a class=" black-text modal-trigger" href="#modal<?php echo $row['id']; ?>"> <i class="material-icons  left">remove_red_eye</i></a>   
                
                <div id="modal<?php echo $row['id']; ?>" class="modal">
    <div class="modal-content">
      <h6 style="font-weight:bold;">View </h6>

      <ul class=" " >
      <li style="padding-top:10px;" class="collection-item"><b>#: </b><?php echo $row['id']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Username: </b><?php echo $row['username']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Birthdate: </b><?php echo $row['birthdate']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Age: </b><?php echo $row['age']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Email: </b><?php echo $row['email']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Health Center: </b><?php echo $row['health_center']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Address of Facility: </b><?php echo $row['AddressofFacility']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Fullname: </b><?php echo $row['fullname']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Item: </b><?php echo $row['item']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Mobile Number: </b><?php echo $row['mobile_number']; ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Date Created: </b><?php echo date('F d,Y',strtotime($row['date_created'])); ?></li>
      <li style="padding-top:10px;" class="collection-item"><b>Approved: </b><?php if($row['approved'] == 1) { echo 'YES'; } else { echo 'NO';} ?></li>
    
    </ul>
     
    </div>
    <div class="modal-footer">
      <a href="#!" class="modal-close waves-effect waves-blue blue-grey lighten-4 btn-flat small-btn">Close</a>
    </div>
  </div>
                <label>
                <input class="filled-in blue right" id="chkBox<?php echo $row['id']; ?>" onclick="enable(<?php echo $row['id']; ?>)" type="checkbox" />
        <span></span>
      </label>
    </td>
                <td  ><?php echo $row['id']; ?></td>
                <td  ><?php echo $row['username']; ?></td>
                <td  ><?php echo $row['email']; ?></td>
                <td style="font-weight:500;" class="teal-text"><?php echo $row['health_center']; ?></td>
                <td  ><?php echo $row['fullname']; ?></td>
                <td  ><?php echo $row['mobile_number']; ?></td>
                
                <?php if($row['approved'] == 1) {  ?>
                <td>  <span class="new badge teal accent-3" data-badge-caption="">Approved</span></td>
                <?php } else {  ?>
                    <td><span class="new badge blue-grey lighten-4 black-text" data-badge-caption="">Pending</div></td>
                    <?php } ?>
                <td>
                    <button disabled id="btn<?php echo $row['id']; ?>" class="edit-btn btn btn-small orange" style="font-weight:500; font-size:11px;" data-id="<?php echo $row['id']; ?>" 
                            data-username="<?php echo $row['username']; ?>"
                            data-email="<?php echo $row['email']; ?>"
                            data-health_center="<?php echo $row['health_center']; ?>"
                            data-fullname="<?php echo $row['fullname']; ?>"
                            data-mobile_number="<?php echo $row['mobile_number']; ?>"
                            data-date_created="<?php echo $row['date_created']; ?>">
                        Edit
                    </button>
                </td>
            </tr>
            <script>
function enable(rowId) {
    // Get the checkbox, button, and row
    var checkBox = document.getElementById("chkBox" + rowId);
    var btn = document.getElementById("btn" + rowId);
    var row = document.getElementById("row_" + rowId); // Correct row ID reference

    // Enable the button when checkbox is checked, disable when unchecked
    btn.disabled = !checkBox.checked;

    // Change the background color when checked, reset when unchecked
    row.style.backgroundColor = checkBox.checked ? "#e3f2fd" : "";
 
}
</script>





            <?php }   ?>

            <!-- Modal Structure -->




    </tbody>
    <!-- Modal Structure -->
<div id="editModal" class="modal  ">
    <div class="modal-content">
        <form id="editForm">
            <input  type="hidden" id="edit_id"  >
            
            <div class="input-field">
              
                 
                <input style="display:none;border:none !important;" type="text"     id="edit_username" readonly>
                <input style="display:none;border:none !important;" type="email"    id="edit_email" readonly>
                <p>Approve <input style="border:none !important;  " type="text"     id="edit_health_center" readonly></p>
                <input style="display:none;border:none !important;" type="text"     id="edit_fullname" readonly>
                <input style="display:none;border:none !important;" type="text"     id="edit_mobile_number" readonly>
        
            
       
       
         
            
            
            
 
               
                <button type="submit" class="btn blue darken-2">Yes  </button>
                <button type="button" class="btn red darken-2 modal-close" id="closeModal">Cancel</button>
            </div>
        </form>
    </div>
</div>
</table>

 



                    </div>


                   


</body>
</html>
 
<script>
function addRecord() {
    // Change button text and add loading icon
    document.getElementById('buttonText').innerHTML = '<i class="material-icons left">hourglass_empty</i>Loading...';
    
    // Simulate a delay for loading (replace with actual redirection or action)
    setTimeout(() => {
        window.location.href = 'nip.php';
    }, 200); // Adjust the delay as needed
}
</script>

<script>
    $(document).ready(function() {
    // Open modal and populate data
    $(".edit-btn").click(function() {
        $("#edit_id").val($(this).data("id"));
        $("#edit_username").val($(this).data("username"));
        $("#edit_email").val($(this).data("email"));
        $("#edit_health_center").val($(this).data("health_center"));
        $("#edit_fullname").val($(this).data("fullname"));
        $("#edit_mobile_number").val($(this).data("mobile_number"));

        $("#editModal").show();
    });

    // Close modal
    $("#closeModal").click(function() {
        $("#editModal").hide();
    });

    // AJAX to update the database
    $("#editForm").submit(function(e) {
        e.preventDefault();
        $.ajax({
            url: "update_user.php",
            type: "POST",
            data: {
                id: $("#edit_id").val(),
                username: $("#edit_username").val(),
                email: $("#edit_email").val(),
                health_center: $("#edit_health_center").val(),
                fullname: $("#edit_fullname").val(),
                mobile_number: $("#edit_mobile_number").val()
            },
            success: function(response) {
                if (response == "success") {
                    alert("Record updated successfully!");

                    // Update the table row dynamically
                    let id = $("#edit_id").val();
                    $("#row_" + id).html(`
               
                    <td> <input class="filled-in blue"   type="checkbox" />
        <span></span></td>
                        <td >${id}</td>
                        <td >${$("#edit_username").val()}</td>
                        <td >${$("#edit_email").val()}</td>
                        <td style="font-weight:bold;">${$("#edit_health_center").val()}</td>
                        <td >${$("#edit_fullname").val()}</td>
                        <td >${$("#edit_mobile_number").val()}</td>
                        <td><span class="new badge teal accent-3" data-badge-caption="">Approved</span></td>
                        <td> </td>
                    `);

                    $("#editModal").hide();
                } else {
                    alert("Failed to update!");
                }
            }
        });
    });
});

</script>

<script>
     $(document).ready(function(){
    $('.modal').modal();
  });
</script>


<script>
    new DataTable('#example', {
    layout: {
        topStart: {
            buttons: ['copy', 'csv', 'excel', 'pdf', 'print']
        }
    }
});
</script>