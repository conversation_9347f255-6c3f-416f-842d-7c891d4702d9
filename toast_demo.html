<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast Notification Demo - Required Fields</title>
    
    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <style>
        body {
            background-color: #f5f5f5;
            font-family: 'Roboto', sans-serif;
        }
        
        .container {
            margin-top: 50px;
        }
        
        .demo-card {
            padding: 30px;
            border-radius: 8px;
        }
        
        /* Error styling for required fields */
        .error {
            border-bottom: 2px solid #f44336 !important;
            box-shadow: 0 1px 0 0 #f44336 !important;
        }

        .error + label {
            color: #f44336 !important;
        }

        /* Error styling for select elements */
        .select-wrapper.error .select-dropdown {
            border-bottom: 2px solid #f44336 !important;
            box-shadow: 0 1px 0 0 #f44336 !important;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-title {
            color: #1976d2;
            font-weight: 500;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col s12 m8 offset-m2">
                <div class="card demo-card">
                    <h4 class="center-align blue-text text-darken-2">
                        <i class="material-icons left">notification_important</i>
                        Toast Notification Demo
                    </h4>
                    <p class="center-align grey-text">
                        This demo shows toast notifications for required fields validation
                    </p>
                    
                    <div class="demo-section">
                        <h5 class="demo-title">Sample Form with Required Fields</h5>
                        
                        <form id="demoForm">
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <input id="firstName" name="firstName" type="text">
                                    <label for="firstName">First Name *</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input id="lastName" name="lastName" type="text">
                                    <label for="lastName">Last Name *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <input id="email" name="email" type="email">
                                    <label for="email">Email *</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input id="birthdate" name="birthdate" type="date">
                                    <label for="birthdate">Birth Date *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <select id="gender" name="gender">
                                        <option value="" disabled selected>Choose your option</option>
                                        <option value="male">Male</option>
                                        <option value="female">Female</option>
                                    </select>
                                    <label for="gender">Gender *</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input id="phone" name="phone" type="tel">
                                    <label for="phone">Phone Number *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12">
                                    <h6>Preferred Contact Method *</h6>
                                    <p>
                                        <label>
                                            <input name="contactMethod" type="radio" value="email" />
                                            <span>Email</span>
                                        </label>
                                    </p>
                                    <p>
                                        <label>
                                            <input name="contactMethod" type="radio" value="phone" />
                                            <span>Phone</span>
                                        </label>
                                    </p>
                                    <p>
                                        <label>
                                            <input name="contactMethod" type="radio" value="mail" />
                                            <span>Mail</span>
                                        </label>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12">
                                    <button class="btn waves-effect waves-light blue darken-2" type="submit">
                                        <i class="material-icons left">send</i>
                                        Submit Form
                                    </button>
                                    <button class="btn waves-effect waves-light grey" type="button" id="clearForm" style="margin-left: 10px;">
                                        <i class="material-icons left">clear</i>
                                        Clear Form
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <div class="demo-section">
                        <h5 class="demo-title">Manual Toast Examples</h5>
                        <div class="row">
                            <div class="col s12 m6 l3">
                                <button class="btn red darken-1 waves-effect waves-light full-width" onclick="showErrorToast()" style="margin-bottom: 10px;">
                                    <i class="material-icons left">error</i>
                                    Error Toast
                                </button>
                            </div>
                            <div class="col s12 m6 l3">
                                <button class="btn green darken-1 waves-effect waves-light full-width" onclick="showSuccessToast()" style="margin-bottom: 10px;">
                                    <i class="material-icons left">check_circle</i>
                                    Success Toast
                                </button>
                            </div>
                            <div class="col s12 m6 l3">
                                <button class="btn blue darken-1 waves-effect waves-light full-width" onclick="showInfoToast()" style="margin-bottom: 10px;">
                                    <i class="material-icons left">info</i>
                                    Info Toast
                                </button>
                            </div>
                            <div class="col s12 m6 l3">
                                <button class="btn orange darken-1 waves-effect waves-light full-width" onclick="showWarningToast()" style="margin-bottom: 10px;">
                                    <i class="material-icons left">warning</i>
                                    Warning Toast
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s12 m6 offset-m3">
                                <button class="btn blue darken-1 waves-effect waves-light full-width" onclick="showLoadingToast()">
                                    <i class="material-icons left">hourglass_empty</i>
                                    Loading Toast
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Materialize JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Materialize components
            M.FormSelect.init(document.querySelectorAll('select'));
            M.updateTextFields();

            // Enhanced toast notification function
            function showToast(message, type = 'info', duration = 4000) {
                let classes = 'blue darken-1'; // default
                let icon = 'info';

                switch(type) {
                    case 'success':
                        classes = 'green darken-1';
                        icon = 'check_circle';
                        break;
                    case 'error':
                        classes = 'red darken-1';
                        icon = 'error';
                        break;
                    case 'warning':
                        classes = 'orange darken-1';
                        icon = 'warning';
                        break;
                    case 'loading':
                        classes = 'blue darken-1';
                        icon = 'hourglass_empty';
                        break;
                }

                M.toast({
                    html: `<i class="material-icons left">${icon}</i>${message}`,
                    classes: classes,
                    displayLength: duration
                });
            }

            // Function to show toast notification for required fields
            function showRequiredFieldToast(fieldName, element) {
                showToast(`${fieldName} is required`, 'error');

                // Add error styling
                element.classList.add('error');
                element.focus();
            }

            // Function to validate required fields
            function validateRequiredFields() {
                const requiredFields = [
                    { name: 'firstName', label: 'First Name' },
                    { name: 'lastName', label: 'Last Name' },
                    { name: 'email', label: 'Email' },
                    { name: 'birthdate', label: 'Birth Date' },
                    { name: 'gender', label: 'Gender' },
                    { name: 'phone', label: 'Phone Number' },
                    { name: 'contactMethod', label: 'Preferred Contact Method' }
                ];

                const form = document.getElementById('demoForm');
                
                // Clear previous error styles
                form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));

                for (let field of requiredFields) {
                    const element = document.querySelector(`[name="${field.name}"]`);
                    
                    if (!element) continue;
                    
                    // Check different input types
                    if (element.type === 'radio') {
                        const radioGroup = document.querySelectorAll(`[name="${field.name}"]`);
                        const isChecked = Array.from(radioGroup).some(radio => radio.checked);
                        if (!isChecked) {
                            showRequiredFieldToast(field.label, radioGroup[0]);
                            return false;
                        }
                    } else if (element.tagName === 'SELECT') {
                        if (!element.value || element.value === '') {
                            showRequiredFieldToast(field.label, element);
                            return false;
                        }
                    } else {
                        if (!element.value || element.value.trim() === '') {
                            showRequiredFieldToast(field.label, element);
                            return false;
                        }
                    }
                }
                
                return true;
            }

            // Form submission handler
            document.getElementById('demoForm').addEventListener('submit', function(e) {
                // Always prevent default form submission
                e.preventDefault();

                if (validateRequiredFields()) {
                    // Show loading toast
                    showToast('Validating form...', 'loading', 1000);

                    // Simulate form processing
                    setTimeout(() => {
                        showToast('Form validation passed! All required fields are filled.', 'success');

                        // Show additional success info
                        setTimeout(() => {
                            showToast('Form is ready for submission', 'info', 3000);
                        }, 1000);
                    }, 1000);
                }
            });
            
            // Clear form handler
            document.getElementById('clearForm').addEventListener('click', function() {
                document.getElementById('demoForm').reset();
                document.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
                M.FormSelect.init(document.querySelectorAll('select'));
                M.updateTextFields();
                
                showToast('Form cleared', 'info', 2000);
            });
        });
        
        // Manual toast examples using the enhanced toast function
        function showErrorToast() {
            showToast('This is an error message', 'error');
        }

        function showSuccessToast() {
            showToast('Operation completed successfully!', 'success');
        }

        function showInfoToast() {
            showToast('This is an informational message', 'info');
        }

        function showWarningToast() {
            showToast('This is a warning message', 'warning');
        }

        function showLoadingToast() {
            showToast('Loading...', 'loading', 2000);
        }
    </script>
</body>
</html>
