<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta charset="UTF-8">
    <title>National Immunization Program</title>
    <?php include 'style.php'; ?>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
      
     
        .container {
            background: #ffffff;
            padding: 40px;
            
            margin: 2em auto;
           
            
            color: #1a1a1a;
            
        }

        h4 {
            font-weight: 700;
            font-size: 26px;
            margin: 25px 0;
         
            text-align: center;
            letter-spacing: -0.5px;
        }

        .blue-text {
            color: #1565c0;
            
            font-size: 24px;
            display: block;
            
        }

        p {
            font-size: 17.2px;
        
          
            text-align: center;
            margin: 20px 0;
        }
 
        
td,tr,th {
    padding-top:10px;
    padding-bottom:10px;
  border-bottom:0;
  border:1px solid #ddd;
}
        img {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 1em;
            }

            h4 {
                font-size: 24px;
            }

            .black-text {
                font-size: 22px;
            }

            .blue-text {
                font-size: 20px;
            }

            p {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <?php include 'nav.php'; ?>
    <br>
    <br>
    <div class="row ">
        
        <div class="col s12 m12 container offset-m1"  >
        <div class="z-depth-1 ">
        
        <h5 class="  white-text" style="background:#0d47a1; padding-top:14px;padding-bottom:14px;padding-left:10px; font-size:16.7px;font-weight:500 !important;"><i class="material-icons left">note_add</i>Verify Record</h5>
        
        
        <div class="row  " style="margin-left:10px;"> 
        <div class="input-field col s12 m10 ">
        <input type="text" id="searchInput" placeholder="">
        <label for="searchInput">Enter Child's Family Serial Number</label>
    </div>
    <div class="input-field col s12 m2 ">
    <button class="btn blue darken-1" id="searchBtn">Search</button>
</div>

</div> 

</div>

<!-- Modal Structure -->
<div id="searchModal" class="modal white" >
    <h4 class="grey lighten-2" style="padding-top:15px;padding-bottom:15px;">Search Result</h4>
    <div class="modal-content " id="modalContent">
        <p id="resultData" style="text-align:start;font-size:13.3px;padding:0px;">Loading...</p>
    </div>
    <div class="modal-footer">
        <a href="#!" class="modal-close btn-flat">Close</a>
    </div>

</div>
 

</body>
</html>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.querySelectorAll('.modal');


    M.Modal.init(modal);

    document.getElementById('searchBtn').addEventListener('click', function() {
        const searchValue = document.getElementById('searchInput').value.trim();
        if (!searchValue) return;
       
        fetch(`search_handler.php?name=${encodeURIComponent(searchValue)}`)
            .then(res => res.json())
            .then(data => {
                let output = '';
                if (data.success) {
                    const d = data.result;
                    output = `
    <table class="responsive-table" style="width:100%;">
        <tbody>
            <tr>
                <th>Name of Child</th>
                <td colspan="3"><strong>${d['NameOfChild']} ${d['lastnameOfChild']}</strong></td>
            </tr>
            <tr>
                <th>Date of Birth</th>
                <td>${d['DateOfBirth']}</td>
                <th>Barangay</th>
                <td>${d['Barangay']}</td>
            </tr>
            <tr>
                <th>Mother's Name</th>
                <td colspan="3">${d['NameofMother']}</td>
            </tr>
            <tr>
                <th>Place of Delivery</th>
                <td colspan="3">${d['PlaceofDelivery']}</td>
            </tr>
        </tbody>
    </table>
    <br>
  <button 
    class="btn small-btn teal darken-2" 
    onclick="this.disabled = true; this.innerText = 'Approved'; this.classList.remove('teal', 'darken-2'); this.classList.add('red');">
    Approve
</button>

`;



                } else {
                    output = '<span class="red-text">No record found.</span>';
                }
                document.getElementById('resultData').innerHTML = output;
                var instance = M.Modal.getInstance(document.getElementById('searchModal'));
                instance.open();
            }).catch(err => {
                document.getElementById('resultData').innerHTML = 'Error fetching data.';
                var instance = M.Modal.getInstance(document.getElementById('searchModal'));
                instance.open();
            });
    });
});
</script>
