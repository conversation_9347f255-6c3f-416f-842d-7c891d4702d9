<?php
session_start();

// Clear remember me cookies and database tokens if they exist
if (isset($_CO<PERSON>IE['remember_user']) && isset($_COOKIE['remember_token'])) {
    include 'db.php';

    $health_center = $_COOKIE['remember_user'];
    $remember_token = $_COOKIE['remember_token'];

    // Clear remember token from database
    $stmt = $conn->prepare("UPDATE health_facility SET remember_token = NULL WHERE health_center = ? AND remember_token = ?");
    $stmt->bind_param("ss", $health_center, $remember_token);
    $stmt->execute();
    $stmt->close();
    $conn->close();

    // Clear remember me cookies
    setcookie('remember_user', '', time() - 3600, '/');
    setcookie('remember_token', '', time() - 3600, '/');
}

// Unset all session variables
$_SESSION = array();

// If it's desired to kill the session, also delete the session cookie.
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy the session
session_destroy();

// Redirect the user to the login page
header('Location: login.php');
exit();
?>