<?php 
session_start();
error_reporting(0);

$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Connection failed: ' . mysqli_connect_error());
}

date_default_timezone_set('Asia/Manila');

if (isset($_POST['submitBtn'])) {

    $today = date("Y-m-d");
    $rand = strtoupper(substr(uniqid(sha1(time())), 0, 10));

    $DateOfRegistration = date('Y-m-d');
    $DateOfBirth = $_POST['DateOfBirth'] ?? null; 
    $AgeofChild = $_POST['AgeofChild'] ?? null;
    $FamilySerialNumber = 'CHO-' . $today . $rand;
    $ChildNumber = rand(100, 999);
    $LastNameOfChild = $_POST['LastNameOfChild'] ?? null;
    $NameOfChild = $_POST['NameOfChild'] ?? null;
    $middlename_of_child = $_POST['middlename_of_child'] ?? null;
    $Sex = $_POST['Sex'] ?? null;
    $NameofMother = $_POST['NameofMother'] ?? null;
    $BirthdateofMother = $_POST['BirthdateofMother'] ?? null;
    $AgeeofMother = $_POST['AgeeofMother'] ?? null;
    $Barangay = $_POST['Barangay'] ?? null;
    $PurokStreetSitio = $_POST['PurokStreetSitio'] ?? null;
    $HouseNo = $_POST['HouseNo'] ?? null;
    $Address = $_POST['Address'] ?? null;
    $PlaceofDelivery = $_POST['PlaceofDelivery'] ?? null;
    $NameofFacility = $_POST['NameofFacility'] ?? null;
    $Attendant = $_POST['Attendant'] ?? null;
    $TypeofDelivery = $_POST['TypeofDelivery'] ?? null;
    $PhoneNumber = $_POST['PhoneNumber'] ?? null;
    $approvedBy = $_SESSION['fullname'] ?? "Unknown";

    $dateOfExpiration = date('Y-m-d', strtotime('+365 days'));

    // **Check for Duplicate Entry**
    $checkStmt = $conn->prepare("SELECT id FROM nip_table WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ?");
    $checkStmt->bind_param("sss", $NameOfChild, $LastNameOfChild, $DateOfBirth);
    $checkStmt->execute();
    $checkStmt->store_result();

    if ($checkStmt->num_rows > 0) {
        echo '<div class="card yellow lighten-4 yellow-text text-darken-4">
                <div class="card-content">
                    <p>Duplicate record found! This child is already registered.</p>
                </div>
              </div>';
    } elseif (empty($DateOfBirth) || empty($NameOfChild) || empty($LastNameOfChild) || empty($Attendant) || empty($PlaceofDelivery) || empty($TypeofDelivery)) {
        echo '<div class="card red lighten-4 red-text text-darken-4">
                <div class="card-content">
                    <p><i class="material-icons left">report</i>Missing required fields. Please complete the form.</p>
                </div>
              </div>';
    } else {
        // **Prepare SQL Statement to Prevent SQL Injection**
        $stmt = $conn->prepare("INSERT INTO nip_table (DateOfRegistration, DateOfBirth, AgeofChild, FamilySerialNumber, ChildNumber, LastNameOfChild, NameOfChild, middlename_of_child, Sex, NameofMother, BirthdateofMother, Age, Barangay, PurokStreetSitio, HouseNo, Address, PlaceofDelivery, NameofFacility, Attendant, TypeofDelivery, PhoneNumber, approvedBy, dateOfExpiration) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        $stmt->bind_param("sssisssssssssssssssssss", $DateOfRegistration, $DateOfBirth, $AgeofChild, $FamilySerialNumber, $ChildNumber, $LastNameOfChild, $NameOfChild, $middlename_of_child, $Sex, $NameofMother, $BirthdateofMother, $AgeeofMother, $Barangay, $PurokStreetSitio, $HouseNo, $Address, $PlaceofDelivery, $NameofFacility, $Attendant, $TypeofDelivery, $PhoneNumber, $approvedBy, $dateOfExpiration);

        if ($stmt->execute()) {
            echo '<div class="card green lighten-4 green-text text-darken-4">
                    <div class="card-content">
                        <p>Registration successful!</p>
                    </div>
                  </div>';
        } else {
            echo '<div class="card red lighten-4 red-text text-darken-4">
                    <div class="card-content">
                        <p>Error: ' . $stmt->error . '</p>
                    </div>
                  </div>';
        }
        
        $stmt->close();
    }
    
    $checkStmt->close();
}
$conn->close();
?>
