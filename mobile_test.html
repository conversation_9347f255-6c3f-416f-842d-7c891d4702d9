<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile View Test - NIP Filter</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .device-frame {
            max-width: 375px;
            margin: 0 auto;
            border: 8px solid #333;
            border-radius: 25px;
            background: #333;
            padding: 20px 8px;
            position: relative;
        }
        
        .device-screen {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            min-height: 600px;
        }
        
        .enhancement-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #2196f3;
        }
        
        .responsive-demo {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .demo-header {
            background: #2196f3;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
        }
        
        .demo-content {
            padding: 15px;
        }
        
        /* Simulate mobile styles for demo */
        .mobile-simulation {
            max-width: 375px;
            margin: 0 auto;
            border: 1px solid #ccc;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .mobile-simulation .card {
            margin: 0.25rem 0 1rem 0;
        }
        
        .mobile-simulation .card-content {
            padding: 15px;
        }
        
        .mobile-simulation .card-title {
            font-size: 14px !important;
            padding: 8px !important;
        }
        
        .mobile-simulation .chip {
            font-size: 12px;
            height: 28px;
            line-height: 28px;
            margin: 10px 0;
        }
        
        .mobile-simulation .btn {
            width: 100%;
            margin: 5px 0;
            font-size: 13px;
            height: 40px;
            line-height: 40px;
        }
        
        .mobile-simulation table {
            font-size: 11px;
            min-width: 100%;
        }
        
        .mobile-simulation th, 
        .mobile-simulation td {
            padding: 6px 4px;
            font-size: 10px;
        }
        
        .table-scroll-demo {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .table-scroll-demo table {
            min-width: 800px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        @media screen and (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .device-frame {
                border: none;
                background: transparent;
                padding: 0;
                max-width: 100%;
            }
            
            .device-screen {
                border-radius: 0;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-section">
            <h2 class="center-align">📱 Mobile View Enhancements</h2>
            <p class="center-align">Enhanced mobile responsiveness for the NIP Filter Records page</p>
        </div>

        <div class="demo-section">
            <h3>✨ Mobile Enhancements Applied</h3>
            <div class="enhancement-list">
                <h4>Key Mobile Improvements:</h4>
                <ul>
                    <li><strong>Responsive Layout:</strong> Optimized for screens 768px and below</li>
                    <li><strong>Touch-Friendly Buttons:</strong> Minimum 44px height for better touch targets</li>
                    <li><strong>Horizontal Scrolling Tables:</strong> Tables scroll horizontally on small screens</li>
                    <li><strong>Sticky Table Headers:</strong> Headers stay visible while scrolling</li>
                    <li><strong>Optimized Typography:</strong> Smaller fonts that remain readable</li>
                    <li><strong>Improved Spacing:</strong> Reduced margins and padding for mobile</li>
                    <li><strong>Stacked Buttons:</strong> Form buttons stack vertically on mobile</li>
                    <li><strong>Compressed Cards:</strong> Reduced card padding for more content</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 Responsive Breakpoints</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h5>📱 Mobile (≤ 480px)</h5>
                    <ul>
                        <li>Ultra-compact layout</li>
                        <li>9px table font size</li>
                        <li>36px button height</li>
                        <li>Minimal padding</li>
                        <li>Full-width elements</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>📱 Tablet (≤ 768px)</h5>
                    <ul>
                        <li>Compact layout</li>
                        <li>10px table font size</li>
                        <li>40px button height</li>
                        <li>Horizontal table scroll</li>
                        <li>Sticky headers</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h5>🖥️ Desktop (> 768px)</h5>
                    <ul>
                        <li>Full desktop layout</li>
                        <li>14px table font size</li>
                        <li>Standard button sizes</li>
                        <li>No horizontal scroll</li>
                        <li>Original spacing</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📋 Table Enhancements</h3>
            <div class="responsive-demo">
                <div class="demo-header">Mobile Table Features</div>
                <div class="demo-content">
                    <h5>Horizontal Scrolling Demo:</h5>
                    <div class="table-scroll-demo">
                        <table class="striped">
                            <thead>
                                <tr style="background: #2196f3; color: white;">
                                    <th>ID</th>
                                    <th>Registration Date</th>
                                    <th>Full Name</th>
                                    <th>Birth Date</th>
                                    <th>Sex</th>
                                    <th>Mother</th>
                                    <th>Barangay</th>
                                    <th>Phone</th>
                                    <th>Family Serial</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>001</td>
                                    <td>2024-01-15</td>
                                    <td>Juan Dela Cruz</td>
                                    <td>2023-05-10</td>
                                    <td>MALE</td>
                                    <td>Maria Dela Cruz</td>
                                    <td>Barangay 1</td>
                                    <td>09123456789</td>
                                    <td>FS-001</td>
                                    <td><a href="#" class="btn-small blue">View</a></td>
                                </tr>
                                <tr>
                                    <td>002</td>
                                    <td>2024-01-16</td>
                                    <td>Maria Santos</td>
                                    <td>2023-06-15</td>
                                    <td>FEMALE</td>
                                    <td>Ana Santos</td>
                                    <td>Barangay 2</td>
                                    <td>09987654321</td>
                                    <td>FS-002</td>
                                    <td><a href="#" class="btn-small blue">View</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p><small><em>← Swipe left/right to see more columns →</em></small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 Touch-Friendly Features</h3>
            <div class="comparison-grid">
                <div class="responsive-demo">
                    <div class="demo-header">Before (Desktop Only)</div>
                    <div class="demo-content">
                        <button class="btn blue" style="height: 32px; line-height: 32px; font-size: 12px;">Small Button</button>
                        <p><small>32px height - Hard to tap on mobile</small></p>
                        
                        <input type="text" style="height: 32px; font-size: 12px;" placeholder="Small Input">
                        <p><small>32px height - Difficult to focus</small></p>
                    </div>
                </div>
                
                <div class="responsive-demo">
                    <div class="demo-header">After (Touch-Friendly)</div>
                    <div class="demo-content">
                        <button class="btn blue" style="height: 44px; line-height: 44px; font-size: 14px;">Touch Button</button>
                        <p><small>44px height - Easy to tap</small></p>
                        
                        <input type="text" style="height: 44px; font-size: 14px;" placeholder="Touch Input">
                        <p><small>44px height - Easy to focus</small></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📱 Mobile Layout Simulation</h3>
            <p>This shows how the filter page looks on mobile devices:</p>
            
            <div class="mobile-simulation">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title blue-grey lighten-5" style="padding: 8px; font-size: 14px;">
                            <i class="material-icons left">search</i>
                            Filter Child Records
                        </span>
                        
                        <div class="chip blue white-text" style="margin: 10px 0; font-size: 12px;">
                            <i class="material-icons left">location_on</i>
                            Health Center: Barangay 1
                        </div>
                        
                        <div class="row">
                            <div class="input-field col s12">
                                <input type="text" id="mobile_first_name" style="height: 2.3rem; font-size: 13px;">
                                <label for="mobile_first_name" style="font-size: 0.9rem;">First Name</label>
                            </div>
                            
                            <div class="input-field col s12">
                                <input type="text" id="mobile_last_name" style="height: 2.3rem; font-size: 13px;">
                                <label for="mobile_last_name" style="font-size: 0.9rem;">Last Name</label>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col s12">
                                <button class="btn cyan btn-small waves-effect" style="width: 100%; margin: 5px 0; height: 40px;">
                                    <i class="material-icons left">search</i>Search Records
                                </button>
                            </div>
                            <div class="col s12">
                                <button class="btn grey darken-1 btn-small waves-effect" style="width: 100%; margin: 5px 0; height: 40px;">
                                    <i class="material-icons left">clear</i>Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-content">
                        <span class="card-title" style="font-size: 14px;">
                            <i class="material-icons left">list</i>Search Results
                        </span>
                        <p style="font-size: 13px; text-align: center;">Found 2 record(s)</p>
                        
                        <div style="overflow-x: auto; -webkit-overflow-scrolling: touch;">
                            <table style="font-size: 10px; min-width: 600px;">
                                <thead>
                                    <tr style="background: #2196f3; color: white;">
                                        <th style="padding: 4px;">ID</th>
                                        <th style="padding: 4px;">Name</th>
                                        <th style="padding: 4px;">Birth Date</th>
                                        <th style="padding: 4px;">Sex</th>
                                        <th style="padding: 4px;">Mother</th>
                                        <th style="padding: 4px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 4px;">001</td>
                                        <td style="padding: 4px;">Juan Cruz</td>
                                        <td style="padding: 4px;">2023-05-10</td>
                                        <td style="padding: 4px;">MALE</td>
                                        <td style="padding: 4px;">Maria Cruz</td>
                                        <td style="padding: 4px;"><a href="#" class="btn-small blue">View</a></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p style="font-size: 11px; text-align: center; color: #999; margin-top: 10px;">
                            ← Swipe to see more →
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🔧 Technical Implementation</h3>
            <div class="enhancement-list">
                <h4>CSS Media Queries Added:</h4>
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>/* Mobile Responsive Enhancements */
@media screen and (max-width: 768px) {
    .filter-container { margin-top: 10px; padding: 0 5px; }
    .card-content { padding: 15px; }
    .btn { width: 100%; margin: 5px 0; height: 40px; }
    table { font-size: 11px; min-width: 800px; }
    th, td { padding: 6px 4px; font-size: 10px; }
    .results-table { overflow-x: auto; -webkit-overflow-scrolling: touch; }
}

@media screen and (max-width: 480px) {
    .card-content { padding: 10px; }
    .btn { height: 36px; font-size: 12px; }
    table { font-size: 10px; min-width: 700px; }
    th, td { padding: 4px 3px; font-size: 9px; }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .btn { min-height: 44px; }
    input[type=text], input[type=date] { min-height: 44px; }
}</code></pre>
            </div>
        </div>

        <div class="demo-section">
            <h3>✅ Benefits of Mobile Enhancements</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h5>👆 Better Touch Experience</h5>
                    <p>Larger touch targets (44px minimum) make buttons and inputs easier to tap on mobile devices.</p>
                </div>
                
                <div class="feature-card">
                    <h5>📊 Readable Tables</h5>
                    <p>Horizontal scrolling with sticky headers ensures all data remains accessible on small screens.</p>
                </div>
                
                <div class="feature-card">
                    <h5>⚡ Faster Loading</h5>
                    <p>Optimized layouts and smaller fonts reduce rendering time on mobile devices.</p>
                </div>
                
                <div class="feature-card">
                    <h5>🎯 Better Usability</h5>
                    <p>Stacked buttons and full-width elements provide a more intuitive mobile experience.</p>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h3>🔗 Test the Mobile Enhancements</h3>
            <p>Open the filter page on your mobile device or use browser developer tools to test mobile view.</p>
            <a href="filter_records.php" class="btn large blue waves-effect">
                <i class="material-icons left">smartphone</i>Test Mobile Filter Page
            </a>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
