<?php
session_start();
include 'db.php';
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Create notifications table if it doesn't exist
$create_table_sql = "
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    health_center VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    status ENUM('unread', 'read', 'dismissed') DEFAULT 'unread',
    created_at DATETIME NOT NULL,
    expires_at DATETIME NULL,
    action_url VARCHAR(500) NULL,
    action_text VARCHAR(100) NULL,
    metadata JSON NULL,
    INDEX idx_health_center (health_center),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_priority (priority)
)";

$conn->query($create_table_sql);

// Function to create notification
function createNotification($conn, $data) {
    $stmt = $conn->prepare("
        INSERT INTO notifications 
        (health_center, type, title, message, priority, created_at, expires_at, action_url, action_text, metadata) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->bind_param("ssssssssss", 
        $data['health_center'], $data['type'], $data['title'], 
        $data['message'], $data['priority'], $data['created_at'],
        $data['expires_at'], $data['action_url'], $data['action_text'],
        $data['metadata']
    );
    
    $result = $stmt->execute();
    $notification_id = $conn->insert_id;
    $stmt->close();
    
    return $result ? $notification_id : false;
}

// Function to get notifications
function getNotifications($conn, $health_center, $status = null, $limit = 50) {
    $sql = "SELECT * FROM notifications WHERE health_center = ?";
    $params = [$health_center];
    $types = "s";
    
    if ($status) {
        $sql .= " AND status = ?";
        $params[] = $status;
        $types .= "s";
    }
    
    // Don't show expired notifications
    $sql .= " AND (expires_at IS NULL OR expires_at > NOW())";
    $sql .= " ORDER BY priority DESC, created_at DESC LIMIT ?";
    $params[] = $limit;
    $types .= "i";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $notifications[] = $row;
    }
    
    $stmt->close();
    return $notifications;
}

// Function to mark notification as read
function markAsRead($conn, $notification_id, $health_center) {
    $stmt = $conn->prepare("UPDATE notifications SET status = 'read' WHERE id = ? AND health_center = ?");
    $stmt->bind_param("is", $notification_id, $health_center);
    $result = $stmt->execute();
    $stmt->close();
    return $result;
}

// Function to dismiss notification
function dismissNotification($conn, $notification_id, $health_center) {
    $stmt = $conn->prepare("UPDATE notifications SET status = 'dismissed' WHERE id = ? AND health_center = ?");
    $stmt->bind_param("is", $notification_id, $health_center);
    $result = $stmt->execute();
    $stmt->close();
    return $result;
}

// Function to generate system notifications based on data analysis
function generateSystemNotifications($conn, $health_center) {
    $notifications = [];
    
    // Check for data quality issues
    $quality_stmt = $conn->prepare("
        SELECT COUNT(*) as missing_names
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (NameOfChild IS NULL OR NameOfChild = '' OR lastnameOfChild IS NULL OR lastnameOfChild = '')
    ");
    $quality_stmt->bind_param("s", $health_center);
    $quality_stmt->execute();
    $missing_names = $quality_stmt->get_result()->fetch_assoc()['missing_names'];
    $quality_stmt->close();
    
    if ($missing_names > 10) {
        $notifications[] = [
            'type' => 'data_quality',
            'title' => 'Data Quality Alert',
            'message' => "Found {$missing_names} records with missing child names. Please review and update these records to improve data quality.",
            'priority' => $missing_names > 50 ? 'high' : 'medium',
            'action_url' => 'data_quality_checker.php',
            'action_text' => 'Review Data Quality'
        ];
    }
    
    // Check for recent downloads
    $download_stmt = $conn->prepare("
        SELECT COUNT(*) as recent_downloads
        FROM download_history 
        WHERE health_center = ? AND download_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $download_stmt->bind_param("s", $health_center);
    $download_stmt->execute();
    $recent_downloads = $download_stmt->get_result()->fetch_assoc()['recent_downloads'];
    $download_stmt->close();
    
    if ($recent_downloads > 10) {
        $notifications[] = [
            'type' => 'security',
            'title' => 'High Download Activity',
            'message' => "Detected {$recent_downloads} Excel downloads in the last 24 hours. Please verify all downloads are authorized.",
            'priority' => 'medium',
            'action_url' => 'download_history.php',
            'action_text' => 'View Download History'
        ];
    }
    
    // Check for old records without immunization status
    $immunization_stmt = $conn->prepare("
        SELECT COUNT(*) as missing_immunization
        FROM nip_table 
        WHERE deleted = 0 AND Barangay = ? 
        AND (IMMUNIZATIONSTATUS IS NULL OR IMMUNIZATIONSTATUS = '')
        AND DateOfRegistration < DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $immunization_stmt->bind_param("s", $health_center);
    $immunization_stmt->execute();
    $missing_immunization = $immunization_stmt->get_result()->fetch_assoc()['missing_immunization'];
    $immunization_stmt->close();
    
    if ($missing_immunization > 5) {
        $notifications[] = [
            'type' => 'data_update',
            'title' => 'Immunization Status Update Needed',
            'message' => "Found {$missing_immunization} older records without immunization status. Consider updating these records.",
            'priority' => 'low',
            'action_url' => 'advanced_data_filter.php',
            'action_text' => 'Filter Records'
        ];
    }
    
    // Check for system updates or maintenance
    $last_backup_check = $conn->query("SELECT UNIX_TIMESTAMP() - UNIX_TIMESTAMP(MAX(download_time)) as hours_since_last_download FROM download_history WHERE health_center = '$health_center'");
    if ($last_backup_check) {
        $hours_since = $last_backup_check->fetch_assoc()['hours_since_last_download'] / 3600;
        
        if ($hours_since > 168) { // 1 week
            $notifications[] = [
                'type' => 'maintenance',
                'title' => 'Regular Backup Reminder',
                'message' => 'It has been over a week since your last data download. Consider creating a backup of your records.',
                'priority' => 'low',
                'action_url' => 'advanced_excel_manager.php',
                'action_text' => 'Download Backup'
            ];
        }
    }
    
    return $notifications;
}

try {
    $health_center = $_SESSION['health_center'];
    $action = $_GET['action'] ?? 'get_notifications';
    
    switch ($action) {
        case 'get_notifications':
            $status = $_GET['status'] ?? null;
            $limit = intval($_GET['limit'] ?? 50);
            
            $notifications = getNotifications($conn, $health_center, $status, $limit);
            
            echo json_encode([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications)
            ]);
            break;
            
        case 'mark_read':
            $notification_id = intval($_GET['id'] ?? 0);
            
            if ($notification_id > 0) {
                $result = markAsRead($conn, $notification_id, $health_center);
                echo json_encode([
                    'success' => $result,
                    'message' => $result ? 'Notification marked as read' : 'Failed to mark notification as read'
                ]);
            } else {
                echo json_encode(['error' => 'Invalid notification ID']);
            }
            break;
            
        case 'dismiss':
            $notification_id = intval($_GET['id'] ?? 0);
            
            if ($notification_id > 0) {
                $result = dismissNotification($conn, $notification_id, $health_center);
                echo json_encode([
                    'success' => $result,
                    'message' => $result ? 'Notification dismissed' : 'Failed to dismiss notification'
                ]);
            } else {
                echo json_encode(['error' => 'Invalid notification ID']);
            }
            break;
            
        case 'generate_system_notifications':
            $system_notifications = generateSystemNotifications($conn, $health_center);
            $created_count = 0;
            
            foreach ($system_notifications as $notification) {
                // Check if similar notification already exists
                $check_stmt = $conn->prepare("
                    SELECT COUNT(*) as count 
                    FROM notifications 
                    WHERE health_center = ? AND type = ? AND status != 'dismissed' 
                    AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ");
                $check_stmt->bind_param("ss", $health_center, $notification['type']);
                $check_stmt->execute();
                $existing = $check_stmt->get_result()->fetch_assoc()['count'];
                $check_stmt->close();
                
                if ($existing == 0) {
                    $notification_data = [
                        'health_center' => $health_center,
                        'type' => $notification['type'],
                        'title' => $notification['title'],
                        'message' => $notification['message'],
                        'priority' => $notification['priority'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days')),
                        'action_url' => $notification['action_url'] ?? null,
                        'action_text' => $notification['action_text'] ?? null,
                        'metadata' => null
                    ];
                    
                    if (createNotification($conn, $notification_data)) {
                        $created_count++;
                    }
                }
            }
            
            echo json_encode([
                'success' => true,
                'created_count' => $created_count,
                'message' => "Generated {$created_count} new system notifications"
            ]);
            break;
            
        case 'get_counts':
            $unread_stmt = $conn->prepare("
                SELECT COUNT(*) as count 
                FROM notifications 
                WHERE health_center = ? AND status = 'unread' 
                AND (expires_at IS NULL OR expires_at > NOW())
            ");
            $unread_stmt->bind_param("s", $health_center);
            $unread_stmt->execute();
            $unread_count = $unread_stmt->get_result()->fetch_assoc()['count'];
            $unread_stmt->close();
            
            $high_priority_stmt = $conn->prepare("
                SELECT COUNT(*) as count 
                FROM notifications 
                WHERE health_center = ? AND priority IN ('high', 'critical') AND status = 'unread'
                AND (expires_at IS NULL OR expires_at > NOW())
            ");
            $high_priority_stmt->bind_param("s", $health_center);
            $high_priority_stmt->execute();
            $high_priority_count = $high_priority_stmt->get_result()->fetch_assoc()['count'];
            $high_priority_stmt->close();
            
            echo json_encode([
                'success' => true,
                'unread_count' => $unread_count,
                'high_priority_count' => $high_priority_count
            ]);
            break;
            
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Notification system error: ' . $e->getMessage()
    ]);
}
?>
