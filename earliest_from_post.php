<?php
/**
 * Function to get earliest consultation date from POST data
 * Works with your exact POST variable structure
 */

function printEarliestDateFromPost() {
    // Create data array from POST variables
    $consultation_data = [
        'DateofConsultationBCG' => $_POST['DateofConsultationBCG'] ?? '',
        'DateofConsultationPENTAHIB1' => $_POST['DateofConsultationPENTAHIB1'] ?? '',
        'DateofConsultationPENTAHIB2' => $_POST['DateofConsultationPENTAHIB2'] ?? '',
        'DateofConsultationPENTAHIB3' => $_POST['DateofConsultationPENTAHIB3'] ?? '',
        'DateofConsultationOPV1v' => $_POST['DateofConsultationOPV1v'] ?? '',
        'DateofConsultationOPV2' => $_POST['DateofConsultationOPV2'] ?? '',
        'DateofConsultationOPV3' => $_POST['DateofConsultationOPV3'] ?? '',
        'DateofConsultationIPV1' => $_POST['DateofConsultationIPV1'] ?? '',
        'DateofConsultationIPV2' => $_POST['DateofConsultationIPV2'] ?? '',
        'DateofConsultationPCV1' => $_POST['DateofConsultationPCV1'] ?? '',
        'DateofConsultationPCV2' => $_POST['DateofConsultationPCV2'] ?? '',
        'DateofConsultationPCV3' => $_POST['DateofConsultationPCV3'] ?? '',
        'DateofConsultationHEPAatBirth' => $_POST['DateofConsultationHEPAatBirth'] ?? '',
        'DateofConsultationHEPAB1' => $_POST['DateofConsultationHEPAB1'] ?? '',
        'DateofConsultationHEPAB2' => $_POST['DateofConsultationHEPAB2'] ?? '',
        'DateofConsultationHEPAB3' => $_POST['DateofConsultationHEPAB3'] ?? '',
        'DateofConsultationHEPAB4' => $_POST['DateofConsultationHEPAB4'] ?? '',
        'DateofConsultationAMV1' => $_POST['DateofConsultationAMV1'] ?? '',
        'DateofConsultationMMR' => $_POST['DateofConsultationMMR'] ?? '',
        'DateofConsultationFIC' => $_POST['DateofConsultationFIC'] ?? '',
        'DateofConsultationCIC' => $_POST['DateofConsultationCIC'] ?? '',
        'DateofConsultationAMV2' => $_POST['DateofConsultationAMV2'] ?? '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => $_POST['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        'DATEOFCONSULTTT1' => $_POST['DATEOFCONSULTTT1'] ?? '',
        'DATEOFCONSULTTT2' => $_POST['DATEOFCONSULTTT2'] ?? '',
        'DATEOFCONSULTTT3' => $_POST['DATEOFCONSULTTT3'] ?? '',
        'DATEOFCONSULTTT4' => $_POST['DATEOFCONSULTTT4'] ?? '',
        'DATEOFCONSULTT5' => $_POST['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    // Collect all valid dates
    foreach ($consultation_data as $field => $value) {
        if (!empty($value) && $value !== '0000-00-00') {
            $valid_dates[] = $value;
        }
    }
    
    // Print earliest date in <p> tag
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        echo '<p>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p>No consultation date recorded</p>';
    }
}

// Function to get earliest date (returns value instead of printing)
function getEarliestDateFromPost() {
    $consultation_data = [
        'DateofConsultationBCG' => $_POST['DateofConsultationBCG'] ?? '',
        'DateofConsultationPENTAHIB1' => $_POST['DateofConsultationPENTAHIB1'] ?? '',
        'DateofConsultationPENTAHIB2' => $_POST['DateofConsultationPENTAHIB2'] ?? '',
        'DateofConsultationPENTAHIB3' => $_POST['DateofConsultationPENTAHIB3'] ?? '',
        'DateofConsultationOPV1v' => $_POST['DateofConsultationOPV1v'] ?? '',
        'DateofConsultationOPV2' => $_POST['DateofConsultationOPV2'] ?? '',
        'DateofConsultationOPV3' => $_POST['DateofConsultationOPV3'] ?? '',
        'DateofConsultationIPV1' => $_POST['DateofConsultationIPV1'] ?? '',
        'DateofConsultationIPV2' => $_POST['DateofConsultationIPV2'] ?? '',
        'DateofConsultationPCV1' => $_POST['DateofConsultationPCV1'] ?? '',
        'DateofConsultationPCV2' => $_POST['DateofConsultationPCV2'] ?? '',
        'DateofConsultationPCV3' => $_POST['DateofConsultationPCV3'] ?? '',
        'DateofConsultationHEPAatBirth' => $_POST['DateofConsultationHEPAatBirth'] ?? '',
        'DateofConsultationHEPAB1' => $_POST['DateofConsultationHEPAB1'] ?? '',
        'DateofConsultationHEPAB2' => $_POST['DateofConsultationHEPAB2'] ?? '',
        'DateofConsultationHEPAB3' => $_POST['DateofConsultationHEPAB3'] ?? '',
        'DateofConsultationHEPAB4' => $_POST['DateofConsultationHEPAB4'] ?? '',
        'DateofConsultationAMV1' => $_POST['DateofConsultationAMV1'] ?? '',
        'DateofConsultationMMR' => $_POST['DateofConsultationMMR'] ?? '',
        'DateofConsultationFIC' => $_POST['DateofConsultationFIC'] ?? '',
        'DateofConsultationCIC' => $_POST['DateofConsultationCIC'] ?? '',
        'DateofConsultationAMV2' => $_POST['DateofConsultationAMV2'] ?? '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => $_POST['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? '',
        'DATEOFCONSULTTT1' => $_POST['DATEOFCONSULTTT1'] ?? '',
        'DATEOFCONSULTTT2' => $_POST['DATEOFCONSULTTT2'] ?? '',
        'DATEOFCONSULTTT3' => $_POST['DATEOFCONSULTTT3'] ?? '',
        'DATEOFCONSULTTT4' => $_POST['DATEOFCONSULTTT4'] ?? '',
        'DATEOFCONSULTT5' => $_POST['DATEOFCONSULTT5'] ?? ''
    ];
    
    $valid_dates = [];
    
    foreach ($consultation_data as $field => $value) {
        if (!empty($value) && $value !== '0000-00-00') {
            $valid_dates[] = $value;
        }
    }
    
    return !empty($valid_dates) ? min($valid_dates) : null;
}

// Function with styling for POST data
function printEarliestDateFromPostStyled($class = '', $style = '') {
    $earliest = getEarliestDateFromPost();
    
    $class_attr = $class ? ' class="' . htmlspecialchars($class) . '"' : '';
    $style_attr = $style ? ' style="' . htmlspecialchars($style) . '"' : '';
    
    if ($earliest) {
        echo '<p' . $class_attr . $style_attr . '>' . htmlspecialchars($earliest) . '</p>';
    } else {
        echo '<p' . $class_attr . $style_attr . '>No consultation date recorded</p>';
    }
}

// Function to use in your existing code where you have POST variables
function applyEarliestDateToPost() {
    // This function can be called after you've set all your POST variables
    // It will work with the existing POST data structure
    
    echo '<div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    echo '<strong>Earliest Consultation Date: </strong>';
    printEarliestDateFromPost();
    echo '</div>';
}

// Example usage in your existing code structure:
/*
// After your POST variable assignments:
$BCG = $_POST['BCG'];
$DateBCGwasgiven = $_POST['DateBCGwasgiven'];
$DateofConsultationBCG = $_POST['DateofConsultationBCG'];
// ... all your other POST assignments ...

// Then call this to show earliest date:
applyEarliestDateToPost();

// Or use it inline:
echo "First consultation was on: ";
printEarliestDateFromPost();

// Or get the value for processing:
$earliest_date = getEarliestDateFromPost();
if ($earliest_date) {
    echo "The earliest consultation was: " . $earliest_date;
}
*/
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Earliest Date from POST Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .result-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-section">
        <h2>Earliest Consultation Date from POST Data</h2>
        <p>This function works directly with your POST variables to find and print the earliest consultation date.</p>
        
        <h3>How to Use in Your Code:</h3>
        
        <h4>Method 1: After your POST assignments</h4>
        <div class="code-block">
&lt;?php
// Include the function file
include 'earliest_from_post.php';

// Your existing POST variable assignments
$BCG = $_POST['BCG'];
$DateBCGwasgiven = $_POST['DateBCGwasgiven'];
$DateofConsultationBCG = $_POST['DateofConsultationBCG'];
$PENTAHIB1 = $_POST['PENTAHIB1'];
$DatePenta1wasgiven = $_POST['DatePenta1wasgiven'];
$DateofConsultationPENTAHIB1 = $_POST['DateofConsultationPENTAHIB1'];
// ... all your other POST assignments ...

// Then show the earliest consultation date
echo "Earliest consultation date: ";
printEarliestDateFromPost();
?&gt;
        </div>
        
        <h4>Method 2: Get the date for processing</h4>
        <div class="code-block">
&lt;?php
// Get the earliest date value
$earliest_date = getEarliestDateFromPost();

if ($earliest_date) {
    echo "First consultation was on: " . $earliest_date;
    
    // You can use this date for calculations, database storage, etc.
    $days_since = (strtotime($earliest_date) - strtotime($_POST['DateOfBirth'])) / (60*60*24);
    echo "That was " . $days_since . " days after birth.";
} else {
    echo "No consultation dates found.";
}
?&gt;
        </div>
        
        <h4>Method 3: With styling</h4>
        <div class="code-block">
&lt;?php
// Print with custom CSS class and style
printEarliestDateFromPostStyled('highlight', 'color: blue; font-weight: bold;');
?&gt;
        </div>
        
        <h4>Method 4: Complete display box</h4>
        <div class="code-block">
&lt;?php
// Shows a complete styled box with the earliest date
applyEarliestDateToPost();
?&gt;
        </div>
    </div>

    <div class="demo-section">
        <h3>Integration in Your Forms:</h3>
        
        <h4>In db.php (after form processing):</h4>
        <div class="code-block">
&lt;?php
include 'earliest_from_post.php';

// Your existing POST processing
$BCG = $_POST['BCG'];
$DateofConsultationBCG = $_POST['DateofConsultationBCG'];
// ... all other assignments ...

// After successful database insert
if (mysqli_query($conn, $sql)) {
    echo '&lt;div class="success-message"&gt;';
    echo 'Record successfully added!&lt;br&gt;';
    echo 'First consultation date: ';
    printEarliestDateFromPost();
    echo '&lt;/div&gt;';
}
?&gt;
        </div>
        
        <h4>In nip.php (form display):</h4>
        <div class="code-block">
&lt;?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    include 'earliest_from_post.php';
    
    // Process form data
    // ... your existing code ...
    
    // Show earliest consultation date
    echo '&lt;div class="consultation-summary"&gt;';
    echo '&lt;h5&gt;Consultation Summary&lt;/h5&gt;';
    echo 'Earliest consultation date: ';
    printEarliestDateFromPost();
    echo '&lt;/div&gt;';
}
?&gt;
        </div>
    </div>

    <div class="demo-section">
        <h3>Features:</h3>
        <ul>
            <li>✅ Works directly with your POST variables</li>
            <li>✅ Uses exact consultation date field names</li>
            <li>✅ Prints in &lt;p&gt; tag as requested</li>
            <li>✅ Handles empty/missing dates gracefully</li>
            <li>✅ Multiple usage options (print, return, styled)</li>
            <li>✅ Easy to integrate in existing code</li>
            <li>✅ No need to modify existing POST assignments</li>
        </ul>
    </div>

    <div class="demo-section">
        <h3>POST Variables Used:</h3>
        <p>The function automatically reads these POST variables:</p>
        <div style="columns: 2; column-gap: 20px; font-size: 13px;">
            <ul>
                <li>DateofConsultationBCG</li>
                <li>DateofConsultationPENTAHIB1</li>
                <li>DateofConsultationPENTAHIB2</li>
                <li>DateofConsultationPENTAHIB3</li>
                <li>DateofConsultationOPV1v</li>
                <li>DateofConsultationOPV2</li>
                <li>DateofConsultationOPV3</li>
                <li>DateofConsultationIPV1</li>
                <li>DateofConsultationIPV2</li>
                <li>DateofConsultationPCV1</li>
                <li>DateofConsultationPCV2</li>
                <li>DateofConsultationPCV3</li>
                <li>DateofConsultationHEPAatBirth</li>
                <li>DateofConsultationHEPAB1</li>
                <li>DateofConsultationHEPAB2</li>
                <li>DateofConsultationHEPAB3</li>
                <li>DateofConsultationHEPAB4</li>
                <li>DateofConsultationAMV1</li>
                <li>DateofConsultationMMR</li>
                <li>DateofConsultationFIC</li>
                <li>DateofConsultationCIC</li>
                <li>DateofConsultationAMV2</li>
                <li>DATEOFCONSULTATIONEXCLUSIVEBF</li>
                <li>DATEOFCONSULTTT1</li>
                <li>DATEOFCONSULTTT2</li>
                <li>DATEOFCONSULTTT3</li>
                <li>DATEOFCONSULTTT4</li>
                <li>DATEOFCONSULTT5</li>
            </ul>
        </div>
    </div>
</body>
</html>
