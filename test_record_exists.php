<?php
// Test Record Exists Function - Modernized Version of Your Code
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Test Record Exists Function</h2>";
echo "<p>This demonstrates the modernized version of your mysql_query code using mysqli.</p>";

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('<p style="color: red;">Connection failed: ' . mysqli_connect_error() . '</p>');
}

echo "<p style='color: green;'>✅ Database connected successfully</p>";

// Your original code (deprecated - DO NOT USE):
/*
$key_id = 123;
$result = mysql_query("SELECT * FROM table WHERE key_id='$key_id'");
$num_rows = mysql_num_rows($result);
if ($num_rows) {
   trigger_error('It exists.', E_USER_WARNING);
}
*/

// Modernized version using mysqli:
function checkRecordExists($conn, $table, $key_field, $key_id) {
    // Use prepared statement for security (prevents SQL injection)
    $sql = "SELECT * FROM `$table` WHERE `$key_field` = ?";
    $stmt = mysqli_prepare($conn, $sql);
    
    if ($stmt) {
        // Bind parameter (assuming key_id is a string/number)
        mysqli_stmt_bind_param($stmt, "s", $key_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $num_rows = mysqli_num_rows($result);
        mysqli_stmt_close($stmt);
        
        if ($num_rows > 0) {
            trigger_error('Record exists in ' . $table . ' with ' . $key_field . ' = ' . $key_id, E_USER_WARNING);
            return true;
        }
        return false;
    } else {
        trigger_error('Database query failed: ' . mysqli_error($conn), E_USER_ERROR);
        return false;
    }
}

// Test 1: Check if a specific record exists in nip_table
echo "<h3>Test 1: Check Specific Record by ID</h3>";

// First, let's see what records exist
$count_query = mysqli_query($conn, "SELECT COUNT(*) as total FROM nip_table");
$count_result = mysqli_fetch_assoc($count_query);
echo "<p>Total records in nip_table: " . $count_result['total'] . "</p>";

if ($count_result['total'] > 0) {
    // Get the first record ID
    $first_record = mysqli_query($conn, "SELECT id FROM nip_table LIMIT 1");
    $record = mysqli_fetch_assoc($first_record);
    $existing_id = $record['id'];
    
    echo "<p>Testing with existing record ID: $existing_id</p>";
    
    // Test with existing record
    if (checkRecordExists($conn, 'nip_table', 'id', $existing_id)) {
        echo "<p style='color: green;'>✅ Function correctly detected existing record!</p>";
    } else {
        echo "<p style='color: red;'>❌ Function failed to detect existing record</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ No records in table to test with</p>";
}

// Test 2: Check with non-existing record
echo "<h3>Test 2: Check Non-Existing Record</h3>";
$non_existing_id = 99999;

echo "<p>Testing with non-existing record ID: $non_existing_id</p>";

if (checkRecordExists($conn, 'nip_table', 'id', $non_existing_id)) {
    echo "<p style='color: red;'>❌ Function incorrectly detected non-existing record</p>";
} else {
    echo "<p style='color: green;'>✅ Function correctly identified non-existing record!</p>";
}

// Test 3: Check by name instead of ID
echo "<h3>Test 3: Check by Child Name</h3>";

// Insert a test record first
$test_name = "TestChild_" . time();
$insert_sql = "INSERT INTO nip_table (DateOfRegistration, NameOfChild, LastNameOfChild, DateOfBirth, Sex) 
               VALUES (NOW(), ?, 'TestLastName', '2020-01-01', 'MALE')";
$insert_stmt = mysqli_prepare($conn, $insert_sql);
mysqli_stmt_bind_param($insert_stmt, "s", $test_name);

if (mysqli_stmt_execute($insert_stmt)) {
    $test_id = mysqli_insert_id($conn);
    echo "<p>Inserted test record with name: $test_name (ID: $test_id)</p>";
    
    // Now test if we can find it by name
    if (checkRecordExists($conn, 'nip_table', 'NameOfChild', $test_name)) {
        echo "<p style='color: green;'>✅ Function correctly found record by name!</p>";
    } else {
        echo "<p style='color: red;'>❌ Function failed to find record by name</p>";
    }
    
    // Clean up test record
    mysqli_query($conn, "DELETE FROM nip_table WHERE id = $test_id");
    echo "<p>Test record cleaned up</p>";
} else {
    echo "<p style='color: red;'>Failed to insert test record</p>";
}

mysqli_stmt_close($insert_stmt);

// Test 4: Error handling test
echo "<h3>Test 4: Error Handling</h3>";

// Test with invalid table name
echo "<p>Testing with invalid table name...</p>";
$error_handler = function($errno, $errstr) {
    echo "<p style='color: orange;'>⚠️ Caught error: $errstr</p>";
    return true; // Don't execute PHP's internal error handler
};

set_error_handler($error_handler);

checkRecordExists($conn, 'invalid_table_name', 'id', 123);

restore_error_handler();

// Test 5: Practical usage examples
echo "<h3>Test 5: Practical Usage Examples</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>How to use this function in your code:</h4>";

echo "<h5>Example 1: Check if child already exists before registration</h5>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo htmlspecialchars('
// Check if child with specific name already exists
$child_name = "Juan Dela Cruz";
if (checkRecordExists($conn, "nip_table", "NameOfChild", $child_name)) {
    echo "Child already registered!";
} else {
    echo "Child not found, can proceed with registration";
}
');
echo "</pre>";

echo "<h5>Example 2: Check if family serial number is unique</h5>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo htmlspecialchars('
// Check if family serial number already exists
$family_serial = "CHO-20240101-ABC123";
if (checkRecordExists($conn, "nip_table", "FamilySerialNumber", $family_serial)) {
    echo "Family serial already exists, generate new one";
} else {
    echo "Family serial is unique, can use it";
}
');
echo "</pre>";

echo "<h5>Example 3: Validate record before update</h5>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
echo htmlspecialchars('
// Check if record exists before updating
$record_id = 123;
if (checkRecordExists($conn, "nip_table", "id", $record_id)) {
    // Record exists, safe to update
    $update_sql = "UPDATE nip_table SET ... WHERE id = ?";
} else {
    echo "Record not found, cannot update";
}
');
echo "</pre>";
echo "</div>";

// Summary
echo "<h3>📋 Summary</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
echo "<h4>✅ Modernization Complete!</h4>";
echo "<p>Your original code has been successfully modernized:</p>";
echo "<ul>";
echo "<li><strong>Security:</strong> Uses prepared statements to prevent SQL injection</li>";
echo "<li><strong>Compatibility:</strong> Uses mysqli instead of deprecated mysql functions</li>";
echo "<li><strong>Flexibility:</strong> Can check any table and field combination</li>";
echo "<li><strong>Error Handling:</strong> Proper error reporting with trigger_error()</li>";
echo "<li><strong>Reusability:</strong> Function can be used throughout your application</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Integration</h3>";
echo "<p>The function has been added to your <code>db.php</code> file and is ready to use!</p>";
echo "<p>";
echo "<a href='nip.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Test in Registration Form</a>";
echo "<a href='db.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>📄 View db.php</a>";
echo "</p>";

mysqli_close($conn);

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h2, h3, h4, h5 {
    color: #343a40;
}

pre {
    font-size: 14px;
    line-height: 1.4;
    overflow-x: auto;
}

ul {
    line-height: 1.6;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}
</style>
