<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Earliest Date with Conditions - Complete Demo</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .condition-box {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 15px 0;
        }
        
        .usage-example {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4caf50;
            margin: 15px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #9c27b0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left blue-text">event</i>
                Print Earliest Date with Conditions - Complete Implementation
            </h3>
            <p class="center-align">Print the earliest consultation date and apply conditions based on all consultation date fields</p>
        </div>

        <div class="demo-section">
            <h4>✅ Implementation Complete</h4>
            <p>The function now prints the earliest date and applies conditions based on all these consultation date fields:</p>
            
            <div class="condition-box">
                <h6>All Consultation Date Fields with Conditions:</h6>
                <div class="row">
                    <div class="col s12 m6">
                        <ul style="font-size: 13px; line-height: 1.6;">
                            <li><code>$row['DateofConsultationBCG'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationPENTAHIB1'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationPENTAHIB2'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationPENTAHIB3'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationOPV1v'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationOPV2'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationOPV3'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationIPV1'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationIPV2'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationPCV1'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationPCV2'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationPCV3'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationHEPAatBirth'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationHEPAB1'] ?? ''</code></li>
                        </ul>
                    </div>
                    <div class="col s12 m6">
                        <ul style="font-size: 13px; line-height: 1.6;">
                            <li><code>$row['DateofConsultationHEPAB2'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationHEPAB3'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationHEPAB4'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationAMV1'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationMMR'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationFIC'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationCIC'] ?? ''</code></li>
                            <li><code>$row['DateofConsultationAMV2'] ?? ''</code></li>
                            <li><code>$row['DATEOFCONSULTATIONEXCLUSIVEBF'] ?? ''</code></li>
                            <li><code>$row['DATEOFCONSULTTT1'] ?? ''</code></li>
                            <li><code>$row['DATEOFCONSULTTT2'] ?? ''</code></li>
                            <li><code>$row['DATEOFCONSULTTT3'] ?? ''</code></li>
                            <li><code>$row['DATEOFCONSULTTT4'] ?? ''</code></li>
                            <li><code>$row['DATEOFCONSULTT5'] ?? ''</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔧 Conditions Applied</h4>
            <p>Each consultation date field is checked with these conditions:</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h6><i class="material-icons left purple-text">check_circle</i>Not Empty</h6>
                    <p><code>!empty($date)</code></p>
                    <p>Ensures the field has a value</p>
                </div>
                
                <div class="feature-card">
                    <h6><i class="material-icons left purple-text">cancel</i>Not Null Date</h6>
                    <p><code>$date !== '0000-00-00'</code></p>
                    <p>Excludes invalid null dates</p>
                </div>
                
                <div class="feature-card">
                    <h6><i class="material-icons left purple-text">verified</i>Not Null</h6>
                    <p><code>$date !== null</code></p>
                    <p>Excludes null values</p>
                </div>
                
                <div class="feature-card">
                    <h6><i class="material-icons left purple-text">safety_check</i>Null Coalescing</h6>
                    <p><code>$row['field'] ?? ''</code></p>
                    <p>Handles missing array keys</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🎯 Usage Methods</h4>
            
            <h5>Method 1: Simple Print in &lt;p&gt; Tag</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
include 'simple_earliest_with_conditions.php';

// Print earliest date in &lt;p&gt; tag with conditions
printEarliestDateWithConditions($row);
// Output: &lt;p&gt;2023-01-20&lt;/p&gt;
?&gt;
                </div>
                <p><strong>Use when:</strong> You just need the earliest date displayed</p>
            </div>
            
            <h5>Method 2: Get Value for Processing</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
$earliest = getEarliestDateWithConditions($row);

if ($earliest) {
    echo "First consultation was on: " . $earliest;
    
    // Use the date for calculations
    $days_since_birth = (strtotime($earliest) - strtotime($row['DateOfBirth'])) / (60*60*24);
    echo " (That was " . round($days_since_birth) . " days after birth)";
} else {
    echo "No consultation dates found.";
}
?&gt;
                </div>
                <p><strong>Use when:</strong> You need the date value for further processing</p>
            </div>
            
            <h5>Method 3: Print with Analysis</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
// Print earliest date with timing analysis
printEarliestDateWithAnalysis($row);
// Shows date + early/late consultation status
?&gt;
                </div>
                <p><strong>Use when:</strong> You want automatic analysis of consultation timing</p>
            </div>
            
            <h5>Method 4: Check Conditions</h5>
            <div class="usage-example">
                <div class="code-block">
&lt;?php
$conditions = checkConsultationConditions($row);

if ($conditions['has_consultations']) {
    echo "Total consultations: " . $conditions['consultation_count'];
    
    if ($conditions['is_early']) {
        echo " ✅ Early consultation";
    }
    
    if ($conditions['is_well_documented']) {
        echo " ✅ Well documented";
    }
} else {
    echo "❌ No consultations recorded";
}
?&gt;
                </div>
                <p><strong>Use when:</strong> You need detailed condition checking</p>
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Condition Logic</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Date Validation</span>
                            <div class="code-block" style="font-size: 11px;">
foreach ($consultation_dates as $date) {
    if (!empty($date) && 
        $date !== '0000-00-00' && 
        $date !== null) {
        $valid_dates[] = $date;
    }
}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Earliest Selection</span>
                            <div class="code-block" style="font-size: 11px;">
if (!empty($valid_dates)) {
    $earliest = min($valid_dates);
    echo '&lt;p&gt;' . $earliest . '&lt;/p&gt;';
} else {
    echo '&lt;p&gt;No consultation date recorded&lt;/p&gt;';
}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Timing Analysis</span>
                            <div class="code-block" style="font-size: 11px;">
$days_after_birth = (strtotime($earliest) - 
    strtotime($row['DateOfBirth'])) / (60*60*24);

if ($days_after_birth <= 30) {
    echo "✅ Early consultation";
} else {
    echo "⚠️ Late consultation";
}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">Documentation Check</span>
                            <div class="code-block" style="font-size: 11px;">
$count = count($valid_dates);

if ($count >= 5) {
    echo "✅ Well documented";
} else {
    echo "⚠️ Needs more documentation";
}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔗 Integration Examples</h4>
            
            <h5>In Database Loops:</h5>
            <div class="code-block">
&lt;?php
include 'simple_earliest_with_conditions.php';

$sql = "SELECT * FROM nip_table WHERE ...";
$result = mysqli_query($conn, $sql);

while ($row = mysqli_fetch_assoc($result)) {
    echo "Child: " . $row['NameOfChild'];
    echo " | Earliest consultation: ";
    printEarliestDateWithConditions($row);
}
?&gt;
            </div>
            
            <h5>In Forms and Displays:</h5>
            <div class="code-block">
&lt;div class="consultation-info"&gt;
    &lt;label&gt;First Consultation Date:&lt;/label&gt;
    &lt;?php printEarliestDateWithConditions($row); ?&gt;
&lt;/div&gt;

&lt;!-- With analysis --&gt;
&lt;div class="consultation-analysis"&gt;
    &lt;h6&gt;Consultation Analysis:&lt;/h6&gt;
    &lt;?php printEarliestDateWithAnalysis($row); ?&gt;
&lt;/div&gt;
            </div>
            
            <h5>In Tables:</h5>
            <div class="code-block">
&lt;table&gt;
    &lt;tr&gt;
        &lt;th&gt;Child Name&lt;/th&gt;
        &lt;th&gt;Earliest Consultation&lt;/th&gt;
        &lt;th&gt;Status&lt;/th&gt;
    &lt;/tr&gt;
    &lt;?php while ($row = mysqli_fetch_assoc($result)) { ?&gt;
    &lt;tr&gt;
        &lt;td&gt;&lt;?php echo $row['NameOfChild']; ?&gt;&lt;/td&gt;
        &lt;td&gt;&lt;?php printEarliestDateWithConditions($row); ?&gt;&lt;/td&gt;
        &lt;td&gt;
            &lt;?php 
            $conditions = checkConsultationConditions($row);
            echo $conditions['is_early'] ? '✅ Early' : '⚠️ Late';
            ?&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
    &lt;?php } ?&gt;
&lt;/table&gt;
            </div>
        </div>

        <div class="demo-section">
            <h4>✅ Files Updated</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">✅ Already Working</span>
                            <ul>
                                <li><strong>print.php</strong> - Shows earliest date with analysis in certificate</li>
                                <li><strong>simple_earliest_with_conditions.php</strong> - Main function file</li>
                                <li><strong>earliest_date_with_conditions.php</strong> - Detailed version</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">🔧 Easy to Add</span>
                            <p>Add to any file with 3 steps:</p>
                            <ol>
                                <li>Include the function file</li>
                                <li>Call the function with $row data</li>
                                <li>Get earliest date in &lt;p&gt; tag</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>All consultation date fields are now working with conditions!</p>
            
            <div class="row">
                <div class="col s12 m4">
                    <a href="earliest_date_with_conditions.php?test=1" class="btn large blue waves-effect">
                        <i class="material-icons left">event</i>Detailed Demo
                    </a>
                    <p><small>Full analysis demo</small></p>
                </div>
                
                <div class="col s12 m4">
                    <a href="print.php?id=1" target="_blank" class="btn large green waves-effect">
                        <i class="material-icons left">print</i>Test in Certificate
                    </a>
                    <p><small>See in print.php</small></p>
                </div>
                
                <div class="col s12 m4">
                    <a href="filter_records.php" class="btn large orange waves-effect">
                        <i class="material-icons left">table_chart</i>Records Table
                    </a>
                    <p><small>View in table format</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📋 Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>✅ Print Earliest Date with Conditions - Complete!</h6>
                    <ul>
                        <li><strong>28 Fields:</strong> All consultation date fields with conditions</li>
                        <li><strong>Validation:</strong> Empty, null, and 0000-00-00 date checking</li>
                        <li><strong>Print in &lt;p&gt;:</strong> <code>printEarliestDateWithConditions($row)</code></li>
                        <li><strong>Get Value:</strong> <code>getEarliestDateWithConditions($row)</code></li>
                        <li><strong>Analysis:</strong> <code>printEarliestDateWithAnalysis($row)</code></li>
                        <li><strong>Conditions:</strong> <code>checkConsultationConditions($row)</code></li>
                        <li><strong>Integration:</strong> Working in print.php certificate</li>
                        <li><strong>Easy Use:</strong> Include file and call function</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
