<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>
    td{
        font-family:arial !important;
        margin:0;
    }
    th {
        background:green;
    }
</style>
<body>
   <?php
// Database connection
$host = "localhost";
$username = "root";      // change if needed
$password = "";          // change if needed
$database = "choims"; // change this to your actual DB

$conn = mysqli_connect($host, $username, $password, $database);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// SQL query
$sql = "SELECT username, password FROM users";
$result = mysqli_query($conn, $sql);

// Check if there are results
if (mysqli_num_rows($result) > 0) {
    echo "<table border='1'><tr><th>Username</th><th>Password (hashed)</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr><td>" . htmlspecialchars($row['username']) . "</td><td>" .  ($row['password']) . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "No users found.";
}

// Close connection
mysqli_close($conn);
?>

</body>
</html>

