
<?php include '../templates/header.php';
    include "../templates/base_url.php";
?>

<?php 
    // session_start();
    if(session_status() !== PHP_SESSION_ACTIVE) session_start();
    // if(session_status() !== PHP_SESSION_ACTIVE)
    // { 
      
    //  echo 'active';
    // }
    // include_once "db_disconnect.php";
    // if(session_status() === PHP_SESSION_NONE) session_start();
   
?> 


<!-- <?php $base_url = 'http://192.168.88.11/fhsis/'; ?> -->
<?php $base_url = $rootbase_url; ?>
<?php

// Check if the "userid" session variable is set
if (!isset($_SESSION['userid'])) {
  // Redirect the user to the login page
  header('Location:/FHSIS/');
  exit;
}

// The rest of the dashboard code goes here
?>


<!DOCTYPE html>
<html>
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

  <style>
    body {font-family: system-ui;}

      .navbar1 
        {
          width: 100%;
          background-color: #00BFFF;
          overflow: auto;
        }
  
      .navbar a {
          
          padding: 11px;
          /* color: white; */
          /* text-decoration: none; */
          font-size: 17px;
        }

      .navbar a:hover {
          background-color: #007a85;
        }

      

      @media screen and (max-width: 500px) {
          .navbar a {
            float: none;
            display: block;
          }
        }


  </style>

  <body>


      <nav class="navbar navbar-expand-sm navbar-light rounded-0" style="background-color: #00838f;">
        <a class="nav-link text-white" style="font-weight:500;" href="#">PCHIMS</a>
        
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNavDropdown">
          <ul class="navbar-nav">
            <li class="nav-item active">
              
              
              <a class="  nav-link text-white" href="#">Home</a> 
            </li>
            <li class="nav-item active">
              
              
              <a class="  nav-link text-white" href="http://192.168.88.11/fhsis//UHC/07_POSReport_H.php">Export Data</a> 
            </li>
          <!--   <li class="nav-item">
              
              <a class="nav-link text-white" href="#">  Features</a> 
            </li>

           <li class="nav-item">
              
              <a class="nav-link text-white" href="#">  Announcement</a> 
            </li> -->


            <!-- <li class="nav-item dropdown"> -->
            <li class="nav-item dropdown" <?php if(empty($_SESSION['MA_UHC'])) { ?>   <?php } ?> >
                  <a class="nav-link dropdown-toggle text-white" href="#" id="UHC" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">UHC </a>
                  
                  <div class="dropdown-menu" aria-labelledby="UHC">
                    <?php error_reporting(0); ?>
<?php if ($_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_Consultation']) {?> <a class="dropdown-item" href="<?php echo $base_url.'/UHC/01_Consultation_A.php' ?> ">   Consultation</a>  <?php } ?>
<?php if ($_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_Dispensing']) {?>           <a class="dropdown-item" href="<?php echo $base_url.'/UHC/02_Dispensing_A.php' ?> ">   Dispensing</a>              <?php } ?>
<?php if ($_SESSION['userid'] == 'marceloghc' || $_SESSION['userid'] == 'johnpaul' || $_SESSION['userid'] == 'dhaniesa' || $_SESSION['userid'] == 'uhcjona' || $_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_POS']) {?>                  <a class="dropdown-item" href="<?php echo $base_url.'/UHC/03_POS_A.php' ?> ">   POS</a>              <?php  } ?>
<?php if ($_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_EditPOS']) {?>              <a class="dropdown-item" href="<?php echo $base_url.'/UHC/04_EditPOS_A.php' ?> ">   Edit POS</a>              <?php   } ?>
<?php if ($_SESSION['userid'] == 'gutierrezrecil' || $_SESSION['userid'] == 'kwinbfhc' || $_SESSION['userid'] == 'uhcjona' ||$_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_ScaneKonsultaReg']) {?>     <a class="dropdown-item" href="<?php echo $base_url.'/UHC/05_ScaneKonsultaReg_A.php' ?> ">  Scan eKonsulta Registration</a>              <?php } ?>
<?php if ($_SESSION['userid'] == 'uhcjona' ||$_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_PatientFirstEncounter']) {?><a class="dropdown-item" href="<?php echo $base_url.'/UHC/06_PatientFirstEncounter_A.php' ?> ">   First Patient Encounter</a>              <?php } ?>
<?php if($_SESSION['userid'] == 'marceloghc' || $_SESSION['userid'] == 'uhclmorales' || $_SESSION['userid'] == 'uhcmruiz' || $_SESSION['userid'] == 'johnpaul' || $_SESSION['userid'] == 'dhaniesa' || $_SESSION['userid'] == 'gutierrezrecil' || $_SESSION['userid'] == 'uhcevelyn' ||   $_SESSION['userid'] == 'uhcjona' ||$_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['userid'] == 'jccarpio' || $_SESSION['userid'] == 'carolr' || $_SESSION['userid'] == 'rsarino' || $_SESSION['userid'] == 'mmurillo' || $_SESSION['userid'] == 'angelica' || $_SESSION['userid'] == 'ISIAH' || $_SESSION['userid'] == 'jenifersmdp' || $_SESSION['userid'] == 'clairsmdp' || $_SESSION['userid'] == 'mmbilog') {?>        <a class="dropdown-item" href="<?php echo $base_url.'/UHC/07_POSReport_A.php' ?> ">   POS Report</a>  <?php  }     ?>
<?php if ($_SESSION['userid'] == 'hccatama' || $_SESSION['userid'] == 'angelica' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['userid'] == 'dgutierrez' ||   $_SESSION['userid'] == 'gcalangian' || $_SESSION['userid'] == 'ISIAH' || $_SESSION['userid'] == 'hazels' || $_SESSION['userid'] == 'carolr'  ) { ?><a class="dropdown-item" href="<?php echo $base_url.'/UHC/07_POSReport_E.php' ?> ">   POS Report Admin</a> <?php} else {  ?>         <?php } ?>
<?php if ($_SESSION['userid'] == 'uhcjona' ||$_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_RegistrationList']) {?>     <a class="dropdown-item" href="<?php echo $base_url.'/UHC/08_RegistrationList_A.php' ?> ">  Registration List</a>  <?php } ?>
<?php if ($_SESSION['userid'] == 'marceloghc' || $_SESSION['userid'] == 'gutierrezrecil' || $_SESSION['userid'] == 'kwinbfhc' || $_SESSION['userid'] == 'uhcjona' ||$_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_FPE']) {?>                  <a class="dropdown-item" href="<?php echo $base_url.'/UHC/09_FPE_A.php' ?> ">  FPE</a>  <?php } ?>
<?php if ($_SESSION['userid'] == 'jhay' || $_SESSION['userid'] == 'johnpaul' || $_SESSION['userid'] == 'gutierrezrecil' ||$_SESSION['userid'] == 'uhcmyra' || $_SESSION['userid'] == 'tyignacio' || $_SESSION['MA_MedicineTransaction']) {?>  <a class="dropdown-item" href="<?php echo $base_url.'/UHC/03_POS_N.php' ?> ">  Medicine Transaction</a>  <?php } ?>

                     <!-- <?php if ($_SESSION['MA_FPEAndUHCIDPrinting']) {?>  <a class="dropdown-item" href="<?php echo $base_url.'/UHC/11_FPEAndUHCIDPrinting_A.php' ?> "> FPE And UHC ID Printing</a>  <?php } ?> -->
                  </div>

            </li>

            
            <li class="nav-item dropdown" <?php if(empty($_SESSION['MA_Laboratory'])) { ?> hidden <?php } ?> >
                  <a class="nav-link dropdown-toggle text-white" href="#" id="Laboratory" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">   Laboratory </a>

                  <div class="dropdown-menu" aria-labelledby="Laboratory">

                      <?php if ($_SESSION['MA_LabServices']) {?>          <a class="dropdown-item" href="<?php echo $base_url.'/LabServices/10_LabServices_B.php' ?>">Lab Services  </a>   <?php } ?>
                      <?php if ($_SESSION['MA_LabResult']) {?>            <a class="dropdown-item" href="<?php echo $base_url.'/LabServices/10_LabServices_G.php' ?>"> Lab Result    </a>   <?php } ?>
                      <?php if ($_SESSION['MA_LabReports']) {?>            <a class="dropdown-item" href="<?php echo $base_url.'/LabServices/10_LabServices_H.php' ?>"> Lab Reports    </a>   <?php } ?>
                  </div>

            </li>



            
            <li class="nav-item dropdown" <?php if(empty($_SESSION['MA_Settings'])) { ?>   <?php } ?> >
                  <a class="nav-link dropdown-toggle text-white" href="#" id="Settings" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Settings </a>

                  <div class="dropdown-menu" aria-labelledby="Settings">
                      <?php if ($_SESSION['MA_AccountSettings']) {?>        <a class="dropdown-item" href="<?php echo $base_url.'/Settings/01_AccountSettings_A.php' ?> ">  Account Settings</a>  <?php } ?>
                      <?php if ($_SESSION['MA_PatientList']) {?>            <a class="dropdown-item" href="<?php echo $base_url.'/Settings/02_PatientList_A.php' ?> ">   Patient List</a>              <?php } ?>

                      </div>

            </li>




            
            <li class="nav-item dropdown" <?php if(empty($_SESSION['MA_Programs'])) { ?> hidden <?php } ?> >
                  <a class="nav-link dropdown-toggle" href="#" id="Programs" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Programs </a>

                  <div class="dropdown-menu" aria-labelledby="Programs">

                      <?php if ($_SESSION['MA_Morbidity']) {?>                <a class="dropdown-item" href="<?php echo $base_url.'/Programs/01_Morbidity_A.php' ?>"> Morbidity </a>                                          <?php } ?>
                      
                  </div>

            </li>

            <li class="nav-item dropdown" <?php if(empty($_SESSION['MA_Reports'])) { ?> hidden <?php } ?> >
                  <a class="nav-link dropdown-toggle" href="#" id="Reports" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">  Reports </a>

                  <div class="dropdown-menu" aria-labelledby="Reports">

                      <?php if ($_SESSION['MA_AuditReport']) {?>              <a class="dropdown-item" href="<?php echo $base_url.'/Reports/01_AuditReport.php' ?>"><i class="fa fa-file-text-o" aria-hidden="true"></i> Audit Report</a>                               <?php } ?>
                      <?php if ($_SESSION['MA_CESU']) {?>                     <a class="dropdown-item" href="<?php echo $base_url.'/Reports/02_CESU.php' ?>"><i class="fa fa-list-alt" aria-hidden="true"></i> CESU </a>                                   <?php } ?>
                      
                  </div>

            </li>




            <li >
              <a class="nav-link text-white" href="logout.php"> Logout </a>
            </li>
             
            <li class="nav-item">

              <a class="nav-link text-white " style="position:relative;left:40;background-color: #007a85;"><?php echo $_SESSION['userid']; ?></a>
              </li>
        
            








          </ul>
        </div>
      </nav>


</body>
</html> 
