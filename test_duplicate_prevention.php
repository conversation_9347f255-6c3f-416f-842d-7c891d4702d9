<?php
session_start();
include 'db.php';

// Set up session for testing (if not already logged in)
if (!isset($_SESSION['health_center'])) {
    $_SESSION['health_center'] = 'TEST_CENTER';
    $_SESSION['fullname'] = 'Test User';
}

echo "<h2>🧪 Duplicate Prevention Test</h2>";

// Test data for duplicate prevention
$testData = [
    'original' => [
        'NameOfChild' => 'Juan',
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => 'Maria Santos',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1',
        'Sex' => 'MALE',
        'PlaceofDelivery' => 'HOSPITAL',
        'Attendant' => 'DOCTOR',
        'TypeofDelivery' => 'NSD',
        'BCG' => 'YES',
        'BirthWeightInGrams' => '3000'
    ],
    'duplicate' => [
        'NameOfChild' => 'Juan',
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => 'Maria Santos',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1',
        'Sex' => 'MALE',
        'PlaceofDelivery' => 'HOSPITAL',
        'Attendant' => 'DOCTOR',
        'TypeofDelivery' => 'NSD',
        'BCG' => 'YES',
        'BirthWeightInGrams' => '3000'
    ],
    'similar' => [
        'NameOfChild' => 'Jon',  // Similar name
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => 'Maria Santos',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1',
        'Sex' => 'MALE',
        'PlaceofDelivery' => 'HOSPITAL',
        'Attendant' => 'DOCTOR',
        'TypeofDelivery' => 'NSD',
        'BCG' => 'YES',
        'BirthWeightInGrams' => '3000'
    ]
];

// Function to simulate form submission
function simulateRegistration($data, $label) {
    global $conn;
    
    echo "<h3>🎯 Test: $label</h3>";
    
    // Set POST data
    $_POST = array_merge($_POST, $data);
    $_POST['submitBtn'] = 'Submit';
    
    // Capture output
    ob_start();
    
    // Simulate the registration process from db.php
    $DateOfRegistration = date('Y-m-d');
    $DateOfBirth = $data['DateOfBirth'];
    $AgeofChild = '';
    $FamilySerialNumber = generateUniqueFamilySerial($conn);
    $ChildNumber = rand(100, 999);
    $LastNameOfChild = $data['LastNameOfChild'];
    $NameOfChild = $data['NameOfChild'];
    $middlename_of_child = '';
    $Sex = $data['Sex'];
    $NameofMother = $data['NameofMother'];
    $BirthdateofMother = '';
    $AgeeofMother = '';
    $Barangay = $data['Barangay'];
    $PurokStreetSitio = '';
    $HouseNo = '';
    $Address = $data['Address'];
    $PlaceofDelivery = $data['PlaceofDelivery'];
    $NameofFacility = '';
    $Attendant = $data['Attendant'];
    $TypeofDelivery = $data['TypeofDelivery'];
    $BirthWeightInGrams = $data['BirthWeightInGrams'];
    $BirthWeightClassification = '';
    $PhoneNumber = $data['PhoneNumber'];
    $approvedBy = $_SESSION['fullname'];
    $dateOfExpiration = date('Y-m-d', strtotime('+365 day'));
    
    // Check for duplicates
    $duplicateCheck = checkForDuplicates(
        $conn, 
        $NameOfChild, 
        $LastNameOfChild, 
        $DateOfBirth, 
        $NameofMother, 
        $PhoneNumber, 
        $Address, 
        $Barangay
    );
    
    $duplicateFound = $duplicateCheck['found'];
    $duplicateMessages = $duplicateCheck['messages'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Input Data:</h4>";
    echo "<ul>";
    echo "<li><strong>Name:</strong> {$NameOfChild} {$LastNameOfChild}</li>";
    echo "<li><strong>DOB:</strong> {$DateOfBirth}</li>";
    echo "<li><strong>Mother:</strong> {$NameofMother}</li>";
    echo "<li><strong>Phone:</strong> {$PhoneNumber}</li>";
    echo "<li><strong>Address:</strong> {$Address}</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Duplicate Check Results:</h4>";
    echo "<p><strong>Duplicates Found:</strong> " . ($duplicateFound ? 'YES' : 'NO') . "</p>";
    echo "<p><strong>Messages Count:</strong> " . count($duplicateMessages) . "</p>";
    
    if (!empty($duplicateMessages)) {
        echo "<h5>Messages:</h5>";
        foreach ($duplicateMessages as $msg) {
            $color = '';
            switch ($msg['type']) {
                case 'error': $color = 'red'; break;
                case 'warning': $color = 'orange'; break;
                case 'info': $color = 'blue'; break;
            }
            echo "<div style='background: #{$color}20; padding: 10px; margin: 5px 0; border-left: 4px solid $color;'>";
            echo "<strong>{$msg['title']}</strong><br>";
            echo "{$msg['message']}";
            echo "</div>";
        }
    }
    echo "</div>";
    
    // Check if insertion should be blocked
    if ($duplicateFound) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #f44336;'>";
        echo "<h4>🚫 REGISTRATION BLOCKED</h4>";
        echo "<p><strong>Reason:</strong> Exact duplicate found</p>";
        echo "<p><strong>Action:</strong> Data insertion prevented</p>";
        echo "<p><strong>Status:</strong> Registration failed due to duplicate detection</p>";
        
        // Log the blocked attempt
        $blocked_reason = "Test: Duplicate registration attempt blocked";
        $log_stmt = $conn->prepare("INSERT INTO duplicate_block_log (child_name, last_name, dob, mother_name, blocked_reason, blocked_date, blocked_by, health_center) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)");
        $blocked_by = $_SESSION['fullname'];
        $health_center = $_SESSION['health_center'];
        $log_stmt->bind_param("sssssss", $NameOfChild, $LastNameOfChild, $DateOfBirth, $NameofMother, $blocked_reason, $blocked_by, $health_center);
        
        if ($log_stmt->execute()) {
            echo "<p><strong>Logged:</strong> Blocked attempt recorded in database</p>";
        } else {
            echo "<p><strong>Log Error:</strong> " . $log_stmt->error . "</p>";
        }
        $log_stmt->close();
        
        echo "</div>";
        
    } else {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #4caf50;'>";
        echo "<h4>✅ REGISTRATION ALLOWED</h4>";
        echo "<p><strong>Status:</strong> No exact duplicates found</p>";
        echo "<p><strong>Action:</strong> Would proceed with data insertion</p>";
        
        if (!empty($duplicateMessages)) {
            echo "<p><strong>Warnings:</strong> " . count($duplicateMessages) . " potential issues detected</p>";
        }
        
        echo "<p><strong>Family Serial:</strong> {$FamilySerialNumber}</p>";
        echo "</div>";
    }
    
    $output = ob_get_clean();
    echo $output;
    
    return !$duplicateFound;
}

// Run tests
echo "<div style='background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>⚠️ Test Information</h3>";
echo "<p>This script tests the duplicate prevention system without actually inserting data into the database.</p>";
echo "<p>It simulates the registration process and shows how duplicates are detected and blocked.</p>";
echo "</div>";

// Test 1: Register original child
echo "<hr>";
$result1 = simulateRegistration($testData['original'], "Original Registration (Should Pass)");

// Test 2: Try to register exact duplicate
echo "<hr>";
$result2 = simulateRegistration($testData['duplicate'], "Exact Duplicate (Should Be Blocked)");

// Test 3: Try to register similar child
echo "<hr>";
$result3 = simulateRegistration($testData['similar'], "Similar Name (Should Show Warning)");

// Summary
echo "<hr>";
echo "<h3>📊 Test Summary</h3>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; background: white;'>";
echo "<tr style='background: #2196f3; color: white;'>";
echo "<th>Test Case</th><th>Expected Result</th><th>Actual Result</th><th>Status</th>";
echo "</tr>";

echo "<tr>";
echo "<td>Original Registration</td>";
echo "<td>Should Pass</td>";
echo "<td>" . ($result1 ? "Passed" : "Failed") . "</td>";
echo "<td style='color: " . ($result1 ? "green" : "red") . ";'>" . ($result1 ? "✅ PASS" : "❌ FAIL") . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td>Exact Duplicate</td>";
echo "<td>Should Be Blocked</td>";
echo "<td>" . (!$result2 ? "Blocked" : "Allowed") . "</td>";
echo "<td style='color: " . (!$result2 ? "green" : "red") . ";'>" . (!$result2 ? "✅ PASS" : "❌ FAIL") . "</td>";
echo "</tr>";

echo "<tr>";
echo "<td>Similar Name</td>";
echo "<td>Should Show Warning</td>";
echo "<td>" . ($result3 ? "Allowed with Warning" : "Blocked") . "</td>";
echo "<td style='color: " . ($result3 ? "green" : "orange") . ";'>" . ($result3 ? "✅ PASS" : "⚠️ REVIEW") . "</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

// Show blocked attempts log
echo "<hr>";
echo "<h3>📋 Recent Blocked Attempts</h3>";
try {
    $blocked_result = $conn->query("SELECT * FROM duplicate_block_log ORDER BY blocked_date DESC LIMIT 10");
    if ($blocked_result && $blocked_result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0' style='width: 100%; background: white;'>";
        echo "<tr style='background: #f44336; color: white;'>";
        echo "<th>Child Name</th><th>DOB</th><th>Mother</th><th>Blocked Date</th><th>Reason</th><th>Blocked By</th>";
        echo "</tr>";
        
        while ($row = $blocked_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['child_name']} {$row['last_name']}</td>";
            echo "<td>{$row['dob']}</td>";
            echo "<td>{$row['mother_name']}</td>";
            echo "<td>{$row['blocked_date']}</td>";
            echo "<td>{$row['blocked_reason']}</td>";
            echo "<td>{$row['blocked_by']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No blocked attempts found in the log.</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error retrieving blocked attempts: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Next Steps</h3>";
echo "<ol>";
echo "<li><strong>Live Test:</strong> Try the actual registration form with duplicate data</li>";
echo "<li><strong>Monitor Logs:</strong> Check the duplicate_block_log table for real attempts</li>";
echo "<li><strong>Review Settings:</strong> Adjust duplicate detection criteria if needed</li>";
echo "<li><strong>Train Users:</strong> Educate staff about the duplicate prevention system</li>";
echo "</ol>";

echo "<h3>🔗 Tools & Links</h3>";
echo "<p>";
echo "<a href='nip.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='duplicate_checker.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Duplicate Checker</a>";
echo "<a href='realtime_duplicate_check.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>⚡ Real-time Check</a>";
echo "<a href='setup_duplicate_prevention.php' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🛠️ Setup Status</a>";
echo "</p>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    border-collapse: collapse;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3, h4, h5 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}

ul, ol {
    line-height: 1.6;
}
</style>
