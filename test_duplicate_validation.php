<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duplicate Record Validation Test - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
        }
        
        .demo-container {
            margin-top: 30px;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            background: white;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f44336;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .validation-rule {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
            margin: 10px 0;
        }
        
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .toast-demo {
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        
        .toast-demo.success {
            background: #4caf50;
        }
        
        .toast-demo i {
            margin-right: 10px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        @media screen and (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container demo-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align">
                            <i class="material-icons left red-text">error</i>
                            Duplicate Record Validation System
                        </span>
                        
                        <p class="center-align">
                            Prevents duplicate child records with red toast notifications and detailed error messages.
                        </p>
                        
                        <!-- Validation Rules -->
                        <div class="demo-section">
                            <h4>🔍 Duplicate Detection Rules</h4>
                            
                            <div class="validation-rule">
                                <h5><i class="material-icons left orange-text">rule</i>Primary Validation Criteria</h5>
                                <p>A record is considered a duplicate if ALL of the following fields match an existing record:</p>
                                <ul>
                                    <li><strong>Child's First Name</strong> (NameOfChild)</li>
                                    <li><strong>Child's Last Name</strong> (LastNameOfChild)</li>
                                    <li><strong>Date of Birth</strong> (DateOfBirth)</li>
                                    <li><strong>Mother's Name</strong> (NameofMother)</li>
                                </ul>
                                <p><em>Note: Only active records are checked (deleted records are ignored)</em></p>
                            </div>
                            
                            <div class="feature-card">
                                <h5><i class="material-icons left red-text">security</i>Why These Fields?</h5>
                                <ul>
                                    <li><strong>Child's Full Name:</strong> Primary identifier for the child</li>
                                    <li><strong>Date of Birth:</strong> Unique biological identifier</li>
                                    <li><strong>Mother's Name:</strong> Family relationship verification</li>
                                    <li><strong>Combined Check:</strong> Extremely unlikely to have false positives</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Implementation Details -->
                        <div class="demo-section">
                            <h4>⚙️ Technical Implementation</h4>
                            
                            <h5>Database Query for Duplicate Check:</h5>
                            <div class="code-example">
SELECT id FROM nip_table 
WHERE NameOfChild = ? 
AND LastNameOfChild = ? 
AND DateOfBirth = ? 
AND NameofMother = ? 
AND (deleted IS NULL OR deleted = 0)
                            </div>
                            
                            <h5>PHP Implementation:</h5>
                            <div class="code-example">
// Check for duplicate records before inserting
$duplicate_check_sql = "SELECT id FROM nip_table 
                       WHERE NameOfChild = ? 
                       AND LastNameOfChild = ? 
                       AND DateOfBirth = ? 
                       AND NameofMother = ? 
                       AND (deleted IS NULL OR deleted = 0)";

$duplicate_stmt = mysqli_prepare($conn, $duplicate_check_sql);
mysqli_stmt_bind_param($duplicate_stmt, "ssss", 
    $NameOfChild, $LastNameOfChild, $DateOfBirth, $NameofMother);
mysqli_stmt_execute($duplicate_stmt);
$duplicate_result = mysqli_stmt_get_result($duplicate_stmt);

if (mysqli_num_rows($duplicate_result) > 0) {
    // Show red toast and error message
    // Stop execution - don't insert duplicate
}
                            </div>
                        </div>
                        
                        <!-- Toast Notifications -->
                        <div class="demo-section">
                            <h4>🍞 Toast Notification Examples</h4>
                            
                            <div class="comparison-grid">
                                <div>
                                    <h5>Duplicate Record Found (Red Toast)</h5>
                                    <div class="toast-demo">
                                        <i class="material-icons">error</i>
                                        Record already exists! Child with same name, birth date, and mother already registered.
                                    </div>
                                    
                                    <h6>Toast Configuration:</h6>
                                    <div class="code-example">
M.toast({
    html: "&lt;i class=\"material-icons left\"&gt;error&lt;/i&gt;Record already exists!",
    classes: "red darken-2 white-text",
    displayLength: 6000
});
                                    </div>
                                </div>
                                
                                <div>
                                    <h5>Record Successfully Added (Green Toast)</h5>
                                    <div class="toast-demo success">
                                        <i class="material-icons">check_circle</i>
                                        Record successfully added to the system!
                                    </div>
                                    
                                    <h6>Toast Configuration:</h6>
                                    <div class="code-example">
M.toast({
    html: "&lt;i class=\"material-icons left\"&gt;check_circle&lt;/i&gt;Record successfully added!",
    classes: "green darken-1 white-text",
    displayLength: 4000
});
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Error Messages -->
                        <div class="demo-section">
                            <h4>📋 Error Message Details</h4>
                            
                            <div class="feature-card">
                                <h5>Duplicate Record Error Card</h5>
                                <div class="card red lighten-4 red-text text-darken-4" style="margin: 15px 0;">
                                    <div class="card-content">
                                        <p><i class="material-icons left">error</i><span>Record already exists!</span></p>
                                        <p>A child with the same name, birth date, and mother is already registered in the system.</p>
                                        <p><strong>Child:</strong> Juan Dela Cruz</p>
                                        <p><strong>Birth Date:</strong> 2023-05-15</p>
                                        <p><strong>Mother:</strong> Maria Dela Cruz</p>
                                    </div>
                                </div>
                                
                                <h6>Features of Error Message:</h6>
                                <ul>
                                    <li>Clear error icon and title</li>
                                    <li>Explanation of why it's considered duplicate</li>
                                    <li>Shows the conflicting data fields</li>
                                    <li>Red color scheme for immediate attention</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Validation Flow -->
                        <div class="demo-section">
                            <h4>🔄 Validation Flow</h4>
                            
                            <div class="row">
                                <div class="col s12 m6 l3">
                                    <div class="card blue lighten-4">
                                        <div class="card-content center-align">
                                            <i class="material-icons large blue-text">input</i>
                                            <h6>1. Form Submission</h6>
                                            <p>User submits registration form</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <div class="card orange lighten-4">
                                        <div class="card-content center-align">
                                            <i class="material-icons large orange-text">search</i>
                                            <h6>2. Duplicate Check</h6>
                                            <p>Search for existing records with same key fields</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <div class="card red lighten-4">
                                        <div class="card-content center-align">
                                            <i class="material-icons large red-text">error</i>
                                            <h6>3. If Duplicate</h6>
                                            <p>Show red toast and error message</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6 l3">
                                    <div class="card green lighten-4">
                                        <div class="card-content center-align">
                                            <i class="material-icons large green-text">check_circle</i>
                                            <h6>4. If Unique</h6>
                                            <p>Insert record and show success toast</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Benefits -->
                        <div class="demo-section">
                            <h4>✅ Benefits of Duplicate Validation</h4>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <div class="card green lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">Data Integrity</span>
                                            <ul>
                                                <li>Prevents duplicate child records</li>
                                                <li>Maintains clean database</li>
                                                <li>Reduces data inconsistencies</li>
                                                <li>Improves reporting accuracy</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6">
                                    <div class="card blue lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">User Experience</span>
                                            <ul>
                                                <li>Immediate feedback with toast notifications</li>
                                                <li>Clear error messages</li>
                                                <li>Shows conflicting data details</li>
                                                <li>Prevents accidental re-registration</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12 m6">
                                    <div class="card orange lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">System Efficiency</span>
                                            <ul>
                                                <li>Reduces storage waste</li>
                                                <li>Improves query performance</li>
                                                <li>Simplifies data management</li>
                                                <li>Enhances system reliability</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col s12 m6">
                                    <div class="card purple lighten-4">
                                        <div class="card-content">
                                            <span class="card-title">Compliance</span>
                                            <ul>
                                                <li>Meets data quality standards</li>
                                                <li>Supports audit requirements</li>
                                                <li>Ensures record uniqueness</li>
                                                <li>Maintains data governance</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Testing Scenarios -->
                        <div class="demo-section">
                            <h4>🧪 Testing Scenarios</h4>
                            
                            <div class="validation-rule">
                                <h5>Scenario 1: Exact Duplicate</h5>
                                <p><strong>Existing Record:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                                <p><strong>New Submission:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                                <p><strong>Result:</strong> ❌ Blocked - Red toast notification</p>
                            </div>
                            
                            <div class="validation-rule">
                                <h5>Scenario 2: Different Birth Date</h5>
                                <p><strong>Existing Record:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                                <p><strong>New Submission:</strong> Juan Dela Cruz, Born: 2023-06-15, Mother: Maria Dela Cruz</p>
                                <p><strong>Result:</strong> ✅ Allowed - Different birth date</p>
                            </div>
                            
                            <div class="validation-rule">
                                <h5>Scenario 3: Different Mother</h5>
                                <p><strong>Existing Record:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                                <p><strong>New Submission:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Ana Santos</p>
                                <p><strong>Result:</strong> ✅ Allowed - Different mother</p>
                            </div>
                            
                            <div class="validation-rule">
                                <h5>Scenario 4: Similar Name (Different Spelling)</h5>
                                <p><strong>Existing Record:</strong> Juan Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                                <p><strong>New Submission:</strong> John Dela Cruz, Born: 2023-05-15, Mother: Maria Dela Cruz</p>
                                <p><strong>Result:</strong> ✅ Allowed - Different first name spelling</p>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="demo-section">
                            <h4>🔗 Test the Duplicate Validation</h4>
                            
                            <div class="row">
                                <div class="col s12 m6 l4">
                                    <a href="nip.php" class="btn blue waves-effect full-width">
                                        <i class="material-icons left">add</i>Test Registration Form
                                    </a>
                                    <p><small>Try registering a new child</small></p>
                                </div>
                                
                                <div class="col s12 m6 l4">
                                    <a href="filter_records.php" class="btn green waves-effect full-width">
                                        <i class="material-icons left">search</i>View Existing Records
                                    </a>
                                    <p><small>Check existing children</small></p>
                                </div>
                                
                                <div class="col s12 m6 l4">
                                    <a href="/nip/" class="btn orange waves-effect full-width">
                                        <i class="material-icons left">dashboard</i>Return to Dashboard
                                    </a>
                                    <p><small>Go back to main menu</small></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="center-align" style="margin-top: 30px;">
                            <a href="nip.php" class="btn large red waves-effect">
                                <i class="material-icons left">bug_report</i>Test Duplicate Validation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Demo toast notifications
            setTimeout(function() {
                M.toast({
                    html: '<i class="material-icons left">info</i>This is a demo of the duplicate validation system!',
                    classes: 'blue darken-1 white-text',
                    displayLength: 3000
                });
            }, 1000);
        });
    </script>
</body>
</html>
