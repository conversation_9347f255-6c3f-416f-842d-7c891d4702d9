<?php session_start(); include 'db.php'; ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php include 'style.php'; ?>
    <meta charset="UTF-8">
    <meta name="viewport" 
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>National Immunization Program</title>
    <script src="https://code.highcharts.com/highcharts.js"></script>  
    <script src="https://code.highcharts.com/modules/variable-pie.js"></script>
    <style>
    
        .pagination li.active a {
    color: #fff;
    
    background: #42a5f5;
}
table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        tbody tr:nth-child(even) {
            background-color:white;
        }
        tbody tr:nth-child(odd) {
            background-color:#eef3f7;
        }
       thead {
            background-color: #f2f2f2;
        }
         th {
            padding: 15px;
            text-align: start !important;
            font-weight: 500;
            color: #666;
            border-bottom: 1px solid #ddd;
        }
        th, td {
            border: 1px solid #b0bec5;
            padding: 6px;
            font-size:13.5px;
            text-align: left;
        }
         
         tbody tr:hover {
            background-color: #f9f9f9;
        }
  
 
    .dataTables_paginate  {
        display: inline-block !important;
            padding: 5px 10px !important;
            margin: 0 3px !important;
            border: 1px solid #ccc !important;
            border-radius: 4px !important;
            text-decoration: none !important;
            color: #666 !important;
    }
    .pagination li.disabled a {
    cursor: default;
    color: white !important;
}
 .dt-button   {
    background:#5e9834 !important;
    border:none !important;
    color:white !important;
    font-weight:500 !important;
    padding-top:0.4em !important;
}
nav { 
    color: #fff;
     background-color: white; 
    width: 100%;
    /* height: 56px; */
    color:black !important;
    
} 
.dt-paging-button ,.current {
    background:white !important;
    color:black !important;
}
    /* Modern Table Styles */
    .dataTables_wrapper {
        margin: 20px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }

   

    /* Container Styles */
    .container {
        
        background: white;
        border-radius: 4px;
        
    }

    /* Header Styles */
    h5 {
        
        font-weight: 500;
        margin-bottom: 20px;
        padding-bottom: 10px;
        
    }

   
 
    /* Search Input Styles */
    .dataTables_filter input {
        padding: 8px 12px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        font-size: 13px;
        transition: border-color 0.3s ease;
    }

    table {
            width: 100%;
            border: none;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

    /* Pagination Styles */
    .dataTables_paginate .paginate_button {
        padding: 8px 12px !important;
        margin: 0 2px !important;
        border-radius: 4px !important;
        border: 1px solid #e0e0e0 !important;
        background: white !important;
        color: #1976d2 !important;
    }

    .dataTables_paginate .paginate_button.current {
        background: #1976d2 !important;
        color: white !important;
        border-color: #1976d2 !important;
    }

    .dataTables_paginate .paginate_button:hover {
        background: #e3f2fd !important;
        border-color: #1976d2 !important;
    }

  
    

     

    .btn {
        background: #1976d2;
        color: white;
        
        border-radius: 1px;
        border: none;
        font-weight: 500;
       
    }

    input[type=search] {
        height: 1.7em !important;
        background-color: transparent;
    border: 1px solid #cbcbcb  !important;
    border-bottom: 1px solid #cbcbcb;
    border-radius: 3px !important;
    outline: none;
     
    width: 100%;
    font-size: 16px;
    margin: 0 0 8px 0;
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    -webkit-transition: border .3s, -webkit-box-shadow .3s;
    transition: border .3s, -webkit-
    }
    </style>
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
   <script src="https://cdn.datatables.net/2.1.8/js/dataTables.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/dataTables.buttons.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.dataTables.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.html5.min.js"></script>
   <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.css">
   <link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.2.0/css/buttons.dataTables.css">


   <script src="https://code.highcharts.com/modules/exporting.js"></script>
<!-- optional -->
<script src="https://code.highcharts.com/modules/offline-exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script>
</head>
<body class="  " style="background:#fafafa;">
    <?php include 'nav.php'; ?>

    <?php
           if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
            $sql = "SELECT PurokStreetSitio, COUNT(*) AS counts FROM nip_table WHERE deleted = 0";
            if(isset($_POST['btn'])) {
                $from = $_POST['from'];
                $to = $_POST['to'];
                $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
            }
        } else {
            $sql = "SELECT PurokStreetSitio, COUNT(*) AS counts FROM nip_table WHERE deleted = 0 AND Barangay = ?";
            if(isset($_POST['btn'])) {
                $from = $_POST['from'];
                $to = $_POST['to'];
                $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
            }
        }
        
        $stmt = $conn->prepare($sql);
        if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
            if(isset($_POST['btn'])) {
                $stmt->bind_param("ss", $from, $to);
            }
        } else {
            if(isset($_POST['btn'])) {
                $stmt->bind_param("sss", $_SESSION['health_center'], $from, $to);
            } else {
                $stmt->bind_param("s", $_SESSION['health_center']);
            }
        }
        $stmt->execute();
        $result = $stmt->get_result();
 
                     $row = $result->fetch_assoc();   $ct = number_format($row['counts']); ?>



   

  <!-- Modal Structure -->
  <div id="modal1" class="modal">
      <h4 style="font-weight:500;font-size:20px;padding-top:15px;padding-bottom:15px;padding-left:25px;" class="blue darken-3 white-text">Filter Data</h4>
    <div class="modal-content">
    <form action="" method="POST" class=" "  >
 
    <div class="row  ">
        <div class="input-field col s12 m12">
            <input name="from"  id="from" type="date" class="validate">
            <label class="active "  for="from">From</label>
        </div>
        <div class="input-field col s12 m12">
            <input name="to" id="to" type="date" class="validate">
            <label class="active" for="to">To</label>
        </div>
        <div class="input-field col s12 m3">
            <button name="btn" class="small-btn btn blue-grey darken-2">Apply</button>
        </div>
    </form>
    </div>
    <div class="modal-footer">
      <a href="#!" class="modal-close waves-effect waves-green btn-flat">Cancel</a>
    </div>
  </div>
  </div>
  
  <?php 
  if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {

    $sql = "SELECT DateOfRegistration, COUNT(*) AS count FROM nip_table WHERE deleted = 0";
    if(isset($_POST['btn'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }
    $sql .= " GROUP BY DateOfRegistration ORDER BY DateOfRegistration";
}
else {

    $sql = "SELECT DateOfRegistration, COUNT(*) AS count FROM nip_table WHERE deleted = 0 AND Barangay = ?";
    if(isset($_POST['btn'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }
    $sql .= " GROUP BY DateOfRegistration ORDER BY DateOfRegistration";
}
$stmt = $conn->prepare($sql);
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    if(isset($_POST['btn'])) {
        $stmt->bind_param("ss", $from, $to);
    }
} else {
    if(isset($_POST['btn'])) {
        $stmt->bind_param("sss", $_SESSION['health_center'], $from, $to);
    } else {
        $stmt->bind_param("s", $_SESSION['health_center']);
    }
}

$stmt->execute();
$result = $stmt->get_result();

$dates = [];
$counts = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $dates[] = date('F d,Y',strtotime($row['DateOfRegistration']));
        $counts[] = $row['count'];
    }
}

$datesJson = json_encode($dates);
$countsJson = json_encode($counts);
  
?>
  
    <div class="container m12 s12 white" >
    <h5 class="black-text " style="font-weight:500;padding-left:10px;padding-top:10px; ">DATA ANALYSIS:NATIONAL IMMUNIZATION PROGRAM  <a class="btn-floating btn-small right   waves-light blue modal-trigger  " href="#modal1"><i class="material-icons">search</i></a></h5>
       
    <div class="row   white-text" style="background:rgb(11, 51, 92);" >
    <div class="col m12 s12" style="padding-left:10px;padding-bottom:0px;">
        <h4  style="font-size:20px !important;font-weight: bold;">DESCRIPTIVE ANALYSIS BY TIME</h4>
    </div>
</div>
    <h5 class="" style="  font-size:22.2px;padding-top:7px;padding-bottom:8px;padding:15px;">Distribution of National Immunization Program Per day (N=<?php echo $ct; ?>),<?php echo $_SESSION['health_center']; ?> <br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span></h5>
        <div id="registrationChart" style="width: 100%; height: 450px; "></div> 
    </div>
    <div class="container m12 s12 white" >
     
        <h5 class="" style="  font-size:22.2px;padding-top:7px;padding-bottom:8px;padding:15px;">National Immunization Program Per Month (N=<?php echo $ct; ?>),<?php echo $_SESSION['health_center']; ?> <br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span></h5>
        <div id="permonth" style="width: 100%; height: 450px; "></div> 
    </div>
    <div class="container m12 s12 white" >
     
        <h5 class="" style="  font-size:22.2px;padding-top:7px;padding-bottom:8px;padding:15px;">National Immunization Program Per Quarter (N=<?php echo $ct; ?>),<?php echo $_SESSION['health_center']; ?> <br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span></h5>
        <div id="perquarter" style="width: 100%; height: 450px; "></div> 
    </div>
   
    <div class="container m12 s12 white" style=" padding:15px;">
        
    <div class="row   white-text" style="background:rgb(11, 51, 92);" >
    <div class="col m12 s12" style="padding-left:10px;padding-bottom:0px;">
        <h4  style="font-size:20px !important;font-weight: bold;">DESCRIPTIVE ANALYSIS BY PERSON</h4>
    </div>
</div>
    <h5 class="" style="  font-size:22.6px;padding-top:7px;padding-bottom:8px;">Distribution of National Immunization Program by Gender,<?php echo $_SESSION['health_center']; ?>
<br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span></h5>
        
    <table class="white" id="mainTable2">
    <thead class=" ">
        <tr>
            <th>Sex</th>
            <th>Number</th>
        </tr>
    </thead>
    <tbody>
        <?php
        if (isset($_POST['btn'])) {
            $from = $_POST['from'];
            $to = $_POST['to'];
            $health_center = $_SESSION['health_center'];

            $grand_total = 0;

            if ($health_center == 'CITY HEALTH OFFICE') {
                $sql = "SELECT Sex, COUNT(*) AS counts 
                        FROM nip_table 
                        WHERE DateOfRegistration BETWEEN ? AND ? AND deleted = 0 
                        GROUP BY Sex";
            } else {
                $sql = "SELECT Sex, COUNT(*) AS counts 
                        FROM nip_table 
                        WHERE deleted = 0 AND Barangay = ? AND DateOfRegistration BETWEEN ? AND ? 
                        GROUP BY Sex";
            }

            $stmt = $conn->prepare($sql);

            if ($health_center == 'CITY HEALTH OFFICE') {
                $stmt->bind_param("ss", $from, $to);
            } else {
                $stmt->bind_param("sss", $health_center, $from, $to);
            }

            $stmt->execute();
            $result = $stmt->get_result();

            while ($row = $result->fetch_assoc()) {
                $grand_total += $row['counts'];
                ?>
                <tr>
                    <td><?php echo $row['Sex']; ?></td>
                    <td><?php echo $row['counts']; ?></td>
                </tr>
                <?php
            }
            $stmt->close();
        }
        ?>
    </tbody>
    <tfoot>
        <tr>
            <th>Total</th>
            <th><?php echo isset($grand_total) ? $grand_total : 0; ?></th>
        </tr>
    </tfoot>
</table>


    </div>


    <div class="container m12 s12 white" style=" padding:15px;">
        <?php
        if(isset($_POST))
                $sql = "SELECT PurokStreetSitio,COUNT(*) AS counts,SUM(PurokStreetSitio) as sm,AVG(PurokStreetSitio) AS av FROM nip_table WHERE deleted = 0 AND Barangay = '".$_SESSION['health_center']."'  AND deleted = 0 GROUP BY PurokStreetSitio ";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $result = $stmt->get_result();
                $row = $result->fetch_assoc()
                
        ?>
 <div class="row   white-text" style="background:rgb(11, 51, 92);" >
    <div class="col m12 s12" style="padding-left:10px;padding-bottom:0px;">
        <h4  style="font-size:20px !important;font-weight: bold;">DESCRIPTIVE ANALYSIS BY PLACE</h4>
    </div>
</div>
      
    <h5 class="" style="  font-size:22.6px;padding-top:7px;padding-bottom:8px;">Distribution of National Immunization Program Per Purok/Sitio NIP Parañaque City (N=<?php echo $ct; ?>),<?php echo $_SESSION['health_center']; ?><br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span></h5>
        
        <table class="white" id="pr">
   <thead>
        <tr>
            <th>Purok/Sitio</th>
            <th>Barangay</th>
            <th>Total</th>
            <th>Percentage</th>
            <th>SUM</th>
            <th>AVG</th>
        </tr>
    </thead>
    <tbody>
    <?php
if(isset($_POST['btn'])) { 
    $from = $_POST['from'];
    $to = $_POST['to'];
    $health_center = $_SESSION['health_center'];

    if ($health_center == 'CITY HEALTH OFFICE') {
        $sql = "SELECT Barangay, PurokStreetSitio, COUNT(*) AS counts 
                FROM nip_table  
                WHERE DateOfRegistration BETWEEN ? AND ? AND deleted = 0
                GROUP BY PurokStreetSitio, Barangay";
    } else {
        $sql = "SELECT Barangay, PurokStreetSitio, COUNT(*) AS counts 
                FROM nip_table 
                WHERE deleted = 0 AND Barangay = ? AND DateOfRegistration BETWEEN ? AND ?
                GROUP BY PurokStreetSitio, Barangay";
    }

    // Get total count for percentage calculation
    $sql_total = "SELECT COUNT(*) AS total FROM nip_table WHERE deleted = 0";
    $stmt_total = $conn->prepare($sql_total);
    $stmt_total->execute();
    $result_total = $stmt_total->get_result();
    $total_count = $result_total->fetch_assoc()['total'];
    $stmt_total->close();

    // Prepare statement
    $stmt = $conn->prepare($sql);

    if ($health_center == 'CITY HEALTH OFFICE') {
        $stmt->bind_param("ss", $from, $to);
    } else {
        $stmt->bind_param("sss", $health_center, $from, $to);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    $grand_total = 0;
    $sum = 0;
    $count_rows = 0;

    while ($row = $result->fetch_assoc()) {
        $count = $row['counts'];
        $grand_total += $count;
        $sum += $count;
        $count_rows++;
        $percentage = ($total_count > 0) ? ($count / $total_count) * 100 : 0;
?>
        <tr>
            <td><?php echo $row['PurokStreetSitio']; ?></td>
            <td><?php echo $row['Barangay']; ?></td>
            <td><?php echo $count; ?></td>
            <td><?php echo number_format($percentage,0); ?>%</td>
            <td><?php echo $sum; ?></td>
            <td><?php echo ($count_rows > 0) ? number_format($sum / $count_rows, 2) : 0; ?></td>
        </tr>
<?php 
    } 
    $stmt->close();
} 
?>

    </tbody>
    <tfoot class="black-text">
        <tr>
            <th colspan="2">Total</th>
            <th><?php echo $grand_total; ?></th>
            <th>100%</th>

      
            <th><?php echo $sum; ?></th>
            <th><?php echo ($count_rows > 0) ? number_format($sum / $count_rows, 2) : 0; ?></th>
        </tr>
    </tfoot>
</table>

    </div>



   





    <?php
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
?>
    <div class="container m12 s12 white" style=" padding:15px;">
<?php 
} else { 
?>
    <div class="container m12 " style="display:none;">
<?php 
} 
?>



<h5 style="font-size:22.6px; padding-top:7px; padding-bottom:8px;">
    Distribution of National Immunization Program by Barangay, <?php echo $_SESSION['health_center']; ?>
    <br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span>
</h5>

<table class="white" id="brgy">
    <thead>
        <tr>
            <th>Barangay</th>
            <th>Number</th>
        </tr>
    </thead>
    <tbody>
        <?php
        if (isset($_POST['btn'])) {
            $from = $_POST['from'];
            $to = $_POST['to'];

            if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
                $sql = "SELECT COUNT(*) AS brgy_count, Barangay 
                        FROM nip_table 
                        WHERE deleted = 0 AND DateOfRegistration BETWEEN ? AND ? 
                        GROUP BY Barangay 
                        ORDER BY brgy_count DESC";

                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ss", $from, $to);
                $stmt->execute();
                $result = $stmt->get_result();

                while ($row = $result->fetch_assoc()) { ?>
                    <tr>
                        <td><?php echo $row['Barangay']; ?></td>
                        <td><?php echo $row['brgy_count']; ?></td>
                    </tr>
                <?php 
                }
                $stmt->close();
            }
        } 
        ?>
    </tbody>
</table>
 
    </div>

    <?php
     if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    ?>
 <div class="container m12 s12 white" style=" padding:15px;">
        <?php } else {   ?>
    <div class="container m12 " style="display:none;">
    <?php } ?>
    <h5 class="" style="  font-size:22.6px;padding-top:7px;padding-bottom:8px;">Distribution of National Immunization Program by Health Facility,<?php echo $_SESSION['health_center']; ?>
<br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span>
</h5>
        
    <table class="white" id="NameofFacility">
    <thead>
        <tr>
            <th>Name of Facility</th>
            <th>Number</th>
            <th>Percentage</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $grand_total = 0;
        $grand_percentage = 0;

        if (isset($_POST['btn'])) {
            $from = $_POST['from'];
            $to = $_POST['to'];

            if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
                // Get total count of all facilities within date range
                $sql_total = "SELECT COUNT(*) AS total_count FROM nip_table WHERE deleted = 0 AND DateOfRegistration BETWEEN ? AND ?";
                $stmt_total = $conn->prepare($sql_total);
                $stmt_total->bind_param("ss", $from, $to);
                $stmt_total->execute();
                $result_total = $stmt_total->get_result();
                $total_count = $result_total->fetch_assoc()['total_count'];
                $stmt_total->close();

                // Get count per facility within date range
                $sql = "SELECT COUNT(*) AS NameofFacility_count, NameofFacility FROM nip_table WHERE deleted = 0 AND DateOfRegistration BETWEEN ? AND ? GROUP BY NameofFacility ORDER BY NameofFacility_count DESC";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ss", $from, $to);
                $stmt->execute();
                $result = $stmt->get_result();

                while ($row = $result->fetch_assoc()) {
                    $facility_count = $row['NameofFacility_count'];
                    $percentage = ($total_count > 0) ? ($facility_count / $total_count) * 100 : 0;
                    $grand_total += $facility_count;
                    $grand_percentage += $percentage;
                    ?>
                    <tr>
                        <td><?php echo $row['NameofFacility']; ?></td>
                        <td><?php echo $facility_count; ?></td>
                        <td><?php echo number_format($percentage, 2); ?>%</td>
                    </tr>
                    <?php
                }
                $stmt->close();
            } else {
                echo '';
            }
        }
        ?>
    </tbody>
    <tfoot>
        <tr>
            <th>Grand Total</th>
            <th><?php echo $grand_total; ?></th>
            <th><?php echo number_format($grand_percentage, 2); ?>%</th>
        </tr>
    </tfoot>
</table>


    </div>





    <div class="container m12 s12 white" style=" padding:15px;">
    <h5 class="" style="  font-size:22.6px;padding-top:7px;padding-bottom:8px;">Distribution of National Immunization Program by Antigen,<?php echo $_SESSION['health_center']; ?>
<br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span>
</h5>
        
 
            <?php
            if($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {

                
                $sql = "
SELECT 
    (
        SELECT COUNT(*)
        FROM nip_table WHERE BCG = 'YES' AND deleted = 0
    ) AS total_BCG,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE BCG = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_BCG,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE BCG = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_BCG,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB1 = 'YES' AND deleted = 0
    ) AS total_PENTAHIB1,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB1 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_PENTAHIB1,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB1 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_PENTAHIB1,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB2 = 'YES' AND deleted = 0
    ) AS total_PENTAHIB2,


      (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB2 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_PENTAHIB2,
       (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB2 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_PENTAHIB2,






    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB3 = 'YES' AND deleted = 0
    ) AS total_PENTAHIB3,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB3 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_PENTAHIB3,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB3 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_PENTAHIB3,




    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV1 = 'YES' AND deleted = 0
    ) AS total_OPV1, 
     
    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV1 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_OPV1,
(
        SELECT COUNT(*)
        FROM nip_table WHERE OPV1 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_OPV1,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV2 = 'YES' AND deleted = 0
    ) AS total_OPV2,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV2 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_OPV2,
(
        SELECT COUNT(*)
        FROM nip_table WHERE OPV2 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_OPV2,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV3 = 'YES' AND deleted = 0
    ) AS total_OPV3,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV3 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_OPV3,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV3 = 'YES'AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_OPV3,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAatBirth = 'YES' AND deleted = 0
    ) AS total_HEPAatBirth,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAatBirth = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_HEPAatBirth,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAatBirth = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_HEPAatBirth,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAB1 = 'YES' AND deleted = 0
    ) AS total_HEPAB1,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAB1 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_HEPAB1,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAB1 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_HEPAB1,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV19monthstobelow12months = 'YES' AND deleted = 0
    ) AS total_MCV1,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV19monthstobelow12months = 'YES'  AND Sex = 'MALE' AND deleted = 0
    ) AS male_MCV1,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV19monthstobelow12months = 'YES'  AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_MCV1,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE MMR12MOSTO15MOS = 'YES' AND deleted = 0
    ) AS total_MCV2,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE MMR12MOSTO15MOS = 'YES'  AND Sex = 'MALE' AND deleted = 0
    ) AS male_MCV2,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE MMR12MOSTO15MOS = 'YES'  AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_MCV2,


      (
        SELECT COUNT(*)
        FROM nip_table WHERE CIC = 'YES'  AND    deleted = 0
    ) AS total_CIC,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE CIC = 'YES' AND Sex = 'MALE'  AND  deleted = 0
    ) AS male_CIC,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE CIC = 'YES' AND Sex = 'FEMALE'  AND   deleted = 0
    ) AS female_CIC,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE FIC = 'YES' AND deleted = 0
    ) AS total_FIC,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE FIC = 'YES'  AND Sex = 'MALE' AND deleted = 0
    ) AS male_FIC,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE FIC = 'YES'  AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_FIC,




    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT1 = 'YES' AND deleted = 0
    ) AS total_TT1,
(
        SELECT COUNT(*)
        FROM nip_table WHERE TT1 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_TT1,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT1 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_TT1,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT2 = 'YES' AND deleted = 0
    ) AS total_TT2,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE TT2 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_TT2,
       (
        SELECT COUNT(*)
        FROM nip_table WHERE TT2 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_TT2,

(
        SELECT COUNT(*)
        FROM nip_table WHERE TT3 = 'YES'    AND deleted = 0
    ) AS total_TT3,

    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT3 = 'YES' AND Sex = 'MALE' AND deleted = 0
    ) AS male_TT3,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT3 = 'YES' AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_TT3,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT4 = 'YES' AND deleted = 0
    ) AS total_TT4,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT4 = 'YES'  AND Sex = 'MALE' AND deleted = 0
    ) AS male_TT4,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT4 = 'YES'  AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_TT4,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT5 = 'YES' AND deleted = 0
    ) AS total_TT5,

 (
        SELECT COUNT(*)
        FROM nip_table WHERE TT5 = 'YES'   AND Sex = 'MALE' AND deleted = 0
    ) AS male_TT5,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT5 = 'YES'  AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_TT5,


    -- Calculate the total count of records in nip_table
    (
        SELECT COUNT(*)
        FROM nip_table  WHERE deleted = 0  
    ) AS total_records 
    
    

                
                ";
            }
            else {
                $sql = "
                SELECT 
                    (
                        SELECT COUNT(*)
                        FROM nip_table WHERE BCG = 'YES' AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0 AND deleted = 0
                    )   AS total_BCG,
                    (
                        SELECT COUNT(*)
                        FROM nip_table WHERE BCG = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0 AND deleted = 0
                    ) AS male_BCG,
                    (
                        SELECT COUNT(*)
                        FROM nip_table WHERE BCG = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0 AND deleted = 0
                    ) AS female_BCG,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB1 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_PENTAHIB1,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB1 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_PENTAHIB1,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB1 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_PENTAHIB1,

    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB2 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_PENTAHIB2,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB2 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_PENTAHIB2,
       (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB2 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_PENTAHIB2,

    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB3 = 'YES' AND Sex = 'MALE' AND    Barangay = '".$_SESSION['health_center']."' AND deleted = 0
    ) AS male_PENTAHIB3,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB3 = 'YES' AND Sex = 'FEMALE' AND    Barangay = '".$_SESSION['health_center']."' AND deleted = 0
    ) AS female_PENTAHIB3,

    (
        SELECT COUNT(*)
        FROM nip_table WHERE PENTAHIB3 = 'YES' AND Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_PENTAHIB3,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV1 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_OPV1,
(
        SELECT COUNT(*)
        FROM nip_table WHERE OPV1 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_OPV1,
(
        SELECT COUNT(*)
        FROM nip_table WHERE OPV1 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_OPV1,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV2 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_OPV2,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV2 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_OPV2,
(
        SELECT COUNT(*)
        FROM nip_table WHERE OPV2 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_OPV2,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV3 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_OPV3,
(
        SELECT COUNT(*)
        FROM nip_table WHERE OPV3 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_OPV3,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE OPV3 = 'YES'AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_OPV3,




    (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAatBirth = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_HEPAatBirth,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAatBirth = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_HEPAatBirth,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAatBirth = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_HEPAatBirth,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAB1 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_HEPAB1,
(
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAB1 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_HEPAB1,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE HEPAB1 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_HEPAB1, 


    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV19monthstobelow12months = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_MCV1,
(
        SELECT COUNT(*)
        FROM nip_table WHERE AMV19monthstobelow12months = 'YES'  AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_MCV1,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV19monthstobelow12months = 'YES'  AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_MCV1,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE MMR12MOSTO15MOS = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_MCV2,
(
        SELECT COUNT(*)
        FROM nip_table WHERE MMR12MOSTO15MOS = 'YES'  AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_MCV2,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE MMR12MOSTO15MOS = 'YES'  AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_MCV2,


    
  
    (
        SELECT COUNT(*)
        FROM nip_table WHERE CIC = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_CIC,
     (
        SELECT COUNT(*)
        FROM nip_table WHERE CIC = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_CIC,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE CIC = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_CIC,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV2_16MOSTO5YRSOLD = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_AMV2_16MOSTO5YRSOLD,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV2_16MOSTO5YRSOLD = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_AMV2_16MOSTO5YRSOLD,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE AMV2_16MOSTO5YRSOLD = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_AMV2_16MOSTO5YRSOLD,

(
        SELECT COUNT(*)
        FROM nip_table WHERE FIC = 'YES' AND deleted = 0
    ) AS total_FIC,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE FIC = 'YES' AND    Barangay = '".$_SESSION['health_center']."'  AND Sex = 'MALE' AND deleted = 0
    ) AS male_FIC,
 (
        SELECT COUNT(*)
        FROM nip_table WHERE FIC = 'YES' AND    Barangay = '".$_SESSION['health_center']."'  AND Sex = 'FEMALE' AND deleted = 0
    ) AS female_FIC,

    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT1 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_TT1,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT1 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_TT1,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT1 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_TT1,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT2 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_TT2,
       (
        SELECT COUNT(*)
        FROM nip_table WHERE TT2 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_TT2,
       (
        SELECT COUNT(*)
        FROM nip_table WHERE TT2 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_TT2,




    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT3 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_TT3,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT3 = 'YES' AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_TT3,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT3 = 'YES' AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_TT3,


    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT4 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_TT4,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT4 = 'YES'  AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_TT4,
    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT4 = 'YES'  AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_TT4,



    (
        SELECT COUNT(*)
        FROM nip_table WHERE TT5 = 'YES'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_TT5,

 (
        SELECT COUNT(*)
        FROM nip_table WHERE TT5 = 'YES'   AND Sex = 'MALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS male_TT5,
      (
        SELECT COUNT(*)
        FROM nip_table WHERE TT5 = 'YES'  AND Sex = 'FEMALE'  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS female_TT5,


    -- Calculate the total count of records in nip_table  AND    Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    (
        SELECT COUNT(*)
        FROM nip_table    WHERE Barangay = '".$_SESSION['health_center']."'  AND deleted = 0
    ) AS total_records FROM nip_table WHERE Barangay = '".$_SESSION['health_center']."'  AND deleted = 0 AND    deleted = 0  
                
                
                ";
              
            }
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $result = $stmt->get_result();
 
                $row = $result->fetch_assoc();  
                $male =    number_format($row['male_BCG'] + $row['male_PENTAHIB1'] + $row['male_PENTAHIB2'] + $row['male_PENTAHIB3'] + $row['male_OPV1'] + $row['male_OPV2'] + $row['male_OPV3'] + $row['male_HEPAatBirth'] + $row['male_HEPAB1'] + $row['male_MCV1'] + $row['male_MCV2'] + $row['male_CIC'] + $row['male_AMV2_16MOSTO5YRSOLD'] + $row['male_TT1'] + $row['male_TT2'] + $row['male_TT3'] + $row['male_TT4'] + $row['male_TT5']); 
                $female =  number_format($row['female_BCG'] + $row['female_PENTAHIB1'] + $row['female_PENTAHIB2'] + $row['female_PENTAHIB3'] + $row['female_OPV1'] + $row['female_OPV2'] + $row['female_OPV3'] + $row['female_HEPAatBirth'] + $row['female_HEPAB1'] + $row['female_MCV1'] + $row['female_MCV2'] + $row['female_CIC'] + $row['female_AMV2_16MOSTO5YRSOLD'] + $row['female_TT1'] + $row['female_TT2'] + $row['female_TT3'] + $row['female_TT4'] + $row['female_TT5']); 
  
               
                   
                     ?>
 
        <table id="mainTable3" class=" " style="width:100%">
         <thead>
                <tr>
                <th>Antigen</th>
                <th>Number</th>
               <th>Male</th>
               <th>Female</th>
                </tr>
            </thead>
    <tbody>
        <tr><td>BCG</td><td><?= $row['total_BCG'] ?></td><td><?= $row['male_BCG'] ?></td><td><?= $row['female_BCG'] ?></td></tr>
        <tr><td>PENTAHIB1</td><td><?= $row['total_PENTAHIB1'] ?></td><td><?= $row['male_PENTAHIB1']; ?></td><td><?= $row['female_PENTAHIB1'] ?></td></tr>
        <tr><td>PENTAHIB2</td><td><?= $row['total_PENTAHIB2'] ?></td><td><?= $row['male_PENTAHIB2']; ?></td><td><?= $row['female_PENTAHIB2'] ?></td></tr>
        <tr><td>PENTAHIB3</td><td><?= $row['total_PENTAHIB3'] ?></td><td><?= $row['male_PENTAHIB3']; ?></td><td><?= $row['female_PENTAHIB3'] ?></td></tr>
        <tr><td>OPV1</td><td><?= $row['total_OPV1'] ?></td><td><?= $row['male_OPV1'] ?></td><td><?= $row['female_OPV1'] ?></td></tr>
        <tr><td>OPV2</td><td><?= $row['total_OPV2'] ?></td><td><?= $row['male_OPV2'] ?></td><td><?= $row['female_OPV2'] ?></td></tr>
        <tr><td>OPV3</td><td><?= $row['total_OPV3'] ?></td><td><?= $row['male_OPV3'] ?></td><td><?= $row['female_OPV3'] ?></td></tr>
        <tr><td>HEPA at Birth</td><td><?= $row['total_HEPAatBirth'] ?></td><td><?= $row['male_HEPAatBirth'] ?></td><td><?= $row['female_HEPAatBirth'] ?></td></tr>
        <tr><td>HEPAB1</td><td><?= $row['total_HEPAB1'] ?></td><td><?= $row['male_HEPAB1'] ?></td><td><?= $row['female_HEPAB1'] ?></td></tr>
        <tr><td>MCV1</td><td><?= $row['total_MCV1'] ?></td><td><?= $row['male_MCV1'] ?></td><td><?= $row['female_MCV1'] ?></td></tr>
        <tr><td>MCV2</td><td><?= $row['total_MCV2'] ?></td><td><?= $row['male_MCV2'] ?></td><td><?= $row['female_MCV2'] ?></td></tr>
        <tr><td>CIC</td><td><?= $row['total_CIC'] ?></td><td><?= $row['male_CIC'] ?></td><td><?= $row['female_CIC'] ?></td></tr>
        <tr><td>FIC</td><td><?= $row['total_FIC'] ?></td><td><?= $row['male_FIC'] ?></td><td><?= $row['female_FIC'] ?></td></tr>
        <tr><td>TT1</td><td><?= $row['total_TT1'] ?></td><td><?= $row['male_TT1'] ?></td><td><?= $row['female_TT1'] ?></td></tr>
        <tr><td>TT2</td><td><?= $row['total_TT2'] ?></td><td><?= $row['male_TT2'] ?></td><td><?= $row['female_TT2'] ?></td></tr>
        <tr><td>TT3</td><td><?= $row['total_TT3'] ?></td><td><?= $row['male_TT3'] ?></td><td><?= $row['female_TT3'] ?></td></tr>
        <tr><td>TT4</td><td><?= $row['total_TT4'] ?></td><td><?= $row['male_TT4'] ?></td><td><?= $row['female_TT4'] ?></td></tr>
        <tr><td>TT5</td><td><?= $row['total_TT5'] ?></td><td><?= $row['male_TT5'] ?></td><td><?= $row['female_TT5'] ?></td></tr>
    </tbody>
    <tfoot>
        <tr>
            <th>Grand Total</th>
            <th><?= number_format(array_sum([$row['total_BCG'], $row['total_PENTAHIB1'], $row['total_PENTAHIB2'], $row['total_PENTAHIB3'], $row['total_OPV1'], $row['total_OPV2'], $row['total_OPV3'], $row['total_HEPAatBirth'], $row['total_HEPAB1'], $row['total_MCV1'], $row['total_MCV2'], $row['total_CIC'], $row['total_FIC'], $row['total_TT1'], $row['total_TT2'], $row['total_TT3'], $row['total_TT4'], $row['total_TT5']])) ?></th>
            <th><?= number_format(array_sum([$row['male_BCG'], $row['male_PENTAHIB1'], $row['male_PENTAHIB2'], $row['male_PENTAHIB3'], $row['male_OPV1'], $row['male_OPV2'], $row['male_OPV3'], $row['male_HEPAatBirth'], $row['male_HEPAB1'], $row['male_MCV1'], $row['male_MCV2'], $row['male_CIC'], $row['male_FIC'], $row['male_TT1'], $row['male_TT2'], $row['male_TT3'], $row['male_TT4'], $row['male_TT5']])) ?></th>
            <th><?= number_format(array_sum([$row['female_BCG'], $row['female_PENTAHIB1'], $row['female_PENTAHIB2'], $row['female_PENTAHIB3'], $row['female_OPV1'], $row['female_OPV2'], $row['female_OPV3'], $row['female_HEPAatBirth'], $row['female_HEPAB1'], $row['female_MCV1'], $row['female_MCV2'], $row['female_CIC'], $row['female_FIC'], $row['female_TT1'], $row['female_TT2'], $row['female_TT3'], $row['female_TT4'], $row['female_TT5']])) ?></th>
        </tr>
    </tfoot>
    
</table>


    </div>

     








    <div class="container m12 s12 white" style=" padding:15px;">
    <h5 class="" style="  font-size:22.6px;padding-top:7px;padding-bottom:8px;">Distribution of National Immunization Program by Birthweight(N=<?php echo $ct; ?>),<?php echo $_SESSION['health_center']; ?>

<br><span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span></h5>
  
    <div id="birthweight"></div>
   
        </div>
    
        <div class="container m12 s12 white" style=" padding:15px;">
        <h5 class="" style="  font-size:22.6px;padding-top:7px;padding-bottom:8px;">Distribution of National Immunization Program by Type of Delivery(N=<?php echo $ct; ?>),<?php echo $_SESSION['health_center']; ?><br>
        <span class="blue-text"><?php if($from == '' || $to == ''){echo '';} else { echo date('F d,Y',strtotime($from)).' - '. date('F d,Y',strtotime($to)); } ?></span>
    </h5>
  
    <div id="TypeofDelivery"></div>
   
        </div>
        <script>
    // Data passed from PHP
    const dates = <?php echo $datesJson; ?>;
    const counts = <?php echo $countsJson; ?>;

    // Highcharts configuration
    Highcharts.chart('registrationChart', {
        chart: {
            type: 'column'
        },
        title: {
            text: ' '
        },
        xAxis: {
            categories: dates, // Use formatted dates from PHP
            title: {
                text: 'Date of Registration'
            },
            labels: {
                rotation: -45, // Rotate labels for better readability
                style: {
                    fontSize: '10px',
                    whiteSpace: 'nowrap'
                }
            }
        },
        yAxis: {
            title: {
                text: 'Number of Registrations'
            },
            allowDecimals: false,
            min: 0
        },
        series: [{
            name: '',
            data: counts,
            color: '#1976d2'
        }],
        tooltip: {
            valueSuffix: ' registrations'
        },
        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'center',
                        layout: 'horizontal'
                    },
                    yAxis: {
                        title: {
                            text: ''
                        }
                    },
                    subtitle: {
                        text: ''
                    }
                }
            }]
        }
    });
</script>

    <script>
    new DataTable('#mainTable2', {
         
        processing: true,
     ordering:false,
     searching:false,
     paging:false,
    layout: {
        topEnd: {
            search: {
              return:true,
              serverSide: true
            },
            
            
        },
        
        topStart: {
            buttons: ['copyHtml5', 'excelHtml5' ],
           
           
        },
      

    // Initialize Materialize CSS select dropdown
},
scrollX: true // Enable horizontal scrolling
});
$('select').formSelect();
</script>
    <script>
    new DataTable('#mainTable3', {
         
        processing: true,
     ordering:true,
     searching:false,
     paging:false,
    layout: {
        topEnd: {
            search: {
              return:true,
              serverSide: true
            },
            
            
        },
        
        topStart: {
            buttons: ['copyHtml5', 'excelHtml5' ],
           
           
        },
      

    // Initialize Materialize CSS select dropdown
},
scrollX: true // Enable horizontal scrolling
});
$('select').formSelect();
</script>
    <script>
    new DataTable('#pr', {
         
        processing: true,
     ordering:true,
     searching:false,
     paging:true,
    layout: {
        topEnd: {
            search: {
              return:true,
              serverSide: true
            },
            
            
        },
        
        topStart: {
            buttons: ['copyHtml5', 'excelHtml5' ],
           
           
        },
      

    // Initialize Materialize CSS select dropdown
},
scrollX: true // Enable horizontal scrolling
});
$('select').formSelect();
</script>
    <script>
    new DataTable('#brgy', {
         
        processing: true,
     ordering:false,
     searching:false,
     paging:false,
    layout: {
        topEnd: {
            search: {
              return:true,
              serverSide: true
            },
            
            
        },
        
        topStart: {
            buttons: ['copyHtml5', 'excelHtml5' ],
           
           
        },
      

    // Initialize Materialize CSS select dropdown
},
scrollX: true // Enable horizontal scrolling
});
$('select').formSelect();
</script>
    <script>
    new DataTable('#NameofFacility', {
         
        processing: true,
     ordering:false,
     searching:false,
     paging:false,
    layout: {
        topEnd: {
            search: {
              return:true,
              serverSide: true
            },
            
            
        },
        
        topStart: {
            buttons: ['copyHtml5', 'excelHtml5' ],
           
           
        },
      

    // Initialize Materialize CSS select dropdown
},
scrollX: true // Enable horizontal scrolling
});
$('select').formSelect();
</script>
<script>
    const table = new DataTable('#mainTable');
 
 table.on('click', 'tbody tr', function (e) {
     e.currentTarget.classList.toggle('selected');
 });
  
 document.querySelector('#button').addEventListener('click', function () {
     alert(table.rows('.selected').data().length + ' row(s) selected');
 });
</script>
<script>
    // Remove the warning notice pop-up and enhance backward compatibility
    (function() {
        if (window.history && window.history.replaceState) {
            window.addEventListener('load', function() {
                // Ensure the script runs only after the page fully loads
                window.history.replaceState(null, null, window.location.href);
            });
        }
    })();
</script>



<?php 
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    $sql = "SELECT BirthWeightClassification, COUNT(*) AS count FROM nip_table WHERE deleted = 0";
    if(isset($_POST['btn'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }
    $sql .= " GROUP BY BirthWeightClassification";
} else {
    $sql = "SELECT BirthWeightClassification, COUNT(*) AS count FROM nip_table WHERE deleted = 0 AND Barangay = ?";
    if(isset($_POST['btn'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }
    $sql .= " GROUP BY BirthWeightClassification";
}

$stmt = $conn->prepare($sql);
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    if(isset($_POST['btn'])) {
        $stmt->bind_param("ss", $from, $to);
    }
} else {
    if(isset($_POST['btn'])) {
        $stmt->bind_param("sss", $_SESSION['health_center'], $from, $to);
    } else {
        $stmt->bind_param("s", $_SESSION['health_center']);
    }
}

$stmt->execute();
$result = $stmt->get_result();

$BirthWeightInGrams = [];
$counts = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $BirthWeightInGrams[] = $row['BirthWeightClassification'];
        $counts[] = $row['count'];
    }
}

$BirthWeightInGramsJson = json_encode($BirthWeightInGrams);
$BirthWeightInGramscountsJson = json_encode($counts);
?>

<script>
  const BirthWeightInGrams = <?php echo $BirthWeightInGramsJson; ?>;
  const BirthWeightInGramscountsJson = <?php echo $BirthWeightInGramscountsJson; ?>;

  Highcharts.chart('birthweight', {
      chart: {
          type: 'pie'
      },
      title: {
          text: 'Birthweight'
      },
      series: [{
          minPointSize: 10,
          innerSize: '20%',
          zMin: 0,
          name: 'Total Birthweight in grams',
          borderRadius: 5,
          data: BirthWeightInGrams.map((name, index) => ({
              name: name,
              y: BirthWeightInGramscountsJson[index] 
              
          })),
          colors: ['#37a463', '#fb8c00','#039be5']
      }]
  });
</script>






<?php 
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    $sql = "SELECT TypeofDelivery, COUNT(*) AS count FROM nip_table WHERE deleted = 0";
    if(isset($_POST['btn'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }
    $sql .= " GROUP BY TypeofDelivery";
} else {
    $sql = "SELECT TypeofDelivery, COUNT(*) AS count FROM nip_table WHERE deleted = 0 AND Barangay = ?";
    if(isset($_POST['btn'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }
    $sql .= " GROUP BY TypeofDelivery";
}

$stmt = $conn->prepare($sql);
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    if(isset($_POST['btn'])) {
        $stmt->bind_param("ss", $from, $to);
    }
} else {
    if(isset($_POST['btn'])) {
        $stmt->bind_param("sss", $_SESSION['health_center'], $from, $to);
    } else {
        $stmt->bind_param("s", $_SESSION['health_center']);
    }
}

$stmt->execute();
$result = $stmt->get_result();

$TypeofDeliveryJson = [];
$counts = [];

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $TypeofDeliveryJson[] = $row['TypeofDelivery'];
        $counts[] = $row['count'];
    }
}

$TypeofDeliveryJson = json_encode($TypeofDeliveryJson);
$TypeofDeliverycountsJson = json_encode($counts);
?>

<script>
  const TypeofDelivery = <?php echo $TypeofDeliveryJson; ?>;
  const TypeofDeliverycountsJson = <?php echo $TypeofDeliverycountsJson; ?>;

  Highcharts.chart('TypeofDelivery', {
      chart: {
          type: 'pie'
      },
      title: {
          text: 'TypeofDelivery'
      },
      series: [{
          minPointSize: 10,
          innerSize: '20%',
          zMin: 0,
          name: 'Total Typeof Delivery ',
          borderRadius: 5,
          data: TypeofDelivery.map((name, index) => ({
              name: name,
              y: TypeofDeliverycountsJson[index] 
              
          })),
          colors: ['#64b5f6', '#0097a7','#fb8c00']
      }]
  });
</script>



















<?php

include 'db.php'; // Ensure this path is correct

// Initialize variables to hold the data
$permonthJson = [];
$counts = [];

// Construct the SQL query based on the user's health center and filter conditions
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    $sql = "SELECT 
                MONTHNAME(DateOfRegistration) AS permonth,
                COUNT(*) AS registration_count
            FROM nip_table 
            WHERE deleted = 0";

    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }

    $sql .= " GROUP BY permonth
              ORDER BY MIN(DateOfRegistration)"; // Order by the earliest registration date in each month
} else {
    $sql = "SELECT 
                MONTHNAME(DateOfRegistration) AS permonth,
                COUNT(*) AS registration_count
            FROM nip_table 
            WHERE deleted = 0 AND Barangay = ?";

    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }

    $sql .= " GROUP BY permonth
              ORDER BY MIN(DateOfRegistration)"; // Order by the earliest registration date in each month
}

// Prepare the SQL statement
$stmt = $conn->prepare($sql);

// Bind parameters based on conditions
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $stmt->bind_param("ss", $from, $to);
    }
} else {
    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $stmt->bind_param("sss", $_SESSION['health_center'], $from, $to);
    } else {
        $stmt->bind_param("s", $_SESSION['health_center']);
    }
}

// Execute the query
$stmt->execute();
$result = $stmt->get_result();

// Fetch the data and store it in arrays
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $permonthJson[] = $row['permonth'];
        $counts[] = (int)$row['registration_count']; // Ensure counts are integers
    }
}

// Convert the PHP arrays to JSON format
$permonthJson = json_encode($permonthJson);
$countsJson = json_encode($counts);

// Close the database connection
$stmt->close();
$conn->close();
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const permonthJson = <?php echo $permonthJson; ?>;
    const countsJson = <?php echo $countsJson; ?>;

    Highcharts.chart('permonth', {
        chart: {
            type: 'column'
        },
        title: {
            text: 'Distribution of National Immunization Program Per Month'
        },
        xAxis: {
            categories: permonthJson, // Use month names from PHP
            title: {
                text: 'Month'
            },
            labels: {
                rotation: 0,
                style: {
                    fontSize: '14.2px',
                    whiteSpace: 'nowrap'
                }
            }
        },
        yAxis: {
            title: {
                text: 'Number of Registrations'
            },
            allowDecimals: false,
            min: 0
        },
        series: [{
            name: 'Registrations',
            data: countsJson, // Use registration counts from PHP
            color: '#37a463',
            pointWidth: 150
        }],
        tooltip: {
            valueSuffix: ' registrations'
        },
        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'bottom',
                        layout: 'horizontal'
                    },
                    yAxis: {
                        title: {
                            text: ''
                        }
                    },
                    subtitle: {
                        text: ''
                    }
                }
            }]
        }
    });
});
</script>













































<?php

include 'db.php'; // Ensure this path is correct

// Initialize variables to hold the data
$quarterJson = [];
$counts = [];

// Construct the SQL query based on the user's health center and filter conditions
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    $sql = "SELECT 
                QUARTER(DateOfRegistration) AS quarter_name,
                COUNT(*) AS quarter_count 
            FROM 
                `nip_table` 
            WHERE 
                deleted = 0";

    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }

    $sql .= " GROUP BY QUARTER(DateOfRegistration) ORDER BY QUARTER(DateOfRegistration)";
} else {
    $sql = "SELECT 
                QUARTER(DateOfRegistration) AS quarter_name,
                COUNT(*) AS quarter_count 
            FROM 
                `nip_table` 
            WHERE 
                deleted = 0 AND Barangay = ?";

    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $from = $_POST['from'];
        $to = $_POST['to'];
        $sql .= " AND DateOfRegistration BETWEEN ? AND ?";
    }

    $sql .= " GROUP BY QUARTER(DateOfRegistration) ORDER BY QUARTER(DateOfRegistration)";
}

// Prepare the SQL statement
$stmt = $conn->prepare($sql);

// Bind parameters based on conditions
if ($_SESSION['health_center'] == 'CITY HEALTH OFFICE') {
    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $stmt->bind_param("ss", $from, $to);
    }
} else {
    if (isset($_POST['btn']) && !empty($_POST['from']) && !empty($_POST['to'])) {
        $stmt->bind_param("sss", $_SESSION['health_center'], $from, $to);
    } else {
        $stmt->bind_param("s", $_SESSION['health_center']);
    }
}

// Execute the query
$stmt->execute();
$result = $stmt->get_result();

// Fetch the data and store it in arrays
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $quarterJson[] = "Quarter " . $row['quarter_name']; // Add "Q" prefix
        $counts[] = (int)$row['quarter_count']; // Ensure counts are integers
    }
}

// Convert the PHP arrays to JSON format
$quarterJson = json_encode($quarterJson);
$countsJson = json_encode($counts);

// Close the database connection
$stmt->close();
$conn->close();
?>
 

 

 <script>
document.addEventListener('DOMContentLoaded', function() {
    const quarterJson = <?php echo $quarterJson; ?>;
    const countsJson = <?php echo $countsJson; ?>;

    Highcharts.chart('perquarter', {
        chart: {
            type: 'column'
        },
        title: {
            text: 'Distribution of National Immunization Program Per Quarter'
        },
        xAxis: {
            categories: quarterJson, // Use quarter names from PHP
            title: {
                text: 'Quarter'
            },
            labels: {
                rotation: 0,
                style: {
                    fontSize: '15.5px',
                    whiteSpace: 'nowrap'
                }
            }
        },
        yAxis: {
            title: {
                text: 'Registrations'
            },
            allowDecimals: false,
            min: 0
        },
        series: [{
            name: 'Per Quarter',
            data: countsJson, // Use registration counts from PHP
            color: '#2962ff',
            pointWidth: 400,
            dataLabels: {
                enabled: true,
                rotation:  0,
                color: '#FFFFFF',
                align: 'center',
              
                y: 100, // 10 pixels down from the top
                style: {
                    fontSize: '17px' 
                    
                }
            }
        }],
        tooltip: {
            valueSuffix: ''
        },
        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'center',
                        layout: 'horizontal'
                    },
                    yAxis: {
                        title: {
                            text: ''
                        }
                    },
                    subtitle: {
                        text: ''
                    }
                }
            }]
        }
    });
});
</script>
<script>
     $(document).ready(function(){
    $('.modal').modal();
  });
</script>
</body>

</html>
