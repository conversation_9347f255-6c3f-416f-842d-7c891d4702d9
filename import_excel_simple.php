<?php
// Simple Excel/CSV Import for NIP Table
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Connection failed: ' . mysqli_connect_error());
}
date_default_timezone_set('Asia/Manila');

$message = '';
$messageType = '';
$import_errors = [];

// Handle file upload
if (isset($_POST['import_excel'])) {
    if (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] == 0) {
        $allowed_extensions = ['csv'];
        $file_extension = strtolower(pathinfo($_FILES['excel_file']['name'], PATHINFO_EXTENSION));
        
        if (in_array($file_extension, $allowed_extensions)) {
            $upload_dir = 'uploads/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_path = $upload_dir . time() . '_' . $_FILES['excel_file']['name'];
            
            if (move_uploaded_file($_FILES['excel_file']['tmp_name'], $file_path)) {
                $result = importCSV($file_path, $conn);
                $imported_count = $result['count'];
                $import_errors = $result['errors'];
                
                if ($imported_count > 0) {
                    $message = "Successfully imported $imported_count records.";
                    if (!empty($import_errors)) {
                        $message .= " " . count($import_errors) . " errors occurred.";
                    }
                    $messageType = 'success';
                } else {
                    $message = "No records were imported. Please check your CSV format and data.";
                    $messageType = 'error';
                }
                
                // Clean up uploaded file
                unlink($file_path);
            } else {
                $message = "Failed to upload file.";
                $messageType = 'error';
            }
        } else {
            $message = "Invalid file format. Please upload CSV files only.";
            $messageType = 'error';
        }
    } else {
        $message = "Please select a file to upload.";
        $messageType = 'error';
    }
}

function importCSV($file_path, $conn) {
    $imported_count = 0;
    $errors = [];
    
    if (($handle = fopen($file_path, "r")) !== FALSE) {
        $header = fgetcsv($handle); // Read header row
        $row_number = 1;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            $row_number++;
            
            // Skip empty rows
            if (empty(array_filter($data))) {
                continue;
            }
            
            $result = processRow($data, $row_number, $conn);
            if ($result['success']) {
                $imported_count++;
            } else {
                $errors[] = $result['error'];
            }
        }
        fclose($handle);
    }
    
    return ['count' => $imported_count, 'errors' => $errors];
}

function processRow($data, $row_number, $conn) {
    // Map CSV columns to database fields (complete mapping based on Excel structure)
    $record = [
        'DateOfBirth' => isset($data[0]) ? trim($data[0]) : '',
        'AgeofChild' => isset($data[1]) ? trim($data[1]) : '',
        'NameOfChild' => isset($data[2]) ? trim($data[2]) : '',
        'LastNameOfChild' => isset($data[3]) ? trim($data[3]) : '',
        'middlename_of_child' => isset($data[4]) ? trim($data[4]) : '',
        'Sex' => isset($data[5]) ? trim($data[5]) : '',
        'NameofMother' => isset($data[6]) ? trim($data[6]) : '',
        'BirthdateofMother' => isset($data[7]) ? trim($data[7]) : '',
        'Age' => isset($data[8]) ? trim($data[8]) : '',
        'Barangay' => isset($_SESSION['health_center']) ? $_SESSION['health_center'] :   '',
        'PurokStreetSitio' => isset($data[10]) ? trim($data[10]) : '',
        'HouseNo' => isset($data[11]) ? trim($data[11]) : '',
        'Address' => isset($data[12]) ? trim($data[12]) : '',
        'PlaceofDelivery' => isset($data[13]) ? trim($data[13]) : '',
        'NameofFacility' => isset($data[14]) ? trim($data[14]) : '',
        'Attendant' => isset($data[15]) ? trim($data[15]) : '',
        'TypeofDelivery' => isset($data[16]) ? trim($data[16]) : '',
        'BirthWeightInGrams' => isset($data[17]) ? trim($data[17]) : '',
        'BirthWeightClassification' => isset($data[18]) ? trim($data[18]) : '',
        'WastheChildReferredforNewbornScreening' => isset($data[19]) ? trim($data[19]) : '',
        'DateReferredforNewbornScreening' => isset($data[20]) ? trim($data[20]) : '',
        'DateofConsultationNewbornScreening' => isset($data[21]) ? trim($data[21]) : '',
        'TTStatusofMother' => isset($data[22]) ? trim($data[22]) : '',
        'Dateassessed' => isset($data[23]) ? trim($data[23]) : '',
        'WastheChildProtectedatBirth' => isset($data[24]) ? trim($data[24]) : '',
        'DateofConsultationCPAB' => isset($data[25]) ? trim($data[25]) : '',
        'BCG' => isset($data[26]) ? trim($data[26]) : '',
        'DateBCGwasgiven' => isset($data[27]) ? trim($data[27]) : '',
        'DateofConsultationBCG' => isset($data[28]) ? trim($data[28]) : '',
        'PENTAHIB1' => isset($data[29]) ? trim($data[29]) : '',
        'DatePenta1wasgiven' => isset($data[30]) ? trim($data[30]) : '',
        'DateofConsultationPENTAHIB1' => isset($data[31]) ? trim($data[31]) : '',
        'PENTAHIB2' => isset($data[32]) ? trim($data[32]) : '',
        'DatePentahib2wasgiven' => isset($data[33]) ? trim($data[33]) : '',
        'DateofConsultationPENTAHIB2' => isset($data[34]) ? trim($data[34]) : '',
        'PENTAHIB3' => isset($data[35]) ? trim($data[35]) : '',
        'DatePentahib3wasgiven' => isset($data[36]) ? trim($data[36]) : '',
        'DateofConsultationPENTAHIB3' => isset($data[37]) ? trim($data[37]) : '',
        'OPV1' => isset($data[38]) ? trim($data[38]) : '',
        'DateOPV1wasgiven' => isset($data[39]) ? trim($data[39]) : '',
        'DateofConsultationOPV1v' => isset($data[40]) ? trim($data[40]) : '',
        'OPV2' => isset($data[41]) ? trim($data[41]) : '',
        'DateOPV2wasgiven' => isset($data[42]) ? trim($data[42]) : '',
        'DateofConsultationOPV2' => isset($data[43]) ? trim($data[43]) : '',
        'OPV3' => isset($data[44]) ? trim($data[44]) : '',
        'dateOPV3wasgiven' => isset($data[45]) ? trim($data[45]) : '',
        'DateofConsultationOPV3' => isset($data[46]) ? trim($data[46]) : '',
        'HEPAatBirth' => isset($data[47]) ? trim($data[47]) : '',
        'TimingHEPAatBirth' => isset($data[48]) ? trim($data[48]) : '',
        'DateHEPAatBirthwasgiven' => isset($data[49]) ? trim($data[49]) : '',
        'DateofConsultationHEPAatBirth' => isset($data[50]) ? trim($data[50]) : '',
        'HEPAB1' => isset($data[51]) ? trim($data[51]) : '',
        'dateHEPA1wasgiven' => isset($data[52]) ? trim($data[52]) : '',
        'DateofConsultationHEPAB1' => isset($data[53]) ? trim($data[53]) : '',
        'HEPAB2' => isset($data[54]) ? trim($data[54]) : '',
        'dateHEPA2wasgiven' => isset($data[55]) ? trim($data[55]) : '',
        'DateofConsultationHEPAB2' => isset($data[56]) ? trim($data[56]) : '',
        'HEPAB3' => isset($data[57]) ? trim($data[57]) : '',
        'dateHEPA3WASGIVEN' => isset($data[58]) ? trim($data[58]) : '',
        'DateofConsultationHEPAB3' => isset($data[59]) ? trim($data[59]) : '',
        'HEPAB4' => isset($data[60]) ? trim($data[60]) : '',
        'dateHEPA4WASGIVEN' => isset($data[61]) ? trim($data[61]) : '',
        'DateofConsultationHEPAB4' => isset($data[62]) ? trim($data[62]) : '',
        'AMV19monthstobelow12months' => isset($data[63]) ? trim($data[63]) : '', // MCV 1 (at 9months)
        'DateAMV1WASGIVEN' => isset($data[64]) ? trim($data[64]) : '', // DATE MCV 1 WAS GIVEN
        'DateofConsultationAMV1' => isset($data[65]) ? trim($data[65]) : '', // DATE OF CONSULTATON (MCV 1)
        'MMR12MOSTO15MOS' => isset($data[66]) ? trim($data[66]) : '', // MCV 2(at 12 Months)
        'dateMMRWASGIVEN' => isset($data[67]) ? trim($data[67]) : '', // DATE MCV2 WAS GIVEN
        'DateofConsultationMMR' => isset($data[68]) ? trim($data[68]) : '', // DATE OF CONSULTATION (MCV2)
        'FIC' => isset($data[69]) ? trim($data[69]) : '',
        'dateFICWASGIVEN' => isset($data[70]) ? trim($data[70]) : '',
        'DateofConsultationFIC' => isset($data[71]) ? trim($data[71]) : '',
        'CIC' => isset($data[72]) ? trim($data[72]) : '',
        'dateCICWASGIVEN' => isset($data[73]) ? trim($data[73]) : '',
        'DateofConsultationCIC' => isset($data[74]) ? trim($data[74]) : '',
        'AMV2_16MOSTO5YRSOLD' => isset($data[75]) ? trim($data[75]) : '',
        'dateAMV2WASGIVEN' => isset($data[76]) ? trim($data[76]) : '',
        'DateofConsultationAMV2' => isset($data[77]) ? trim($data[77]) : '',
        'IMMUNIZATIONSTATUS' => isset($data[78]) ? trim($data[78]) : '',
        'DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED' => isset($data[79]) ? trim($data[79]) : '',
        'FIRSTMONTH' => isset($data[80]) ? trim($data[80]) : '',
        'SECONDMONTH' => isset($data[81]) ? trim($data[81]) : '',
        'THIRDMONTH' => isset($data[82]) ? trim($data[82]) : '',
        'FOURTHDMONTH' => isset($data[83]) ? trim($data[83]) : '',
        'FIFTMONTH' => isset($data[84]) ? trim($data[84]) : '',
        'SIXTHMONTH' => isset($data[85]) ? trim($data[85]) : '',
        'date6MONTHS' => isset($data[86]) ? trim($data[86]) : '',
        'WASTHECHILDEXCLUSIVELY' => isset($data[87]) ? trim($data[87]) : '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => isset($data[88]) ? trim($data[88]) : '',
        'TT1' => isset($data[89]) ? trim($data[89]) : '',
        'DATEOFCONSULTTT1' => isset($data[90]) ? trim($data[90]) : '',
        'TT2' => isset($data[91]) ? trim($data[91]) : '',
        'DATEOFCONSULTTT2' => isset($data[92]) ? trim($data[92]) : '',
        'TT3' => isset($data[93]) ? trim($data[93]) : '',
        'DATEOFCONSULTTT3' => isset($data[94]) ? trim($data[94]) : '',
        'TT4' => isset($data[95]) ? trim($data[95]) : '',
        'DATEOFCONSULTTT4' => isset($data[96]) ? trim($data[96]) : '',
        'TT5' => isset($data[97]) ? trim($data[97]) : '',
        'DATEOFCONSULTT5' => isset($data[98]) ? trim($data[98]) : '',
        'PhoneNumber' => isset($data[99]) ? trim($data[99]) : ''
    ];
    
 
    
    // Set default values for optional fields if empty
    $defaultFields = [
        'AgeofChild' => '',
        'Sex' => '',
        'NameofMother' => 'Not Specified',
        'BirthdateofMother' => '',
        'Age' => '',
        'Barangay' => 'Not Specified',
        'PurokStreetSitio' => '',
        'HouseNo' => '',
        'Address' => 'Not Specified',
        'PlaceofDelivery' => 'Not Specified',
        'NameofFacility' => '',
        'Attendant' => 'Not Specified',
        'TypeofDelivery' => 'Not Specified',
        'BirthWeightInGrams' => '',
        'BirthWeightClassification' => '',
        'WastheChildReferredforNewbornScreening' => '',
        'DateReferredforNewbornScreening' => '',
        'DateofConsultationNewbornScreening' => '',
        'TTStatusofMother' => '',
        'Dateassessed' => '',
        'WastheChildProtectedatBirth' => '',
        'DateofConsultationCPAB' => '',
        'BCG' => 'Not Given',
        'DateBCGwasgiven' => '',
        'DateofConsultationBCG' => '',
        'PENTAHIB1' => '',
        'DatePenta1wasgiven' => '',
        'DateofConsultationPENTAHIB1' => '',
        'PENTAHIB2' => '',
        'DatePentahib2wasgiven' => '',
        'DateofConsultationPENTAHIB2' => '',
        'PENTAHIB3' => '',
        'DatePentahib3wasgiven' => '',
        'DateofConsultationPENTAHIB3' => '',
        'OPV1' => '',
        'DateOPV1wasgiven' => '',
        'DateofConsultationOPV1v' => '',
        'OPV2' => '',
        'DateOPV2wasgiven' => '',
        'DateofConsultationOPV2' => '',
        'OPV3' => '',
        'dateOPV3wasgiven' => '',
        'DateofConsultationOPV3' => '',
        'HEPAatBirth' => '',
        'TimingHEPAatBirth' => '',
        'DateHEPAatBirthwasgiven' => '',
        'DateofConsultationHEPAatBirth' => '',
        'HEPAB1' => '',
        'dateHEPA1wasgiven' => '',
        'DateofConsultationHEPAB1' => '',
        'HEPAB2' => '',
        'dateHEPA2wasgiven' => '',
        'DateofConsultationHEPAB2' => '',
        'HEPAB3' => '',
        'dateHEPA3WASGIVEN' => '',
        'DateofConsultationHEPAB3' => '',
        'HEPAB4' => '',
        'dateHEPA4WASGIVEN' => '',
        'DateofConsultationHEPAB4' => '',
        'AMV19monthstobelow12months' => '',
        'DateAMV1WASGIVEN' => '',
        'DateofConsultationAMV1' => '',
        'MMR12MOSTO15MOS' => '',
        'dateMMRWASGIVEN' => '',
        'DateofConsultationMMR' => '',
        'FIC' => '',
        'dateFICWASGIVEN' => '',
        'DateofConsultationFIC' => '',
        'CIC' => '',
        'dateCICWASGIVEN' => '',
        'DateofConsultationCIC' => '',
        'AMV2_16MOSTO5YRSOLD' => '',
        'dateAMV2WASGIVEN' => '',
        'DateofConsultationAMV2' => '',
        'IMMUNIZATIONSTATUS' => '',
        'DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED' => '',
        'FIRSTMONTH' => '',
        'SECONDMONTH' => '',
        'THIRDMONTH' => '',
        'FOURTHDMONTH' => '',
        'FIFTMONTH' => '',
        'SIXTHMONTH' => '',
        'date6MONTHS' => '',
        'WASTHECHILDEXCLUSIVELY' => '',
        'DATEOFCONSULTATIONEXCLUSIVEBF' => '',
        'TT1' => '',
        'DATEOFCONSULTTT1' => '',
        'TT2' => '',
        'DATEOFCONSULTTT2' => '',
        'TT3' => '',
        'DATEOFCONSULTTT3' => '',
        'TT4' => '',
        'DATEOFCONSULTTT4' => '',
        'TT5' => '',
        'DATEOFCONSULTT5' => '',
        'PhoneNumber' => '',
        'middlename_of_child' => ''
    ];
    
    // Apply default values for empty fields
    foreach ($defaultFields as $field => $defaultValue) {
        if (empty($record[$field])) {
            $record[$field] = $defaultValue;
        }
    }
    
    // Validate and convert date format
    $convertedDate = convertDate($record['DateOfBirth']);
    if (!$convertedDate) {
        return ['success' => false, 'error' => "Row $row_number: Invalid date format for DateOfBirth. Accepted formats: YYYY-MM-DD "];
    }
    $record['DateOfBirth'] = $convertedDate;
    
    // Check for duplicates
    $checkStmt = $conn->prepare("SELECT id FROM nip_table WHERE NameOfChild = ? AND LastNameOfChild = ? AND DateOfBirth = ?");
    if (!$checkStmt) {
        return ['success' => false, 'error' => "Row $row_number: Database prepare error - " . $conn->error];
    }
    
    $checkStmt->bind_param("sss", $record['NameOfChild'], $record['LastNameOfChild'], $record['DateOfBirth']);
    $checkStmt->execute();
    $checkStmt->store_result();
    
    if ($checkStmt->num_rows > 0) {
        return ['success' => false, 'error' => "Row $row_number: Duplicate record found for " . $record['NameOfChild'] . " " . $record['LastNameOfChild']];
    }
    
    // Generate required fields
    $today = date("Ymd");
    $rand = strtoupper(substr(uniqid(sha1(time())),0,10));
    $DateOfRegistration = date('Y-m-d');
    $FamilySerialNumber = 'CHO-'.$today . $rand;
    $ChildNumber = rand(100, 999);
    $AgeofChild = calculateAge($record['DateOfBirth']);
    $approvedBy = isset($_SESSION['health_center']) ? $_SESSION['health_center'] : 'System Import';
    $dateOfExpiration = date('Y-m-d', strtotime('+365 day'));
    
    // Insert record with all fields (matching db.php structure)
    $sql = "INSERT INTO nip_table (
        DateOfRegistration, DateOfBirth, AgeofChild, FamilySerialNumber, ChildNumber,
        LastNameOfChild, NameOfChild, middlename_of_child, Sex, NameofMother, BirthdateofMother, Age,
        Barangay, PurokStreetSitio, HouseNo, Address, PlaceofDelivery, NameofFacility, Attendant, TypeofDelivery,
        BirthWeightInGrams, BirthWeightClassification, WastheChildReferredforNewbornScreening, DateReferredforNewbornScreening,
        DateofConsultationNewbornScreening, TTStatusofMother, Dateassessed, WastheChildProtectedatBirth, DateofConsultationCPAB,
        BCG, DateBCGwasgiven, DateofConsultationBCG, PENTAHIB1, DatePenta1wasgiven, DateofConsultationPENTAHIB1,
        PENTAHIB2, DatePentahib2wasgiven, DateofConsultationPENTAHIB2, PENTAHIB3, DatePentahib3wasgiven, DateofConsultationPENTAHIB3,
        OPV1, DateOPV1wasgiven, DateofConsultationOPV1v, OPV2, DateOPV2wasgiven, DateofConsultationOPV2,
        OPV3, dateOPV3wasgiven, DateofConsultationOPV3, HEPAatBirth, TimingHEPAatBirth, DateHEPAatBirthwasgiven,
        DateofConsultationHEPAatBirth, HEPAB1, dateHEPA1wasgiven, DateofConsultationHEPAB1,
        HEPAB2, dateHEPA2wasgiven, DateofConsultationHEPAB2, HEPAB3, dateHEPA3WASGIVEN, DateofConsultationHEPAB3,
        HEPAB4, dateHEPA4WASGIVEN, DateofConsultationHEPAB4, AMV19monthstobelow12months, DateAMV1WASGIVEN, DateofConsultationAMV1,
        MMR12MOSTO15MOS, dateMMRWASGIVEN, DateofConsultationMMR,
        FIC, dateFICWASGIVEN, DateofConsultationFIC, CIC, dateCICWASGIVEN, DateofConsultationCIC,
        AMV2_16MOSTO5YRSOLD, dateAMV2WASGIVEN, DateofConsultationAMV2,
        IMMUNIZATIONSTATUS, DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED,
        FIRSTMONTH, SECONDMONTH, THIRDMONTH, FOURTHDMONTH, FIFTMONTH, SIXTHMONTH, date6MONTHS,
        WASTHECHILDEXCLUSIVELY, DATEOFCONSULTATIONEXCLUSIVEBF,
        TT1, DATEOFCONSULTTT1, TT2, DATEOFCONSULTTT2, TT3, DATEOFCONSULTTT3, TT4, DATEOFCONSULTTT4, TT5, DATEOFCONSULTT5,
        PhoneNumber, approvedBy, dateOfExpiration
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        return ['success' => false, 'error' => "Row $row_number: Database prepare error - " . $conn->error];
    }
    
    $stmt->bind_param("sssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss", 
        $DateOfRegistration, $record['DateOfBirth'], $AgeofChild, $FamilySerialNumber, $ChildNumber,
        $record['LastNameOfChild'], $record['NameOfChild'], $record['middlename_of_child'], $record['Sex'], $record['NameofMother'], $record['BirthdateofMother'], $record['Age'],
        $record['Barangay'], $record['PurokStreetSitio'], $record['HouseNo'], $record['Address'], $record['PlaceofDelivery'], $record['NameofFacility'], $record['Attendant'], $record['TypeofDelivery'],
        $record['BirthWeightInGrams'], $record['BirthWeightClassification'], $record['WastheChildReferredforNewbornScreening'], $record['DateReferredforNewbornScreening'],
        $record['DateofConsultationNewbornScreening'], $record['TTStatusofMother'], $record['Dateassessed'], $record['WastheChildProtectedatBirth'], $record['DateofConsultationCPAB'],
        $record['BCG'], $record['DateBCGwasgiven'], $record['DateofConsultationBCG'], $record['PENTAHIB1'], $record['DatePenta1wasgiven'], $record['DateofConsultationPENTAHIB1'],
        $record['PENTAHIB2'], $record['DatePentahib2wasgiven'], $record['DateofConsultationPENTAHIB2'], $record['PENTAHIB3'], $record['DatePentahib3wasgiven'], $record['DateofConsultationPENTAHIB3'],
        $record['OPV1'], $record['DateOPV1wasgiven'], $record['DateofConsultationOPV1v'], $record['OPV2'], $record['DateOPV2wasgiven'], $record['DateofConsultationOPV2'],
        $record['OPV3'], $record['dateOPV3wasgiven'], $record['DateofConsultationOPV3'], $record['HEPAatBirth'], $record['TimingHEPAatBirth'], $record['DateHEPAatBirthwasgiven'],
        $record['DateofConsultationHEPAatBirth'], $record['HEPAB1'], $record['dateHEPA1wasgiven'], $record['DateofConsultationHEPAB1'],
        $record['HEPAB2'], $record['dateHEPA2wasgiven'], $record['DateofConsultationHEPAB2'], $record['HEPAB3'], $record['dateHEPA3WASGIVEN'], $record['DateofConsultationHEPAB3'],
        $record['HEPAB4'], $record['dateHEPA4WASGIVEN'], $record['DateofConsultationHEPAB4'], $record['AMV19monthstobelow12months'], $record['DateAMV1WASGIVEN'], $record['DateofConsultationAMV1'],
        $record['MMR12MOSTO15MOS'], $record['dateMMRWASGIVEN'], $record['DateofConsultationMMR'],
        $record['FIC'], $record['dateFICWASGIVEN'], $record['DateofConsultationFIC'], $record['CIC'], $record['dateCICWASGIVEN'], $record['DateofConsultationCIC'],
        $record['AMV2_16MOSTO5YRSOLD'], $record['dateAMV2WASGIVEN'], $record['DateofConsultationAMV2'],
        $record['IMMUNIZATIONSTATUS'], $record['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'],
        $record['FIRSTMONTH'], $record['SECONDMONTH'], $record['THIRDMONTH'], $record['FOURTHDMONTH'], $record['FIFTMONTH'], $record['SIXTHMONTH'], $record['date6MONTHS'],
        $record['WASTHECHILDEXCLUSIVELY'], $record['DATEOFCONSULTATIONEXCLUSIVEBF'],
        $record['TT1'], $record['DATEOFCONSULTTT1'], $record['TT2'], $record['DATEOFCONSULTTT2'], $record['TT3'], $record['DATEOFCONSULTTT3'], $record['TT4'], $record['DATEOFCONSULTTT4'], $record['TT5'], $record['DATEOFCONSULTT5'],
        $record['PhoneNumber'], $approvedBy, $dateOfExpiration
    );
    
    if ($stmt->execute()) {
        return ['success' => true, 'error' => ''];
    } else {
        return ['success' => false, 'error' => "Row $row_number: Database error - " . $stmt->error];
    }
}

function convertDate($dateString) {
    // Remove any extra whitespace
    $dateString = trim($dateString);
    
    if (empty($dateString)) {
        return false;
    }
    
    // List of common date formats to try
    $formats = [
        'Y-m-d',        // 2023-01-15
        'Y/m/d',        // 2023/01/15
        'm/d/Y',        // 01/15/2023
        'd/m/Y',        // 15/01/2023
        'm-d-Y',        // 01-15-2023
        'd-m-Y',        // 15-01-2023
        'Y.m.d',        // 2023.01.15
        'm.d.Y',        // 01.15.2023
        'd.m.Y',        // 15.01.2023
        'Y m d',        // 2023 01 15
        'm d Y',        // 01 15 2023
        'd m Y'         // 15 01 2023
    ];
    
    foreach ($formats as $format) {
        $date = DateTime::createFromFormat($format, $dateString);
        if ($date && $date->format($format) === $dateString) {
            // Return in MySQL format
            return $date->format('Y-m-d');
        }
    }
    
    // Try to parse with strtotime as fallback
    $timestamp = strtotime($dateString);
    if ($timestamp !== false) {
        return date('Y-m-d', $timestamp);
    }
    
    return false;
}

function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

function calculateAge($birthdate) {
    try {
        $birth = new DateTime($birthdate);
        $today = new DateTime();
        $age = $birth->diff($today);
        return $age->y;
    } catch (Exception $e) {
        return 0;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import CSV - National Immunization Program</title>
    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        .container {
            margin-top: 20px;
        }
        .file-field .btn {
            background-color: #26a69a;
        }
        .file-field .btn:hover {
            background-color: #2bbbad;
        }
        .sample-table {
            font-size: 12px;
        }
        .sample-table th, .sample-table td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .instructions {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error-list {
            max-height: 300px;
            overflow-y: auto;
        }
    </style</head>
<body>
   <?php include 'db.php'; ?>
   <?php include 'nav.php'; ?>

    <div class="container">
        <!-- Display Messages -->
        <?php if (!empty($message)): ?>
            <div class="card-panel <?php echo $messageType == 'success' ? 'green lighten-4 green-text text-darken-2' : 'red lighten-4 red-text text-darken-2'; ?>">
                <i class="material-icons left"><?php echo $messageType == 'success' ? 'check_circle' : 'error'; ?></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Display Import Errors -->
        <?php if (!empty($import_errors)): ?>
            <div class="card">
                <div class="card-content">
                    <span class="card-title red-text">
                        <i class="material-icons left">error</i>Import Errors (<?php echo count($import_errors); ?>)
                    </span>
                    <div class="error-list">
                        <ul class="collection">
                            <?php foreach ($import_errors as $error): ?>
                                <li class="collection-item red-text text-lighten-1">
                                    <i class="material-icons left tiny">error_outline</i>
                                    <?php echo htmlspecialchars($error); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title ">
                            <i class="material-icons left">cloud_upload</i>
                            Import CSV File
                        </span>
                        
                        <!-- Instructions -->
                        <div class="instructions blue-grey lighten-5">
                            <h6><i class="material-icons left">info</i>Instructions:</h6>
                            <ol>
                                <li>Prepare your CSV file with the required columns (download excel template format below)</li>
                                
                                <li>Date formats accepted: YYYY-MM-DD</li>
                                <li>Upload the file using the form below</li>
                            </ol>
                            
                            
                        </div>

                        <!-- Upload Form -->
                        <form method="POST" enctype="multipart/form-data">
                            <div class="file-field input-field">
                                <div class="btn light-blue darken-1 ">
                                    <span><i class="material-icons left">attach_file</i>Choose CSV File</span>
                                    <input type="file" name="excel_file" accept=".csv" required>
                                </div>
                                <div class="file-path-wrapper">
                                    <input class="file-path validate" type="text" placeholder="Select CSV file">
                                </div>
                            </div>
                            
                            <button type="submit" name="import_excel" class="btn btn-small  green darken-1">
                                <i class="material-icons left">cloud_upload</i>Import Data
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample CSV Format -->
        <div class="row">
            <div class="col m12 s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">table_chart</i>
                            CSV Format Template (88 columns)
                        </span>
                        <p>Your CSV file should have the following column structure:</p>
                        
                        <div style="overflow-x: auto; ">
                         
                        </div>
                        
                        <p> * Required fields <i class="green-text">(inside Excel File)</i> </p>
                        
                        <div class="card-panel blue lighten-5 blue-text text-darken-2" style="margin-top: 20px;">
                            <i class="material-icons left">download</i>
                            <strong>Download CSV Template:</strong> 
                            <a href="#" onclick="downloadSimpleCSV()" class="btn-small waves-effect   blue darken-1">
                                Download Template
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Imports -->
      

    <!-- Materialize JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    
    <script>
        // Initialize Materialize components
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });

        // Function to download simple CSV template
        function downloadSimpleCSV() {
              const csvContent = ` Date Of Birth,AgeofChild,First Name of Child,Last Name of Child,Middle Name of Child,Sex,NameofMother,BirthdateofMother,Age,Barangay,PurokStreetSitio,HouseNo,Address,PlaceofDelivery,NameofFacility,Attendant,TypeofDelivery,BirthWeightInGrams,BirthWeightClassification,WastheChildReferredforNewbornScreening,DateReferredforNewbornScreening,DateofConsultationNewbornScreening,TTStatusofMother,Dateassessed,WastheChildProtectedatBirth,DateofConsultationCPAB,BCG,DateBCGwasgiven,DateofConsultationBCG,PENTAHIB1,DatePenta1wasgiven,DateofConsultationPENTAHIB1,PENTAHIB2,DatePentahib2wasgiven,DateofConsultationPENTAHIB2,PENTAHIB3,DatePentahib3wasgiven,DateofConsultationPENTAHIB3,OPV1,DateOPV1wasgiven,DateofConsultationOPV1v,OPV2,DateOPV2wasgiven,DateofConsultationOPV2,OPV3,dateOPV3wasgiven,DateofConsultationOPV3,HEPAatBirth,TimingHEPAatBirth,DateHEPAatBirthwasgiven,DateofConsultationHEPAatBirth,HEPAB1,dateHEPA1wasgiven,DateofConsultationHEPAB1,MCV 1 (at 9months),DATE MCV 1 WAS GIVEN,DATE OF CONSULTATON (MCV 1),MCV 2(at 12 Months),DATE MCV2 WAS GIVEN,DATE OF CONSULTATION (MCV2),FIC,DATE FIC WAS GIVEN,DATE OF CONSULTATION (FIC),CIC,DATE CIC WAS GIVEN,DATE OF CONSULTATION (CIC),IMMUNIZATION STATUS,DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED,FIRSTMONTH,SECONDMONTH,THIRDMONTH,FOURTHDMONTH,FIFTMONTH,SIXTHMONTH,DATE 6 MONTHS,WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?,DATE OF CONSULTATION (EXCLUSIVE),TD1,DATE OF CONSULT,TT2,DATE OF CONSULT (TT2),TT3,DATE OF CONSULT (TT3),TT4,DATE OF CONSULT (TT4),TT5,DATE OF CONSULT (TT5),Phone Number`;
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.setAttribute('hidden', '');
            a.setAttribute('href', url);
            a.setAttribute('download', 'nip_complete_template.csv');
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
            const messages = document.querySelectorAll('.card-panel');
            messages.forEach(function(message) {
                if (message.classList.contains('green') || message.classList.contains('red')) {
                    message.style.transition = 'opacity 0.5s';
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 500);
                }
            });
        }, 5000);
    </script>
</body>
</html>