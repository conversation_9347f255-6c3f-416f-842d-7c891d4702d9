<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Information</h2>";

// Test database connection
include 'db.php';

if ($conn->connect_error) {
    die("<p style='color: red;'>Connection failed: " . $conn->connect_error . "</p>");
} else {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
}

// Test basic query
try {
    $sql = "SELECT COUNT(*) as total FROM health_facility";
    $result = $conn->query($sql);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ Basic query successful. Total records: " . $row['total'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Basic query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Query exception: " . $e->getMessage() . "</p>";
}

// Check if required fields exist
echo "<h3>Field Existence Check:</h3>";
$required_fields = [
    'id', 'username', 'email', 'fullname', 'health_center',
    'mobile_number', 'approved', 'approved_date', 'disapproved_date',
    'disapproval_reason', 'date_created'
];

foreach ($required_fields as $field) {
    try {
        $check_sql = "SHOW COLUMNS FROM health_facility LIKE '$field'";
        $check_result = $conn->query($check_sql);
        if ($check_result && $check_result->num_rows > 0) {
            echo "<p style='color: green;'>✓ Field '$field' exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Field '$field' does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error checking field '$field': " . $e->getMessage() . "</p>";
    }
}

// Test a sample SELECT query with the fields we're using
echo "<h3>Sample Query Test:</h3>";
try {
    $test_sql = "SELECT id, username, email, fullname, health_center, mobile_number, approved FROM health_facility LIMIT 1";
    $test_result = $conn->query($test_sql);
    if ($test_result) {
        echo "<p style='color: green;'>✓ Sample SELECT query successful</p>";
        if ($test_result->num_rows > 0) {
            $sample_row = $test_result->fetch_assoc();
            echo "<pre>";
            print_r($sample_row);
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>✗ Sample SELECT query failed: " . $conn->error . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Sample query exception: " . $e->getMessage() . "</p>";
}

// Check PHP version and extensions
echo "<h3>PHP Environment:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQLi Extension: " . (extension_loaded('mysqli') ? '✓ Loaded' : '✗ Not loaded') . "</p>";

$conn->close();
?>
