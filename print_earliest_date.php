<?php
/**
 * Simple function to print only the earliest consultation date
 * Usage: include this file and call printEarliestDate($data_array)
 */

function printEarliestDate($data) {
    // All consultation date fields
    $consultation_fields = [
        'DateofConsultationBCG',
        'DateofConsultationPENTAHIB1',
        'DateofConsultationPENTAHIB2', 
        'DateofConsultationPENTAHIB3',
        'DateofConsultationOPV1v',
        'DateofConsultationOPV2',
        'DateofConsultationOPV3',
        'DateofConsultationIPV1',
        'DateofConsultationIPV2',
        'DateofConsultationPCV1',
        'DateofConsultationPCV2',
        'DateofConsultationPCV3',
        'DateofConsultationHEPAatBirth',
        'DateofConsultationHEPAB1',
        'DateofConsultationHEPAB2',
        'DateofConsultationHEPAB3',
        'DateofConsultationHEPAB4',
        'DateofConsultationAMV1',
        'DateofConsultationMMR',
        'DateofConsultationFIC',
        'DateofConsultationCIC',
        'DateofConsultationAMV2',
        'DATEOFCONSULTATIONEXCLUSIVEBF',
        'DATEOFCONSULTTT1',
        'DATEOFCONSULTTT2',
        'DATEOFCONSULTTT3',
        'DATEOFCONSULTTT4',
        'DATEOFCONSULTT5'
    ];
    
    $valid_dates = [];
    
    // Collect valid dates
    foreach ($consultation_fields as $field) {
        if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== '0000-00-00') {
            $valid_dates[] = $data[$field];
        }
    }
    
    // Find and print earliest date
    if (!empty($valid_dates)) {
        $earliest = min($valid_dates);
        echo $earliest;
        return $earliest;
    } else {
        echo "No consultation date recorded";
        return null;
    }
}

// Alternative function that returns the date without printing
function getEarliestDate($data) {
    $consultation_fields = [
        'DateofConsultationBCG', 'DateofConsultationPENTAHIB1', 'DateofConsultationPENTAHIB2', 
        'DateofConsultationPENTAHIB3', 'DateofConsultationOPV1v', 'DateofConsultationOPV2',
        'DateofConsultationOPV3', 'DateofConsultationIPV1', 'DateofConsultationIPV2',
        'DateofConsultationPCV1', 'DateofConsultationPCV2', 'DateofConsultationPCV3',
        'DateofConsultationHEPAatBirth', 'DateofConsultationHEPAB1', 'DateofConsultationHEPAB2',
        'DateofConsultationHEPAB3', 'DateofConsultationHEPAB4', 'DateofConsultationAMV1',
        'DateofConsultationMMR', 'DateofConsultationFIC', 'DateofConsultationCIC',
        'DateofConsultationAMV2', 'DATEOFCONSULTATIONEXCLUSIVEBF', 'DATEOFCONSULTTT1',
        'DATEOFCONSULTTT2', 'DATEOFCONSULTTT3', 'DATEOFCONSULTTT4', 'DATEOFCONSULTT5'
    ];
    
    $valid_dates = [];
    foreach ($consultation_fields as $field) {
        if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== '0000-00-00') {
            $valid_dates[] = $data[$field];
        }
    }
    
    return !empty($valid_dates) ? min($valid_dates) : null;
}

// Function to print earliest date with formatting
function printFormattedEarliestDate($data, $format = 'Y-m-d') {
    $earliest = getEarliestDate($data);
    if ($earliest) {
        echo date($format, strtotime($earliest));
        return $earliest;
    } else {
        echo "No date";
        return null;
    }
}

// Function for database query to get earliest consultation date
function getEarliestDateFromDB($child_id, $conn) {
    $sql = "SELECT LEAST(
                NULLIF(DateofConsultationBCG, '0000-00-00'),
                NULLIF(DateofConsultationPENTAHIB1, '0000-00-00'),
                NULLIF(DateofConsultationPENTAHIB2, '0000-00-00'),
                NULLIF(DateofConsultationPENTAHIB3, '0000-00-00'),
                NULLIF(DateofConsultationOPV1v, '0000-00-00'),
                NULLIF(DateofConsultationOPV2, '0000-00-00'),
                NULLIF(DateofConsultationOPV3, '0000-00-00'),
                NULLIF(DateofConsultationIPV1, '0000-00-00'),
                NULLIF(DateofConsultationIPV2, '0000-00-00'),
                NULLIF(DateofConsultationPCV1, '0000-00-00'),
                NULLIF(DateofConsultationPCV2, '0000-00-00'),
                NULLIF(DateofConsultationPCV3, '0000-00-00'),
                NULLIF(DateofConsultationHEPAatBirth, '0000-00-00'),
                NULLIF(DateofConsultationHEPAB1, '0000-00-00'),
                NULLIF(DateofConsultationAMV1, '0000-00-00'),
                NULLIF(DateofConsultationMMR, '0000-00-00'),
                NULLIF(DateofConsultationFIC, '0000-00-00')
            ) as earliest_consultation_date
            FROM nip_table 
            WHERE id = ? AND (deleted IS NULL OR deleted = 0)";
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $child_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($row = mysqli_fetch_assoc($result)) {
        return $row['earliest_consultation_date'];
    }
    
    return null;
}

// Example usage:
/*
// Method 1: From database row
$earliest = getEarliestDate($row);
echo "Earliest consultation: " . $earliest;

// Method 2: Direct print
echo "First consultation was on: ";
printEarliestDate($row);

// Method 3: Formatted print
echo "Date: ";
printFormattedEarliestDate($row, 'F j, Y'); // January 15, 2023

// Method 4: From database query
$earliest_db = getEarliestDateFromDB($child_id, $conn);
echo $earliest_db;
*/
?>
