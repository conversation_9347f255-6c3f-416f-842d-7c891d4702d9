<?php
session_start();
include 'db.php';

// Check if user is logged in and has admin privileges
if (!isset($_SESSION['health_center'])) {
    http_response_code(401);
    echo "Unauthorized access";
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id = intval($_POST["id"]);
    $reason = trim($_POST["reason"]);
    
    // Validate input
    if (empty($id) || empty($reason)) {
        echo "Invalid input data";
        exit();
    }
    
    // Sanitize reason
    $reason = htmlspecialchars($reason, ENT_QUOTES, 'UTF-8');
    
    try {
        // Begin transaction
        $conn->begin_transaction();
        
        // Get current timestamp
        $disapproved_date = date('Y-m-d H:i:s');
        
        // Update user status to disapproved (0)
        $sql = "UPDATE health_facility SET
                approved = 0,
                disapproved_date = ?,
                disapproval_reason = ?,
                approved_date = NULL
                WHERE id = ?";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        $stmt->bind_param("ssi", $disapproved_date, $reason, $id);
        
        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }
        
        if ($stmt->affected_rows === 0) {
            throw new Exception("No user found with the given ID");
        }
        
        // Log the action in audit table (if table exists)
        $audit_sql = "INSERT INTO user_action_logs (user_id, table_name, action_type, action_date, performed_by, reason, ip_address, user_agent) 
                      VALUES (?, 'health_facility', 'DISAPPROVED', NOW(), ?, ?, ?, ?)";
        
        $audit_stmt = $conn->prepare($audit_sql);
        if ($audit_stmt) {
            $performed_by = $_SESSION['username'] ?? $_SESSION['health_center'] ?? 'Admin';
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
            
            $audit_stmt->bind_param("issss", $id, $performed_by, $reason, $ip_address, $user_agent);
            $audit_stmt->execute();
            $audit_stmt->close();
        }
        
        // Commit transaction
        $conn->commit();
        
        $stmt->close();
        echo "success";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        error_log("Disapprove user error: " . $e->getMessage());
        echo "Error: " . $e->getMessage();
    }
    
} else {
    echo "Invalid request method";
}

$conn->close();
?>
