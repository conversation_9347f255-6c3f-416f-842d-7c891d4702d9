<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Certificate</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        td ,th{
            border:1px solid #ddd;
             font-size:12.6px;
        }
        tr:nth-child(odd) td{
            background:white;
        }
        
        .filter-container {
            margin-top: 20px;
        }
        .results-table {
            margin-top: 30px;
        }
        .print-btn {
            margin: 20px 0;
        }
        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .{
            padding: 20px;
            margin-bottom: 20px;
        }
        .results-card {
            padding: 20px;
        }
        table {
            font-size: 14px;
        }
        .highlight {
            background-color: #fff3e0;
        }
        .record-count {
            margin: 10px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
      <?php include 'nav.php'; ?>
    <div class="container filter-container ">
        <div class="row ">
            <div class="col s12 ">
                <div class="card  " >
                    <div class="card-content  ">
                      <span class="card-title blue-grey lighten-5 " style="padding-top:10px;padding-bottom:10px; padding-left:10px;font-size:16.3px;font-weight:500;">
                            <i class="material-icons left">search</i>
                            Filter Child Records
                        </span>
                        
                        <form id="filterForm" class="" method="POST">
                            <div class="row">
                                <div class="input-field col s12 m6 l3">
                                    <input type="text" id="first_name" name="first_name" value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                                    <label for="first_name">First Name</label>
                                </div>
                                
                                <div class="input-field col s12 m6 l3">
                                    <input type="text" id="last_name" name="last_name" value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                                    <label for="last_name">Last Name</label>
                                </div>
                                
                                <div class="input-field col s12 m6 l3">
                                    <input type="text" id="middle_name" name="middle_name" value="<?php echo isset($_POST['middle_name']) ? htmlspecialchars($_POST['middle_name']) : ''; ?>">
                                    <label for="middle_name">Middle Name</label>
                                </div>
                                
                                <div class="input-field col s12 m6 l3">
                                    <input type="date" id="birth_date" name="birth_date" value="<?php echo isset($_POST['birth_date']) ? htmlspecialchars($_POST['birth_date']) : ''; ?>">
                                    <label for="birth_date">Birth Date</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12">
                                    <button type="submit" name="search" class="btn cyan  btn-small waves-effect">
                                        <i class="material-icons left">search</i>Search Records
                                    </button>
                                    
                                    <button type="button" onclick="clearForm()" class="btn grey darken-1  btn-small waves-effect">
                                        <i class="material-icons left">clear</i>Clear
                                    </button>
                                    
                                     
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <?php
        // Database connection
        $conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
        if (!$conn) {
            echo '<div class="card red lighten-4 red-text"><div class="card-content"><p>Database connection failed: ' . mysqli_connect_error() . '</p></div></div>';
            exit;
        }

        $results = [];
        $search_performed = false;

        if (isset($_POST['search'])) {
            $search_performed = true;
            $hc = $_SESSION['health_center'];
        
            // Get search parameters
            $first_name = trim($_POST['first_name']);
            $last_name = trim($_POST['last_name']);
            $middle_name = trim($_POST['middle_name']);
            $birth_date = trim($_POST['birth_date']);
            
            // Build dynamic query
            $sql = "SELECT id, DateOfRegistration, NameOfChild, middlename_of_child, LastNameOfChild, 
                           DateOfBirth, Sex, NameofMother, Barangay, Address, PhoneNumber, FamilySerialNumber 
                    FROM nip_table 
                    WHERE (deleted IS NULL OR deleted = 0) ";
            
            $params = [];
            $types = "";
            
            // Add conditions based on filled fields
            if (!empty($first_name)) {
                $sql .= " AND NameOfChild LIKE ?";
                $params[] = "%" . $first_name . "%";
                $types .= "s";
            }
            
            if (!empty($last_name)) {
                $sql .= " AND LastNameOfChild LIKE ?";
                $params[] = "%" . $last_name . "%";
                $types .= "s";
            }
            
            if (!empty($middle_name)) {
                $sql .= " AND middlename_of_child LIKE ?";
                $params[] = "%" . $middle_name . "%";
                $types .= "s";
            }
            
            if (!empty($birth_date)) {
                $sql .= " AND DateOfBirth = ?";
                $params[] = $birth_date;
                $types .= "s";
            }
            
            $sql .= " ORDER BY DateOfRegistration DESC, LastNameOfChild, NameOfChild";
            
            // Execute query
            if (!empty($params)) {
                $stmt = mysqli_prepare($conn, $sql);
                mysqli_stmt_bind_param($stmt, $types, ...$params);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                
                while ($row = mysqli_fetch_assoc($result)) {
                    $results[] = $row;
                }
                mysqli_stmt_close($stmt);
            } else {
                // If no search criteria, get all records (limit to prevent overload)
                $sql .= " LIMIT 0";
                $result = mysqli_query($conn, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $results[] = $row;
                }
            }
        }

        if ($search_performed) {
            echo '<div class="row" style="margin-bottom:1em;">';
            echo '<div class="col s12">';
            echo '<div class="card  ">';
            echo '<div class="card-content ">';
            
            if (count($results) > 0) {
                echo ' <span class="card-title blue-grey lighten-5 black-text" style="padding-top:10px;padding-bottom:10px; padding-left:10px;font-size:16.3px;font-weight:500;"><i class="material-icons left">list</i>Search Results</span>';
                echo '<p class="record-count">Found ' . count($results) . ' record(s)</p>';
                
                // Print button
            //    echo '<div class="print-btn">';
              //  echo '<button onclick="printResults()" class="btn orange waves-effect">';
              //  echo '<i class="material-icons left">print</i>Print Results';
              //  echo '</button>';
            //    echo '</div>';
                
                // Results table
                echo '<div class="results-table">';
                echo '<table class="blue-grey lighten-5 striped responsive-table" id="resultsTable">';
                echo '<thead>';
                echo '<tr>';
                echo '<th>ID</th>';
                echo '<th>Registration Date</th>';
                echo '<th>Full Name</th>';
                echo '<th>Birth Date</th>';
                echo '<th>Sex</th>';
                echo '<th>Mother</th>';
                echo '<th>Barangay</th>';
                echo '<th>Phone</th>';
                echo '<th>Family Serial</th>';
                echo '<th>Actions</th>';
                echo '</tr>';
                echo '</thead>';
                echo '<tbody>';
                
                foreach ($results as $row) {
                    $full_name = trim($row['NameOfChild'] . ' ' . $row['middlename_of_child'] . ' ' . $row['LastNameOfChild']);
                    
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                    echo '<td>' . htmlspecialchars($row['DateOfRegistration']) . '</td>';
                    echo '<td><strong>' . htmlspecialchars($full_name) . '</strong></td>';
                    echo '<td>' . htmlspecialchars($row['DateOfBirth']) . '</td>';
                    echo '<td>' . htmlspecialchars($row['Sex']) . '</td>';
                    echo '<td>' . htmlspecialchars($row['NameofMother']) . '</td>';
                    echo '<td>' . htmlspecialchars($row['Barangay']) . '</td>';
                    echo '<td>' . htmlspecialchars($row['PhoneNumber']) . '</td>';
                    echo '<td class="blue-text text-darken-3">' . htmlspecialchars($row['FamilySerialNumber']) . '</td>';
                    echo '<td>';
                    echo '<a href="print.php?id=' . $row['id'] . '" target="_blank" class="btn-small blue waves-effect">';
                    echo '<i class="material-icons">print</i>';
                    echo '</a>';
                    echo '</td>';
                    echo '</tr>';
                }
                
                echo '</tbody>';
                echo '</table>';
                echo '</div>';
                
            } else {
                echo '<div class="no-results">';
                echo '<i class="material-icons large grey-text">search_off</i>';
                echo '<h5>No Records Found</h5>';
                echo '<p>No records match your search criteria. Try adjusting your filters.</p>';
                echo '</div>';
            }
            
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }

        mysqli_close($conn);
        ?>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Update labels for filled inputs
            M.updateTextFields();
        });

        function clearForm() {
            document.getElementById('filterForm').reset();
            M.updateTextFields();
        }

        function printResults() {
            // Create print window with results table
            const table = document.getElementById('resultsTable');
            if (!table) return;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>NIP Records - Search Results</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1 { color: #333; text-align: center; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; font-weight: bold; }
                        tr:nth-child(even) { background-color: #f9f9f9; }
                        .print-info { margin-bottom: 20px; font-size: 14px; color: #666; }
                        @media print {
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <h1>National Immunization Program - Search Results</h1>
                    <div class="print-info">
                        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
                        <p><strong>Total Records:</strong> ${table.rows.length - 1}</p>
                    </div>
                    ${table.outerHTML.replace(/<th>Actions<\/th>|<td><a.*?<\/a><\/td>/g, '')}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
