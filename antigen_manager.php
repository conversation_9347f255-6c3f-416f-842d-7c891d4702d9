<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Database connection failed: ' . mysqli_connect_error());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Antigen Manager - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
        }
        .main-container {
            padding: 20px;
        }
        .search-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .child-result {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            cursor: pointer;
            transition: all 0.3s;
        }
        .child-result:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .antigen-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .antigen-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .antigen-status {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .status-given {
            color: #4caf50;
            font-weight: bold;
        }
        .status-not-given {
            color: #f44336;
            font-weight: bold;
        }
        .quick-edit {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .bulk-actions {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="search-card">
            <h4><i class="material-icons left">medical_services</i>Antigen Manager</h4>
            <p>Search, view, and edit antigen records for children in the immunization program</p>
        </div>

        <!-- Search Section -->
        <div class="search-card">
            <h5><i class="material-icons left">search</i>Search Children</h5>
            <div class="row">
                <div class="input-field col s12 m3">
                    <input type="text" id="search_first_name" placeholder="Enter first name">
                    <label for="search_first_name">First Name</label>
                </div>
                <div class="input-field col s12 m3">
                    <input type="text" id="search_last_name" placeholder="Enter last name">
                    <label for="search_last_name">Last Name</label>
                </div>
                <div class="input-field col s12 m3">
                    <input type="text" id="search_middle_name" placeholder="Enter middle name">
                    <label for="search_middle_name">Middle Name</label>
                </div>
                <div class="col s12 m3">
                    <button class="btn blue waves-effect" onclick="searchChildren()" style="margin-top: 25px;">
                        <i class="material-icons left">search</i>Search
                    </button>
                </div>
            </div>
            
            <!-- Advanced Search Options -->
            <div class="row">
                <div class="col s12">
                    <a class="btn-flat" onclick="toggleAdvancedSearch()">
                        <i class="material-icons left">expand_more</i>Advanced Search
                    </a>
                </div>
            </div>
            
            <div id="advanced_search" style="display: none;">
                <div class="row">
                    <div class="input-field col s12 m4">
                        <input type="date" id="birth_date_from">
                        <label for="birth_date_from">Birth Date From</label>
                    </div>
                    <div class="input-field col s12 m4">
                        <input type="date" id="birth_date_to">
                        <label for="birth_date_to">Birth Date To</label>
                    </div>
                    <div class="input-field col s12 m4">
                        <select id="immunization_status">
                            <option value="">All Status</option>
                            <option value="FULLY IMMUNIZED">Fully Immunized</option>
                            <option value="PARTIALLY IMMUNIZED">Partially Immunized</option>
                            <option value="NOT IMMUNIZED">Not Immunized</option>
                        </select>
                        <label>Immunization Status</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div id="search_results" style="display: none;">
            <div class="search-card">
                <h5><i class="material-icons left">list</i>Search Results</h5>
                <div id="results_container"></div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div id="bulk_actions" class="bulk-actions" style="display: none;">
            <h6><i class="material-icons left">edit</i>Bulk Actions</h6>
            <div class="row">
                <div class="col s12 m6">
                    <select id="bulk_antigen">
                        <option value="">Select Antigen</option>
                        <option value="BCG">BCG</option>
                        <option value="PENTAHIB1">PENTA-HIB 1</option>
                        <option value="PENTAHIB2">PENTA-HIB 2</option>
                        <option value="PENTAHIB3">PENTA-HIB 3</option>
                        <option value="OPV1">OPV 1</option>
                        <option value="OPV2">OPV 2</option>
                        <option value="OPV3">OPV 3</option>
                        <option value="IPV1">IPV 1</option>
                        <option value="IPV2">IPV 2</option>
                        <option value="PCV1">PCV 1</option>
                        <option value="PCV2">PCV 2</option>
                        <option value="PCV3">PCV 3</option>
                    </select>
                    <label>Antigen to Update</label>
                </div>
                <div class="col s12 m6">
                    <input type="date" id="bulk_date">
                    <label for="bulk_date">Date Given</label>
                </div>
            </div>
            <button class="btn orange waves-effect" onclick="bulkUpdateAntigens()">
                <i class="material-icons left">update</i>Update Selected Children
            </button>
        </div>

        <!-- Child Details and Antigen Editor -->
        <div id="child_details" style="display: none;">
            <div class="search-card">
                <h5><i class="material-icons left">person</i>Child Details</h5>
                <div id="child_info"></div>
                
                <!-- Quick Edit Panel -->
                <div class="quick-edit">
                    <h6><i class="material-icons left">flash_on</i>Quick Edit</h6>
                    <div class="row">
                        <div class="col s12 m4">
                            <select id="quick_antigen">
                                <option value="">Select Antigen</option>
                                <option value="BCG">BCG</option>
                                <option value="PENTAHIB1">PENTA-HIB 1</option>
                                <option value="PENTAHIB2">PENTA-HIB 2</option>
                                <option value="PENTAHIB3">PENTA-HIB 3</option>
                                <option value="OPV1">OPV 1</option>
                                <option value="OPV2">OPV 2</option>
                                <option value="OPV3">OPV 3</option>
                            </select>
                            <label>Antigen</label>
                        </div>
                        <div class="col s12 m4">
                            <input type="date" id="quick_date">
                            <label for="quick_date">Date Given</label>
                        </div>
                        <div class="col s12 m4">
                            <button class="btn green waves-effect" onclick="quickUpdateAntigen()" style="margin-top: 25px;">
                                <i class="material-icons left">save</i>Quick Update
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Antigen Grid -->
                <div id="antigen_grid" class="antigen-grid"></div>
                
                <!-- Full Edit Button -->
                <div class="center-align" style="margin: 20px 0;">
                    <button class="btn large blue waves-effect" onclick="openFullEditor()">
                        <i class="material-icons left">edit</i>Full Antigen Editor
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loading_modal" class="modal">
        <div class="modal-content center-align">
            <div class="preloader-wrapper big active">
                <div class="spinner-layer spinner-blue-only">
                    <div class="circle-clipper left">
                        <div class="circle"></div>
                    </div>
                    <div class="gap-patch">
                        <div class="circle"></div>
                    </div>
                    <div class="circle-clipper right">
                        <div class="circle"></div>
                    </div>
                </div>
            </div>
            <p>Processing...</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });

        let selectedChildren = [];
        let currentChild = null;

        function toggleAdvancedSearch() {
            const advancedDiv = document.getElementById('advanced_search');
            const isVisible = advancedDiv.style.display !== 'none';
            advancedDiv.style.display = isVisible ? 'none' : 'block';
        }

        function searchChildren() {
            const firstName = document.getElementById('search_first_name').value;
            const lastName = document.getElementById('search_last_name').value;
            const middleName = document.getElementById('search_middle_name').value;
            
            if (!firstName && !lastName && !middleName) {
                M.toast({html: 'Please enter at least one search criteria', classes: 'orange'});
                return;
            }
            
            // Show loading
            const modal = M.Modal.getInstance(document.getElementById('loading_modal'));
            modal.open();
            
            // Make AJAX request to search
            fetch('search_children_ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `first_name=${encodeURIComponent(firstName)}&last_name=${encodeURIComponent(lastName)}&middle_name=${encodeURIComponent(middleName)}`
            })
            .then(response => response.json())
            .then(data => {
                modal.close();
                displaySearchResults(data);
            })
            .catch(error => {
                modal.close();
                M.toast({html: 'Search failed: ' + error.message, classes: 'red'});
            });
        }

        function displaySearchResults(results) {
            const resultsDiv = document.getElementById('search_results');
            const container = document.getElementById('results_container');
            
            if (results.length === 0) {
                container.innerHTML = '<p>No children found matching your search criteria.</p>';
            } else {
                let html = '';
                results.forEach(child => {
                    html += `
                        <div class="child-result" onclick="selectChild(${child.id})">
                            <div class="row" style="margin-bottom: 0;">
                                <div class="col s12 m8">
                                    <h6 style="margin: 0; color: #2196f3;">
                                        ${child.NameOfChild} ${child.middlename_of_child || ''} ${child.LastNameOfChild}
                                    </h6>
                                    <p style="margin: 5px 0;">
                                        <strong>Birth Date:</strong> ${child.DateOfBirth} | 
                                        <strong>Mother:</strong> ${child.NameofMother}
                                    </p>
                                    <p style="margin: 0; color: #666;">
                                        <strong>Barangay:</strong> ${child.Barangay}
                                    </p>
                                </div>
                                <div class="col s12 m4 right-align">
                                    <label>
                                        <input type="checkbox" onchange="toggleChildSelection(${child.id})" />
                                        <span>Select</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    `;
                });
                container.innerHTML = html;
            }
            
            resultsDiv.style.display = 'block';
            document.getElementById('bulk_actions').style.display = 'block';
        }

        function selectChild(childId) {
            // Load child details and antigens
            fetch(`get_child_details.php?id=${childId}`)
            .then(response => response.json())
            .then(data => {
                currentChild = data;
                displayChildDetails(data);
            })
            .catch(error => {
                M.toast({html: 'Failed to load child details: ' + error.message, classes: 'red'});
            });
        }

        function displayChildDetails(child) {
            const childInfo = document.getElementById('child_info');
            childInfo.innerHTML = `
                <div class="card blue lighten-4">
                    <div class="card-content">
                        <h6>${child.NameOfChild} ${child.middlename_of_child || ''} ${child.LastNameOfChild}</h6>
                        <p>Birth Date: ${child.DateOfBirth} | Mother: ${child.NameofMother}</p>
                        <p>Barangay: ${child.Barangay} | Family Serial: ${child.FamilySerialNumber}</p>
                    </div>
                </div>
            `;
            
            displayAntigenGrid(child);
            document.getElementById('child_details').style.display = 'block';
        }

        function displayAntigenGrid(child) {
            const grid = document.getElementById('antigen_grid');
            const antigens = [
                {name: 'BCG', status: child.BCG, dateGiven: child.DateBCGwasgiven, dateConsult: child.DateofConsultationBCG},
                {name: 'PENTA-HIB 1', status: child.PENTAHIB1, dateGiven: child.DatePenta1wasgiven, dateConsult: child.DateofConsultationPENTAHIB1},
                {name: 'PENTA-HIB 2', status: child.PENTAHIB2, dateGiven: child.DatePentahib2wasgiven, dateConsult: child.DateofConsultationPENTAHIB2},
                {name: 'PENTA-HIB 3', status: child.PENTAHIB3, dateGiven: child.DatePentahib3wasgiven, dateConsult: child.DateofConsultationPENTAHIB3},
                {name: 'OPV 1', status: child.OPV1, dateGiven: child.DateOPV1wasgiven, dateConsult: child.DateofConsultationOPV1v},
                {name: 'OPV 2', status: child.OPV2, dateGiven: child.DateOPV2wasgiven, dateConsult: child.DateofConsultationOPV2},
                {name: 'OPV 3', status: child.OPV3, dateGiven: child.dateOPV3wasgiven, dateConsult: child.DateofConsultationOPV3}
            ];
            
            let html = '';
            antigens.forEach(antigen => {
                const statusClass = antigen.status === 'YES' || antigen.status ? 'status-given' : 'status-not-given';
                const statusText = antigen.status === 'YES' || antigen.status ? 'Given' : 'Not Given';
                
                html += `
                    <div class="antigen-card">
                        <h6>${antigen.name}</h6>
                        <div class="antigen-status">
                            <i class="material-icons left ${statusClass}">${antigen.status === 'YES' || antigen.status ? 'check_circle' : 'cancel'}</i>
                            <span class="${statusClass}">${statusText}</span>
                        </div>
                        <p><strong>Date Given:</strong> ${antigen.dateGiven || 'Not recorded'}</p>
                        <p><strong>Consultation:</strong> ${antigen.dateConsult || 'Not recorded'}</p>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
        }

        function toggleChildSelection(childId) {
            const index = selectedChildren.indexOf(childId);
            if (index > -1) {
                selectedChildren.splice(index, 1);
            } else {
                selectedChildren.push(childId);
            }
        }

        function quickUpdateAntigen() {
            if (!currentChild) {
                M.toast({html: 'No child selected', classes: 'red'});
                return;
            }
            
            const antigen = document.getElementById('quick_antigen').value;
            const date = document.getElementById('quick_date').value;
            
            if (!antigen || !date) {
                M.toast({html: 'Please select antigen and date', classes: 'orange'});
                return;
            }
            
            // Update antigen via AJAX
            const formData = new FormData();
            formData.append('action', 'update_antigens_ajax');
            formData.append('child_id', currentChild.id);
            formData.append(antigen, 'YES');
            formData.append(`Date${antigen}wasgiven`, date);
            
            fetch('update_antigen_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    M.toast({html: data.message, classes: 'green'});
                    // Refresh child details
                    selectChild(currentChild.id);
                } else {
                    M.toast({html: data.message, classes: 'red'});
                }
            })
            .catch(error => {
                M.toast({html: 'Update failed: ' + error.message, classes: 'red'});
            });
        }

        function bulkUpdateAntigens() {
            if (selectedChildren.length === 0) {
                M.toast({html: 'No children selected', classes: 'orange'});
                return;
            }
            
            const antigen = document.getElementById('bulk_antigen').value;
            const date = document.getElementById('bulk_date').value;
            
            if (!antigen || !date) {
                M.toast({html: 'Please select antigen and date', classes: 'orange'});
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'bulk_update_antigens');
            formData.append('child_ids', JSON.stringify(selectedChildren));
            formData.append('antigen_data', JSON.stringify({[antigen]: 'YES', [`Date${antigen}wasgiven`]: date}));
            
            fetch('update_antigen_handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    M.toast({html: data.message, classes: 'green'});
                } else {
                    M.toast({html: data.message, classes: 'red'});
                }
            })
            .catch(error => {
                M.toast({html: 'Bulk update failed: ' + error.message, classes: 'red'});
            });
        }

        function openFullEditor() {
            if (currentChild) {
                window.open(`update_antigen.php?id=${currentChild.id}`, '_blank');
            }
        }
    </script>
</body>
</html>
