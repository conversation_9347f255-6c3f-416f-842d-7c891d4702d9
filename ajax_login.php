<?php
session_start();
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    // Fallback to regular POST data
    $input = $_POST;
}

$username = trim($input['username'] ?? '');
$password = trim($input['password'] ?? '');

// Validate input
if (empty($username)) {
    echo json_encode([
        'success' => false, 
        'message' => 'Username or mobile number is required',
        'field' => 'username'
    ]);
    exit;
}

if (empty($password)) {
    echo json_encode([
        'success' => false, 
        'message' => 'Password is required',
        'field' => 'password'
    ]);
    exit;
}

// Include database connection
include 'db.php';

try {
    // Prepare and execute query to check credentials
    $stmt = $conn->prepare("SELECT * FROM health_facility WHERE (username = ? OR mobile_number = ?) AND password = ?");
    $stmt->bind_param("sss", $username, $username, $password);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        if ($row['approved'] == 1) {
            // Check if user is already logged in
            if (isset($_SESSION['health_center'])) {
                // Check if the login credentials match the current session
                if ($_SESSION['health_center'] === $row['health_center']) {
                    // Password verified successfully - same account
                    echo json_encode([
                        'success' => true,
                        'message' => 'Password verified successfully! Welcome back.',
                        'redirect' => 'index.php',
                        'user' => [
                            'health_center' => $row['health_center'],
                            'fullname' => $row['fullname'],
                            'email' => $row['email']
                        ]
                    ]);
                } else {
                    // This is not your account
                    echo json_encode([
                        'success' => false,
                        'message' => 'This is not your account. You are currently logged in as "' . $_SESSION['health_center'] . '". Please logout first or use your own credentials.',
                        'type' => 'warning',
                        'current_account' => $_SESSION['health_center'],
                        'attempted_account' => $row['health_center'],
                        'show_logout' => true
                    ]);
                }
            } else {
                // No existing session - proceed with normal login
                $_SESSION['health_center'] = $row['health_center'];
                $_SESSION['email'] = $row['email'];
                $_SESSION['fullname'] = $row['fullname'];
                $_SESSION['AddressofFacility'] = $row['AddressofFacility'];
                $_SESSION['mobile_number'] = $row['mobile_number'];

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful! Redirecting...',
                    'redirect' => 'index.php',
                    'user' => [
                        'health_center' => $row['health_center'],
                        'fullname' => $row['fullname'],
                        'email' => $row['email']
                    ]
                ]);
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Please coordinate with Administrator in City Health Office for Account Approval',
                'type' => 'warning'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid username or password',
            'type' => 'error'
        ]);
    }
    
    $stmt->close();
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred. Please try again.',
        'type' => 'error'
    ]);
}

$conn->close();
?>
