-- Database Migration for Enhanced User Approval System
-- This script adds necessary columns for the disapprove functionality

-- Add columns to users table if they don't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS approved_date TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS disapproved_date TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS disapproval_reason TEXT NULL;

-- Add columns to health_facility table if they don't exist
ALTER TABLE health_facility 
ADD COLUMN IF NOT EXISTS approved_date TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS disapproved_date TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS disapproval_reason TEXT NULL;

-- Create audit log table for tracking user approval/disapproval actions
CREATE TABLE IF NOT EXISTS user_action_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    action_type VARCHAR(20) NOT NULL,
    action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    performed_by VARCHA<PERSON>(100),
    reason TEXT,
    INDEX idx_user_id (user_id),
    INDEX idx_action_date (action_date),
    INDEX idx_action_type (action_type)
);

-- Update existing approved users to have approved_date
UPDATE users 
SET approved_date = date_created 
WHERE approved = 1 AND approved_date IS NULL;

UPDATE health_facility 
SET approved_date = date_created 
WHERE approved = 1 AND approved_date IS NULL;

-- Add comments to document the approval status values
-- approved = 1: Approved
-- approved = 0: Pending
-- approved = -1: Disapproved

-- Optional: Create a view for easy reporting
CREATE OR REPLACE VIEW user_approval_status AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.fullname,
    u.health_center,
    u.mobile_number,
    u.date_created,
    CASE 
        WHEN u.approved = 1 THEN 'Approved'
        WHEN u.approved = -1 THEN 'Disapproved'
        ELSE 'Pending'
    END as status,
    u.approved_date,
    u.disapproved_date,
    u.disapproval_reason,
    'users' as source_table
FROM users u
UNION ALL
SELECT 
    hf.id,
    hf.username,
    hf.email,
    hf.fullname,
    hf.health_center,
    NULL as mobile_number,
    hf.date_created,
    CASE 
        WHEN hf.approved = 1 THEN 'Approved'
        WHEN hf.approved = -1 THEN 'Disapproved'
        ELSE 'Pending'
    END as status,
    hf.approved_date,
    hf.disapproved_date,
    hf.disapproval_reason,
    'health_facility' as source_table
FROM health_facility hf;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_approved ON users(approved);
CREATE INDEX IF NOT EXISTS idx_users_approved_date ON users(approved_date);
CREATE INDEX IF NOT EXISTS idx_health_facility_approved ON health_facility(approved);
CREATE INDEX IF NOT EXISTS idx_health_facility_approved_date ON health_facility(approved_date);

-- Insert sample audit log entries for existing data (optional)
INSERT IGNORE INTO user_action_logs (user_id, table_name, action_type, performed_by, reason)
SELECT id, 'users', 'APPROVED', 'System Migration', 'Migrated from existing approved users'
FROM users 
WHERE approved = 1;

INSERT IGNORE INTO user_action_logs (user_id, table_name, action_type, performed_by, reason)
SELECT id, 'health_facility', 'APPROVED', 'System Migration', 'Migrated from existing approved facilities'
FROM health_facility 
WHERE approved = 1;
