<?php session_start(); ?>

<?php
if (!isset($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta charset="UTF-8">
    
    <title>Records</title>
    <?php include 'style.php';?>
    <style>
    
 .dt-paging-button.current {
    
  
    background:#0277bd !important;
   
    
}
.btn-small {
    height: 29px;
    line-height: 28px;
    font-size: 12.3px;
}
    .pagination li.active a {
    color: #fff;
    font-weight: 500;
    background: #0277bd;
}
    @media only screen and (max-width : 492px) {
      header, main, footer {
        padding-left: 0;
      }
    }
    th {
        
        border-radius:0px;

      text-align:start !important;
        font-weight:600 !important;
        font-size:13.5px !important;
        font-family: 'Roboto', sans-serif !important;

    }
   
    td { 
        text-align:start !important;
        padding:2px 5px;
      
        font-weight:normal;
        font-size: 13.5px;
        border:1px solid #ddd !important;
    }
    input[type=search] {
        height: 1.7em !important;
        background-color: transparent;
    border: 1px solid #cbcbcb  !important;
    border-bottom: 1px solid #cbcbcb;
    border-radius: 3px !important;
    outline: none;
     
    width: 100%;
    font-size: 16px;
    margin: 0 0 8px 0;
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    -webkit-transition: border .3s, -webkit-box-shadow .3s;
    transition: border .3s, -webkit-
    }
    label {
        color:black !important;
    }
  
    div.dt-container div.dt-layout-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
     margin: 0 !important;  
}
    .dataTables_paginate  {
        background-color: #0277bd  !important;
        padding:2px;
    }
   
    .pagination li.disabled a {
    cursor: default;
    color: white !important;
}
 .dt-button   {
    background:#5e9834 !important;
    border:none !important;
    color:white !important;
    font-weight:500 !important;
    padding-top:0.4em !important;
}
 
nav { 
    color: #fff;
     background-color: white; 
    width: 100%;
    /* height: 56px; */
    color:black !important;
    
} 
.dt-paging-button ,.current {
    background:white !important;
    color:black !important;
}

    </style>
 
   
   <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
   <script src="https://cdn.datatables.net/2.1.8/js/dataTables.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/dataTables.buttons.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.dataTables.js"></script>
   <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
 
   <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
   <script src="https://cdn.datatables.net/buttons/3.2.0/js/buttons.html5.min.js"></script>
   <link rel="stylesheet" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.css">
   <link rel="stylesheet" href="https://cdn.datatables.net/buttons/3.2.0/css/buttons.dataTables.css">

</head>
<body style="background:#e9eef5;">
    <?php include 'nav.php'; ?>

 
    <div class="row   white    " style="margin:17px;" >
        <form class="col s12 " action="#" method="POST" style="padding:16px;">
          <h4 style=" margin-left: 1px; font-weight:500;font-size:22.5px;margin-top:5px;">Records</h4>
          <div class="row  " style="padding:4px;top:0em !important;">
              <div class="input-field col s12 m3">
                <input id="icon_prefix" type="text" name="LName" placeholder="">
                <label for="icon_prefix" style="font-size:18px !important;">Last Name</label>
            </div>
              <div class="input-field col s12 m3">
                  <input id="icon_prefix" type="text" name="FName" placeholder="">
                  <label for="icon_prefix" style="font-size:18px !important;">First Name</label>
              </div>
           
            <div class="input-field col s12 m2">
                <input id="icon_prefix" type="text" name="MName" placeholder="">
                <label for="icon_prefix" style="font-size:18px !important;">Middle Name</label>
            </div>
            <div class="input-field col s12 m3">
                <input id="icon_prefix" type="date" name="Bday" placeholder="">
                <label for="icon_prefix" style="font-size:18px !important;">Birthdate of Child</label>
            </div>
        
        <div class="input-field col s12 m1">
        <button class="btn large   deep-purple darken-2  " type="submit" name="action" style="font-weight:600;font-size:14.1px; ">Search 
    <i class="material-icons  left">search</i>
  </button>
</div>
 
     
     
    </form>
</div>

<div class="row  " style="padding:20px;  ">
     
    <?php include'db.php'; ?>
    
    <a class=" small-btn blue    btn modal-trigger" href="#modal1" style="font-size:12.3px;padding-bottom:10px;">View Duplicate Records</a>
    <button type="button" onclick="addRecord()" class="btn green darken-4 right  btn-small" style="font-weight: 500; margin-right: 1em;">
        <i class="material-icons left">add</i>
    <span id="buttonText">Add New Record</span>
</button>
<!-- Modal Structure -->

    <table id="mainTable" class=" small-table nowrap " style="width:auto%;">
          <thead class="   black-text " style="background: #eceff1;border:0px !important;">
              <tr >
                  <th>id</th>
                  <th >Action</th>
<th>DateOfRegistration</th>									 
<!--<th>UHCID</th>	 -->										 
<!-- <th>dateOfExpiration</th>	-->								 
<th>Date Of Birth</th>											 
<th>AgeofChild</th>											 
<!--<th>Family Serial Number</th>		-->										 
<!-- <th>Child Number</th>			-->									 
 									 
<th>First Name of Child</th>											 
<th>Last Name of Child</th>											 
<th>Middle Name of Child</th>											 
<th>Sex</th>													 
<th>NameofMother</th>										 
<th>BirthdateofMother</th>										 
<th>Age</th>													 
<th>Barangay</th>											 
<th>PurokStreetSitio</th>									 
<th>HouseNo</th>												 
<th>Address</th>												 
<th>PlaceofDelivery</th>										 
<th>NameofFacility</th>										 
<th>Attendant</th>											 
<th>TypeofDelivery</th>										 
<th>BirthWeightInGrams</th>									 
<th>BirthWeightClassification</th>							 
<th>WastheChildReferredforNewbornScreening</th>				 
<th>DateReferredforNewbornScreening</th>							
<th>DateofConsultationNewbornScreening</th>						
<th>TTStatusofMother</th>									 	
<th>Dateassessed</th>											
<th>WastheChildProtectedatBirth</th>								
<th>DateofConsultationCPAB</th>									
<th>BCG</th>														
<th>DateBCGwasgiven</th>											
<th>DateofConsultationBCG</th>									
<th>PENTAHIB1</th>												
<th>DatePenta1wasgiven</th>
<th>DateofConsultationPENTAHIB1</th>								
<th>PENTAHIB2</th>												
<th>DatePentahib2wasgiven</th>									
<th>DateofConsultationPENTAHIB2</th>								
<th>PENTAHIB3</th>												
<th>DatePentahib3wasgiven</th>									
<th>DateofConsultationPENTAHIB3</th>								
<th>OPV1</th>													
<th>DateOPV1wasgiven</th>										
<th>DateofConsultationOPV1v</th>									
<th>OPV2</th>													
<th>DateOPV2wasgiven</th>										
<th>DateofConsultationOPV2</th>									
<th>OPV3</th>													
<th>dateOPV3wasgiven</th>										
<th>DateofConsultationOPV3</th>									
<th>HEPAatBirth</th>	
<th>TimingHEPAatBirth</th>										
<th>DateHEPAatBirthwasgiven</th>									
<th>DateofConsultationHEPAatBirth</th>							
<th>HEPAB1</th>													
<th>dateHEPA1wasgiven</th>										
<th>DateofConsultationHEPAB1</th>								
 												
 										
 							
 												
 									
 								
 									
<th>MCV 1 (at 9months)</th>											
<th>DATE MCV 1 WAS GIVEN</th>											
<th>DATE OF CONSULTATON (MCV 1)</th>									
<th>MCV 2(at 12 Months)</th>										
<th>DATE MCV2 WAS GIVEN</th>										
<th>DATE OF CONSULTATION (MCV2)</th>									
<th>FIC</th>									
<th>DATE FIC WAS GIVEN</th>									
<th>DATE OF CONSULTATION (FIC)</th>									
<th>CIC</th>									 	
<th>DATE CIC WAS GIVEN</th>		
<th>DATE OF CONSULTATION (CIC)</th>												
<th>IMMUNIZATION STATUS</th>												
 											
<th>DATE WHEN CHILD WAS FULLY IMMUNIZED OR COMPLETELY IMMUNIZED</th>											
<th>FIRSTMONTH</th>												
<th>SECONDMONTH</th>												
<th>THIRDMONTH</th>												
<th>FOURTHDMONTH</th>									
<th>FIFTMONTH</th>							
<th>SIXTHMONTH</th>														
<th>DATE 6 MONTHS</th>										
<th>WAS THE CHILD EXCLUSIVELY BREASTFED FOR 6 MONTHS?</th>														
<th>DATE OF CONSULTATION (EXCLUSIVE)</th>										
<th>TD1</th>														
<th>DATE OF CONSULT (TT1)</th>										
<th>TT2</th>														
<th>DATE OF CONSULT (TT2)</th>										
<th>TT3</th>														
<th>DATE OF CONSULT (TT3)</th>											
<th>TT4</th>
<th>DATE OF CONSULT (TT4)</th>
<th>TT5</th>
<th>DATE OF CONSULT (TT5)</th>
<th  >Phone Number</th>

                </tr>
            </thead>
         
  
            <tbody class="   ">
 

            <?php
           date_default_timezone_set('Asia/Manila');




$LName = $_POST['LName'];
$FName = $_POST['FName'];
$MName = $_POST['MName'];
$Bday = $_POST['Bday'];
$deleted = 0;
$health_center = $_SESSION['health_center'];

if ($health_center == 'CITY HEALTH OFFICE') {
    $sql = "SELECT * FROM nip_table WHERE lastnameOfChild LIKE ? AND NameOfChild LIKE ? AND middlename_of_child LIKE ? AND DateOfBirth LIKE ? AND deleted LIKE ?   ";
    $stmt = $conn->prepare($sql);
    
    // Assuming $LName, $FName, $MName, $from, and $to are defined
    $LName = "%$LName%"; // For partial matching in LIKE
    $FName = "%$FName%"; // For partial matching in LIKE
    $MName = "%$MName%"; // For partial matching in LIKE
    $Bday = "%$Bday%"; // For partial matching in LIKE
    $deleted = "%$deleted%"; // For partial matching in LIKE
    
    $stmt->bind_param("sssss", $LName, $FName, $MName,$Bday,$deleted ); // Bind parameters
    
    
   
} else {
    $deleted = 0;
    $sql = "SELECT * FROM nip_table  WHERE lastnameOfChild LIKE ? AND NameOfChild LIKE ? AND middlename_of_child LIKE ? AND DateOfBirth LIKE ?  AND  Barangay = ? AND deleted LIKE ? ";
    $stmt = $conn->prepare($sql);
    $LName = "%$LName%"; // For partial matching in LIKE
    $FName = "%$FName%"; // For partial matching in LIKE
    $MName = "%$MName%"; // For partial matching in LIKE
    $Bday = "%$Bday%"; // For partial matching in LIKE
    $deleted = "%$deleted%"; // For partial matching in LIKE
    $stmt->bind_param("ssssss",$LName, $FName, $MName,$Bday, $health_center, $deleted);
}

$stmt->execute();
$result = $stmt->get_result();


 



if ($result->num_rows > 0) {
    // Output data for each row
    while ($row = $result->fetch_assoc()) { 
        $birthdate = $row['DateOfBirth']; // Example birthdate (YYYY-MM-DD)
        $today = new DateTime(); // Get current date
        $dob = new DateTime($birthdate); // Convert birthdate to DateTime
        $age = $dob->diff($today)->y; // Get age in years
        
     //   echo "Age: " . $age;
         
     //   if(date('F d,Y',strtotime($row['DateOfRegistration'])) == date('F d,Y',strtotime($row['dateOfExpiration']))) {
      
       
       
        
        ?>
       <?php if($row['deleted'] == 1) {
?>
           <tr style="display:none;">
  <?php      } else {?>
        <tr> 
        <?php } ?>

        <?php 
        $dob = strtotime($row['DateOfBirth']);
        $currentDate = time();
        $ageInYears = ($currentDate - $dob) / (365 * 24 * 60 * 60); // Age in years
        
              htmlspecialchars($row['lastnameOfChild']) . ', ' . 
                 htmlspecialchars($row['NameOfChild']) . ' ' . 
                 htmlspecialchars($row['middlename_of_child']) . ', ';

            // Fix the age condition
          

             
        ?>
        
<td style="         text-transform: uppercase !important;"><?php echo $row['id']; ?></td>
<td style="         text-transform: uppercase !important;">
    
                <a target="_blank"  
                href="print.php?id=<?php echo urlencode($row['id']); ?>"

                class="waves-light btn btn-small   transparent blue-text z-depth-0 ">
                  Print
             </a>|
             <a target="_blank" 
                href="edit.php?id=<?php echo urlencode($row['id']); ?>"

                class="btn btn-small  transparent blue-text z-depth-0  modal-trigger">
                   Edit
             </a>|
                
               
                
                <button style="display:none;" type="button" class="btn btn-small   transparent blue-text z-depth-0  modal-trigger" href="#modalView<?php echo htmlspecialchars($row['id']); ?>">View</button>
                <button type="button" class="btn btn-small   transparent blue-text z-depth-0  modal-trigger" href="#modalUpdate<?php echo htmlspecialchars($row['id']); ?>">
     Delete
</button>


    
             
            </td>
<?php if( date('F d,Y',strtotime($row['dateOfExpiration'])) == date('Y-m-d', strtotime('+365 day')) ) { ?>							 
<td class="red"><?php echo $row['DateOfRegistration']; ?></td>		
<td class="red"><?php echo $row['dateOfExpiration']; ?></td>									 
<?php } else {  ?>
    <td class=" "><?php echo date('F d,Y',strtotime($row['DateOfRegistration'])); ?></td>									 
   <!-- <td class="teal lighten-5"><?php // echo $row['dateOfExpiration']; ?></td>	 -->
<?php } ?>
<!--<td style="         text-transform: uppercase !important;"><?php // echo $row['UHCID']; ?></td>	 -->										 
<td style=" text-transform: uppercase !important;"><?php echo date('F d,Y',strtotime($row['DateOfBirth'])); ?>
<br>


</td>											 
<td  style=" text-transform: uppercase !important;"><?php
               if (round($ageInYears, 1) < 0.9) {  
                echo round($ageInYears * 12, 1) . ' Months old ';  // Convert to months
            } else {
                echo round($ageInYears, 1) . ' Year  old ';
            } ?></td>											 
 <!--<td ><?php //echo  $row['FamilySerialNumber']; ?></td>		 -->							 
 <!--<td ><?php //<td style="         text-transform: uppercase !important;">HC-<?php echo $row['ChildNumber']; ?></td>		 -->										 
 									 
 									 
<td class="yellow lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['NameOfChild']; ?></td>											 
<td class="yellow lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['lastnameOfChild']; ?></td>											 
<td class="yellow lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['middlename_of_child']; ?></td>											 
<td class="yellow lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['Sex']; ?></td>													 
<td class="blue lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['NameofMother']; ?></td>										 
<td class="blue lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['BirthdateofMother']; ?></td>													 
<td class="blue lighten-5" style="         text-transform: uppercase !important;"><?php echo $row['Age']; ?></td>													 
<td style="         text-transform: uppercase !important;"><?php echo $row['Barangay']; ?></td>											 
<td style="         text-transform: uppercase !important;"><?php echo $row['PurokStreetSitio']; ?></td>									 
<td style="         text-transform: uppercase !important;"><?php echo $row['HouseNo']; ?></td>												 
<td style="         text-transform: uppercase !important;"><?php echo $row['Address']; ?></td>												 
<td style="         text-transform: uppercase !important;"><?php echo $row['PlaceofDelivery']; ?></td>										 
<td style="         text-transform: uppercase !important;"><?php echo $row['NameofFacility']; ?></td>										 
<td style="         text-transform: uppercase !important;"><?php echo $row['Attendant']; ?></td>											 
<td style="         text-transform: uppercase !important;"><?php echo $row['TypeofDelivery']; ?></td>										 
<td style="         text-transform: uppercase !important;"><?php echo $row['BirthWeightInGrams']; ?></td>									 
<td style="         text-transform: uppercase !important;"><?php echo $row['BirthWeightClassification']; ?></td>							 
<td style="         text-transform: uppercase !important;"><?php echo $row['WastheChildReferredforNewbornScreening']; ?></td>				 
<td style="         text-transform: uppercase !important;"><?php echo $row['DateReferredforNewbornScreening']; ?></td>							
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationNewbornScreening']; ?></td>						
<td style="         text-transform: uppercase !important;"><?php echo $row['TTStatusofMother']; ?></td>									 	
<td style="         text-transform: uppercase !important;"><?php echo $row['Dateassessed']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['WastheChildProtectedatBirth']; ?></td>								
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationCPAB']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['BCG']; ?></td>														
<td style="         text-transform: uppercase !important;"><?php echo $row['DateBCGwasgiven']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationBCG']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['PENTAHIB1']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['DatePenta1wasgiven']; ?></td>
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationPENTAHIB1']; ?></td>								
<td style="         text-transform: uppercase !important;"><?php echo $row['PENTAHIB2']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['DatePentahib2wasgiven']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationPENTAHIB2']; ?></td>								
<td style="         text-transform: uppercase !important;"><?php echo $row['PENTAHIB3']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['DatePentahib3wasgiven']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationPENTAHIB3']; ?></td>								
<td style="         text-transform: uppercase !important;"><?php echo $row['OPV1']; ?></td>													
<td style="         text-transform: uppercase !important;"><?php echo $row['DateOPV1wasgiven']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationOPV1v']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['OPV2']; ?></td>													
<td style="         text-transform: uppercase !important;"><?php echo $row['DateOPV2wasgiven']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationOPV2']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['OPV3']; ?></td>													
<td style="         text-transform: uppercase !important;"><?php echo $row['dateOPV3wasgiven']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationOPV3']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['HEPAatBirth']; ?></td>	
<td style="         text-transform: uppercase !important;"><?php echo $row['TimingHEPAatBirth']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DateHEPAatBirthwasgiven']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationHEPAatBirth']; ?></td>							
<td style="         text-transform: uppercase !important;"><?php echo $row['HEPAB1']; ?></td>													
<td style="         text-transform: uppercase !important;"><?php echo $row['dateHEPA1wasgiven']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationHEPAB1']; ?></td>								
 												
 									
 							
 												
 									
 							
 								
 								
<td style="         text-transform: uppercase !important;"><?php echo $row['MMR12MOSTO15MOS']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['dateMMRWASGIVEN']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationMMR']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['MMR12MOSTO15MOS']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['dateMMRWASGIVEN']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationMMR']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['FIC']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['dateFICWASGIVEN']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationFIC']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['CIC']; ?></td>									 	
<td style="         text-transform: uppercase !important;"><?php echo $row['dateCICWASGIVEN']; ?></td>		
<td style="         text-transform: uppercase !important;"><?php echo $row['DateofConsultationCIC']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['IMMUNIZATIONSTATUS']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['FIRSTMONTH']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['SECONDMONTH']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['THIRDMONTH']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['FOURTHDMONTH']; ?></td>												
<td style="         text-transform: uppercase !important;"><?php echo $row['FIFTMONTH']; ?></td>									
<td style="         text-transform: uppercase !important;"><?php echo $row['SIXTHMONTH']; ?></td>							
<td style="         text-transform: uppercase !important;"><?php echo $row['date6MONTHS']; ?></td>														
<td style="         text-transform: uppercase !important;"><?php echo $row['WASTHECHILDEXCLUSIVELY']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEOFCONSULTATIONEXCLUSIVEBF']; ?></td>														
<td style="         text-transform: uppercase !important;"><?php echo $row['TT1']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEOFCONSULTTT1']; ?></td>														
<td style="         text-transform: uppercase !important;"><?php echo $row['TT2']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEOFCONSULTTT2']; ?></td>														
<td style="         text-transform: uppercase !important;"><?php echo $row['TT3']; ?></td>										
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEOFCONSULTTT3']; ?></td>														
<td style="         text-transform: uppercase !important;"><?php echo $row['TT4']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEOFCONSULTTT4']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['TT5']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['DATEOFCONSULTT5']; ?></td>											
<td style="         text-transform: uppercase !important;"><?php echo $row['PhoneNumber']; ?></td>
           
        </tr>
    <!-- Update Modal Structure -->
<div id="modalUpdate<?php echo htmlspecialchars($row['id']); ?>" class="modal">
    <div class="modal-content">
        <h4 class="black-text">Delete Record?</h4>
        <form action="update.php" method="POST">
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($row['id']); ?>">
            
         Delete Record <?php echo htmlspecialchars($row['id']); ?>

            <div class="input-field">
                <input type="text" style="display:none !important;" name="deleted" value="<?php echo htmlspecialchars($row['deleted']); ?>" readonly>
                 
            </div>

            <div class="modal-footer">
                <button type="submit" style="font-weight:500;" class="btn black white-text">Delete</button>
                <a href="#!" class="modal-close btn red white-text">Cancel</a>
            </div>
        </form>
        
    </div>
</div>
<div id="modalView<?php echo htmlspecialchars($row['id']); ?>" class="modal">
    <div class="modal-content">
        <h4 class="black-text">View Record</h4>
       
        <?php 
        
     echo $row['id'];
        
        
        ?>
        
    </div>
</div>
    <?php }
} else {
    echo " ";
}
$stmt->close();
?>


                </tr>
                
            </tbody>
             
        </table>
        
   <!-- <i class="close material-icons">close</i> -->
  <!-- <a class="  modal-trigger grey-text" href="#trashRecords"> -->

      <!--   <i class="material-icons  start ">delete</i> Trash -->
  <!--   </a> -->




    <div id="trashRecords" class="modal">
    <div class="modal-content">
      <small>Records that have been in Trash more than 30 days will be automatically deleted</small>
      <table class="grey lighten-3" style="border-left:0px;">
        <thead  style="border-left:0px;">
           
          <tr>
          
              <th>id</th>
              <th>Family Serial Number</th>
              <th>Name</th>
              <th>Date of Birth</th>
          </tr>
         
        </thead>

        <tbody >
        <?php
$deleted = 1;
$health_center = $_SESSION['health_center'];

if (!isset($LName)) $LName = "";
if (!isset($FName)) $FName = "";
if (!isset($MName)) $MName = "";
if (!isset($Bday)) $Bday = "";

// Prepare values for LIKE
$LName = "%{$LName}%";
$FName = "%{$FName}%";
$MName = "%{$MName}%";
$Bday = "%{$Bday}%";
$deleted = "%{$deleted}%"; // Keeping deleted as LIKE may not be needed (use = instead)

// Choose query based on condition
if ($health_center == 'CITY HEALTH OFFICE') {
    $sql = "SELECT * FROM nip_table 
            WHERE lastnameOfChild LIKE ? 
            AND NameOfChild LIKE ? 
            AND middlename_of_child LIKE ? 
            AND DateOfBirth LIKE ?  
            AND deleted LIKE ? 
            LIMIT 50 OFFSET 0";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssss", $LName, $FName, $MName, $Bday, $deleted);
} else {
    $sql = "SELECT * FROM nip_table 
            WHERE lastnameOfChild LIKE ? 
            AND NameOfChild LIKE ? 
            AND middlename_of_child LIKE ? 
            AND DateOfBirth LIKE ?  
            AND Barangay = ? 
            AND deleted LIKE ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssss", $LName, $FName, $MName, $Bday, $health_center, $deleted);
}

// Execute and fetch results
$stmt->execute();
$result = $stmt->get_result();
 
             while ($row = $result->fetch_assoc()) { 
            ?>
          <tr >
         
            <td style="         text-transform: uppercase !important;"><?php echo $row['id']; ?></td>
            <td style="         text-transform: uppercase !important;"><?php echo $row['FamilySerialNumber']; ?></td>
            <td style="         text-transform: uppercase !important;"><?php echo $row['lastnameOfChild'].','.$row['NameOfChild'].' '.$row['middlename_of_child']; ?></td>
            <td style="         text-transform: uppercase !important;"><?php echo $row['DateOfBirth']; ?></td>
          </tr>
          <?php } ?>
        </tbody>
      </table>
    </div>
    <div class="modal-footer">
    <button class="btn btn-small blue">Undo</button>
    <button class="btn btn-small red darken-1">Delete</button>
    </div>
  </div>
   </div>
 
   
       
    </div>
    </div>
    </div>
</div>






<div id="modal1" class="modal">
  <div class="modal-content">
    <?php
 if ($health_center == 'CITY HEALTH OFFICE') {
 
 $sql = "SELECT Barangay, NameOfChild, middlename_of_child, LastNameOfChild,NameofMother, COUNT(*) as count 
         FROM nip_table 
         WHERE deleted = 0 
         GROUP BY NameOfChild, middlename_of_child, LastNameOfChild 
         HAVING COUNT(*) > 1";
 }
 else {
    $sql = "SELECT NameOfChild, middlename_of_child, LastNameOfChild,NameofMother,Barangay, COUNT(*) as count 
    FROM nip_table 
    WHERE Barangay = '".$_SESSION['health_center']."' AND deleted = 0   
    GROUP BY NameOfChild, middlename_of_child, LastNameOfChild 
    HAVING COUNT(*) > 1";
 }
 $result = $conn->query($sql);
 ?>
 <h5>Duplicate Records: <?php if($health_center == 'CITY HEALTH OFFICE'){ echo ''; } else { echo htmlspecialchars($health_center);} ?></h5>
<table class="responsive">
    <tr >
        <th>First Name</th>
        <th>Middle Name</th>
        <th>Last Name</th>
        <th>Name of Mother</th>
        <th>Health Center</th>
        <th>Count</th>
    </tr>

    <?php
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo "<tr style='font-size:4px !important;'>
                    <td >{$row['NameOfChild']}</td>
                    <td >{$row['middlename_of_child']}</td>
                    <td >{$row['LastNameOfChild']}</td>
                    <td >{$row['NameofMother']}</td>
                    <td >{$row['Barangay']}</td>
                    <td >{$row['count']}</td>
                  </tr>";
        }
    } else {
        echo "<tr><td colspan='4'>No duplicate records found</td></tr>";
    }
    $stmt->close();
    $conn->close();
    ?>

</table>
  </div>
  <div class="modal-footer">
    <a href="#!" class="modal-close waves-effect waves-green btn-flat">Close</a>
  </div>
</div>







</body>
</html>
 
<script>
  $(document).ready(function(){
    $('.modal').modal();
  });
</script>

<script>
    // Remove the warning notice pop-up and enhance backward compatibility
    (function() {
        if (window.history && window.history.replaceState) {
            window.addEventListener('load', function() {
                // Ensure the script runs only after the page fully loads
                window.history.replaceState(null, null, window.location.href);
            });
        }
    })();
</script>
<script>
    new DataTable('#mainTable', {
        pageLength: 10,
        processing: true,
        responsive: {
        details: true
    },
    layout: {
        topEnd: {
            search: {
              return:true,
              serverSide: true,
              placeholder: ''
            },
            
            
        },
        
        topStart: {
             
           
           
       },
      

    // Initialize Materialize CSS select dropdown
},
scrollX: true // Enable horizontal scrolling
});

$('select').formSelect();

</script>
 
<script>
function addRecord() {
    // Change button text and add loading icon
    document.getElementById('buttonText').innerHTML = '<i class="material-icons left">hourglass_empty</i>Loading...';
    
    // Simulate a delay for loading (replace with actual redirection or action)
    setTimeout(() => {
        window.location.href = 'nip.php';
    }, 200); // Adjust the delay as needed
}
</script>
 


