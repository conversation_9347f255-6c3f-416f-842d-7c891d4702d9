<?php
// Bulk Print Page for NIP Records
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user has valid health center assignment
if (!isset($_SESSION['health_center']) || empty($_SESSION['health_center'])) {
    header('Location: login.php');
    exit;
}

$user_health_center = $_SESSION['health_center'];

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    die('Database connection failed: ' . mysqli_connect_error());
}

// Get search parameters from URL or POST
$search_params = [];
if (isset($_GET['first_name'])) $search_params['first_name'] = $_GET['first_name'];
if (isset($_GET['last_name'])) $search_params['last_name'] = $_GET['last_name'];
if (isset($_GET['middle_name'])) $search_params['middle_name'] = $_GET['middle_name'];
if (isset($_GET['birth_date'])) $search_params['birth_date'] = $_GET['birth_date'];

// Build query with health center filter
$sql = "SELECT id, DateOfRegistration, NameOfChild, middlename_of_child, LastNameOfChild, 
               DateOfBirth, Sex, NameofMother, Barangay, Address, PhoneNumber, FamilySerialNumber,
               PlaceofDelivery, Attendant, TypeofDelivery, BirthWeightInGrams
        FROM nip_table 
        WHERE (deleted IS NULL OR deleted = 0) AND Barangay = ?";

$params = [$user_health_center];
$types = "s";

// Add search conditions
if (!empty($search_params['first_name'])) {
    $sql .= " AND NameOfChild LIKE ?";
    $params[] = "%" . $search_params['first_name'] . "%";
    $types .= "s";
}

if (!empty($search_params['last_name'])) {
    $sql .= " AND LastNameOfChild LIKE ?";
    $params[] = "%" . $search_params['last_name'] . "%";
    $types .= "s";
}

if (!empty($search_params['middle_name'])) {
    $sql .= " AND middlename_of_child LIKE ?";
    $params[] = "%" . $search_params['middle_name'] . "%";
    $types .= "s";
}

if (!empty($search_params['birth_date'])) {
    $sql .= " AND DateOfBirth = ?";
    $params[] = $search_params['birth_date'];
    $types .= "s";
}

$sql .= " ORDER BY DateOfRegistration DESC, LastNameOfChild, NameOfChild";

// Execute query
$stmt = mysqli_prepare($conn, $sql);
$results = [];

if ($stmt) {
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    while ($row = mysqli_fetch_assoc($result)) {
        if ($row['Barangay'] === $user_health_center) {
            $results[] = $row;
        }
    }
    mysqli_stmt_close($stmt);
}

mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Print - NIP Records</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2196f3;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            color: #2196f3;
            font-size: 28px;
        }
        
        .header h2 {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 20px;
            font-weight: normal;
        }
        
        .report-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        
        .report-info h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .records-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 12px;
        }
        
        .records-table th,
        .records-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .records-table th {
            background-color: #2196f3;
            color: white;
            font-weight: bold;
            font-size: 11px;
        }
        
        .records-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .records-table tr:hover {
            background-color: #e3f2fd;
        }
        
        .summary-stats {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            border-left: 4px solid #4caf50;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 2px solid #ddd;
            padding-top: 20px;
        }
        
        .no-print {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .print-btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        .print-btn:hover {
            background: #1976d2;
        }
        
        .back-btn {
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-btn:hover {
            background: #555;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .records-table {
                font-size: 10px;
            }
            
            .records-table th,
            .records-table td {
                padding: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button onclick="window.print()" class="print-btn">🖨️ Print Report</button>
        <a href="filter_records.php" class="back-btn">← Back to Filter</a>
    </div>

    <div class="header">
        <h1>National Immunization Program</h1>
        <h2>Bulk Records Report</h2>
    </div>

    <div class="report-info">
        <h3>Report Information</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Health Center:</span>
                <span class="info-value"><?php echo htmlspecialchars($user_health_center); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Generated:</span>
                <span class="info-value"><?php echo date('F j, Y g:i A'); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Total Records:</span>
                <span class="info-value"><?php echo count($results); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Generated By:</span>
                <span class="info-value"><?php echo htmlspecialchars($_SESSION['fullname'] ?? 'System User'); ?></span>
            </div>
        </div>
        
        <?php if (!empty($search_params)): ?>
        <h4 style="margin-top: 20px; margin-bottom: 10px;">Search Criteria Applied:</h4>
        <ul style="margin: 0; padding-left: 20px;">
            <?php foreach ($search_params as $key => $value): ?>
                <?php if (!empty($value)): ?>
                    <li><strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong> <?php echo htmlspecialchars($value); ?></li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>

    <?php if (count($results) > 0): ?>
        <div class="summary-stats">
            <h3 style="margin: 0 0 20px 0; text-align: center;">Summary Statistics</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo count($results); ?></div>
                    <div class="stat-label">Total Children</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count(array_filter($results, function($r) { return $r['Sex'] === 'MALE'; })); ?></div>
                    <div class="stat-label">Male Children</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo count(array_filter($results, function($r) { return $r['Sex'] === 'FEMALE'; })); ?></div>
                    <div class="stat-label">Female Children</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo date('Y') - min(array_map(function($r) { return date('Y', strtotime($r['DateOfBirth'])); }, $results)); ?></div>
                    <div class="stat-label">Age Range (Years)</div>
                </div>
            </div>
        </div>

        <table class="records-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Registration Date</th>
                    <th>Full Name</th>
                    <th>Birth Date</th>
                    <th>Sex</th>
                    <th>Mother's Name</th>
                    <th>Address</th>
                    <th>Phone</th>
                    <th>Family Serial</th>
                    <th>Birth Weight (g)</th>
                    <th>Place of Delivery</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($results as $row): ?>
                    <?php $full_name = trim($row['NameOfChild'] . ' ' . $row['middlename_of_child'] . ' ' . $row['LastNameOfChild']); ?>
                    <tr>
                        <td><?php echo htmlspecialchars($row['id']); ?></td>
                        <td><?php echo htmlspecialchars($row['DateOfRegistration']); ?></td>
                        <td><strong><?php echo htmlspecialchars($full_name); ?></strong></td>
                        <td><?php echo htmlspecialchars($row['DateOfBirth']); ?></td>
                        <td><?php echo htmlspecialchars($row['Sex']); ?></td>
                        <td><?php echo htmlspecialchars($row['NameofMother']); ?></td>
                        <td><?php echo htmlspecialchars($row['Address']); ?></td>
                        <td><?php echo htmlspecialchars($row['PhoneNumber']); ?></td>
                        <td><?php echo htmlspecialchars($row['FamilySerialNumber']); ?></td>
                        <td><?php echo htmlspecialchars($row['BirthWeightInGrams'] ?? 'N/A'); ?></td>
                        <td><?php echo htmlspecialchars($row['PlaceofDelivery'] ?? 'N/A'); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div style="text-align: center; padding: 40px; background: #fff3e0; border-radius: 8px; border-left: 4px solid #ff9800;">
            <h3>No Records Found</h3>
            <p>No records match the specified criteria for health center: <strong><?php echo htmlspecialchars($user_health_center); ?></strong></p>
        </div>
    <?php endif; ?>

    <div class="footer">
        <p><strong>National Immunization Program</strong></p>
        <p>Health Center: <?php echo htmlspecialchars($user_health_center); ?></p>
        <p>This is an official report generated on <?php echo date('F j, Y \a\t g:i A'); ?></p>
        <p>Report contains <?php echo count($results); ?> record(s) | Generated by: <?php echo htmlspecialchars($_SESSION['fullname'] ?? 'System User'); ?></p>
    </div>

    <script>
        // Auto-print if requested
        if (window.location.search.includes('autoprint=1')) {
            window.onload = function() {
                window.print();
            };
        }
    </script>
</body>
</html>
