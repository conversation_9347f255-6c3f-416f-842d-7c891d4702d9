<?php
// Quick Password Lookup Script
// This script allows you to quickly find passwords by username or health center

include 'db.php';

// Simple security check - change this password
$ACCESS_PASSWORD = 'lookup2024';

$authenticated = false;
$results = [];
$search_performed = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['access_password'])) {
        if ($_POST['access_password'] === $ACCESS_PASSWORD) {
            $authenticated = true;
            session_start();
            $_SESSION['lookup_auth'] = true;
        } else {
            $error = "Invalid access password!";
        }
    }
    
    if (isset($_POST['search']) && isset($_SESSION['lookup_auth'])) {
        $search_term = trim($_POST['search_term']);
        $search_type = $_POST['search_type'];
        
        if (!empty($search_term)) {
            $search_performed = true;
            
            switch ($search_type) {
                case 'username':
                    $stmt = $conn->prepare("SELECT id, username, password, fullname, health_center, email, mobile_number, approved FROM health_facility WHERE username LIKE ? ORDER BY username");
                    $search_param = "%$search_term%";
                    break;
                case 'health_center':
                    $stmt = $conn->prepare("SELECT id, username, password, fullname, health_center, email, mobile_number, approved FROM health_facility WHERE health_center LIKE ? ORDER BY username");
                    $search_param = "%$search_term%";
                    break;
                case 'email':
                    $stmt = $conn->prepare("SELECT id, username, password, fullname, health_center, email, mobile_number, approved FROM health_facility WHERE email LIKE ? ORDER BY username");
                    $search_param = "%$search_term%";
                    break;
                case 'mobile':
                    $stmt = $conn->prepare("SELECT id, username, password, fullname, health_center, email, mobile_number, approved FROM health_facility WHERE mobile_number LIKE ? ORDER BY username");
                    $search_param = "%$search_term%";
                    break;
                default:
                    $stmt = $conn->prepare("SELECT id, username, password, fullname, health_center, email, mobile_number, approved FROM health_facility WHERE username LIKE ? OR health_center LIKE ? OR email LIKE ? OR fullname LIKE ? ORDER BY username");
                    $search_param = "%$search_term%";
            }
            
            if (isset($stmt)) {
                if ($search_type === 'all') {
                    $stmt->bind_param("ssss", $search_param, $search_param, $search_param, $search_param);
                } else {
                    $stmt->bind_param("s", $search_param);
                }
                
                $stmt->execute();
                $result = $stmt->get_result();
                
                while ($row = $result->fetch_assoc()) {
                    $results[] = $row;
                }
                $stmt->close();
            }
        }
    }
}

// Check if already authenticated
session_start();
if (isset($_SESSION['lookup_auth'])) {
    $authenticated = true;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Password Lookup - NIP System</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
        }
        .lookup-container {
            margin-top: 30px;
            max-width: 1000px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        .password-field {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
            color: #e74c3c;
            border-left: 3px solid #3498db;
        }
        .result-card {
            margin-bottom: 10px;
            border-left: 4px solid #27ae60;
        }
        .status-badge {
            font-size: 11px;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <div class="container lookup-container">
        <div class="row">
            <div class="col s12">
                <div class="card">
                    <div class="card-content">
                        <span class="card-title center-align blue-text">
                            <i class="material-icons left">search</i>
                            Quick Password Lookup
                        </span>

                        <?php if (isset($error)): ?>
                            <div class="card red lighten-4">
                                <div class="card-content red-text">
                                    <i class="material-icons left">error</i>
                                    <?php echo htmlspecialchars($error); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!$authenticated): ?>
                            <!-- Access Authentication -->
                            <div class="row">
                                <div class="col s12 m6 offset-m3">
                                    <div class="card blue lighten-5">
                                        <div class="card-content">
                                            <span class="card-title blue-text">
                                                <i class="material-icons left">lock</i>
                                                Access Required
                                            </span>
                                            <form method="POST">
                                                <div class="input-field">
                                                    <i class="material-icons prefix">vpn_key</i>
                                                    <input id="access_password" name="access_password" type="password" required>
                                                    <label for="access_password">Access Password</label>
                                                </div>
                                                <button type="submit" class="btn blue waves-effect">
                                                    <i class="material-icons left">login</i>Access
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Search Form -->
                            <div class="row">
                                <div class="col s12">
                                    <form method="POST">
                                        <div class="row">
                                            <div class="col s12 m4">
                                                <div class="input-field">
                                                    <select name="search_type" required>
                                                        <option value="" disabled selected>Choose search type</option>
                                                        <option value="username">Username</option>
                                                        <option value="health_center">Health Center</option>
                                                        <option value="email">Email</option>
                                                        <option value="mobile">Mobile Number</option>
                                                        <option value="all">All Fields</option>
                                                    </select>
                                                    <label>Search By</label>
                                                </div>
                                            </div>
                                            <div class="col s12 m6">
                                                <div class="input-field">
                                                    <i class="material-icons prefix">search</i>
                                                    <input id="search_term" name="search_term" type="text" required>
                                                    <label for="search_term">Search Term</label>
                                                </div>
                                            </div>
                                            <div class="col s12 m2">
                                                <button type="submit" name="search" class="btn blue waves-effect" style="margin-top: 25px;">
                                                    <i class="material-icons">search</i>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <?php if ($search_performed): ?>
                                <div class="divider"></div>
                                <div class="section">
                                    <h5 class="blue-text">
                                        <i class="material-icons left">list</i>
                                        Search Results (<?php echo count($results); ?> found)
                                    </h5>

                                    <?php if (empty($results)): ?>
                                        <div class="card orange lighten-4">
                                            <div class="card-content orange-text text-darken-2">
                                                <i class="material-icons left">info</i>
                                                No users found matching your search criteria.
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="row">
                                            <?php foreach ($results as $user): ?>
                                                <div class="col s12 m6">
                                                    <div class="card result-card">
                                                        <div class="card-content">
                                                            <div class="card-title" style="font-size: 16px;">
                                                                <strong><?php echo htmlspecialchars($user['fullname']); ?></strong>
                                                                <span class="right">
                                                                    <?php if ($user['approved'] == 1): ?>
                                                                        <span class="badge green white-text status-badge">Approved</span>
                                                                    <?php elseif ($user['approved'] == 0): ?>
                                                                        <span class="badge red white-text status-badge">Disapproved</span>
                                                                    <?php else: ?>
                                                                        <span class="badge orange white-text status-badge">Pending</span>
                                                                    <?php endif; ?>
                                                                </span>
                                                            </div>
                                                            
                                                            <p><strong>ID:</strong> <?php echo $user['id']; ?></p>
                                                            <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                                                            <p><strong>Health Center:</strong> <?php echo htmlspecialchars($user['health_center']); ?></p>
                                                            <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                                                            <p><strong>Mobile:</strong> <?php echo htmlspecialchars($user['mobile_number']); ?></p>
                                                            
                                                            <div style="margin-top: 15px;">
                                                                <strong>Password:</strong>
                                                                <div class="password-field">
                                                                    <?php echo htmlspecialchars($user['password']); ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <div class="center-align" style="margin-top: 30px;">
                                <a href="password_recovery.php" class="btn green waves-effect">
                                    <i class="material-icons left">build</i>Full Recovery Tool
                                </a>
                                <a href="login.php" class="btn blue waves-effect">
                                    <i class="material-icons left">arrow_back</i>Back to Login
                                </a>
                                <a href="?logout=1" class="btn red waves-effect">
                                    <i class="material-icons left">logout</i>Logout
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>

    <?php
    // Handle logout
    if (isset($_GET['logout'])) {
        session_destroy();
        header('Location: quick_password_lookup.php');
        exit;
    }
    ?>
</body>
</html>
