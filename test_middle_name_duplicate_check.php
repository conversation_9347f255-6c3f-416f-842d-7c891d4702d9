<?php
session_start();
include 'db.php';

// Set up session for testing (if not already logged in)
if (!isset($_SESSION['health_center'])) {
    $_SESSION['health_center'] = 'TEST_CENTER';
    $_SESSION['fullname'] = 'Test User';
}

echo "<h2>🧪 Enhanced Duplicate Check Test - Including Middle Names</h2>";

// Test data for enhanced duplicate prevention with middle names
$testData = [
    'exact_match' => [
        'NameOfChild' => '<PERSON>',
        'middlename_of_child' => '<PERSON>',
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => '<PERSON>',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1'
    ],
    'exact_duplicate' => [
        'NameOfChild' => 'Juan',
        'middlename_of_child' => '<PERSON>',
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => '<PERSON>',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1'
    ],
    'different_middle' => [
        'NameOfChild' => 'Juan',
        'middlename_of_child' => 'Garcia',  // Different middle name
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => 'Maria Santos',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1'
    ],
    'similar_middle' => [
        'NameOfChild' => 'Juan',
        'middlename_of_child' => 'Santo',  // Similar sounding middle name
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => 'Maria Santos',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1'
    ],
    'no_middle' => [
        'NameOfChild' => 'Juan',
        'middlename_of_child' => '',  // No middle name
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',
        'NameofMother' => 'Maria Santos',
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1'
    ],
    'twins_same_mother' => [
        'NameOfChild' => 'Maria',
        'middlename_of_child' => 'Santos',
        'LastNameOfChild' => 'Dela Cruz',
        'DateOfBirth' => '2020-01-15',  // Same birth date as Juan
        'NameofMother' => 'Maria Santos',  // Same mother
        'PhoneNumber' => '09123456789',
        'Address' => '123 Main Street',
        'Barangay' => 'Barangay 1'
    ]
];

// Function to simulate enhanced duplicate checking
function testDuplicateCheck($data, $label) {
    global $conn;
    
    echo "<h3>🎯 Test: $label</h3>";
    
    $firstName = $data['NameOfChild'];
    $middleName = $data['middlename_of_child'];
    $lastName = $data['LastNameOfChild'];
    $dateOfBirth = $data['DateOfBirth'];
    $motherName = $data['NameofMother'];
    $phoneNumber = $data['PhoneNumber'];
    $address = $data['Address'];
    $barangay = $data['Barangay'];
    
    $fullName = trim($firstName . ' ' . $middleName . ' ' . $lastName);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Input Data:</h4>";
    echo "<ul>";
    echo "<li><strong>First Name:</strong> {$firstName}</li>";
    echo "<li><strong>Middle Name:</strong> " . ($middleName ? $middleName : '<em>None</em>') . "</li>";
    echo "<li><strong>Last Name:</strong> {$lastName}</li>";
    echo "<li><strong>Full Name:</strong> {$fullName}</li>";
    echo "<li><strong>DOB:</strong> {$dateOfBirth}</li>";
    echo "<li><strong>Mother:</strong> {$motherName}</li>";
    echo "<li><strong>Phone:</strong> {$phoneNumber}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Check for duplicates using the enhanced function
    $duplicateCheck = checkForDuplicates(
        $conn, 
        $firstName, 
        $lastName, 
        $middleName, 
        $dateOfBirth, 
        $motherName, 
        $phoneNumber, 
        $address, 
        $barangay
    );
    
    $duplicateFound = $duplicateCheck['found'];
    $duplicateMessages = $duplicateCheck['messages'];
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Duplicate Check Results:</h4>";
    echo "<p><strong>Exact Duplicates Found:</strong> " . ($duplicateFound ? 'YES' : 'NO') . "</p>";
    echo "<p><strong>Total Messages:</strong> " . count($duplicateMessages) . "</p>";
    
    if (!empty($duplicateMessages)) {
        echo "<h5>Detailed Messages:</h5>";
        foreach ($duplicateMessages as $msg) {
            $color = '';
            $icon = '';
            switch ($msg['type']) {
                case 'error': 
                    $color = 'red'; 
                    $icon = '🚫';
                    break;
                case 'warning': 
                    $color = 'orange'; 
                    $icon = '⚠️';
                    break;
                case 'info': 
                    $color = 'blue'; 
                    $icon = 'ℹ️';
                    break;
            }
            echo "<div style='background: #{$color}20; padding: 10px; margin: 5px 0; border-left: 4px solid $color;'>";
            echo "<strong>{$icon} {$msg['title']}</strong><br>";
            echo "{$msg['message']}";
            echo "</div>";
        }
    }
    echo "</div>";
    
    // Decision
    if ($duplicateFound) {
        echo "<div style='background: #ffebee; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #f44336;'>";
        echo "<h4>🚫 REGISTRATION WOULD BE BLOCKED</h4>";
        echo "<p><strong>Reason:</strong> Exact duplicate found (same first name, middle name, last name, and birth date)</p>";
        echo "<p><strong>Action:</strong> Data insertion prevented</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #4caf50;'>";
        echo "<h4>✅ REGISTRATION WOULD BE ALLOWED</h4>";
        echo "<p><strong>Status:</strong> No exact duplicates found</p>";
        if (!empty($duplicateMessages)) {
            echo "<p><strong>Warnings:</strong> " . count($duplicateMessages) . " potential issues detected (user would be warned)</p>";
        }
        echo "</div>";
    }
    
    return !$duplicateFound;
}

// Run enhanced tests
echo "<div style='background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>⚠️ Enhanced Duplicate Detection Test</h3>";
echo "<p>This test demonstrates the enhanced duplicate checking system that includes:</p>";
echo "<ul>";
echo "<li><strong>First Name + Middle Name + Last Name + Date of Birth</strong> for exact matching</li>";
echo "<li><strong>Partial matching</strong> for same first/last name but different middle name</li>";
echo "<li><strong>SOUNDEX matching</strong> for similar sounding names</li>";
echo "<li><strong>Mother name validation</strong> for twins detection</li>";
echo "<li><strong>Phone number conflicts</strong> detection</li>";
echo "<li><strong>Household detection</strong> for family members</li>";
echo "</ul>";
echo "</div>";

// Test 1: Original registration
echo "<hr>";
$result1 = testDuplicateCheck($testData['exact_match'], "Original Registration (Should Pass)");

// Test 2: Exact duplicate
echo "<hr>";
$result2 = testDuplicateCheck($testData['exact_duplicate'], "Exact Duplicate - Same Middle Name (Should Be Blocked)");

// Test 3: Different middle name
echo "<hr>";
$result3 = testDuplicateCheck($testData['different_middle'], "Different Middle Name (Should Show Warning)");

// Test 4: Similar middle name
echo "<hr>";
$result4 = testDuplicateCheck($testData['similar_middle'], "Similar Middle Name (Should Show Warning)");

// Test 5: No middle name
echo "<hr>";
$result5 = testDuplicateCheck($testData['no_middle'], "No Middle Name (Should Show Warning)");

// Test 6: Twins (same mother, same DOB, different names)
echo "<hr>";
$result6 = testDuplicateCheck($testData['twins_same_mother'], "Potential Twins (Should Show Warning)");

// Summary
echo "<hr>";
echo "<h3>📊 Enhanced Test Summary</h3>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; background: white;'>";
echo "<tr style='background: #2196f3; color: white;'>";
echo "<th>Test Case</th><th>Expected Result</th><th>Actual Result</th><th>Status</th><th>Key Feature</th>";
echo "</tr>";

$tests = [
    ['Original Registration', 'Should Pass', $result1 ? 'Passed' : 'Failed', $result1, 'Baseline test'],
    ['Exact Duplicate', 'Should Be Blocked', !$result2 ? 'Blocked' : 'Allowed', !$result2, 'Full name + DOB matching'],
    ['Different Middle Name', 'Should Show Warning', $result3 ? 'Allowed with Warning' : 'Blocked', $result3, 'Partial name matching'],
    ['Similar Middle Name', 'Should Show Warning', $result4 ? 'Allowed with Warning' : 'Blocked', $result4, 'SOUNDEX matching'],
    ['No Middle Name', 'Should Show Warning', $result5 ? 'Allowed with Warning' : 'Blocked', $result5, 'Missing middle name detection'],
    ['Potential Twins', 'Should Show Warning', $result6 ? 'Allowed with Warning' : 'Blocked', $result6, 'Same mother + DOB detection']
];

foreach ($tests as $test) {
    $status = $test[3];
    $color = $status ? 'green' : 'red';
    $icon = $status ? '✅ PASS' : '❌ FAIL';
    
    echo "<tr>";
    echo "<td>{$test[0]}</td>";
    echo "<td>{$test[1]}</td>";
    echo "<td>{$test[2]}</td>";
    echo "<td style='color: {$color};'>{$icon}</td>";
    echo "<td><em>{$test[4]}</em></td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Show database statistics
echo "<hr>";
echo "<h3>📈 Database Statistics</h3>";
try {
    // Total records
    $total_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE (deleted IS NULL OR deleted = 0)");
    $total_row = $total_result->fetch_assoc();
    echo "<p><strong>Total Active Records:</strong> {$total_row['total']}</p>";
    
    // Records with middle names
    $middle_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE middlename_of_child IS NOT NULL AND middlename_of_child != '' AND (deleted IS NULL OR deleted = 0)");
    $middle_row = $middle_result->fetch_assoc();
    echo "<p><strong>Records with Middle Names:</strong> {$middle_row['total']}</p>";
    
    // Records without middle names
    $no_middle_result = $conn->query("SELECT COUNT(*) as total FROM nip_table WHERE (middlename_of_child IS NULL OR middlename_of_child = '') AND (deleted IS NULL OR deleted = 0)");
    $no_middle_row = $no_middle_result->fetch_assoc();
    echo "<p><strong>Records without Middle Names:</strong> {$no_middle_row['total']}</p>";
    
    // Blocked attempts
    $blocked_result = $conn->query("SELECT COUNT(*) as total FROM duplicate_block_log");
    if ($blocked_result) {
        $blocked_row = $blocked_result->fetch_assoc();
        echo "<p><strong>Total Blocked Attempts:</strong> {$blocked_row['total']}</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error retrieving statistics: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🎯 Key Enhancements</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;'>";
echo "<h4>✅ Enhanced Duplicate Detection Features:</h4>";
echo "<ul>";
echo "<li><strong>Full Name Matching:</strong> First Name + Middle Name + Last Name + Date of Birth</li>";
echo "<li><strong>Partial Name Detection:</strong> Same first/last name with different middle name</li>";
echo "<li><strong>SOUNDEX Matching:</strong> Detects similar sounding names (typos)</li>";
echo "<li><strong>Twins Detection:</strong> Same mother + same birth date + different names</li>";
echo "<li><strong>Missing Middle Name Alerts:</strong> Warns when middle name is missing</li>";
echo "<li><strong>Phone Number Conflicts:</strong> Prevents duplicate phone numbers</li>";
echo "<li><strong>Household Detection:</strong> Identifies family members</li>";
echo "<li><strong>Complete Audit Trail:</strong> Logs all blocked attempts with full names</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Tools & Links</h3>";
echo "<p>";
echo "<a href='nip.php' style='background: #2196f3; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 Registration Form</a>";
echo "<a href='realtime_duplicate_check.php' style='background: #ff9800; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>⚡ Real-time Check</a>";
echo "<a href='duplicate_checker.php' style='background: #4caf50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔍 Duplicate Manager</a>";
echo "<a href='duplicate_prevention_demo.php' style='background: #9c27b0; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🎮 System Demo</a>";
echo "</p>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

table {
    border-collapse: collapse;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e9ecef;
}

h2, h3, h4, h5 {
    color: #343a40;
}

a {
    display: inline-block;
    margin: 5px;
    transition: all 0.3s;
}

a:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

hr {
    border: none;
    border-top: 2px solid #dee2e6;
    margin: 30px 0;
}

ul, ol {
    line-height: 1.6;
}
</style>
