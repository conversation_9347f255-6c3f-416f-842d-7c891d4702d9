<?php
include 'db_connect.php'; // Ensure this file connects to your database

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id = intval($_POST['id']); // Ensure ID is an integer
    $deleted = 1; // Soft delete flag

    // Prepare the SQL statement with a WHERE clause
    $stmt = $conn->prepare("UPDATE nip_table SET deleted = ? WHERE id = ?");
    $stmt->bind_param("ii", $deleted, $id); // Corrected binding

    if ($stmt->execute()) {
        echo "<script>alert('Record moved to Trash.'); window.location.href='records.php';</script>";
    } else {
        echo "<script>alert('Error updating record: " . $conn->error . "'); window.history.back();</script>";
    }

    $stmt->close();
    $conn->close();
}
?>
