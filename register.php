<?php include 'db.php'; ?>

<!DOCTYPE html>
<html>
<head>
    <title>Health Facility Registration</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <?php include 'style.php'; ?>
    <style>
        
        body{
            font-family: 'Roboto', sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .form-card {
            width: 90%;
            max-width: 800px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .input-field label {
            color: #757575;
            font-size: 1rem;
        }
        .input-field input[type=text]:focus + label,
        .input-field input[type=password]:focus + label,
        .input-field input[type=email]:focus + label,
        .input-field input[type=number]:focus + label,
        .input-field input[type=date]:focus + label {
            color: #26a69a;
        }
        .input-field input[type=text]:focus,
        .input-field input[type=password]:focus,
        .input-field input[type=email]:focus,
        .input-field input[type=number]:focus,
        .input-field input[type=date]:focus {
            border-bottom: 2px solid #26a69a;
            box-shadow: 0 1px 0 0 #26a69a;
        }
       
        select.browser-default {
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ccc;
            border-radius: 4px;
            height: 3rem;
            line-height: 3rem;
            padding: 0 0.5rem;
        }
        select:focus {
            outline: none;
            border-color: #26a69a;
        }
        .terms-agreement {
            padding-left: 0;
        }
        .terms-agreement label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        .terms-agreement input[type="checkbox"] {
            opacity: 0;
            position: absolute;
        }
        .terms-agreement label span {
            position: relative;
            padding-left: 28px;
            font-size: 1rem;
            color: #757575;
        }
        .terms-agreement label span:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 18px;
            height: 18px;
            border: 2px solid #26a69a;
            border-radius: 3px;
            background-color: transparent;
            transition: all 0.3s ease;
        }
        .terms-agreement input[type="checkbox"]:checked + label span:before {
            background-color: #26a69a;
            border-color: #26a69a;
        }
        .terms-agreement input[type="checkbox"]:checked + label span:after {
            content: '\f00c';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            left: 3px;
            transform: translateY(-50%);
            font-size: 12px;
            color: white;
        }
        .card-title {
            font-size: 2.2rem;
            font-weight: 300;
            color: #26a69a;
        }
        .card .card-content p {
            margin: 0;
        }
        .card .card-action a:not(.btn):not(.btn-large):not(.btn-small):not(.btn-floating) {
            color: #26a69a;
        }
        .card .card-action a:not(.btn):not(.btn-large):not(.btn-small):not(.btn-floating):hover {
            background-color: rgba(38, 166, 154, 0.2);
        }
        .register-button {
            background-color: #26a69a;
            color: white;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .register-button:hover {
            background-color: #2bbbad;
        }
        .section-title {
            color: #26a69a;
            font-weight: 500;
            margin-top: 2rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        /* Responsive layout */
        @media (min-width: 768px) {
            .form-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
            }
        }
    </style>
</head>
<body class="  ">
 
<div class="" style="width:100%;  ">
  <br>
    
    <div class="  ">
        <div class="row ">
            <div class="col s12 m8 offset-m2  ">
                    
                    <form action="" method="post" class="col z-depth-1 white "  >
                        <h5 class="  darken-3    " style=" padding-left: 0.5em;padding-bottom: 0.7em;font-size:20.1px;font-weight:500;">Register Form</h5>
    <div class="input-field col m12 s12 offset-m0" >
        
        
        <label>Username:</label>
        <input type="text" name="username" placeholder="" required>
        
</div>
<div class="input-field col col m12 s12 offset-m0">
 
        <label>Password:</label>
        <input type="password" name="password" placeholder="" required>
        </div>
        <div class=" input-field col m12 s12 offset-m0" style="   ">
        <h5 class="blue-grey lighten-5    black-text" style="padding-top:10px;padding-bottom:10px;padding-left:10px;  font-size:14.3px; font-weight: 500; ">Personal Information</h5>
</div>
        <div class="input-field col   m4 s12 offset-m0">
   
 
        <label>Birthdate:</label>
        <input type="date" name="birthdate" id="birthdate" placeholder="" required>
        </div>
<div class="input-field col   m4 s12 offset-m0">
 
        <label>Age:</label>
        <input type="number" name="age" id="age" placeholder="" readonly>
        </div>
<div class="input-field col m4 s12 offset-m0">
 
        <label>Email:</label>
        <input type="email" name="email" placeholder="" required>
        </div>
<div class="input-field col m7 s12 offset-m0">
 
      
        <select class="browser" name="health_center" placeholder="" required>
            <option value=""></option>
                <?php
                $sql = "SELECT * FROM facility_list ";
                $result = $conn->query($sql);
                    while ($row = $result->fetch_assoc()) {
                        echo '<option value="'.$row["facility_name"].'">'.$row["facility_name"].'</option>';
                    } 
                ?>  
        </select>
        <label for="hc">Facility:</label>
        </div>
<div class="input-field col m5 s12">
 
        <label>Address of Facility:</label>
        <input type="text" name="AddressofFacility" placeholder="" required>
        </div>
<div class="input-field col s12">
 
        <label>Full Name:</label>
        <input type="text" name="fullname" placeholder="" required>
        </div>
<div class="input-field col s12">
 
        <label>Item:</label>
        <input type="text" name="item" placeholder="" required>
        
        </div>
        <div class="input-field col s12">
 
        <label>Mobile Number:</label>
        <input type="number" name="mobile_number" placeholder=""  >
        </div>
 
<p style="padding-left:0.9em;"> 
<label>
    <input  onchange="check_agree()" id="chkbx"  value="" type="checkbox" class="filled-in"  />
    <span style="padding-top:0.9em;padding-left:2.4em;">I Agree to the Terms and Agreement</span>
                </label> 
              </p>

 
<div class="input-field col  m12 s12">
    
        <input  class="btn  blue accent-4 m2 s12 col " id="btn" style="font-weight:500; font-size:13px;" type="submit" name="submit" value="Register" disabled>
</div>
    </form>
<script>
    function check_agree() {
        var chkbx = document.getElementById('chkbx');
        if(chkbx.checked) {

            document.getElementById('btn').disabled = false;
        }
        else {
            document.getElementById('btn').disabled = true;

        }
    }
</script>
    <?php
include 'db.php';
 
date_default_timezone_set('Asia/Manila');


if (isset($_POST['submit'])) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    $birthdate = $_POST['birthdate'];
    $age = $_POST['age'];
    $email = $_POST['email'];
    $health_center = $_POST['health_center'];
    $AddressofFacility = $_POST['AddressofFacility'];
    $fullname = $_POST['fullname'];
    $item = $_POST['item'];
    $mobile_number = $_POST['mobile_number'];
     
$date_created = date('Y-m-d'); 

 

    
    $check_mobile_sql = "SELECT * FROM health_facility WHERE mobile_number = '$mobile_number'";
    $result_mobile = $conn->query($check_mobile_sql);

    if ($result_mobile->num_rows > 0) {
        echo '<div class="input-field col s12"><h5 class="red lighten-5 red-text col s12 m12 offset-m0" style="padding:0.9em;">This mobile number already exists</span></div>';
    }
    else if($age <= 18) {
        echo '<div class="input-field col s12"><h5 class="red lighten-5 red-text col s12 m12 offset-m0" style="padding:0.9em;">Too Young</div>';
    }
    else {
        
        $check_username_sql = "SELECT * FROM health_facility WHERE username = '$username'";
        $result_username = $conn->query($check_username_sql);

        if ($result_username->num_rows > 0) {
            echo '<div class="input-field col s12"><span class="red lighten-5 red-text col s12 m12 offset-m0" style="padding:0.9em;">Username already exists</span></div>';
        } else {
            
            $sql = "INSERT INTO health_facility (username, password, birthdate, age, email, health_center, AddressofFacility, fullname, item, mobile_number,date_created) 
                    VALUES ('$username', '$password', '$birthdate', '$age', '$email', '$health_center', '$AddressofFacility', '$fullname', '$item', '$mobile_number','$date_created')";

            if ($conn->query($sql) === TRUE) {
                echo '<div class="input-field col s12"><span class="light-green lighten-5 green-text col s12 m12 offset-m0" style="padding:0.9em;">Registration Successfully Submitted</span></div>';
            } else {
                echo "Error: " . $sql . "<br>" . $conn->error;
            }
        }
    }
}
?>


</div>
</div>
 
</body>
</html>

<script>
    
    (function() {
        if (window.history && window.history.replaceState) {
            window.addEventListener('load', function() {
               
                window.history.replaceState(null, null, window.location.href);
            });
        }
    })();
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var elems = document.querySelectorAll('select');
        M.FormSelect.init(elems);
    });
  </script>

<script>
        // Enhanced checkbox functionality
        function check_agree() {
            const checkbox = document.getElementById('chkbx');
            const button = document.getElementById('btn');

            if (checkbox.checked) {
                button.disabled = false;
                button.classList.remove('disabled');
                // Add a subtle animation
                button.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 150);
            } else {
                button.disabled = true;
                button.classList.add('disabled');
            }
        }

        // Initialize Materialize components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize select dropdowns
            var selects = document.querySelectorAll('select');
            M.FormSelect.init(selects);

            // Initialize tooltips
            var tooltips = document.querySelectorAll('.tooltipped');
            M.Tooltip.init(tooltips);

            // Add form submission with toast validation
            const form = document.querySelector('form');
            const submitButton = document.getElementById('btn');

            form.addEventListener('submit', function(e) {
                // Validate form fields and show toast messages
                let isValid = true;
                
                // Check username
                const username = document.querySelector('input[name="username"]').value.trim();
                if (username === '') {
                    M.toast({html: 'Username is required', classes: 'red'});
                    isValid = false;
                } else if (username.length < 3) {
                    M.toast({html: 'Username must be at least 3 characters', classes: 'red'});
                    isValid = false;
                }
                
                // Check password
                const password = document.querySelector('input[name="password"]').value;
                if (password === '') {
                    M.toast({html: 'Password is required', classes: 'red'});
                    isValid = false;
                } else if (password.length < 6) {
                    M.toast({html: 'Password must be at least 6 characters', classes: 'red'});
                    isValid = false;
                }
                
                // Check birthdate
                const birthdate = document.querySelector('input[name="birthdate"]').value;
                if (birthdate === '') {
                    M.toast({html: 'Birthdate is required', classes: 'red'});
                    isValid = false;
                }
                
                // Check email
                const email = document.querySelector('input[name="email"]').value.trim();
                if (email === '') {
                    M.toast({html: 'Email is required', classes: 'red'});
                    isValid = false;
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    M.toast({html: 'Please enter a valid email address', classes: 'red'});
                    isValid = false;
                }
                
                // Check health center
                const healthCenter = document.querySelector('select[name="health_center"]').value;
                if (healthCenter === '') {
                    M.toast({html: 'Please select a health facility', classes: 'red'});
                    isValid = false;
                }
                
                // Check facility address
                const facilityAddress = document.querySelector('input[name="AddressofFacility"]').value.trim();
                if (facilityAddress === '') {
                    M.toast({html: 'Facility address is required', classes: 'red'});
                    isValid = false;
                }
                
                // Check full name
                const fullname = document.querySelector('input[name="fullname"]').value.trim();
                if (fullname === '') {
                    M.toast({html: 'Full name is required', classes: 'red'});
                    isValid = false;
                }
                
                // Check item
                const item = document.querySelector('input[name="item"]').value.trim();
                if (item === '') {
                    M.toast({html: 'Item/Service is required', classes: 'red'});
                    isValid = false;
                }
                
                // Check mobile number
                const mobileNumber = document.querySelector('input[name="mobile_number"]').value.trim();
                if (mobileNumber === '') {
                    M.toast({html: 'Mobile number is required', classes: 'red'});
                    isValid = false;
                } else if (!/^[0-9]{10,15}$/.test(mobileNumber)) {
                    M.toast({html: 'Mobile number must be 10-15 digits', classes: 'red'});
                    isValid = false;
                }
                
                // Check terms agreement
                const checkbox = document.getElementById('chkbx');
                if (!checkbox.checked) {
                    M.toast({html: 'Please agree to the terms and conditions', classes: 'red'});
                    isValid = false;
                }
                
                if (!isValid) {
                    e.preventDefault();
                    return false;
                }
                
                if (!submitButton.disabled) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="material-icons left">hourglass_empty</i>Processing...';

                    // Re-enable after 3 seconds in case of errors
                    setTimeout(() => {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="material-icons left">send</i>Register Now';
                    }, 3000);
                }
            });
        });

        // Age calculation from birthdate
        document.getElementById('birthdate').addEventListener('change', function() {
            const birthDate = new Date(this.value);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDifference = today.getMonth() - birthDate.getMonth();

            if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            document.getElementById('age').value = age;

            // Update label if age is calculated
            if (age > 0) {
                const ageLabel = document.querySelector('label[for="age"]');
                ageLabel.classList.add('active');
            }
        });

        // Prevent form resubmission on page refresh
        (function() {
            if (window.history && window.history.replaceState) {
                window.addEventListener('load', function() {
                    window.history.replaceState(null, null, window.location.href);
                });
            }
        })();
    </script>



 