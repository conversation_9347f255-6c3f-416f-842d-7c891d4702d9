# Remember Me Functionality Setup Instructions

## 🚀 **Quick Setup**

### Step 1: Run Database Migration
1. Open your browser and navigate to: `http://your-domain/add_remember_token_migration.php`
2. The script will automatically add the `remember_token` column to your `health_facility` table
3. You should see a success message confirming the migration

### Step 2: Test the Functionality
1. Go to the login page: `http://your-domain/login.php`
2. Enter your credentials
3. Check the "Remember me for 30 days" checkbox
4. Click Login
5. Close your browser completely
6. Reopen your browser and navigate to any protected page (like profile.php)
7. You should automatically be logged in without entering credentials

## 🔧 **What Was Added**

### Database Changes:
- Added `remember_token` column to `health_facility` table
- Added database index for performance optimization

### Login System:
- Added "Remember Me" checkbox to login form
- Secure token generation and storage
- Automatic session restoration from cookies

### Security Features:
- Token rotation (new token generated on each use)
- Secure HTTP-only cookies
- Automatic token cleanup on logout
- 30-day expiration for remember tokens

### Files Modified:
- `profile.php` - Added change password modal and remember me authentication
- `login.php` - Added remember me checkbox and token generation
- `logout.php` - Added token cleanup on logout
- `index.php` - Added remember me authentication check
- `change_password.php` - New password change handler
- `auth_check.php` - Reusable authentication function
- `add_remember_token_migration.php` - Database migration script

## 🔒 **Security Features**

### Token Security:
- 64-character random tokens using `random_bytes()`
- Tokens are rotated on each use for maximum security
- HTTP-only cookies prevent JavaScript access
- Tokens are cleared from database on logout

### Session Management:
- Automatic session restoration from valid tokens
- Invalid tokens are immediately cleared
- 30-day expiration with automatic cleanup

## 🛠 **Troubleshooting**

### If Remember Me Doesn't Work:
1. Check if the migration ran successfully
2. Verify the `remember_token` column exists in `health_facility` table
3. Check browser cookies are enabled
4. Ensure your server supports secure cookies

### Database Issues:
```sql
-- Manually add the column if migration fails:
ALTER TABLE health_facility ADD COLUMN remember_token VARCHAR(64) NULL DEFAULT NULL;
CREATE INDEX idx_remember_token ON health_facility(remember_token);
```

### Clear All Remember Tokens:
```sql
-- If you need to reset all remember tokens:
UPDATE health_facility SET remember_token = NULL;
```

## 📱 **User Experience**

### For Users:
1. **Login**: Check "Remember me for 30 days" when logging in
2. **Automatic Login**: Browser will remember you for 30 days
3. **Security**: Tokens automatically refresh for security
4. **Logout**: Properly clears all remember tokens

### For Administrators:
1. **Password Changes**: Users can change passwords via profile page
2. **Security Logging**: All password changes are logged
3. **Token Management**: Tokens are automatically managed
4. **Session Control**: Full control over user sessions

## 🔄 **Change Password Feature**

### How to Use:
1. Go to Profile page
2. Click "Change Password" button
3. Enter current password
4. Enter new password (minimum 6 characters)
5. Confirm new password
6. Click "Change Password"

### Security Features:
- Current password verification required
- Minimum password length validation
- Password confirmation matching
- Prevents reusing current password
- AJAX submission (no page refresh)
- Success/error feedback

## 🗑️ **Cleanup**

After successful setup, you can delete these files:
- `add_remember_token_migration.php`
- `REMEMBER_ME_SETUP_INSTRUCTIONS.md`

## ✅ **Verification**

To verify everything is working:
1. Login with "Remember me" checked
2. Close browser completely
3. Reopen and visit profile.php
4. Should be automatically logged in
5. Try changing password
6. Logout and verify tokens are cleared

---

**Note**: This implementation uses secure practices including token rotation, HTTP-only cookies, and proper session management. The remember me functionality will persist for 30 days or until the user explicitly logs out.
