<?php
session_start();
header('Content-Type: application/json');

// Database connection
$conn = mysqli_connect('localhost', 'root', '', 'national_immunization_program');
if (!$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// Get child ID
$child_id = intval($_GET['id'] ?? 0);

if ($child_id <= 0) {
    echo json_encode(['error' => 'Invalid child ID']);
    exit;
}

// Get child details with all antigen information
$sql = "SELECT * FROM nip_table WHERE id = ? AND (deleted IS NULL OR deleted = 0)";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $child_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    echo json_encode(['error' => 'Child not found']);
    exit;
}

$child = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

// Calculate immunization completion percentage
$antigens = [
    'BCG', 'PENTAHIB1', 'PENTAHIB2', 'PENTAHIB3',
    'OPV1', 'OPV2', 'OPV3', 'IPV1', 'IPV2',
    'PCV1', 'PCV2', 'PCV3', 'HEPAatBirth', 'HEPAB1'
];

$given_count = 0;
$total_count = count($antigens);

foreach ($antigens as $antigen) {
    if ($child[$antigen] === 'YES' || !empty($child[$antigen])) {
        $given_count++;
    }
}

$completion_percentage = round(($given_count / $total_count) * 100);

// Add calculated fields
$child['completion_percentage'] = $completion_percentage;
$child['antigens_given'] = $given_count;
$child['total_antigens'] = $total_count;

// Format dates for display
$date_fields = [
    'DateOfBirth', 'DateBCGwasgiven', 'DateofConsultationBCG',
    'DatePenta1wasgiven', 'DateofConsultationPENTAHIB1',
    'DatePentahib2wasgiven', 'DateofConsultationPENTAHIB2',
    'DatePentahib3wasgiven', 'DateofConsultationPENTAHIB3',
    'DateOPV1wasgiven', 'DateofConsultationOPV1v',
    'DateOPV2wasgiven', 'DateofConsultationOPV2',
    'dateOPV3wasgiven', 'DateofConsultationOPV3',
    'dateIPV1wasgiven', 'DateofConsultationIPV1',
    'dateIPV2wasgiven', 'DateofConsultationIPV2',
    'datePCV1wasgiven', 'DateofConsultationPCV1',
    'datePCV2wasgiven', 'DateofConsultationPCV2',
    'datePCV3wasgiven', 'DateofConsultationPCV3',
    'DateHEPAatBirthwasgiven', 'DateofConsultationHEPAatBirth',
    'dateHEPA1wasgiven', 'DateofConsultationHEPAB1',
    'DateAMV1WASGIVEN', 'DateofConsultationAMV1',
    'dateMMRWASGIVEN', 'DateofConsultationMMR',
    'dateFICWASGIVEN', 'DateofConsultationFIC',
    'DATEWHENCHILDWASFULLYIMMUNIZEDORCOMPLETELYIMMUNIZED'
];

foreach ($date_fields as $field) {
    if (!empty($child[$field]) && $child[$field] !== '0000-00-00') {
        $child[$field] = date('Y-m-d', strtotime($child[$field]));
    } else {
        $child[$field] = '';
    }
}

mysqli_close($conn);

echo json_encode($child);
?>
