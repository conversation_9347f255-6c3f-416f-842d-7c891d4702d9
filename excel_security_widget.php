<?php
// Excel Security Widget - Include this in your dashboard
session_start();
include 'db.php';

// Check if user is logged in
if (!isset($_SESSION['health_center'])) {
    return;
}

// Function to verify user's health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    $stmt = $conn->prepare("SELECT health_center FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

$current_health_center = $_SESSION['health_center'];
$access_granted = verifyHealthCenterAccess($conn, $current_health_center);

// Get quick statistics
$record_count = 0;
$today_count = 0;

try {
    if ($access_granted) {
        // Get total records
        $total_stmt = $conn->prepare("SELECT COUNT(*) as count FROM nip_table WHERE deleted = 0 AND Barangay = ?");
        $total_stmt->bind_param("s", $current_health_center);
        $total_stmt->execute();
        $total_result = $total_stmt->get_result();
        $record_count = $total_result->fetch_assoc()['count'];
        $total_stmt->close();
        
        // Get today's records
        $today = date('Y-m-d');
        $today_stmt = $conn->prepare("SELECT COUNT(*) as count FROM nip_table WHERE deleted = 0 AND Barangay = ? AND DATE(DateOfRegistration) = ?");
        $today_stmt->bind_param("ss", $current_health_center, $today);
        $today_stmt->execute();
        $today_result = $today_stmt->get_result();
        $today_count = $today_result->fetch_assoc()['count'];
        $today_stmt->close();
    }
} catch (Exception $e) {
    // Handle error silently for widget
}
?>

<style>
.excel-security-widget {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    color: white;
    margin: 15px 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.excel-security-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.widget-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.widget-icon {
    font-size: 2.5rem;
    margin-right: 15px;
    opacity: 0.9;
}

.widget-title {
    font-size: 1.4rem;
    font-weight: 500;
    margin: 0;
}

.security-status {
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.status-icon {
    margin-right: 10px;
    font-size: 1.2rem;
}

.widget-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 15px 0;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.widget-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.widget-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 8px;
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.widget-btn:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.widget-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.widget-btn.disabled:hover {
    transform: none;
    background: rgba(255,255,255,0.2);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
</style>

<div class="excel-security-widget">
    <div class="widget-header">
        <i class="material-icons widget-icon">security</i>
        <div>
            <h5 class="widget-title">Excel Security Status</h5>
            <small style="opacity: 0.8;"><?php echo htmlspecialchars($current_health_center); ?></small>
        </div>
    </div>
    
    <div class="security-status">
        <?php if ($access_granted): ?>
            <i class="material-icons status-icon" style="color: #4caf50;">verified_user</i>
            <div>
                <strong>Access Granted</strong><br>
                <small>Excel downloads are available for your health center</small>
            </div>
        <?php else: ?>
            <i class="material-icons status-icon pulse-animation" style="color: #ff5722;">warning</i>
            <div>
                <strong>Access Blocked</strong><br>
                <small>Excel download blocked - Health center not verified</small>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if ($access_granted): ?>
        <div class="widget-stats">
            <div class="stat-item">
                <span class="stat-number"><?php echo number_format($record_count); ?></span>
                <span class="stat-label">Total Records</span>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?php echo number_format($today_count); ?></span>
                <span class="stat-label">Today's Records</span>
            </div>
        </div>
        
        <div class="widget-actions">
            <a href="sheetjs.php" class="widget-btn">
                <i class="material-icons left" style="font-size: 1rem;">file_download</i>
                Excel Export
            </a>
            <a href="test_excel_security.php" class="widget-btn">
                <i class="material-icons left" style="font-size: 1rem;">security</i>
                Security Test
            </a>
        </div>
    <?php else: ?>
        <div class="widget-actions">
            <button class="widget-btn disabled" disabled>
                <i class="material-icons left" style="font-size: 1rem;">block</i>
                Excel Blocked
            </button>
            <a href="test_excel_security.php" class="widget-btn">
                <i class="material-icons left" style="font-size: 1rem;">help</i>
                Get Help
            </a>
        </div>
    <?php endif; ?>
    
    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid rgba(255,255,255,0.2);">
        <small style="opacity: 0.7;">
            <i class="material-icons" style="font-size: 1rem; vertical-align: middle;">info</i>
            <?php if ($access_granted): ?>
                Your health center is verified. You can download Excel data securely.
            <?php else: ?>
                Contact administrator to verify your health center registration.
            <?php endif; ?>
        </small>
    </div>
</div>

<script>
// Auto-refresh widget every 5 minutes
setInterval(function() {
    // You can implement auto-refresh here if needed
    console.log('Excel security widget - status check');
}, 300000);

// Add click tracking
document.querySelectorAll('.widget-btn:not(.disabled)').forEach(btn => {
    btn.addEventListener('click', function(e) {
        // Track button clicks for analytics
        console.log('Excel widget button clicked:', this.textContent.trim());
    });
});
</script>
