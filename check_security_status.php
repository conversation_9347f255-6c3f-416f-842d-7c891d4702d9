<?php
session_start();
include 'db.php';
header('Content-Type: application/json');

// Function to verify user's health center access
function verifyHealthCenterAccess($conn, $session_health_center) {
    if (empty($session_health_center)) {
        return false;
    }
    
    $stmt = $conn->prepare("SELECT health_center, approved FROM health_facility WHERE health_center = ?");
    $stmt->bind_param("s", $session_health_center);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $stmt->close();
        return $row['approved'] == 1; // Only return true if approved
    } else {
        $stmt->close();
        return false;
    }
}

// Function to check session validity
function checkSessionValidity() {
    // Check if session exists
    if (!isset($_SESSION['health_center'])) {
        return [
            'valid' => false,
            'reason' => 'No active session'
        ];
    }
    
    // Check session timeout (optional - implement if needed)
    $session_timeout = 8 * 60 * 60; // 8 hours
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $session_timeout) {
        return [
            'valid' => false,
            'reason' => 'Session expired'
        ];
    }
    
    return [
        'valid' => true,
        'reason' => 'Session active'
    ];
}

// Function to check database connectivity
function checkDatabaseConnection($conn) {
    try {
        $result = $conn->query("SELECT 1");
        return $result !== false;
    } catch (Exception $e) {
        return false;
    }
}

// Function to check user permissions
function checkUserPermissions($conn, $health_center) {
    try {
        $stmt = $conn->prepare("SELECT approved, fullname FROM health_facility WHERE health_center = ?");
        $stmt->bind_param("s", $health_center);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $stmt->close();
            return [
                'approved' => $row['approved'] == 1,
                'user_name' => $row['fullname']
            ];
        } else {
            $stmt->close();
            return [
                'approved' => false,
                'user_name' => null
            ];
        }
    } catch (Exception $e) {
        return [
            'approved' => false,
            'user_name' => null
        ];
    }
}

try {
    // Perform comprehensive security checks
    $security_checks = [];
    
    // 1. Session validity check
    $session_check = checkSessionValidity();
    $security_checks['session'] = $session_check;
    
    // 2. Database connectivity check
    $db_check = checkDatabaseConnection($conn);
    $security_checks['database'] = [
        'connected' => $db_check,
        'reason' => $db_check ? 'Database connected' : 'Database connection failed'
    ];
    
    // 3. Health center verification
    if ($session_check['valid'] && isset($_SESSION['health_center'])) {
        $health_center_valid = verifyHealthCenterAccess($conn, $_SESSION['health_center']);
        $security_checks['health_center'] = [
            'valid' => $health_center_valid,
            'name' => $_SESSION['health_center'],
            'reason' => $health_center_valid ? 'Health center verified' : 'Health center not found or not approved'
        ];
        
        // 4. User permissions check
        $permissions = checkUserPermissions($conn, $_SESSION['health_center']);
        $security_checks['permissions'] = [
            'approved' => $permissions['approved'],
            'user_name' => $permissions['user_name'],
            'reason' => $permissions['approved'] ? 'User approved' : 'User not approved'
        ];
    } else {
        $security_checks['health_center'] = [
            'valid' => false,
            'name' => null,
            'reason' => 'No health center in session'
        ];
        $security_checks['permissions'] = [
            'approved' => false,
            'user_name' => null,
            'reason' => 'No session to check permissions'
        ];
    }
    
    // 5. Excel download eligibility
    $excel_eligible = $session_check['valid'] && 
                     $db_check && 
                     $security_checks['health_center']['valid'] && 
                     $security_checks['permissions']['approved'];
    
    $security_checks['excel_download'] = [
        'eligible' => $excel_eligible,
        'reason' => $excel_eligible ? 'Excel download allowed' : 'Excel download blocked - security requirements not met'
    ];
    
    // Determine overall security status
    $all_checks_passed = $session_check['valid'] && 
                        $db_check && 
                        $security_checks['health_center']['valid'] && 
                        $security_checks['permissions']['approved'];
    
    // Update last activity
    if ($session_check['valid']) {
        $_SESSION['last_activity'] = time();
    }
    
    // Prepare response
    $response = [
        'success' => $all_checks_passed,
        'message' => $all_checks_passed ? 'All security checks passed' : 'One or more security checks failed',
        'timestamp' => date('Y-m-d H:i:s'),
        'checks' => $security_checks,
        'summary' => [
            'session_valid' => $session_check['valid'],
            'database_connected' => $db_check,
            'health_center_verified' => $security_checks['health_center']['valid'],
            'user_approved' => $security_checks['permissions']['approved'],
            'excel_download_allowed' => $excel_eligible
        ]
    ];
    
    // Add warning messages for failed checks
    if (!$all_checks_passed) {
        $warnings = [];
        if (!$session_check['valid']) {
            $warnings[] = $session_check['reason'];
        }
        if (!$db_check) {
            $warnings[] = 'Database connection issue';
        }
        if (!$security_checks['health_center']['valid']) {
            $warnings[] = $security_checks['health_center']['reason'];
        }
        if (!$security_checks['permissions']['approved']) {
            $warnings[] = $security_checks['permissions']['reason'];
        }
        
        $response['warnings'] = $warnings;
        $response['message'] = 'Security issues detected: ' . implode(', ', $warnings);
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Security check failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'error' => true
    ]);
    error_log("Security Check Error: " . $e->getMessage());
}
?>
