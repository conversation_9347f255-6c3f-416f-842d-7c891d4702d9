<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Earliest Consultation Date - Implementation Demo</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
    <style>
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
            font-family: monospace;
            font-size: 13px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .usage-example {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4caf50;
        }
        
        .output-example {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-section">
            <h3 class="center-align">
                <i class="material-icons left blue-text">event</i>
                Earliest Consultation Date - Complete Implementation
            </h3>
            <p class="center-align">Print only the earliest date from all DateofConsultation fields</p>
        </div>

        <div class="demo-section">
            <h4>✅ Implementation Complete</h4>
            <p>The earliest consultation date functionality has been successfully implemented with multiple usage methods:</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h6><i class="material-icons left green-text">functions</i>Simple Print Function</h6>
                    <p>Direct printing of earliest date with <code>printEarliestDate($data)</code></p>
                </div>
                
                <div class="feature-card">
                    <h6><i class="material-icons left blue-text">assignment_returned</i>Return Function</h6>
                    <p>Get date value with <code>getEarliestDate($data)</code> for further processing</p>
                </div>
                
                <div class="feature-card">
                    <h6><i class="material-icons left purple-text">text_format</i>Formatted Output</h6>
                    <p>Custom date formatting with <code>printFormattedEarliestDate($data, $format)</code></p>
                </div>
                
                <div class="feature-card">
                    <h6><i class="material-icons left orange-text">storage</i>Database Query</h6>
                    <p>Direct SQL query with <code>getEarliestDateFromDB($id, $conn)</code></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📋 Usage Examples</h4>
            
            <h5>1. Simple Print (Most Common)</h5>
            <div class="usage-example">
                <h6>Code:</h6>
                <div class="code-block">
&lt;?php
include 'print_earliest_date.php';

// In your form or display page
echo "First consultation: ";
printEarliestDate($row);
?&gt;
                </div>
                <h6>Output:</h6>
                <div class="output-example">
                    First consultation: <strong>2023-02-15</strong>
                </div>
            </div>
            
            <h5>2. Get Date for Processing</h5>
            <div class="usage-example">
                <h6>Code:</h6>
                <div class="code-block">
&lt;?php
$earliest = getEarliestDate($row);
if ($earliest) {
    $days_since_birth = (strtotime($earliest) - strtotime($row['DateOfBirth'])) / (60*60*24);
    echo "First consultation was " . $days_since_birth . " days after birth";
} else {
    echo "No consultations recorded";
}
?&gt;
                </div>
                <h6>Output:</h6>
                <div class="output-example">
                    First consultation was <strong>31 days after birth</strong>
                </div>
            </div>
            
            <h5>3. Formatted Display</h5>
            <div class="usage-example">
                <h6>Code:</h6>
                <div class="code-block">
&lt;?php
echo "Consultation date: ";
printFormattedEarliestDate($row, 'F j, Y'); // January 15, 2023

echo "&lt;br&gt;Short format: ";
printFormattedEarliestDate($row, 'm/d/Y'); // 01/15/2023
?&gt;
                </div>
                <h6>Output:</h6>
                <div class="output-example">
                    Consultation date: <strong>February 15, 2023</strong><br>
                    Short format: <strong>02/15/2023</strong>
                </div>
            </div>
            
            <h5>4. In Table Display (like filter_records.php)</h5>
            <div class="usage-example">
                <h6>Code:</h6>
                <div class="code-block">
&lt;?php
foreach ($results as $row) {
    $earliest_date = getEarliestDate($row);
    $earliest_display = $earliest_date ? date('M j, Y', strtotime($earliest_date)) : 'No consultation';
    
    echo '&lt;td class="' . ($earliest_date ? 'green-text' : 'orange-text') . '"&gt;';
    echo htmlspecialchars($earliest_display);
    echo '&lt;/td&gt;';
}
?&gt;
                </div>
                <h6>Output:</h6>
                <div class="output-example">
                    <span style="color: #4caf50; font-weight: bold;">Feb 15, 2023</span> (if date exists)<br>
                    <span style="color: #ff9800; font-weight: bold;">No consultation</span> (if no dates)
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🗄️ Database Integration</h4>
            
            <h5>Method 1: PHP Processing (Recommended)</h5>
            <div class="usage-example">
                <h6>Advantages:</h6>
                <ul>
                    <li>More flexible date validation</li>
                    <li>Better error handling</li>
                    <li>Easier to customize formatting</li>
                    <li>Works with existing queries</li>
                </ul>
                <div class="code-block">
&lt;?php
// Your existing query
$sql = "SELECT *, DateofConsultationBCG, DateofConsultationPENTAHIB1, ... FROM nip_table";
$result = mysqli_query($conn, $sql);

while ($row = mysqli_fetch_assoc($result)) {
    echo "Child: " . $row['NameOfChild'];
    echo " | First consultation: ";
    printEarliestDate($row);
}
?&gt;
                </div>
            </div>
            
            <h5>Method 2: Direct SQL Query</h5>
            <div class="usage-example">
                <h6>Advantages:</h6>
                <ul>
                    <li>Faster for large datasets</li>
                    <li>Less PHP processing</li>
                    <li>Can be used in WHERE clauses</li>
                    <li>Good for reports</li>
                </ul>
                <div class="code-block">
&lt;?php
$earliest_date = getEarliestDateFromDB($child_id, $conn);
echo "Earliest consultation: " . ($earliest_date ?: "No date recorded");
?&gt;
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📊 Consultation Date Fields Included</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Primary Antigens</span>
                            <ul>
                                <li>DateofConsultationBCG</li>
                                <li>DateofConsultationPENTAHIB1</li>
                                <li>DateofConsultationPENTAHIB2</li>
                                <li>DateofConsultationPENTAHIB3</li>
                                <li>DateofConsultationOPV1v</li>
                                <li>DateofConsultationOPV2</li>
                                <li>DateofConsultationOPV3</li>
                                <li>DateofConsultationIPV1</li>
                                <li>DateofConsultationIPV2</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card green lighten-4">
                        <div class="card-content">
                            <span class="card-title">Additional Consultations</span>
                            <ul>
                                <li>DateofConsultationPCV1/2/3</li>
                                <li>DateofConsultationHEPAatBirth</li>
                                <li>DateofConsultationHEPAB1/2/3/4</li>
                                <li>DateofConsultationAMV1/2</li>
                                <li>DateofConsultationMMR</li>
                                <li>DateofConsultationFIC/CIC</li>
                                <li>DATEOFCONSULTATIONEXCLUSIVEBF</li>
                                <li>DATEOFCONSULTTT1/2/3/4/5</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🔧 Implementation in Your Files</h4>
            
            <div class="row">
                <div class="col s12 m6">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">✅ Already Updated</span>
                            <ul>
                                <li><strong>filter_records.php</strong> - Shows earliest date in table</li>
                                <li><strong>print_earliest_date.php</strong> - Main function file</li>
                                <li><strong>earliest_consultation_date.php</strong> - Demo page</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m6">
                    <div class="card purple lighten-4">
                        <div class="card-content">
                            <span class="card-title">📝 Easy to Add</span>
                            <p>Add to any file with 3 simple steps:</p>
                            <ol>
                                <li>Include the function file</li>
                                <li>Call the function with data</li>
                                <li>Display the result</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>🧪 Test Scenarios</h4>
            
            <div class="row">
                <div class="col s12 m4">
                    <div class="card red lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 1: Multiple Dates</span>
                            <p><strong>Data:</strong></p>
                            <ul>
                                <li>BCG: 2023-02-15</li>
                                <li>PENTA1: 2023-03-10</li>
                                <li>OPV1: 2023-02-10</li>
                            </ul>
                            <p><strong>Result:</strong> 2023-02-10</p>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m4">
                    <div class="card blue lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 2: Single Date</span>
                            <p><strong>Data:</strong></p>
                            <ul>
                                <li>BCG: 2023-01-20</li>
                                <li>Others: empty</li>
                            </ul>
                            <p><strong>Result:</strong> 2023-01-20</p>
                        </div>
                    </div>
                </div>
                
                <div class="col s12 m4">
                    <div class="card orange lighten-4">
                        <div class="card-content">
                            <span class="card-title">Test 3: No Dates</span>
                            <p><strong>Data:</strong></p>
                            <ul>
                                <li>All fields: empty</li>
                            </ul>
                            <p><strong>Result:</strong> "No consultation date recorded"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section center-align">
            <h4>🔗 Test the Implementation</h4>
            <p>The earliest consultation date functionality is ready to use!</p>
            
            <div class="row">
                <div class="col s12 m4">
                    <a href="earliest_consultation_date.php" class="btn large blue waves-effect">
                        <i class="material-icons left">event</i>Interactive Demo
                    </a>
                    <p><small>Test with sample data</small></p>
                </div>
                
                <div class="col s12 m4">
                    <a href="filter_records.php" class="btn large green waves-effect">
                        <i class="material-icons left">table_chart</i>See in Table
                    </a>
                    <p><small>View in records table</small></p>
                </div>
                
                <div class="col s12 m4">
                    <a href="update_antigen.php" class="btn large orange waves-effect">
                        <i class="material-icons left">edit</i>Update Records
                    </a>
                    <p><small>Edit consultation dates</small></p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>📋 Summary</h4>
            
            <div class="card green lighten-4">
                <div class="card-content">
                    <h6>✅ Earliest Consultation Date - Complete Implementation</h6>
                    <p>The system now provides comprehensive earliest consultation date functionality:</p>
                    <ul>
                        <li><strong>Simple Function:</strong> <code>printEarliestDate($data)</code> - prints only the earliest date</li>
                        <li><strong>Return Function:</strong> <code>getEarliestDate($data)</code> - returns date for processing</li>
                        <li><strong>Formatted Output:</strong> <code>printFormattedEarliestDate($data, $format)</code> - custom formatting</li>
                        <li><strong>Database Query:</strong> <code>getEarliestDateFromDB($id, $conn)</code> - direct SQL approach</li>
                        <li><strong>Validation:</strong> Handles empty dates, invalid formats, and null values</li>
                        <li><strong>Integration:</strong> Already working in filter_records.php table</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
