/* @preserve
 * Leaflet 1.9.2+main.d9100e0, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2025 Volody<PERSON><PERSON>, (c) 2010-2011 CloudMade
 */
var version$1="1.9.2+main.d9100e0b",pkg={version:version$1};let lastId=0;function stamp(t){return"_leaflet_id"in t||(t._leaflet_id=++lastId),t._leaflet_id}function throttle(e,i,o){let n,s;function a(){n=!1,s&&(t.apply(o,s),s=!1)}function t(...t){n?s=t:(e.apply(o,t),setTimeout(a,i),n=!0)}return t}function wrapNum(t,e,i){var o=e[1],e=e[0],n=o-e;return t===o&&i?t:((t-e)%n+n)%n+e}function falseFn(){return!1}function formatNum(t,e){return!1===e?t:(e=10**(void 0===e?6:e),Math.round(t*e)/e)}function splitWords(t){return t.trim().split(/\s+/)}function setOptions(t,e){for(var i in Object.hasOwn(t,"options")||(t.options=t.options?Object.create(t.options):{}),e)Object.hasOwn(e,i)&&(t.options[i]=e[i]);return t.options}let templateRe=/\{ *([\w_ -]+) *\}/g;function template(t,o){return t.replace(templateRe,(t,e)=>{let i=o[e];if(void 0===i)throw new Error("No value provided for variable "+t);return i="function"==typeof i?i(o):i})}let emptyImageUrl="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";var Util={__proto__:null,emptyImageUrl:emptyImageUrl,falseFn:falseFn,formatNum:formatNum,get lastId(){return lastId},setOptions:setOptions,splitWords:splitWords,stamp:stamp,template:template,throttle:throttle,wrapNum:wrapNum};class Class{static extend({statics:t,includes:e,...i}){var o=class extends this{},n=(Object.setPrototypeOf(o,this),this.prototype),s=o.prototype;if(t&&Object.assign(o,t),Array.isArray(e))for(var a of e)Object.assign(s,a);else e&&Object.assign(s,e);return Object.assign(s,i),s.options&&(s.options=n.options?Object.create(n.options):{},Object.assign(s.options,i.options)),s._initHooks=[],o}static include(t){var e=this.prototype.options;return Object.assign(this.prototype,t),t.options&&(this.prototype.options=e,this.mergeOptions(t.options)),this}static mergeOptions(t){return this.prototype.options??={},Object.assign(this.prototype.options,t),this}static addInitHook(t,...e){var i="function"==typeof t?t:function(){this[t].apply(this,e)};return this.prototype._initHooks??=[],this.prototype._initHooks.push(i),this}constructor(...t){this._initHooksCalled=!1,setOptions(this),this.initialize&&this.initialize(...t),this.callInitHooks()}callInitHooks(){if(!this._initHooksCalled){var e,i=[];let t=this;for(;null!==(t=Object.getPrototypeOf(t));)i.push(t);i.reverse();for(e of i)for(var o of e._initHooks??[])o.call(this);this._initHooksCalled=!0}}}let Events={on(t,e,i){if("object"==typeof t)for(var[o,n]of Object.entries(t))this._on(o,n,e);else for(var s of splitWords(t))this._on(s,e,i);return this},off(t,e,i){if(arguments.length)if("object"==typeof t)for(var[o,n]of Object.entries(t))this._off(o,n,e);else{var s,a=1===arguments.length;for(s of splitWords(t))a?this._off(s):this._off(s,e,i)}else delete this._events;return this},_on(t,e,i,o){"function"!=typeof e?console.warn("wrong listener type: "+typeof e):!1===this._listens(t,e,i)&&(e={fn:e,ctx:i=i===this?void 0:i},o&&(e.once=!0),this._events??={},this._events[t]??=[],this._events[t].push(e))},_off(e,i,o){if(this._events){let t=this._events[e];if(t)if(1===arguments.length){if(this._firingCount)for(var n of t)n.fn=falseFn;delete this._events[e]}else"function"!=typeof i?console.warn("wrong listener type: "+typeof i):!1!==(i=this._listens(e,i,o))&&(o=t[i],this._firingCount&&(o.fn=falseFn,this._events[e]=t=t.slice()),t.splice(i,1))}},fire(t,e,i){if(this.listens(t,i)){var o={...e,type:t,target:this,sourceTarget:e?.sourceTarget||this};if(this._events){e=this._events[t];if(e){this._firingCount=this._firingCount+1||1;for(var n of e){var s=n.fn;n.once&&this.off(t,s,n.ctx),s.call(n.ctx||this,o)}this._firingCount--}}i&&this._propagateEvent(o)}return this},listens(t,e,i,o){"string"!=typeof t&&console.warn('"string" type argument expected');let n=e;if("function"!=typeof e&&(o=!!e,i=n=void 0),this._events?.[t]?.length&&!1!==this._listens(t,n,i))return!0;if(o)for(var s of Object.values(this._eventParents??{}))if(s.listens(t,e,i,o))return!0;return!1},_listens(t,e,i){if(!this._events)return!1;t=this._events[t]??[];if(!e)return!!t.length;i===this&&(i=void 0);t=t.findIndex(t=>t.fn===e&&t.ctx===i);return-1!==t&&t},once(t,e,i){if("object"==typeof t)for(var[o,n]of Object.entries(t))this._on(o,n,e,!0);else for(var s of splitWords(t))this._on(s,e,i,!0);return this},addEventParent(t){return this._eventParents??={},this._eventParents[stamp(t)]=t,this},removeEventParent(t){return this._eventParents&&delete this._eventParents[stamp(t)],this},_propagateEvent(t){for(var e of Object.values(this._eventParents??{}))e.fire(t.type,{propagatedFrom:t.target,...t},!0)}},Evented=(Events.addEventListener=Events.on,Events.removeEventListener=Events.clearAllEventListeners=Events.off,Events.addOneTimeEventListener=Events.once,Events.fireEvent=Events.fire,Events.hasEventListeners=Events.listens,Class.extend(Events));class Point{constructor(t,e,i){this.x=i?Math.round(t):t,this.y=i?Math.round(e):e}clone(){return new Point(this.x,this.y)}add(t){return this.clone()._add(toPoint(t))}_add(t){return this.x+=t.x,this.y+=t.y,this}subtract(t){return this.clone()._subtract(toPoint(t))}_subtract(t){return this.x-=t.x,this.y-=t.y,this}divideBy(t){return this.clone()._divideBy(t)}_divideBy(t){return this.x/=t,this.y/=t,this}multiplyBy(t){return this.clone()._multiplyBy(t)}_multiplyBy(t){return this.x*=t,this.y*=t,this}scaleBy(t){return new Point(this.x*t.x,this.y*t.y)}unscaleBy(t){return new Point(this.x/t.x,this.y/t.y)}round(){return this.clone()._round()}_round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}floor(){return this.clone()._floor()}_floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.clone()._ceil()}_ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}trunc(){return this.clone()._trunc()}_trunc(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}distanceTo(t){var e=(t=toPoint(t)).x-this.x,t=t.y-this.y;return Math.sqrt(e*e+t*t)}equals(t){return(t=toPoint(t)).x===this.x&&t.y===this.y}contains(t){return t=toPoint(t),Math.abs(t.x)<=Math.abs(this.x)&&Math.abs(t.y)<=Math.abs(this.y)}toString(){return`Point(${formatNum(this.x)}, ${formatNum(this.y)})`}}function toPoint(t,e,i){return t instanceof Point?t:Array.isArray(t)?new Point(t[0],t[1]):null==t?t:"object"==typeof t&&"x"in t&&"y"in t?new Point(t.x,t.y):new Point(t,e,i)}class Bounds{constructor(t,e){var i;if(t)for(i of e?[t,e]:t)this.extend(i)}extend(t){let e,i;if(t){if(t instanceof Point||"number"==typeof t[0]||"x"in t)e=i=toPoint(t);else if(t=toBounds(t),e=t.min,i=t.max,!e||!i)return this;this.min||this.max?(this.min.x=Math.min(e.x,this.min.x),this.max.x=Math.max(i.x,this.max.x),this.min.y=Math.min(e.y,this.min.y),this.max.y=Math.max(i.y,this.max.y)):(this.min=e.clone(),this.max=i.clone())}return this}getCenter(t){return toPoint((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,t)}getBottomLeft(){return toPoint(this.min.x,this.max.y)}getTopRight(){return toPoint(this.max.x,this.min.y)}getTopLeft(){return this.min}getBottomRight(){return this.max}getSize(){return this.max.subtract(this.min)}contains(t){let e,i;return(t=("number"==typeof t[0]||t instanceof Point?toPoint:toBounds)(t))instanceof Bounds?(e=t.min,i=t.max):e=i=t,e.x>=this.min.x&&i.x<=this.max.x&&e.y>=this.min.y&&i.y<=this.max.y}intersects(t){t=toBounds(t);var e=this.min,i=this.max,o=t.min,t=t.max,n=t.x>=e.x&&o.x<=i.x,e=e.y<=t.y&&o.y<=i.y;return n&&e}overlaps(t){t=toBounds(t);var e=this.min,i=this.max,o=t.min,t=t.max,n=t.x>e.x&&o.x<i.x,e=e.y<t.y&&o.y<i.y;return n&&e}isValid(){return!(!this.min||!this.max)}pad(t){var e=this.min,i=this.max,o=Math.abs(e.x-i.x)*t,t=Math.abs(e.y-i.y)*t;return toBounds(toPoint(e.x-o,e.y-t),toPoint(i.x+o,i.y+t))}equals(t){return!!t&&(t=toBounds(t),this.min.equals(t.getTopLeft()))&&this.max.equals(t.getBottomRight())}}function toBounds(t,e){return!t||t instanceof Bounds?t:new Bounds(t,e)}class LatLngBounds{constructor(t,e){var i;if(t)for(i of e?[t,e]:t)this.extend(i)}extend(t){var e=this._southWest,i=this._northEast;let o,n;if(t instanceof LatLng)o=t,n=t;else{if(!(t instanceof LatLngBounds))return t?this.extend(toLatLng(t)||toLatLngBounds(t)):this;if(o=t._southWest,n=t._northEast,!o||!n)return this}return e||i?(e.lat=Math.min(o.lat,e.lat),e.lng=Math.min(o.lng,e.lng),i.lat=Math.max(n.lat,i.lat),i.lng=Math.max(n.lng,i.lng)):(this._southWest=new LatLng(o.lat,o.lng),this._northEast=new LatLng(n.lat,n.lng)),this}pad(t){var e=this._southWest,i=this._northEast,o=Math.abs(e.lat-i.lat)*t,t=Math.abs(e.lng-i.lng)*t;return new LatLngBounds(new LatLng(e.lat-o,e.lng-t),new LatLng(i.lat+o,i.lng+t))}getCenter(){return new LatLng((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)}getSouthWest(){return this._southWest}getNorthEast(){return this._northEast}getNorthWest(){return new LatLng(this.getNorth(),this.getWest())}getSouthEast(){return new LatLng(this.getSouth(),this.getEast())}getWest(){return this._southWest.lng}getSouth(){return this._southWest.lat}getEast(){return this._northEast.lng}getNorth(){return this._northEast.lat}contains(t){t=("number"==typeof t[0]||t instanceof LatLng||"lat"in t?toLatLng:toLatLngBounds)(t);var e=this._southWest,i=this._northEast;let o,n;return t instanceof LatLngBounds?(o=t.getSouthWest(),n=t.getNorthEast()):o=n=t,o.lat>=e.lat&&n.lat<=i.lat&&o.lng>=e.lng&&n.lng<=i.lng}intersects(t){t=toLatLngBounds(t);var e=this._southWest,i=this._northEast,o=t.getSouthWest(),t=t.getNorthEast(),n=t.lat>=e.lat&&o.lat<=i.lat,t=t.lng>=e.lng&&o.lng<=i.lng;return n&&t}overlaps(t){t=toLatLngBounds(t);var e=this._southWest,i=this._northEast,o=t.getSouthWest(),t=t.getNorthEast(),n=t.lat>e.lat&&o.lat<i.lat,t=t.lng>e.lng&&o.lng<i.lng;return n&&t}toBBoxString(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")}equals(t,e){return!!t&&(t=toLatLngBounds(t),this._southWest.equals(t.getSouthWest(),e))&&this._northEast.equals(t.getNorthEast(),e)}isValid(){return!(!this._southWest||!this._northEast)}}function toLatLngBounds(t,e){return t instanceof LatLngBounds?t:new LatLngBounds(t,e)}class LatLng{constructor(t,e,i){if(isNaN(t)||isNaN(e))throw new Error(`Invalid LatLng object: (${t}, ${e})`);this.lat=+t,this.lng=+e,void 0!==i&&(this.alt=+i)}equals(t,e){return!!t&&(t=toLatLng(t),Math.max(Math.abs(this.lat-t.lat),Math.abs(this.lng-t.lng))<=(e??1e-9))}toString(t){return`LatLng(${formatNum(this.lat,t)}, ${formatNum(this.lng,t)})`}distanceTo(t){return Earth.distance(this,toLatLng(t))}wrap(){return Earth.wrapLatLng(this)}toBounds(t){var t=180*t/40075017,e=t/Math.cos(Math.PI/180*this.lat);return toLatLngBounds([this.lat-t,this.lng-e],[this.lat+t,this.lng+e])}clone(){return new LatLng(this.lat,this.lng,this.alt)}}function toLatLng(t,e,i){return t instanceof LatLng?t:Array.isArray(t)&&"object"!=typeof t[0]?3===t.length?new LatLng(t[0],t[1],t[2]):2===t.length?new LatLng(t[0],t[1]):null:null==t?t:"object"==typeof t&&"lat"in t?new LatLng(t.lat,"lng"in t?t.lng:t.lon,t.alt):void 0===e?null:new LatLng(t,e,i)}let CRS={latLngToPoint(t,e){t=this.projection.project(t),e=this.scale(e);return this.transformation._transform(t,e)},pointToLatLng(t,e){e=this.scale(e),t=this.transformation.untransform(t,e);return this.projection.unproject(t)},project(t){return this.projection.project(t)},unproject(t){return this.projection.unproject(t)},scale(t){return 256*2**t},zoom(t){return Math.log(t/256)/Math.LN2},getProjectedBounds(t){var e,i;return this.infinite?null:(i=this.projection.bounds,t=this.scale(t),e=this.transformation.transform(i.min,t),i=this.transformation.transform(i.max,t),new Bounds(e,i))},infinite:!1,wrapLatLng(t){var e=this.wrapLng?wrapNum(t.lng,this.wrapLng,!0):t.lng,i=this.wrapLat?wrapNum(t.lat,this.wrapLat,!0):t.lat,t=t.alt;return new LatLng(i,e,t)},wrapLatLngBounds(t){var e=t.getCenter(),i=this.wrapLatLng(e),o=e.lat-i.lat,e=e.lng-i.lng;return 0==o&&0==e?t:(i=t.getSouthWest(),t=t.getNorthEast(),i=new LatLng(i.lat-o,i.lng-e),o=new LatLng(t.lat-o,t.lng-e),new LatLngBounds(i,o))}},Earth={...CRS,wrapLng:[-180,180],R:6371e3,distance(t,e){var i=Math.PI/180,o=t.lat*i,n=e.lat*i,s=Math.sin((e.lat-t.lat)*i/2),e=Math.sin((e.lng-t.lng)*i/2),t=s*s+Math.cos(o)*Math.cos(n)*e*e,i=2*Math.atan2(Math.sqrt(t),Math.sqrt(1-t));return this.R*i}},earthRadius=6378137,SphericalMercator={R:earthRadius,MAX_LATITUDE:85.0511287798,project(t){var e=Math.PI/180,i=this.MAX_LATITUDE,i=Math.max(Math.min(i,t.lat),-i),i=Math.sin(i*e);return new Point(this.R*t.lng*e,this.R*Math.log((1+i)/(1-i))/2)},unproject(t){var e=180/Math.PI;return new LatLng((2*Math.atan(Math.exp(t.y/this.R))-Math.PI/2)*e,t.x*e/this.R)},bounds:(()=>{var t=earthRadius*Math.PI;return new Bounds([-t,-t],[t,t])})()};class Transformation{constructor(t,e,i,o){Array.isArray(t)?(this._a=t[0],this._b=t[1],this._c=t[2],this._d=t[3]):(this._a=t,this._b=e,this._c=i,this._d=o)}transform(t,e){return this._transform(t.clone(),e)}_transform(t,e){return t.x=(e||=1)*(this._a*t.x+this._b),t.y=e*(this._c*t.y+this._d),t}untransform(t,e){return e||=1,new Point((t.x/e-this._b)/this._a,(t.y/e-this._d)/this._c)}}function toTransformation(t,e,i,o){return new Transformation(t,e,i,o)}let EPSG3857={...Earth,code:"EPSG:3857",projection:SphericalMercator,transformation:(()=>{var t=.5/(Math.PI*SphericalMercator.R);return toTransformation(t,.5,-t,.5)})()},EPSG900913={...EPSG3857,code:"EPSG:900913"},chrome=userAgentContains("chrome"),safari=!chrome&&userAgentContains("safari"),mobile="undefined"!=typeof orientation||userAgentContains("mobile"),pointer="undefined"!=typeof window&&!!window.PointerEvent,touchNative="undefined"!=typeof window&&("ontouchstart"in window||!!window.TouchEvent),touch=touchNative||pointer,retina="undefined"!=typeof window&&void 0!==window.devicePixelRatio&&1<window.devicePixelRatio,mac="undefined"!=typeof navigator&&void 0!==navigator.platform&&navigator.platform.startsWith("Mac"),linux="undefined"!=typeof navigator&&void 0!==navigator.platform&&navigator.platform.startsWith("Linux");function userAgentContains(t){return"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t)}var Browser={chrome:chrome,safari:safari,mobile:mobile,pointer:pointer,touch:touch,touchNative:touchNative,retina:retina,mac:mac,linux:linux};function makeDblclick(t){let e={bubbles:t.bubbles,cancelable:t.cancelable,composed:t.composed,detail:2,view:t.view,screenX:t.screenX,screenY:t.screenY,clientX:t.clientX,clientY:t.clientY,ctrlKey:t.ctrlKey,shiftKey:t.shiftKey,altKey:t.altKey,metaKey:t.metaKey,button:t.button,buttons:t.buttons,relatedTarget:t.relatedTarget,region:t.region},i;return i=new(t instanceof PointerEvent?(e={...e,pointerId:t.pointerId,width:t.width,height:t.height,pressure:t.pressure,tangentialPressure:t.tangentialPressure,tiltX:t.tiltX,tiltY:t.tiltY,twist:t.twist,pointerType:t.pointerType,isPrimary:t.isPrimary},PointerEvent):MouseEvent)("dblclick",e)}let delay=200;function addDoubleTapListener(t,e){t.addEventListener("dblclick",e);let i=0,o;function n(t){var e;1!==t.detail?o=t.detail:"mouse"===t.pointerType||t.sourceCapabilities&&!t.sourceCapabilities.firesTouchEvents||(e=getPropagationPath(t)).some(t=>t instanceof HTMLLabelElement&&t.attributes.for)&&!e.some(t=>t instanceof HTMLInputElement||t instanceof HTMLSelectElement)||((e=Date.now())-i<=delay?2===++o&&t.target.dispatchEvent(makeDblclick(t)):o=1,i=e)}return t.addEventListener("click",n),{dblclick:e,simDblclick:n}}function removeDoubleTapListener(t,e){t.removeEventListener("dblclick",e.dblclick),t.removeEventListener("click",e.simDblclick)}function get(t){return"string"==typeof t?document.getElementById(t):t}function create$1(t,e,i){t=document.createElement(t);return t.className=e??"",i&&i.appendChild(t),t}function toFront(t){var e=t.parentNode;e&&e.lastChild!==t&&e.appendChild(t)}function toBack(t){var e=t.parentNode;e&&e.firstChild!==t&&e.insertBefore(t,e.firstChild)}function setTransform(t,e,i){e=e??new Point(0,0);t.style.transform=`translate3d(${e.x}px,${e.y}px,0)`+(i?` scale(${i})`:"")}let positions=new WeakMap;function setPosition(t,e){positions.set(t,e),setTransform(t,e)}function getPosition(t){return positions.get(t)??new Point(0,0)}let documentStyle="undefined"==typeof document?{}:document.documentElement.style,userSelectProp=["userSelect","WebkitUserSelect"].find(t=>t in documentStyle),prevUserSelect;function disableTextSelection(){var t=documentStyle[userSelectProp];"none"!==t&&(prevUserSelect=t,documentStyle[userSelectProp]="none")}function enableTextSelection(){void 0!==prevUserSelect&&(documentStyle[userSelectProp]=prevUserSelect,prevUserSelect=void 0)}function disableImageDrag(){on(window,"dragstart",preventDefault)}function enableImageDrag(){off(window,"dragstart",preventDefault)}let _outlineElement,_outlineStyle;function preventOutline(t){for(;-1===t.tabIndex;)t=t.parentNode;t.style&&(restoreOutline(),_outlineElement=t,_outlineStyle=t.style.outlineStyle,t.style.outlineStyle="none",on(window,"keydown",restoreOutline))}function restoreOutline(){_outlineElement&&(_outlineElement.style.outlineStyle=_outlineStyle,_outlineElement=void 0,_outlineStyle=void 0,off(window,"keydown",restoreOutline))}function getSizedParentNode(t){for(;!((t=t.parentNode).offsetWidth&&t.offsetHeight||t===document.body););return t}function getScale(t){var e=t.getBoundingClientRect();return{x:e.width/t.offsetWidth||1,y:e.height/t.offsetHeight||1,boundingClientRect:e}}var DomUtil={__proto__:null,create:create$1,disableImageDrag:disableImageDrag,disableTextSelection:disableTextSelection,enableImageDrag:enableImageDrag,enableTextSelection:enableTextSelection,get:get,getPosition:getPosition,getScale:getScale,getSizedParentNode:getSizedParentNode,preventOutline:preventOutline,restoreOutline:restoreOutline,setPosition:setPosition,setTransform:setTransform,toBack:toBack,toFront:toFront};let activePointers=new Map,initialized=!1;function enablePointerDetection(){initialized||(initialized=!0,document.addEventListener("pointerdown",_onSet,{capture:!0}),document.addEventListener("pointermove",_onUpdate,{capture:!0}),document.addEventListener("pointerup",_onDelete,{capture:!0}),document.addEventListener("pointercancel",_onDelete,{capture:!0}),activePointers=new Map)}function disablePointerDetection(){document.removeEventListener("pointerdown",_onSet,{capture:!0}),document.removeEventListener("pointermove",_onUpdate,{capture:!0}),document.removeEventListener("pointerup",_onDelete,{capture:!0}),document.removeEventListener("pointercancel",_onDelete,{capture:!0}),initialized=!1}function _onSet(t){activePointers.set(t.pointerId,t)}function _onUpdate(t){activePointers.has(t.pointerId)&&activePointers.set(t.pointerId,t)}function _onDelete(t){activePointers.delete(t.pointerId)}function getPointers(){return[...activePointers.values()]}function cleanupPointers(){activePointers.clear()}var DomEvent_PointerEvents={__proto__:null,cleanupPointers:cleanupPointers,disablePointerDetection:disablePointerDetection,enablePointerDetection:enablePointerDetection,getPointers:getPointers};function on(t,e,i,o){if(e&&"object"==typeof e)for(var[n,s]of Object.entries(e))addOne(t,n,s,i);else for(var a of splitWords(e))addOne(t,a,i,o);return this}let eventsKey="_leaflet_events";function off(t,e,i,o){if(1===arguments.length)batchRemove(t),delete t[eventsKey];else if(e&&"object"==typeof e)for(var[n,s]of Object.entries(e))removeOne(t,n,s,i);else if(e=splitWords(e),2===arguments.length)batchRemove(t,t=>e.includes(t));else for(var a of e)removeOne(t,a,i,o);return this}function batchRemove(t,e){for(var i of Object.keys(t[eventsKey]??{})){var o=i.split(/\d/)[0];e&&!e(o)||removeOne(t,o,null,null,i)}}let pointerSubst={pointerenter:"pointerover",pointerleave:"pointerout",wheel:"undefined"!=typeof window&&!("onwheel"in window)&&"mousewheel"};function addOne(e,t,i,o){var n=t+stamp(i)+(o?"_"+stamp(o):"");if(e[eventsKey]&&e[eventsKey][n])return this;let s=function(t){return i.call(o||e,t||window.event)},a=s;Browser.touch&&"dblclick"===t?s=addDoubleTapListener(e,s):"addEventListener"in e?"wheel"===t||"mousewheel"===t?e.addEventListener(pointerSubst[t]||t,s,{passive:!1}):"pointerenter"===t||"pointerleave"===t?(s=function(t){t??=window.event,isExternalTarget(e,t)&&a(t)},e.addEventListener(pointerSubst[t],s,!1)):e.addEventListener(t,a,!1):e.attachEvent("on"+t,s),e[eventsKey]??={},e[eventsKey][n]=s}function removeOne(t,e,i,o,n){n??=e+stamp(i)+(o?"_"+stamp(o):"");i=t[eventsKey]&&t[eventsKey][n];if(!i)return this;Browser.touch&&"dblclick"===e?removeDoubleTapListener(t,i):"removeEventListener"in t?t.removeEventListener(pointerSubst[e]||e,i,!1):t.detachEvent("on"+e,i),t[eventsKey][n]=null}function stopPropagation(t){return t.stopPropagation?t.stopPropagation():t.originalEvent?t.originalEvent._stopped=!0:t.cancelBubble=!0,this}function disableScrollPropagation(t){return addOne(t,"wheel",stopPropagation),this}function disableClickPropagation(t){return on(t,"pointerdown dblclick contextmenu",stopPropagation),t._leaflet_disable_click=!0,this}function preventDefault(t){return t.preventDefault?t.preventDefault():t.returnValue=!1,this}function stop(t){return preventDefault(t),stopPropagation(t),this}function getPropagationPath(t){return t.composedPath()}function getPointerPosition(t,e){var i,o;return e?(o=(i=getScale(e)).boundingClientRect,new Point((t.clientX-o.left)/i.x-e.clientLeft,(t.clientY-o.top)/i.y-e.clientTop)):new Point(t.clientX,t.clientY)}function getWheelPxFactor(){var t=window.devicePixelRatio;return Browser.linux&&Browser.chrome?t:Browser.mac?3*t:0<t?2*t:1}function getWheelDelta(t){return t.deltaY&&0===t.deltaMode?-t.deltaY/getWheelPxFactor():t.deltaY&&1===t.deltaMode?20*-t.deltaY:t.deltaY&&2===t.deltaMode?60*-t.deltaY:(t.deltaX||t.deltaZ,0)}function isExternalTarget(t,e){let i=e.relatedTarget;if(!i)return!0;try{for(;i&&i!==t;)i=i.parentNode}catch(t){return!1}return i!==t}var DomEvent={__proto__:null,PointerEvents:DomEvent_PointerEvents,addListener:on,disableClickPropagation:disableClickPropagation,disableScrollPropagation:disableScrollPropagation,getPointerPosition:getPointerPosition,getPropagationPath:getPropagationPath,getWheelDelta:getWheelDelta,getWheelPxFactor:getWheelPxFactor,isExternalTarget:isExternalTarget,off:off,on:on,preventDefault:preventDefault,removeListener:off,stop:stop,stopPropagation:stopPropagation};let PosAnimation=Evented.extend({run(t,e,i,o){this.stop(),this._el=t,this._inProgress=!0,this._duration=i??.25,this._easeOutPower=1/Math.max(o??.5,.2),this._startPos=getPosition(t),this._offset=e.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop(){this._inProgress&&(this._step(!0),this._complete())},_animate(){this._animId=requestAnimationFrame(this._animate.bind(this)),this._step()},_step(t){var e=+new Date-this._startTime,i=1e3*this._duration;e<i?this._runFrame(this._easeOut(e/i),t):(this._runFrame(1),this._complete())},_runFrame(t,e){t=this._startPos.add(this._offset.multiplyBy(t));e&&t._round(),setPosition(this._el,t),this.fire("step")},_complete(){cancelAnimationFrame(this._animId),this._inProgress=!1,this.fire("end")},_easeOut(t){return 1-(1-t)**this._easeOutPower}}),Map$1=Evented.extend({options:{crs:EPSG3857,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize(t,e){e=setOptions(this,e),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(t),this._initLayout(),this._initEvents(),e.maxBounds&&this.setMaxBounds(e.maxBounds),void 0!==e.zoom&&(this._zoom=this._limitZoom(e.zoom)),e.center&&void 0!==e.zoom&&this.setView(toLatLng(e.center),e.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=this.options.zoomAnimation,this._zoomAnimated&&this._createAnimProxy(),this._addLayers(this.options.layers)},setView(t,e,i){if((e=void 0===e?this._zoom:this._limitZoom(e),t=this._limitCenter(toLatLng(t),e,this.options.maxBounds),i??={},this._stop(),this._loaded&&!i.reset&&!0!==i)&&(void 0!==i.animate&&(i.zoom={animate:i.animate,...i.zoom},i.pan={animate:i.animate,duration:i.duration,...i.pan}),this._zoom!==e?this._tryAnimatedZoom&&this._tryAnimatedZoom(t,e,i.zoom):this._tryAnimatedPan(t,i.pan)))return clearTimeout(this._sizeTimer),this;return this._resetView(t,e,i.pan?.noMoveStart),this},setZoom(t,e){return this._loaded?this.setView(this.getCenter(),t,{zoom:e}):(this._zoom=t,this)},zoomIn(t,e){return t??=this.options.zoomDelta,this.setZoom(this._zoom+t,e)},zoomOut(t,e){return t??=this.options.zoomDelta,this.setZoom(this._zoom-t,e)},setZoomAround(t,e,i){var o=this.getZoomScale(e),n=this.getSize().divideBy(2),t=(t instanceof Point?t:this.latLngToContainerPoint(t)).subtract(n).multiplyBy(1-1/o),o=this.containerPointToLatLng(n.add(t));return this.setView(o,e,{zoom:i})},_getBoundsCenterZoom(t,e){e??={},t=t.getBounds?t.getBounds():toLatLngBounds(t);var i=toPoint(e.paddingTopLeft||e.padding||[0,0]),o=toPoint(e.paddingBottomRight||e.padding||[0,0]),n=this.getBoundsZoom(t,!1,i.add(o));return(n="number"==typeof e.maxZoom?Math.min(e.maxZoom,n):n)===1/0?{center:t.getCenter(),zoom:n}:(e=o.subtract(i).divideBy(2),o=this.project(t.getSouthWest(),n),i=this.project(t.getNorthEast(),n),{center:this.unproject(o.add(i).divideBy(2).add(e),n),zoom:n})},fitBounds(t,e){if((t=toLatLngBounds(t)).isValid())return t=this._getBoundsCenterZoom(t,e),this.setView(t.center,t.zoom,e);throw new Error("Bounds are not valid.")},fitWorld(t){return this.fitBounds([[-90,-180],[90,180]],t)},panTo(t,e){return this.setView(t,this._zoom,{pan:e})},panBy(t,e){var i;return e??={},(t=toPoint(t).round()).x||t.y?(!0===e.animate||this.getSize().contains(t)?(this._panAnim||(this._panAnim=new PosAnimation,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),e.noMoveStart||this.fire("movestart"),!1!==e.animate?(this._mapPane.classList.add("leaflet-pan-anim"),i=this._getMapPanePos().subtract(t).round(),this._panAnim.run(this._mapPane,i,e.duration||.25,e.easeLinearity)):(this._rawPanBy(t),this.fire("move").fire("moveend"))):this._resetView(this.unproject(this.project(this.getCenter()).add(t)),this.getZoom()),this):this.fire("moveend")},flyTo(i,o,t){if(!1===(t??={}).animate)return this.setView(i,o,t);this._stop();let n=this.project(this.getCenter()),s=this.project(i),e=this.getSize(),a=this._zoom,r=(i=toLatLng(i),o=void 0===o?a:this._limitZoom(o),Math.max(e.x,e.y)),h=r*this.getZoomScale(a,o),l=s.distanceTo(n)||1,d=1.42,p=d*d;function _(t){var e=t?-1:1,t=t?h:r,e=(h*h-r*r+e*p*p*l*l)/(2*t*p*l),t=Math.sqrt(e*e+1)-e;return t<1e-9?-18:Math.log(t)}function u(t){return(Math.exp(t)-Math.exp(-t))/2}function c(t){return(Math.exp(t)+Math.exp(-t))/2}let m=_(0);function g(t){return r*(c(m)*(u(t=m+d*t)/c(t))-u(m))/p}let f=Date.now(),v=(_(1)-m)/d,y=t.duration?1e3*t.duration:1e3*v*.8;function L(){var t=(Date.now()-f)/y,e=(1-(1-t)**1.5)*v;t<=1?(this._flyToFrame=requestAnimationFrame(L.bind(this)),this._move(this.unproject(n.add(s.subtract(n).multiplyBy(g(e)/l)),a),this.getScaleZoom(r/(t=e,r*(c(m)/c(m+d*t))),a),{flyTo:!0})):this._move(i,o)._moveEnd(!0)}return this._moveStart(!0,t.noMoveStart),L.call(this),this},flyToBounds(t,e){t=this._getBoundsCenterZoom(t,e);return this.flyTo(t.center,t.zoom,e)},setMaxBounds(t){return t=toLatLngBounds(t),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),t.isValid()?(this.options.maxBounds=t,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom(t){var e=this.options.minZoom;return this.options.minZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(t):this},setMaxZoom(t){var e=this.options.maxZoom;return this.options.maxZoom=t,this._loaded&&e!==t&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(t):this},panInsideBounds(t,e){this._enforcingBounds=!0;var i=this.getCenter(),t=this._limitCenter(i,this._zoom,toLatLngBounds(t));return i.equals(t)||this.panTo(t,e),this._enforcingBounds=!1,this},panInside(t,e){var i=toPoint((e??={}).paddingTopLeft||e.padding||[0,0]),o=toPoint(e.paddingBottomRight||e.padding||[0,0]),n=this.project(this.getCenter()),t=this.project(t),s=this.getPixelBounds(),i=toBounds([s.min.add(i),s.max.subtract(o)]),s=i.getSize();return i.contains(t)||(this._enforcingBounds=!0,o=t.subtract(i.getCenter()),i=i.extend(t).getSize().subtract(s),n.x+=o.x<0?-i.x:i.x,n.y+=o.y<0?-i.y:i.y,this.panTo(this.unproject(n),e),this._enforcingBounds=!1),this},invalidateSize(t){if(!this._loaded)return this;t={animate:!1,pan:!0,...!0===t?{animate:!0}:t};var e=this.getSize(),i=(this._sizeChanged=!0,this._lastCenter=null,this.getSize()),o=e.divideBy(2).round(),n=i.divideBy(2).round(),o=o.subtract(n);return o.x||o.y?(t.animate&&t.pan?this.panBy(o):(t.pan&&this._rawPanBy(o),this.fire("move"),t.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(this.fire.bind(this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:e,newSize:i})):this},stop(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate(t){var e,i;return t=this._locateOptions={timeout:1e4,watch:!1,...t},"geolocation"in navigator?(e=this._handleGeolocationResponse.bind(this),i=this._handleGeolocationError.bind(this),t.watch?this._locationWatchId=navigator.geolocation.watchPosition(e,i,t):navigator.geolocation.getCurrentPosition(e,i,t)):this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this},stopLocate(){return navigator.geolocation?.clearWatch?.(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError(t){var e;this._container._leaflet_id&&(e=t.code,t=t.message||(1===e?"permission denied":2===e?"position unavailable":"timeout"),this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:e,message:`Geolocation error: ${t}.`}))},_handleGeolocationResponse(t){if(this._container._leaflet_id){var e,i,o=t.coords.latitude,n=t.coords.longitude,o=new LatLng(o,n),n=o.toBounds(2*t.coords.accuracy),s=this._locateOptions,a=(s.setView&&(e=this.getBoundsZoom(n),this.setView(o,s.maxZoom?Math.min(e,s.maxZoom):e)),{latlng:o,bounds:n,timestamp:t.timestamp});for(i of Object.keys(t.coords))"number"==typeof t.coords[i]&&(a[i]=t.coords[i]);this.fire("locationfound",a)}},addHandler(t,e){return e&&(e=this[t]=new e(this),this._handlers.push(e),this.options[t])&&e.enable(),this},remove(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch(t){this._container._leaflet_id=void 0,this._containerId=void 0}void 0!==this._locationWatchId&&this.stopLocate(),this._stop(),this._mapPane.remove(),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(cancelAnimationFrame(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),clearTimeout(this._transitionEndTimer),clearTimeout(this._sizeTimer),this._loaded&&this.fire("unload"),this._destroyAnimProxy();for(var t of Object.values(this._layers))t.remove();for(var e of Object.values(this._panes))e.remove();return this._layers={},this._panes={},delete this._mapPane,delete this._renderer,this},createPane(t,e){e=create$1("div","leaflet-pane"+(t?` leaflet-${t.replace("Pane","")}-pane`:""),e||this._mapPane);return t&&(this._panes[t]=e),e},getCenter(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom(){return this._zoom},getBounds(){var t=this.getPixelBounds(),e=this.unproject(t.getBottomLeft()),t=this.unproject(t.getTopRight());return new LatLngBounds(e,t)},getMinZoom(){return this.options.minZoom??this._layersMinZoom??0},getMaxZoom(){return this.options.maxZoom??this._layersMaxZoom??1/0},getBoundsZoom(t,e,i){t=toLatLngBounds(t),i=toPoint(i??[0,0]);let o=this.getZoom()??0;var n=this.getMinZoom(),s=this.getMaxZoom(),a=t.getNorthWest(),t=t.getSouthEast(),i=this.getSize().subtract(i),t=toBounds(this.project(t,o),this.project(a,o)).getSize(),a=this.options.zoomSnap,r=i.x/t.x,i=i.y/t.y,t=e?Math.max(r,i):Math.min(r,i);return o=this.getScaleZoom(t,o),a&&(o=Math.round(o/(a/100))*(a/100),o=e?Math.ceil(o/a)*a:Math.floor(o/a)*a),Math.max(n,Math.min(s,o))},getSize(){return this._size&&!this._sizeChanged||(this._size=new Point(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds(t,e){t=this._getTopLeftPoint(t,e);return new Bounds(t,t.add(this.getSize()))},getPixelOrigin(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds(t){return this.options.crs.getProjectedBounds(t??this.getZoom())},getPane(t){return"string"==typeof t?this._panes[t]:t},getPanes(){return this._panes},getContainer(){return this._container},getZoomScale(t,e){var i=this.options.crs;return e??=this._zoom,i.scale(t)/i.scale(e)},getScaleZoom(t,e){var i=this.options.crs,t=(e??=this._zoom,i.zoom(t*i.scale(e)));return isNaN(t)?1/0:t},project(t,e){return e??=this._zoom,this.options.crs.latLngToPoint(toLatLng(t),e)},unproject(t,e){return e??=this._zoom,this.options.crs.pointToLatLng(toPoint(t),e)},layerPointToLatLng(t){t=toPoint(t).add(this.getPixelOrigin());return this.unproject(t)},latLngToLayerPoint(t){return this.project(toLatLng(t))._round()._subtract(this.getPixelOrigin())},wrapLatLng(t){return this.options.crs.wrapLatLng(toLatLng(t))},wrapLatLngBounds(t){return this.options.crs.wrapLatLngBounds(toLatLngBounds(t))},distance(t,e){return this.options.crs.distance(toLatLng(t),toLatLng(e))},containerPointToLayerPoint(t){return toPoint(t).subtract(this._getMapPanePos())},layerPointToContainerPoint(t){return toPoint(t).add(this._getMapPanePos())},containerPointToLatLng(t){t=this.containerPointToLayerPoint(toPoint(t));return this.layerPointToLatLng(t)},latLngToContainerPoint(t){return this.layerPointToContainerPoint(this.latLngToLayerPoint(toLatLng(t)))},pointerEventToContainerPoint(t){return getPointerPosition(t,this._container)},pointerEventToLayerPoint(t){return this.containerPointToLayerPoint(this.pointerEventToContainerPoint(t))},pointerEventToLatLng(t){return this.layerPointToLatLng(this.pointerEventToLayerPoint(t))},_initContainer(t){t=this._container=get(t);if(!t)throw new Error("Map container not found.");if(t._leaflet_id)throw new Error("Map container is already initialized.");on(t,"scroll",this._onScroll,this),this._containerId=stamp(t),enablePointerDetection()},_initLayout(){var t=this._container,e=(this._fadeAnimated=this.options.fadeAnimation,["leaflet-container"]),e=(Browser.touch&&e.push("leaflet-touch"),Browser.retina&&e.push("leaflet-retina"),Browser.safari&&e.push("leaflet-safari"),this._fadeAnimated&&e.push("leaflet-fade-anim"),t.classList.add(...e),getComputedStyle(t)).position;"absolute"!==e&&"relative"!==e&&"fixed"!==e&&"sticky"!==e&&(t.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes(){var t=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),setPosition(this._mapPane,new Point(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(t.markerPane.classList.add("leaflet-zoom-hide"),t.shadowPane.classList.add("leaflet-zoom-hide"))},_resetView(t,e,i){setPosition(this._mapPane,new Point(0,0));var o=!this._loaded,n=(this._loaded=!0,e=this._limitZoom(e),this.fire("viewprereset"),this._zoom!==e);this._moveStart(n,i)._move(t,e)._moveEnd(n),this.fire("viewreset"),o&&this.fire("load")},_moveStart(t,e){return t&&this.fire("zoomstart"),e||this.fire("movestart"),this},_move(t,e,i,o){void 0===e&&(e=this._zoom);var n=this._zoom!==e;return this._zoom=e,this._lastCenter=t,this._pixelOrigin=this._getNewPixelOrigin(t),o?i?.pinch&&this.fire("zoom",i):((n||i?.pinch)&&this.fire("zoom",i),this.fire("move",i)),this},_moveEnd(t){return t&&this.fire("zoomend"),this.fire("moveend")},_stop(){return cancelAnimationFrame(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy(t){setPosition(this._mapPane,this._getMapPanePos().subtract(t))},_getZoomSpan(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents(t){this._targets={},(t?off:on)((this._targets[stamp(this._container)]=this)._container,"click dblclick pointerdown pointerup pointerover pointerout pointermove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&(t?this._resizeObserver.disconnect():(this._resizeObserver||(this._resizeObserver=new ResizeObserver(this._onResize.bind(this))),this._resizeObserver.observe(this._container))),this.options.transform3DLimit&&(t?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize(){cancelAnimationFrame(this._resizeRequest),this._resizeRequest=requestAnimationFrame(()=>{this.invalidateSize({debounceMoveend:!0})})},_onScroll(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd(){var t=this._getMapPanePos();Math.max(Math.abs(t.x),Math.abs(t.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets(t,e){let i=[],o,n=t.target||t.srcElement,s=!1;for(var a="pointerout"===e||"pointerover"===e;n;){if((o=this._targets[stamp(n)])&&("click"===e||"preclick"===e)&&this._draggableMoved(o)){s=!0;break}if(o&&o.listens(e,!0)){if(a&&!isExternalTarget(n,t))break;if(i.push(o),a)break}if(n===this._container)break;n=n.parentNode}return i=i.length||s||a||!this.listens(e,!0)?i:[this]},_isClickDisabled(t){for(;t&&t!==this._container;){if(t._leaflet_disable_click||!t.parentNode)return!0;t=t.parentNode}},_handleDOMEvent(t){var e,i=t.target??t.srcElement;!this._loaded||i._leaflet_disable_events||"click"===t.type&&this._isClickDisabled(i)||("pointerdown"===(e=t.type)&&preventOutline(i),this._fireDOMEvent(t,e))},_pointerEvents:["click","dblclick","pointerover","pointerout","contextmenu"],_fireDOMEvent(t,e,i){"click"===e&&this._fireDOMEvent(t,"preclick",i);let o=this._findEventTargets(t,e);if(i&&(i=i.filter(t=>t.listens(e,!0)),o=i.concat(o)),o.length){"contextmenu"===e&&preventDefault(t);var n,s,i=o[0],a={originalEvent:t};"keypress"!==t.type&&"keydown"!==t.type&&"keyup"!==t.type&&(n=i.getLatLng&&(!i._radius||i._radius<=10),a.containerPoint=n?this.latLngToContainerPoint(i.getLatLng()):this.pointerEventToContainerPoint(t),a.layerPoint=this.containerPointToLayerPoint(a.containerPoint),a.latlng=n?i.getLatLng():this.layerPointToLatLng(a.layerPoint));for(s of o)if(s.fire(e,a,!0),a.originalEvent._stopped||!1===s.options.bubblingPointerEvents&&this._pointerEvents.includes(e))return}},_draggableMoved(t){return(t=t.dragging&&t.dragging.enabled()?t:this).dragging&&t.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers(){for(var t of this._handlers)t.disable()},whenReady(t,e){return this._loaded?t.call(e||this,{target:this}):this.on("load",t,e),this},_getMapPanePos(){return getPosition(this._mapPane)},_moved(){var t=this._getMapPanePos();return t&&!t.equals([0,0])},_getTopLeftPoint(t,e){return(t&&void 0!==e?this._getNewPixelOrigin(t,e):this.getPixelOrigin()).subtract(this._getMapPanePos())},_getNewPixelOrigin(t,e){var i=this.getSize()._divideBy(2);return this.project(t,e)._subtract(i)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint(t,e,i){i=this._getNewPixelOrigin(i,e);return this.project(t,e)._subtract(i)},_latLngBoundsToNewLayerBounds(t,e,i){i=this._getNewPixelOrigin(i,e);return toBounds([this.project(t.getSouthWest(),e)._subtract(i),this.project(t.getNorthWest(),e)._subtract(i),this.project(t.getSouthEast(),e)._subtract(i),this.project(t.getNorthEast(),e)._subtract(i)])},_getCenterLayerPoint(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset(t){return this.latLngToLayerPoint(t).subtract(this._getCenterLayerPoint())},_limitCenter(t,e,i){var o,n;return!i||(o=this.project(t,e),n=this.getSize().divideBy(2),n=new Bounds(o.subtract(n),o.add(n)),n=this._getBoundsOffset(n,i,e),Math.abs(n.x)<=1&&Math.abs(n.y)<=1)?t:this.unproject(o.add(n),e)},_limitOffset(t,e){var i;return e?(i=this.getPixelBounds(),i=new Bounds(i.min.add(t),i.max.add(t)),t.add(this._getBoundsOffset(i,e))):t},_getBoundsOffset(t,e,i){e=toBounds(this.project(e.getNorthEast(),i),this.project(e.getSouthWest(),i)),i=e.min.subtract(t.min),e=e.max.subtract(t.max),t=this._rebound(i.x,-e.x),i=this._rebound(i.y,-e.y);return new Point(t,i)},_rebound(t,e){return 0<t+e?Math.round(t-e)/2:Math.max(0,Math.ceil(t))-Math.max(0,Math.floor(e))},_limitZoom(t){var e=this.getMinZoom(),i=this.getMaxZoom(),o=this.options.zoomSnap;return o&&(t=Math.round(t/o)*o),Math.max(e,Math.min(i,t))},_onPanTransitionStep(){this.fire("move")},_onPanTransitionEnd(){this._mapPane.classList.remove("leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan(t,e){t=this._getCenterOffset(t)._trunc();return!(!0!==e?.animate&&!this.getSize().contains(t)||(this.panBy(t,e),0))},_createAnimProxy(){this._proxy=create$1("div","leaflet-proxy leaflet-zoom-animated"),this._panes.mapPane.appendChild(this._proxy),this.on("zoomanim",this._animateProxyZoom,this),this.on("load moveend",this._animMoveEnd,this),on(this._proxy,"transitionend",this._catchTransitionEnd,this)},_animateProxyZoom(t){var e=this._proxy.style.transform;setTransform(this._proxy,this.project(t.center,t.zoom),this.getZoomScale(t.zoom,1)),e===this._proxy.style.transform&&this._animatingZoom&&this._onZoomTransitionEnd()},_animMoveEnd(){var t=this.getCenter(),e=this.getZoom();setTransform(this._proxy,this.project(t,e),this.getZoomScale(e,1))},_destroyAnimProxy(){this._proxy&&(off(this._proxy,"transitionend",this._catchTransitionEnd,this),this._proxy.remove(),this.off("zoomanim",this._animateProxyZoom,this),this.off("load moveend",this._animMoveEnd,this),delete this._proxy)},_catchTransitionEnd(t){this._animatingZoom&&t.propertyName.includes("transform")&&this._onZoomTransitionEnd()},_nothingToAnimate(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom(t,e,i){if(!this._animatingZoom){if(i??={},!this._zoomAnimated||!1===i.animate||this._nothingToAnimate()||Math.abs(e-this._zoom)>this.options.zoomAnimationThreshold)return!1;var o=this.getZoomScale(e),o=this._getCenterOffset(t)._divideBy(1-1/o);if(!0!==i.animate&&!this.getSize().contains(o))return!1;requestAnimationFrame(()=>{this._moveStart(!0,i.noMoveStart??!1)._animateZoom(t,e,!0)})}return!0},_animateZoom(t,e,i,o){this._mapPane&&(i&&(this._animatingZoom=!0,this._animateToCenter=t,this._animateToZoom=e,this._mapPane.classList.add("leaflet-zoom-anim")),this.fire("zoomanim",{center:t,zoom:e,noUpdate:o}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._transitionEndTimer=setTimeout(this._onZoomTransitionEnd.bind(this),250))},_onZoomTransitionEnd(){this._animatingZoom&&(this._mapPane&&this._mapPane.classList.remove("leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function createMap(t,e){return new Map$1(t,e)}let Control=Class.extend({options:{position:"topright"},initialize(t){setOptions(this,t)},getPosition(){return this.options.position},setPosition(t){var e=this._map;return e&&e.removeControl(this),this.options.position=t,e&&e.addControl(this),this},getContainer(){return this._container},addTo(t){this.remove(),this._map=t;var e=this._container=this.onAdd(t),i=this.getPosition(),t=t._controlCorners[i];return e.classList.add("leaflet-control"),i.includes("bottom")?t.insertBefore(e,t.firstChild):t.appendChild(e),this._map.on("unload",this.remove,this),this},remove(){return this._map&&(this._container.remove(),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null),this},_refocusOnMap(t){this._map&&t&&(0!==t.screenX||0!==t.screenY)&&this._map.getContainer().focus()}}),control=function(t){return new Control(t)},Layers=(Map$1.include({addControl(t){return t.addTo(this),this},removeControl(t){return t.remove(),this},_initControlPos(){let o=this._controlCorners={},n="leaflet-",s=this._controlContainer=create$1("div",n+"control-container",this._container);function t(t,e){var i=n+t+" "+n+e;o[t+e]=create$1("div",i,s)}t("top","left"),t("top","right"),t("bottom","left"),t("bottom","right")},_clearControlPos(){for(var t of Object.values(this._controlCorners))t.remove();this._controlContainer.remove(),delete this._controlCorners,delete this._controlContainer}}),Control.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction(t,e,i,o){return i<o?-1:o<i?1:0}},initialize(t,e,i){setOptions(this,i),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var[o,n]of Object.entries(t??{}))this._addLayer(n,o);for(var[s,a]of Object.entries(e??{}))this._addLayer(a,s,!0)},onAdd(t){this._initLayout(),this._update(),(this._map=t).on("zoomend",this._checkDisabledLayers,this);for(var e of this._layers)e.layer.on("add remove",this._onLayerChange,this);return this.options.collapsed||t.on("resize",this._expandIfNotCollapsed,this),this._container},addTo(t){return Control.prototype.addTo.call(this,t),this._expandIfNotCollapsed()},onRemove(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var t of this._layers)t.layer.off("add remove",this._onLayerChange,this);this._map.off("resize",this._expandIfNotCollapsed,this)},addBaseLayer(t,e){return this._addLayer(t,e),this._map?this._update():this},addOverlay(t,e){return this._addLayer(t,e,!0),this._map?this._update():this},removeLayer(t){t.off("add remove",this._onLayerChange,this);t=this._getLayer(stamp(t));return t&&this._layers.splice(this._layers.indexOf(t),1),this._map?this._update():this},expand(){this._container.classList.add("leaflet-control-layers-expanded"),this._section.style.height=null;var t=this._map.getSize().y-(this._container.offsetTop+50);return t<this._section.clientHeight?(this._section.classList.add("leaflet-control-layers-scrollbar"),this._section.style.height=t+"px"):this._section.classList.remove("leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse(t){return t&&("pointerleave"===t.type||"pointerout"===t.type)&&"touch"===t.pointerType||this._container.classList.remove("leaflet-control-layers-expanded"),this},_initLayout(){var t="leaflet-control-layers",e=this._container=create$1("div",t),i=this.options.collapsed,o=(disableClickPropagation(e),disableScrollPropagation(e),this._section=create$1("fieldset",t+"-list")),n=(i&&(this._map.on("click",this.collapse,this),on(e,{pointerenter:this._expandSafely,pointerleave:this.collapse},this)),this._layersLink=create$1("a",t+"-toggle",e));n.href="#",n.title="Layers",n.setAttribute("role","button"),on(n,{keydown(t){"Enter"===t.code&&this._expandSafely()},click(t){preventDefault(t),this._expandSafely()}},this),i||this.expand(),this._baseLayersList=create$1("div",t+"-base",o),this._separator=create$1("div",t+"-separator",o),this._overlaysList=create$1("div",t+"-overlays",o),e.appendChild(o)},_getLayer(t){for(var e of this._layers)if(e&&stamp(e.layer)===t)return e},_addLayer(t,e,i){this._map&&t.on("add remove",this._onLayerChange,this),this._layers.push({layer:t,name:e,overlay:i}),this.options.sortLayers&&this._layers.sort((t,e)=>this.options.sortFunction(t.layer,e.layer,t.name,e.name)),this.options.autoZIndex&&t.setZIndex&&(this._lastZIndex++,t.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update(){if(this._container){this._baseLayersList.replaceChildren(),this._overlaysList.replaceChildren(),this._layerControlInputs=[];let t,e,i=0;for(var o of this._layers)this._addItem(o),e||=o.overlay,t||=!o.overlay,i+=o.overlay?0:1;this.options.hideSingleBase&&(t=t&&1<i,this._baseLayersList.style.display=t?"":"none"),this._separator.style.display=e&&t?"":"none"}return this},_onLayerChange(t){this._handlingClick||this._update();var e=this._getLayer(stamp(t.target)),t=e.overlay?"add"===t.type?"overlayadd":"overlayremove":"add"===t.type?"baselayerchange":null;t&&this._map.fire(t,e)},_createRadioElement(t,e){t=`<input type="radio" class="leaflet-control-layers-selector" name="${t}"${e?' checked="checked"':""}/>`,e=document.createElement("div");return e.innerHTML=t,e.firstChild},_addItem(t){var e=document.createElement("label"),i=this._map.hasLayer(t.layer);let o;t.overlay?((o=document.createElement("input")).type="checkbox",o.className="leaflet-control-layers-selector",o.defaultChecked=i):o=this._createRadioElement("leaflet-base-layers_"+stamp(this),i),this._layerControlInputs.push(o),o.layerId=stamp(t.layer),on(o,"click",this._onInputClick,this);var i=document.createElement("span"),n=(i.innerHTML=" "+t.name,document.createElement("span"));return e.appendChild(n),n.appendChild(o),n.appendChild(i),(t.overlay?this._overlaysList:this._baseLayersList).appendChild(e),this._checkDisabledLayers(),e},_onInputClick(t){if(!this._preventClick){var e,i,o,n=this._layerControlInputs,s=[],a=[];this._handlingClick=!0;for(e of n){var r=this._getLayer(e.layerId).layer;(e.checked?s:(e.checked,a)).push(r)}for(i of a)this._map.hasLayer(i)&&this._map.removeLayer(i);for(o of s)this._map.hasLayer(o)||this._map.addLayer(o);this._handlingClick=!1,this._refocusOnMap(t)}},_checkDisabledLayers(){var t,e=this._layerControlInputs,i=this._map.getZoom();for(t of e){var o=this._getLayer(t.layerId).layer;t.disabled=void 0!==o.options.minZoom&&i<o.options.minZoom||void 0!==o.options.maxZoom&&i>o.options.maxZoom}},_expandIfNotCollapsed(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely(){let t=this._section;this._preventClick=!0,on(t,"click",preventDefault),this.expand(),setTimeout(()=>{off(t,"click",preventDefault),this._preventClick=!1})}})),layers=function(t,e,i){return new Layers(t,e,i)},Zoom=Control.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd(t){var e="leaflet-control-zoom",i=create$1("div",e+" leaflet-bar"),o=this.options;return this._zoomInButton=this._createButton(o.zoomInText,o.zoomInTitle,e+"-in",i,this._zoomIn),this._zoomOutButton=this._createButton(o.zoomOutText,o.zoomOutTitle,e+"-out",i,this._zoomOut),this._updateDisabled(),t.on("zoomend zoomlevelschange",this._updateDisabled,this),i},onRemove(t){t.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable(){return this._disabled=!0,this._updateDisabled(),this},enable(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn(t){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(t.shiftKey?3:1))},_zoomOut(t){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(t.shiftKey?3:1))},_createButton(t,e,i,o,n){i=create$1("a",i,o);return i.innerHTML=t,i.href="#",i.title=e,i.setAttribute("role","button"),i.setAttribute("aria-label",e),disableClickPropagation(i),on(i,"click",stop),on(i,"click",n,this),on(i,"click",this._refocusOnMap,this),i},_updateDisabled(){var t=this._map,e="leaflet-disabled";this._zoomInButton.classList.remove(e),this._zoomOutButton.classList.remove(e),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),!this._disabled&&t._zoom!==t.getMinZoom()||(this._zoomOutButton.classList.add(e),this._zoomOutButton.setAttribute("aria-disabled","true")),!this._disabled&&t._zoom!==t.getMaxZoom()||(this._zoomInButton.classList.add(e),this._zoomInButton.setAttribute("aria-disabled","true"))}}),zoom=(Map$1.mergeOptions({zoomControl:!0}),Map$1.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Zoom,this.addControl(this.zoomControl))}),function(t){return new Zoom(t)}),Scale=Control.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd(t){var e="leaflet-control-scale",i=create$1("div",e),o=this.options;return this._addScales(o,e+"-line",i),t.on(o.updateWhenIdle?"moveend":"move",this._update,this),t.whenReady(this._update,this),i},onRemove(t){t.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales(t,e,i){t.metric&&(this._mScale=create$1("div",e,i)),t.imperial&&(this._iScale=create$1("div",e,i))},_update(){var t=this._map,e=t.getSize().y/2,t=t.distance(t.containerPointToLatLng([0,e]),t.containerPointToLatLng([this.options.maxWidth,e]));this._updateScales(t)},_updateScales(t){this.options.metric&&t&&this._updateMetric(t),this.options.imperial&&t&&this._updateImperial(t)},_updateMetric(t){var e=this._getRoundNum(t);this._updateScale(this._mScale,e<1e3?e+" m":e/1e3+" km",e/t)},_updateImperial(t){var e,i,t=3.2808399*t;5280<t?(i=this._getRoundNum(e=t/5280),this._updateScale(this._iScale,i+" mi",i/e)):(i=this._getRoundNum(t),this._updateScale(this._iScale,i+" ft",i/t))},_updateScale(t,e,i){t.style.width=Math.round(this.options.maxWidth*i)+"px",t.innerHTML=e},_getRoundNum(t){var e=10**((""+Math.floor(t)).length-1),t=t/e;return e*(10<=t?10:5<=t?5:3<=t?3:2<=t?2:1)}}),scale=function(t){return new Scale(t)},ukrainianFlag='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Attribution=Control.extend({options:{position:"bottomright",prefix:`<a target="_blank" href="https://leafletjs.com" title="A JavaScript library for interactive maps">${ukrainianFlag}Leaflet</a>`},initialize(t){setOptions(this,t),this._attributions={}},onAdd(t){(t.attributionControl=this)._container=create$1("div","leaflet-control-attribution"),disableClickPropagation(this._container);for(var e of Object.values(t._layers))e.getAttribution&&this.addAttribution(e.getAttribution());return this._update(),t.on("layeradd",this._addAttribution,this),this._container},onRemove(t){t.off("layeradd",this._addAttribution,this)},_addAttribution(t){t.layer.getAttribution&&(this.addAttribution(t.layer.getAttribution()),t.layer.once("remove",()=>this.removeAttribution(t.layer.getAttribution())))},setPrefix(t){return this.options.prefix=t,this._update(),this},addAttribution(t){return t&&(this._attributions[t]||(this._attributions[t]=0),this._attributions[t]++,this._update()),this},removeAttribution(t){return t&&this._attributions[t]&&(this._attributions[t]--,this._update()),this},_update(){var t,e;this._map&&(t=Object.keys(this._attributions).filter(t=>this._attributions[t]),e=[],this.options.prefix&&e.push(this.options.prefix),t.length&&e.push(t.join(", ")),this._container.innerHTML=e.join(' <span aria-hidden="true">|</span> '))}}),attribution=(Map$1.mergeOptions({attributionControl:!0}),Map$1.addInitHook(function(){this.options.attributionControl&&(new Attribution).addTo(this)}),function(t){return new Attribution(t)}),Handler=(Control.Layers=Layers,Control.Zoom=Zoom,Control.Scale=Scale,Control.Attribution=Attribution,control.layers=layers,control.zoom=zoom,control.scale=scale,control.attribution=attribution,Class.extend({initialize(t){this._map=t},enable(){return this._enabled||(this._enabled=!0,this.addHooks()),this},disable(){return this._enabled&&(this._enabled=!1,this.removeHooks()),this},enabled(){return!!this._enabled}})),Draggable=(Handler.addTo=function(t,e){return t.addHandler(e,this),this},Evented.extend({options:{clickTolerance:3},initialize(t,e,i,o){setOptions(this,o),this._element=t,this._dragStartTarget=e??t,this._preventOutline=i},enable(){this._enabled||(on(this._dragStartTarget,"pointerdown",this._onDown,this),this._enabled=!0)},disable(){this._enabled&&(Draggable._dragging===this&&this.finishDrag(!0),off(this._dragStartTarget,"pointerdown",this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown(t){var e;this._enabled&&(this._moved=!1,this._element.classList.contains("leaflet-zoom-anim")||(1!==getPointers().length?Draggable._dragging===this&&this.finishDrag():Draggable._dragging||t.shiftKey||0!==t.button&&"touch"!==t.pointerType||((Draggable._dragging=this)._preventOutline&&preventOutline(this._element),disableImageDrag(),disableTextSelection(),this._moving)||(this.fire("down"),e=getSizedParentNode(this._element),this._startPoint=new Point(t.clientX,t.clientY),this._startPos=getPosition(this._element),this._parentScale=getScale(e),on(document,"pointermove",this._onMove,this),on(document,"pointerup pointercancel",this._onUp,this))))},_onMove(t){var e;this._enabled&&(1<getPointers().length?this._moved=!0:!(e=new Point(t.clientX,t.clientY)._subtract(this._startPoint)).x&&!e.y||Math.abs(e.x)+Math.abs(e.y)<this.options.clickTolerance||(e.x/=this._parentScale.x,e.y/=this._parentScale.y,t.cancelable&&preventDefault(t),this._moved||(this.fire("dragstart"),this._moved=!0,document.body.classList.add("leaflet-dragging"),this._lastTarget=t.target??t.srcElement,this._lastTarget.classList.add("leaflet-drag-target")),this._newPos=this._startPos.add(e),this._moving=!0,this._lastEvent=t,this._updatePosition()))},_updatePosition(){var t={originalEvent:this._lastEvent};this.fire("predrag",t),setPosition(this._element,this._newPos),this.fire("drag",t)},_onUp(){this._enabled&&this.finishDrag()},finishDrag(t){document.body.classList.remove("leaflet-dragging"),this._lastTarget&&(this._lastTarget.classList.remove("leaflet-drag-target"),this._lastTarget=null),off(document,"pointermove",this._onMove,this),off(document,"pointerup pointercancel",this._onUp,this),enableImageDrag(),enableTextSelection();var e=this._moved&&this._moving;this._moving=!1,Draggable._dragging=!1,e&&this.fire("dragend",{noInertia:t,distance:this._newPos.distanceTo(this._startPos)})}}));function clipPolygon(t,e,i){let o,n,s,a,r,h,l,d,p;var _=[1,4,2,8];for(n=0,l=t.length;n<l;n++)t[n]._code=_getBitCode(t[n],e);for(a=0;a<4;a++){for(d=_[a],o=[],n=0,l=t.length,s=l-1;n<l;s=n++)r=t[n],h=t[s],r._code&d?h._code&d||((p=_getEdgeIntersection(h,r,d,e,i))._code=_getBitCode(p,e),o.push(p)):(h._code&d&&((p=_getEdgeIntersection(h,r,d,e,i))._code=_getBitCode(p,e),o.push(p)),o.push(r));t=o}return t}function polygonCenter(t,e){let i,o,n,s,a,r,h,l,d;if(!t||0===t.length)throw new Error("latlngs not passed");isFlat(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);let p=toLatLng([0,0]);var _=toLatLngBounds(t),u=(_.getNorthWest().distanceTo(_.getSouthWest())*_.getNorthEast().distanceTo(_.getNorthWest())<1700&&(p=centroid(t)),t.length),c=[];for(i=0;i<u;i++){var m=toLatLng(t[i]);c.push(e.project(toLatLng([m.lat-p.lat,m.lng-p.lng])))}for(r=h=l=0,i=0,o=u-1;i<u;o=i++)n=c[i],s=c[o],a=n.y*s.x-s.y*n.x,h+=(n.x+s.x)*a,l+=(n.y+s.y)*a,r+=3*a;d=0===r?c[0]:[h/r,l/r];_=e.unproject(toPoint(d));return toLatLng([_.lat+p.lat,_.lng+p.lng])}function centroid(t){let e=0,i=0,o=0;for(var n of t){n=toLatLng(n);e+=n.lat,i+=n.lng,o++}return toLatLng([e/o,i/o])}var PolyUtil={__proto__:null,centroid:centroid,clipPolygon:clipPolygon,polygonCenter:polygonCenter};function simplify(t,e){return e&&t.length?_simplifyDP(t=_reducePoints(t,e=e*e),e):t.slice()}function pointToSegmentDistance(t,e,i){return Math.sqrt(_sqClosestPointOnSegment(t,e,i,!0))}function closestPointOnSegment(t,e,i){return _sqClosestPointOnSegment(t,e,i)}function _simplifyDP(t,e){var i=t.length,o=new("undefined"!=typeof Uint8Array?Uint8Array:Array)(i);o[0]=o[i-1]=1,_simplifyDPStep(t,o,e,0,i-1);let n;var s=[];for(n=0;n<i;n++)o[n]&&s.push(t[n]);return s}function _simplifyDPStep(t,e,i,o,n){let s=0,a,r,h;for(r=o+1;r<=n-1;r++)(h=_sqClosestPointOnSegment(t[r],t[o],t[n],!0))>s&&(a=r,s=h);s>i&&(e[a]=1,_simplifyDPStep(t,e,i,o,a),_simplifyDPStep(t,e,i,a,n))}function _reducePoints(e,i){var o=[e[0]];let n=0;for(let t=1;t<e.length;t++)_sqDist(e[t],e[n])>i&&(o.push(e[t]),n=t);return n<e.length-1&&o.push(e[e.length-1]),o}let _lastCode;function clipSegment(t,e,i,o,n){let s=o?_lastCode:_getBitCode(t,i),a=_getBitCode(e,i),r,h,l;for(_lastCode=a;;){if(!(s|a))return[t,e];if(s&a)return!1;l=_getBitCode(h=_getEdgeIntersection(t,e,r=s||a,i,n),i),r===s?(t=h,s=l):(e=h,a=l)}}function _getEdgeIntersection(t,e,i,o,n){var s=e.x-t.x,e=e.y-t.y,a=o.min,o=o.max;let r,h;return 8&i?(r=t.x+s*(o.y-t.y)/e,h=o.y):4&i?(r=t.x+s*(a.y-t.y)/e,h=a.y):2&i?(r=o.x,h=t.y+e*(o.x-t.x)/s):1&i&&(r=a.x,h=t.y+e*(a.x-t.x)/s),new Point(r,h,n)}function _getBitCode(t,e){let i=0;return t.x<e.min.x?i|=1:t.x>e.max.x&&(i|=2),t.y<e.min.y?i|=4:t.y>e.max.y&&(i|=8),i}function _sqDist(t,e){var i=e.x-t.x,e=e.y-t.y;return i*i+e*e}function _sqClosestPointOnSegment(t,e,i,o){let n=e.x,s=e.y,a=i.x-n,r=i.y-s,h;e=a*a+r*r;return 0<e&&(1<(h=((t.x-n)*a+(t.y-s)*r)/e)?(n=i.x,s=i.y):0<h&&(n+=a*h,s+=r*h)),a=t.x-n,r=t.y-s,o?a*a+r*r:new Point(n,s)}function isFlat(t){return!Array.isArray(t[0])||"object"!=typeof t[0][0]&&void 0!==t[0][0]}function polylineCenter(t,e){let i,o,n,s,a,r,h,l;if(!t||0===t.length)throw new Error("latlngs not passed");isFlat(t)||(console.warn("latlngs are not flat! Only the first ring will be used"),t=t[0]);let d=toLatLng([0,0]);var p=toLatLngBounds(t),_=(p.getNorthWest().distanceTo(p.getSouthWest())*p.getNorthEast().distanceTo(p.getNorthWest())<1700&&(d=centroid(t)),t.length),u=[];for(i=0;i<_;i++){var c=toLatLng(t[i]);u.push(e.project(toLatLng([c.lat-d.lat,c.lng-d.lng])))}for(i=0,o=0;i<_-1;i++)o+=u[i].distanceTo(u[i+1])/2;if(0===o)l=u[0];else for(i=0,s=0;i<_-1;i++)if(a=u[i],r=u[i+1],n=a.distanceTo(r),(s+=n)>o){h=(s-o)/n,l=[r.x-h*(r.x-a.x),r.y-h*(r.y-a.y)];break}p=e.unproject(toPoint(l));return toLatLng([p.lat+d.lat,p.lng+d.lng])}var LineUtil={__proto__:null,_getBitCode:_getBitCode,_getEdgeIntersection:_getEdgeIntersection,_sqClosestPointOnSegment:_sqClosestPointOnSegment,clipSegment:clipSegment,closestPointOnSegment:closestPointOnSegment,isFlat:isFlat,pointToSegmentDistance:pointToSegmentDistance,polylineCenter:polylineCenter,simplify:simplify};let LonLat={project(t){return new Point(t.lng,t.lat)},unproject(t){return new LatLng(t.y,t.x)},bounds:new Bounds([-180,-90],[180,90])},Mercator={R:6378137,R_MINOR:6356752.314245179,bounds:new Bounds([-20037508.34279,-15496570.73972],[20037508.34279,18764656.23138]),project(t){var e=Math.PI/180,i=this.R,o=this.R_MINOR/i,o=Math.sqrt(1-o*o),n=t.lat*e,s=o*Math.sin(n),s=Math.tan(Math.PI/4-n/2)/((1-s)/(1+s))**(o/2),n=-i*Math.log(Math.max(s,1e-10));return new Point(t.lng*e*i,n)},unproject(t){var e=180/Math.PI,i=this.R,o=this.R_MINOR/i,n=Math.sqrt(1-o*o),s=Math.exp(-t.y/i);let a=Math.PI/2-2*Math.atan(s);for(let t=0,e=.1,i;t<15&&1e-7<Math.abs(e);t++)i=n*Math.sin(a),e=Math.PI/2-2*Math.atan(s*((1-i)/(1+i))**(n/2))-a,a+=e;return new LatLng(a*e,t.x*e/i)}};var index={__proto__:null,LonLat:LonLat,Mercator:Mercator,SphericalMercator:SphericalMercator};let EPSG3395={...Earth,code:"EPSG:3395",projection:Mercator,transformation:(()=>{var t=.5/(Math.PI*Mercator.R);return toTransformation(t,.5,-t,.5)})()},EPSG4326={...Earth,code:"EPSG:4326",projection:LonLat,transformation:toTransformation(1/180,1,-1/180,.5)},Simple={...CRS,projection:LonLat,transformation:toTransformation(1,0,-1,0),scale(t){return 2**t},zoom(t){return Math.log(t)/Math.LN2},distance(t,e){var i=e.lng-t.lng,e=e.lat-t.lat;return Math.sqrt(i*i+e*e)},infinite:!0},Layer=(CRS.Earth=Earth,CRS.EPSG3395=EPSG3395,CRS.EPSG3857=EPSG3857,CRS.EPSG900913=EPSG900913,CRS.EPSG4326=EPSG4326,CRS.Simple=Simple,Evented.extend({options:{pane:"overlayPane",attribution:null,bubblingPointerEvents:!0},addTo(t){return t.addLayer(this),this},remove(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom(t){return t&&t.removeLayer(this),this},getPane(t){return this._map.getPane(t?this.options[t]||t:this.options.pane)},addInteractiveTarget(t){return this._map._targets[stamp(t)]=this},removeInteractiveTarget(t){return delete this._map._targets[stamp(t)],this},getAttribution(){return this.options.attribution},_layerAdd(t){let e=t.target;if(e.hasLayer(this)){if(this._map=e,this._zoomAnimated=e._zoomAnimated,this.getEvents){let t=this.getEvents();e.on(t,this),this.once("remove",()=>e.off(t,this))}this.onAdd(e),this.fire("add"),e.fire("layeradd",{layer:this})}}})),LayerGroup=(Map$1.include({addLayer(t){var e;if(t._layerAdd)return e=stamp(t),this._layers[e]||((this._layers[e]=t)._mapToAdd=this,t.beforeAdd&&t.beforeAdd(this),this.whenReady(t._layerAdd,t)),this;throw new Error("The provided object is not a Layer.")},removeLayer(t){var e=stamp(t);return this._layers[e]&&(this._loaded&&t.onRemove(this),delete this._layers[e],this._loaded&&(this.fire("layerremove",{layer:t}),t.fire("remove")),t._map=t._mapToAdd=null),this},hasLayer(t){return stamp(t)in this._layers},eachLayer(t,e){for(var i of Object.values(this._layers))t.call(e,i);return this},_addLayers(t){for(var e of t=t?Array.isArray(t)?t:[t]:[])this.addLayer(e)},_addZoomLimit(t){isNaN(t.options.maxZoom)&&isNaN(t.options.minZoom)||(this._zoomBoundLayers[stamp(t)]=t,this._updateZoomLevels())},_removeZoomLimit(t){t=stamp(t);this._zoomBoundLayers[t]&&(delete this._zoomBoundLayers[t],this._updateZoomLevels())},_updateZoomLevels(){let t=1/0,e=-1/0;var i,o=this._getZoomSpan();for(i of Object.values(this._zoomBoundLayers)){var n=i.options;t=Math.min(t,n.minZoom??1/0),e=Math.max(e,n.maxZoom??-1/0)}this._layersMaxZoom=e===-1/0?void 0:e,this._layersMinZoom=t===1/0?void 0:t,o!==this._getZoomSpan()&&this.fire("zoomlevelschange"),void 0===this.options.maxZoom&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),void 0===this.options.minZoom&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}}),Layer.extend({initialize(t,e){setOptions(this,e),this._layers={};for(var i of t??[])this.addLayer(i)},addLayer(t){var e=this.getLayerId(t);return this._layers[e]=t,this._map&&this._map.addLayer(t),this},removeLayer(t){t=t in this._layers?t:this.getLayerId(t);return this._map&&this._layers[t]&&this._map.removeLayer(this._layers[t]),delete this._layers[t],this},hasLayer(t){return("number"==typeof t?t:this.getLayerId(t))in this._layers},clearLayers(){return this.eachLayer(this.removeLayer,this)},invoke(t,...e){for(var i of Object.values(this._layers))i[t]&&i[t].apply(i,e);return this},onAdd(t){this.eachLayer(t.addLayer,t)},onRemove(t){this.eachLayer(t.removeLayer,t)},eachLayer(t,e){for(var i of Object.values(this._layers))t.call(e,i);return this},getLayer(t){return this._layers[t]},getLayers(){var t=[];return this.eachLayer(t.push,t),t},setZIndex(t){return this.invoke("setZIndex",t)},getLayerId(t){return stamp(t)}})),layerGroup=function(t,e){return new LayerGroup(t,e)},FeatureGroup=LayerGroup.extend({addLayer(t){return this.hasLayer(t)?this:(t.addEventParent(this),LayerGroup.prototype.addLayer.call(this,t),this.fire("layeradd",{layer:t}))},removeLayer(t){return this.hasLayer(t)?((t=t in this._layers?this._layers[t]:t).removeEventParent(this),LayerGroup.prototype.removeLayer.call(this,t),this.fire("layerremove",{layer:t})):this},setStyle(t){return this.invoke("setStyle",t)},bringToFront(){return this.invoke("bringToFront")},bringToBack(){return this.invoke("bringToBack")},getBounds(){var t,e=new LatLngBounds;for(t of Object.values(this._layers))e.extend(t.getBounds?t.getBounds():t.getLatLng());return e}}),featureGroup=function(t,e){return new FeatureGroup(t,e)},Icon=Class.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize(t){setOptions(this,t)},createIcon(t){return this._createIcon("icon",t)},createShadow(t){return this._createIcon("shadow",t)},_createIcon(t,e){var i=this._getIconUrl(t);if(i)return i=this._createImg(i,e&&"IMG"===e.tagName?e:null),this._setIconStyles(i,t),!this.options.crossOrigin&&""!==this.options.crossOrigin||(i.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),i;if("icon"===t)throw new Error("iconUrl not set in Icon options (see the docs).");return null},_setIconStyles(t,e){var i=this.options;let o=i[e+"Size"];var n=toPoint(o="number"==typeof o?[o,o]:o),s=toPoint("shadow"===e&&i.shadowAnchor||i.iconAnchor||n&&n.divideBy(2,!0));t.className=`leaflet-marker-${e} `+(i.className||""),s&&(t.style.marginLeft=-s.x+"px",t.style.marginTop=-s.y+"px"),n&&(t.style.width=n.x+"px",t.style.height=n.y+"px")},_createImg(t,e){return(e??=document.createElement("img")).src=t,e},_getIconUrl(t){return Browser.retina&&this.options[t+"RetinaUrl"]||this.options[t+"Url"]}});function icon(t){return new Icon(t)}let IconDefault=Icon.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl(t){IconDefault.imagePath||(IconDefault.imagePath=this._detectIconPath());t=Icon.prototype._getIconUrl.call(this,t);return t?(this.options.imagePath||IconDefault.imagePath)+t:null},_stripUrl(t){function e(t,e,i){return(e=e.exec(t))&&e[i]}return(t=e(t,/^url\((['"])?(.+)\1\)$/,2))&&e(t,/^(.*)marker-icon\.png$/,1)},_detectIconPath(){var t=create$1("div","leaflet-default-icon-path",document.body),e=this._stripUrl(getComputedStyle(t).backgroundImage);return document.body.removeChild(t),e||((t=document.querySelector('link[href$="leaflet.css"]'))?t.href.substring(0,t.href.length-"leaflet.css".length-1):"")}}),MarkerDrag=Handler.extend({initialize(t){this._marker=t},addHooks(){var t=this._marker._icon;this._draggable||(this._draggable=new Draggable(t,t,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),t.classList.add("leaflet-marker-draggable")},removeHooks(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&this._marker._icon.classList.remove("leaflet-marker-draggable")},moved(){return this._draggable?._moved},_adjustPan(t){var e=this._marker,i=e._map,o=this._marker.options.autoPanSpeed,n=this._marker.options.autoPanPadding,s=getPosition(e._icon),a=i.getPixelBounds(),r=i.getPixelOrigin(),r=toBounds(a.min._subtract(r).add(n),a.max._subtract(r).subtract(n));r.contains(s)||(n=toPoint((Math.max(r.max.x,s.x)-r.max.x)/(a.max.x-r.max.x)-(Math.min(r.min.x,s.x)-r.min.x)/(a.min.x-r.min.x),(Math.max(r.max.y,s.y)-r.max.y)/(a.max.y-r.max.y)-(Math.min(r.min.y,s.y)-r.min.y)/(a.min.y-r.min.y)).multiplyBy(o),i.panBy(n,{animate:!1}),this._draggable._newPos._add(n),this._draggable._startPos._add(n),setPosition(e._icon,this._draggable._newPos),this._onDrag(t),this._panRequest=requestAnimationFrame(this._adjustPan.bind(this,t)))},_onDragStart(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag(t){this._marker.options.autoPan&&(cancelAnimationFrame(this._panRequest),this._panRequest=requestAnimationFrame(this._adjustPan.bind(this,t)))},_onDrag(t){var e=this._marker,i=e._shadow,o=getPosition(e._icon),n=e._map.layerPointToLatLng(o);i&&setPosition(i,o),e._latlng=n,t.latlng=n,t.oldLatLng=this._oldLatLng,e.fire("move",t).fire("drag",t)},_onDragEnd(t){cancelAnimationFrame(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",t)}}),Marker=Layer.extend({options:{icon:new IconDefault,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingPointerEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize(t,e){setOptions(this,e),this._latlng=toLatLng(t)},onAdd(t){this._zoomAnimated=this._zoomAnimated&&t.options.markerZoomAnimation,this._zoomAnimated&&t.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove(t){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&t.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents(){return{zoom:this.update,viewreset:this.update}},getLatLng(){return this._latlng},setLatLng(t){var e=this._latlng;return this._latlng=toLatLng(t),this.update(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},setZIndexOffset(t){return this.options.zIndexOffset=t,this.update()},getIcon(){return this.options.icon},setIcon(t){return this.options.icon=t,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement(){return this._icon},update(){var t;return this._icon&&this._map&&(t=this._map.latLngToLayerPoint(this._latlng).round(),this._setPos(t)),this},_initIcon(){var t=this.options,e="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),i=t.icon.createIcon(this._icon);let o=!1;i!==this._icon&&(this._icon&&this._removeIcon(),o=!0,t.title&&(i.title=t.title),"IMG"===i.tagName)&&(i.alt=t.alt??""),i.classList.add(e),t.keyboard&&(i.tabIndex="0",i.setAttribute("role","button")),this._icon=i,t.riseOnHover&&this.on({pointerover:this._bringToFront,pointerout:this._resetZIndex}),this.options.autoPanOnFocus&&on(i,"focus",this._panOnFocus,this);i=t.icon.createShadow(this._shadow);let n=!1;i!==this._shadow&&(this._removeShadow(),n=!0),i&&(i.classList.add(e),i.alt=""),this._shadow=i,t.opacity<1&&this._updateOpacity(),o&&this.getPane().appendChild(this._icon),this._initInteraction(),i&&n&&this.getPane(t.shadowPane).appendChild(this._shadow)},_removeIcon(){this.options.riseOnHover&&this.off({pointerover:this._bringToFront,pointerout:this._resetZIndex}),this.options.autoPanOnFocus&&off(this._icon,"focus",this._panOnFocus,this),this._icon.remove(),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow(){this._shadow&&this._shadow.remove(),this._shadow=null},_setPos(t){this._icon&&setPosition(this._icon,t),this._shadow&&setPosition(this._shadow,t),this._zIndex=t.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex(t){this._icon&&(this._icon.style.zIndex=this._zIndex+t)},_animateZoom(t){t=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center).round();this._setPos(t)},_initInteraction(){if(this.options.interactive&&(this._icon.classList.add("leaflet-interactive"),this.addInteractiveTarget(this._icon),MarkerDrag)){let t=this.options.draggable;this.dragging&&(t=this.dragging.enabled(),this.dragging.disable()),this.dragging=new MarkerDrag(this),t&&this.dragging.enable()}},setOpacity(t){return this.options.opacity=t,this._map&&this._updateOpacity(),this},_updateOpacity(){var t=this.options.opacity;this._icon&&(this._icon.style.opacity=t),this._shadow&&(this._shadow.style.opacity=t)},_bringToFront(){this._updateZIndex(this.options.riseOffset)},_resetZIndex(){this._updateZIndex(0)},_panOnFocus(){var t,e,i=this._map;i&&(t=(e=this.options.icon.options).iconSize?toPoint(e.iconSize):toPoint(0,0),e=e.iconAnchor?toPoint(e.iconAnchor):toPoint(0,0),i.panInside(this._latlng,{paddingTopLeft:e,paddingBottomRight:t.subtract(e)}))},_getPopupAnchor(){return this.options.icon.options.popupAnchor},_getTooltipAnchor(){return this.options.icon.options.tooltipAnchor}});function marker(t,e){return new Marker(t,e)}let Path=Layer.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingPointerEvents:!0},beforeAdd(t){this._renderer=t.getRenderer(this)},onAdd(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove(){this._renderer._removePath(this)},redraw(){return this._map&&this._renderer._updatePath(this),this},setStyle(t){return setOptions(this,t),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke)&&t&&Object.hasOwn(t,"weight")&&this._updateBounds(),this},bringToFront(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack(){return this._renderer&&this._renderer._bringToBack(this),this},getElement(){return this._path},_reset(){this._project(),this._update()},_clickTolerance(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),CircleMarker=Path.extend({options:{fill:!0,radius:10},initialize(t,e){setOptions(this,e),this._latlng=toLatLng(t),this._radius=this.options.radius},setLatLng(t){var e=this._latlng;return this._latlng=toLatLng(t),this.redraw(),this.fire("move",{oldLatLng:e,latlng:this._latlng})},getLatLng(){return this._latlng},setRadius(t){return this.options.radius=this._radius=t,this.redraw()},getRadius(){return this._radius},setStyle(t){var e=t?.radius??this._radius;return Path.prototype.setStyle.call(this,t),this.setRadius(e),this},_project(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds(){var t=this._radius,e=this._radiusY??t,i=this._clickTolerance(),t=[t+i,e+i];this._pxBounds=new Bounds(this._point.subtract(t),this._point.add(t))},_update(){this._map&&this._updatePath()},_updatePath(){this._renderer._updateCircle(this)},_empty(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint(t){return t.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function circleMarker(t,e){return new CircleMarker(t,e)}let Circle=CircleMarker.extend({initialize(t,e){if(setOptions(this,e),this._latlng=toLatLng(t),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius(t){return this._mRadius=t,this.redraw()},getRadius(){return this._mRadius},getBounds(){var t=[this._radius,this._radiusY??this._radius];return new LatLngBounds(this._map.layerPointToLatLng(this._point.subtract(t)),this._map.layerPointToLatLng(this._point.add(t)))},setStyle:Path.prototype.setStyle,_project(){var e=this._latlng.lng,i=this._latlng.lat,o=this._map,t=o.options.crs;if(t.distance===Earth.distance){var n=Math.PI/180,s=this._mRadius/Earth.R/n,a=o.project([i+s,e]),r=o.project([i-s,e]),r=a.add(r).divideBy(2),h=o.unproject(r).lat;let t=Math.acos((Math.cos(s*n)-Math.sin(i*n)*Math.sin(h*n))/(Math.cos(i*n)*Math.cos(h*n)))/n;!isNaN(t)&&0!==t||(t=s/Math.cos(Math.PI/180*i)),this._point=r.subtract(o.getPixelOrigin()),this._radius=isNaN(t)?0:r.x-o.project([h,e-t]).x,this._radiusY=r.y-a.y}else{n=t.unproject(t.project(this._latlng).subtract([this._mRadius,0]));this._point=o.latLngToLayerPoint(this._latlng),this._radius=Math.abs(this._point.x-o.latLngToLayerPoint(n).x)}this._updateBounds()}});function circle(t,e,i){return new Circle(t,e,i)}let Polyline=Path.extend({options:{smoothFactor:1,noClip:!1},initialize(t,e){setOptions(this,e),this._setLatLngs(t)},getLatLngs(){return this._latlngs},setLatLngs(t){return this._setLatLngs(t),this.redraw()},isEmpty(){return!this._latlngs.length},closestLayerPoint(i){let o=1/0,n=null,s,a;var r,h=_sqClosestPointOnSegment;for(r of this._parts)for(let t=1,e=r.length;t<e;t++){var l=h(i,s=r[t-1],a=r[t],!0);l<o&&(o=l,n=h(i,s,a))}return n&&(n.distance=Math.sqrt(o)),n},getCenter(){if(this._map)return polylineCenter(this._defaultShape(),this._map.options.crs);throw new Error("Must add layer to map before using getCenter()")},getBounds(){return this._bounds},addLatLng(t,e){return e??=this._defaultShape(),t=toLatLng(t),e.push(t),this._bounds.extend(t),this.redraw()},_setLatLngs(t){this._bounds=new LatLngBounds,this._latlngs=this._convertLatLngs(t)},_defaultShape(){return isFlat(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs(i){var o=[],n=isFlat(i);for(let t=0,e=i.length;t<e;t++)n?(o[t]=toLatLng(i[t]),this._bounds.extend(o[t])):o[t]=this._convertLatLngs(i[t]);return o},_project(){var t=new Bounds;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,t),this._bounds.isValid()&&t.isValid()&&(this._rawPxBounds=t,this._updateBounds())},_updateBounds(){var t=this._clickTolerance(),t=new Point(t,t);this._rawPxBounds&&(this._pxBounds=new Bounds([this._rawPxBounds.min.subtract(t),this._rawPxBounds.max.add(t)]))},_projectLatlngs(t,e,i){var o;t[0]instanceof LatLng?((o=t.map(t=>this._map.latLngToLayerPoint(t))).forEach(t=>i.extend(t)),e.push(o)):t.forEach(t=>this._projectLatlngs(t,e,i))},_clipPoints(){var r=this._renderer._bounds;if(this._parts=[],this._pxBounds&&this._pxBounds.intersects(r))if(this.options.noClip)this._parts=this._rings;else{var h=this._parts;let t,e,i,o,n,s,a;for(t=0,i=0,o=this._rings.length;t<o;t++)for(a=this._rings[t],e=0,n=a.length;e<n-1;e++)(s=clipSegment(a[e],a[e+1],r,e,!0))&&(h[i]??=[],h[i].push(s[0]),s[1]===a[e+1]&&e!==n-2||(h[i].push(s[1]),i++))}},_simplifyPoints(){var i=this._parts,o=this.options.smoothFactor;for(let t=0,e=i.length;t<e;t++)i[t]=simplify(i[t],o)},_update(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath(){this._renderer._updatePoly(this)},_containsPoint(t,e){let i,o,n,s,a,r;var h=this._clickTolerance();if(this._pxBounds&&this._pxBounds.contains(t))for(i=0,s=this._parts.length;i<s;i++)for(r=this._parts[i],o=0,a=r.length,n=a-1;o<a;n=o++)if((e||0!==o)&&pointToSegmentDistance(t,r[n],r[o])<=h)return!0;return!1}});function polyline(t,e){return new Polyline(t,e)}let Polygon=Polyline.extend({options:{fill:!0},isEmpty(){return!this._latlngs.length||!this._latlngs[0].length},getCenter(){if(this._map)return polygonCenter(this._defaultShape(),this._map.options.crs);throw new Error("Must add layer to map before using getCenter()")},_convertLatLngs(t){var t=Polyline.prototype._convertLatLngs.call(this,t),e=t.length;return 2<=e&&t[0]instanceof LatLng&&t[0].equals(t[e-1])&&t.pop(),t},_setLatLngs(t){Polyline.prototype._setLatLngs.call(this,t),isFlat(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape(){return(isFlat(this._latlngs[0])?this._latlngs:this._latlngs[0])[0]},_clipPoints(){let t=this._renderer._bounds;var e=this.options.weight,e=new Point(e,e);if(t=new Bounds(t.min.subtract(e),t.max.add(e)),this._parts=[],this._pxBounds&&this._pxBounds.intersects(t))if(this.options.noClip)this._parts=this._rings;else for(var i of this._rings){i=clipPolygon(i,t,!0);i.length&&this._parts.push(i)}},_updatePath(){this._renderer._updatePoly(this,!0)},_containsPoint(t){let e=!1,i,o,n,s,a,r,h,l;if(!this._pxBounds||!this._pxBounds.contains(t))return!1;for(s=0,h=this._parts.length;s<h;s++)for(i=this._parts[s],a=0,l=i.length,r=l-1;a<l;r=a++)o=i[a],n=i[r],o.y>t.y!=n.y>t.y&&t.x<(n.x-o.x)*(t.y-o.y)/(n.y-o.y)+o.x&&(e=!e);return e||Polyline.prototype._containsPoint.call(this,t,!0)}});function polygon(t,e){return new Polygon(t,e)}let GeoJSON=FeatureGroup.extend({initialize(t,e){setOptions(this,e),this._layers={},t&&this.addData(t)},addData(t){var e=Array.isArray(t)?t:t.features;if(e){for(var i of e)(i.geometries||i.geometry||i.features||i.coordinates)&&this.addData(i);return this}var o,e=this.options;return(!e.filter||e.filter(t))&&(o=geometryToLayer(t,e))?(o.feature=asFeature(t),o.defaultOptions=o.options,this.resetStyle(o),e.onEachFeature&&e.onEachFeature(t,o),this.addLayer(o)):this},resetStyle(t){return void 0===t?this.eachLayer(this.resetStyle,this):(t.options=Object.create(t.defaultOptions),this._setLayerStyle(t,this.options.style),this)},setStyle(e){return this.eachLayer(t=>this._setLayerStyle(t,e))},_setLayerStyle(t,e){t.setStyle&&("function"==typeof e&&(e=e(t.feature)),t.setStyle(e))}});function geometryToLayer(t,e){var i="Feature"===t.type?t.geometry:t,o=i?.coordinates,n=[],s=e?.pointToLayer,a=e?.coordsToLatLng??coordsToLatLng;let r,h;if(!o&&!i)return null;switch(i.type){case"Point":return _pointToLayer(s,t,r=a(o),e);case"MultiPoint":for(var l of o)r=a(l),n.push(_pointToLayer(s,t,r,e));return new FeatureGroup(n);case"LineString":case"MultiLineString":return h=coordsToLatLngs(o,"LineString"===i.type?0:1,a),new Polyline(h,e);case"Polygon":case"MultiPolygon":return h=coordsToLatLngs(o,"Polygon"===i.type?1:2,a),new Polygon(h,e);case"GeometryCollection":for(var d of i.geometries){d=geometryToLayer({geometry:d,type:"Feature",properties:t.properties},e);d&&n.push(d)}return new FeatureGroup(n);case"FeatureCollection":for(var p of i.features){p=geometryToLayer(p,e);p&&n.push(p)}return new FeatureGroup(n);default:throw new Error("Invalid GeoJSON object.")}}function _pointToLayer(t,e,i,o){return t?t(e,i):new Marker(i,o?.markersInheritOptions&&o)}function coordsToLatLng(t){return new LatLng(t[1],t[0],t[2])}function coordsToLatLngs(t,e,i){return t.map(t=>e?coordsToLatLngs(t,e-1,i):(i||coordsToLatLng)(t))}function latLngToCoords(t,e){return void 0!==(t=toLatLng(t)).alt?[formatNum(t.lng,e),formatNum(t.lat,e),formatNum(t.alt,e)]:[formatNum(t.lng,e),formatNum(t.lat,e)]}function latLngsToCoords(t,e,i,o){t=t.map(t=>e?latLngsToCoords(t,isFlat(t)?0:e-1,i,o):latLngToCoords(t,o));return!e&&i&&0<t.length&&t.push(t[0].slice()),t}function getFeature(t,e){return t.feature?{...t.feature,geometry:e}:asFeature(e)}function asFeature(t){return"Feature"===t.type||"FeatureCollection"===t.type?t:{type:"Feature",properties:{},geometry:t}}let PointToGeoJSON={toGeoJSON(t){return getFeature(this,{type:"Point",coordinates:latLngToCoords(this.getLatLng(),t)})}};function geoJSON(t,e){return new GeoJSON(t,e)}Marker.include(PointToGeoJSON),Circle.include(PointToGeoJSON),CircleMarker.include(PointToGeoJSON),Polyline.include({toGeoJSON(t){var e=!isFlat(this._latlngs);return getFeature(this,{type:`${e?"Multi":""}LineString`,coordinates:latLngsToCoords(this._latlngs,e?1:0,!1,t)})}}),Polygon.include({toGeoJSON(t){var e=!isFlat(this._latlngs),i=e&&!isFlat(this._latlngs[0]);let o=latLngsToCoords(this._latlngs,i?2:e?1:0,!0,t);return getFeature(this,{type:`${i?"Multi":""}Polygon`,coordinates:o=e?o:[o]})}}),LayerGroup.include({toMultiPoint(e){let i=[];return this.eachLayer(t=>{i.push(t.toGeoJSON(e).geometry.coordinates)}),getFeature(this,{type:"MultiPoint",coordinates:i})},toGeoJSON(e){var t=this.feature?.geometry?.type;if("MultiPoint"===t)return this.toMultiPoint(e);let i="GeometryCollection"===t,o=[];return this.eachLayer(t=>{t.toGeoJSON&&(t=t.toGeoJSON(e),i?o.push(t.geometry):"FeatureCollection"===(t=asFeature(t)).type?o.push.apply(o,t.features):o.push(t))}),i?getFeature(this,{geometries:o,type:"GeometryCollection"}):{type:"FeatureCollection",features:o}}});let geoJson=geoJSON,BlanketOverlay=Layer.extend({options:{padding:.1,continuous:!1},initialize(t){setOptions(this,t)},onAdd(){this._container||(this._initContainer(),this._container.classList.add("leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._resizeContainer(),this._onMoveEnd()},onRemove(){this._destroyContainer()},getEvents(){var t={viewreset:this._reset,zoom:this._onZoom,moveend:this._onMoveEnd,zoomend:this._onZoomEnd,resize:this._resizeContainer};return this._zoomAnimated&&(t.zoomanim=this._onAnimZoom),this.options.continuous&&(t.move=this._onMoveEnd),t},_onAnimZoom(t){this._updateTransform(t.center,t.zoom)},_onZoom(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform(t,e){var i=this._map.getZoomScale(e,this._zoom),o=this._map.getSize().multiplyBy(.5+this.options.padding),n=this._map.project(this._center,e),o=o.multiplyBy(-i).add(n).subtract(this._map._getNewPixelOrigin(t,e));setTransform(this._container,o,i)},_onMoveEnd(t){var e=this.options.padding,i=this._map.getSize(),o=this._map.containerPointToLayerPoint(i.multiplyBy(-e)).round();this._bounds=new Bounds(o,o.add(i.multiplyBy(1+2*e)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom(),this._updateTransform(this._center,this._zoom),this._onSettled(t)},_reset(){this._onSettled(),this._updateTransform(this._center,this._zoom),this._onViewReset()},_initContainer(){this._container=create$1("div")},_destroyContainer(){off(this._container),this._container.remove(),delete this._container},_resizeContainer(){var t=this.options.padding,t=this._map.getSize().multiplyBy(1+2*t).round();return this._container.style.width=t.x+"px",this._container.style.height=t.y+"px",t},_onZoomEnd:falseFn,_onViewReset:falseFn,_onSettled:falseFn}),ImageOverlay=Layer.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:"",decoding:"auto"},initialize(t,e,i){this._url=t,this._bounds=toLatLngBounds(e),setOptions(this,i)},onAdd(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(this._image.classList.add("leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove(){this._image.remove(),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity(t){return this.options.opacity=t,this._image&&this._updateOpacity(),this},setStyle(t){return t.opacity&&this.setOpacity(t.opacity),this},bringToFront(){return this._map&&toFront(this._image),this},bringToBack(){return this._map&&toBack(this._image),this},setUrl(t){return this._url=t,this._image&&(this._image.src=t),this},setBounds(t){return this._bounds=toLatLngBounds(t),this._map&&this._reset(),this},getEvents(){var t={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},setZIndex(t){return this.options.zIndex=t,this._updateZIndex(),this},getBounds(){return this._bounds},getElement(){return this._image},_initImage(){var t="IMG"===this._url.tagName,e=this._image=t?this._url:create$1("img");e.classList.add("leaflet-image-layer"),this._zoomAnimated&&e.classList.add("leaflet-zoom-animated"),this.options.className&&e.classList.add(...splitWords(this.options.className)),e.onselectstart=falseFn,e.onpointermove=falseFn,e.onload=this.fire.bind(this,"load"),e.onerror=this._overlayOnError.bind(this),!this.options.crossOrigin&&""!==this.options.crossOrigin||(e.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),e.decoding=this.options.decoding,this.options.zIndex&&this._updateZIndex(),t?this._url=e.src:(e.src=this._url,e.alt=this.options.alt)},_animateZoom(t){var e=this._map.getZoomScale(t.zoom),t=this._map._latLngBoundsToNewLayerBounds(this._bounds,t.zoom,t.center).min;setTransform(this._image,t,e)},_reset(){var t=this._image,e=new Bounds(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),i=e.getSize();setPosition(t,e.min),t.style.width=i.x+"px",t.style.height=i.y+"px"},_updateOpacity(){this._image.style.opacity=this.options.opacity},_updateZIndex(){this._image&&null!=this.options.zIndex&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError(){this.fire("error");var t=this.options.errorOverlayUrl;t&&this._url!==t&&(this._url=t,this._image.src=t)},getCenter(){return this._bounds.getCenter()}}),imageOverlay=function(t,e,i){return new ImageOverlay(t,e,i)},VideoOverlay=ImageOverlay.extend({options:{autoplay:!0,controls:!1,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage(){var t,e="VIDEO"===this._url.tagName;let i=this._image=e?this._url:create$1("video");if(i.classList.add("leaflet-image-layer"),this._zoomAnimated&&i.classList.add("leaflet-zoom-animated"),this.options.className&&i.classList.add(...splitWords(this.options.className)),on(i,"pointerdown",t=>{i.controls&&stopPropagation(t)}),i.onloadeddata=this.fire.bind(this,"load"),e)t=(e=i.getElementsByTagName("source")).map(t=>t.src),this._url=0<e.length?t:[i.src];else{Array.isArray(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.hasOwn(i.style,"objectFit")&&(i.style.objectFit="fill"),i.autoplay=!!this.options.autoplay,i.controls=!!this.options.controls,i.loop=!!this.options.loop,i.muted=!!this.options.muted,i.playsInline=!!this.options.playsInline;for(var o of this._url){var n=create$1("source");n.src=o,i.appendChild(n)}}}});function videoOverlay(t,e,i){return new VideoOverlay(t,e,i)}let SVGOverlay=ImageOverlay.extend({_initImage(){var t=this._image=this._url;t.classList.add("leaflet-image-layer"),this._zoomAnimated&&t.classList.add("leaflet-zoom-animated"),this.options.className&&t.classList.add(...splitWords(this.options.className)),t.onselectstart=falseFn,t.onpointermove=falseFn}});function svgOverlay(t,e,i){return new SVGOverlay(t,e,i)}let DivOverlay=Layer.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize(t,e){t instanceof LatLng||Array.isArray(t)?(this._latlng=toLatLng(t),setOptions(this,e)):(setOptions(this,t),this._source=e),this.options.content&&(this._content=this.options.content)},openOn(t){return(t=arguments.length?t:this._source._map).hasLayer(this)||t.addLayer(this),this},close(){return this._map&&this._map.removeLayer(this),this},toggle(t){return this._map?this.close():(arguments.length?this._source=t:t=this._source,this._prepareOpen(),this.openOn(t._map)),this},onAdd(t){this._zoomAnimated=t._zoomAnimated,this._container||this._initLayout(),t._fadeAnimated&&(this._container.style.opacity=0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),t._fadeAnimated&&(this._container.style.opacity=1),this.bringToFront(),this.options.interactive&&(this._container.classList.add("leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove(t){t._fadeAnimated?(this._container.style.opacity=0,this._removeTimeout=setTimeout(()=>this._container.remove(),200)):this._container.remove(),this.options.interactive&&(this._container.classList.remove("leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng(){return this._latlng},setLatLng(t){return this._latlng=toLatLng(t),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent(){return this._content},setContent(t){return this._content=t,this.update(),this},getElement(){return this._container},update(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents(){var t={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},isOpen(){return!!this._map&&this._map.hasLayer(this)},bringToFront(){return this._map&&toFront(this._container),this},bringToBack(){return this._map&&toBack(this._container),this},_prepareOpen(t){let e=this._source;if(!e._map)return!1;if(e instanceof FeatureGroup){e=null;for(var i of Object.values(this._source._layers))if(i._map){e=i;break}if(!e)return!1;this._source=e}if(!t)if(e.getCenter)t=e.getCenter();else if(e.getLatLng)t=e.getLatLng();else{if(!e.getBounds)throw new Error("Unable to get source layer LatLng.");t=e.getBounds().getCenter()}return this.setLatLng(t),this._map&&this.update(),!0},_updateContent(){if(this._content){var t=this._contentNode,e="function"==typeof this._content?this._content(this._source??this):this._content;if("string"==typeof e)t.innerHTML=e;else{for(;t.hasChildNodes();)t.removeChild(t.firstChild);t.appendChild(e)}this.fire("contentupdate")}},_updatePosition(){if(this._map){var e=this._map.latLngToLayerPoint(this._latlng),i=this._getAnchor();let t=toPoint(this.options.offset);this._zoomAnimated?setPosition(this._container,e.add(i)):t=t.add(e).add(i);e=this._containerBottom=-t.y,i=this._containerLeft=-Math.round(this._containerWidth/2)+t.x;this._container.style.bottom=e+"px",this._container.style.left=i+"px"}},_getAnchor(){return[0,0]}}),Popup=(Map$1.include({_initOverlay(t,e,i,o){let n=e;return n instanceof t||(n=new t(o).setContent(e)),i&&n.setLatLng(i),n}}),Layer.include({_initOverlay(t,e,i,o){let n=i;return n instanceof t?(setOptions(n,o),n._source=this):(n=e&&!o?e:new t(o,this)).setContent(i),n}}),DivOverlay.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,closeButtonLabel:"Close popup",autoClose:!0,closeOnEscapeKey:!0,className:"",trackResize:!0},openOn(t){return!(t=arguments.length?t:this._source._map).hasLayer(this)&&t._popup&&t._popup.options.autoClose&&t.removeLayer(t._popup),t._popup=this,DivOverlay.prototype.openOn.call(this,t)},onAdd(t){DivOverlay.prototype.onAdd.call(this,t),t.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof Path||this._source.on("preclick",stopPropagation))},onRemove(t){DivOverlay.prototype.onRemove.call(this,t),t.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof Path||this._source.off("preclick",stopPropagation))},getEvents(){var t=DivOverlay.prototype.getEvents.call(this);return(this.options.closeOnClick??this._map.options.closePopupOnClick)&&(t.preclick=this.close),this.options.keepInView&&(t.moveend=this._adjustPan),t},_initLayout(){var t="leaflet-popup",e=this._container=create$1("div",t+` ${this.options.className||""} leaflet-zoom-animated`),i=this._wrapper=create$1("div",t+"-content-wrapper",e);this._contentNode=create$1("div",t+"-content",i),disableClickPropagation(e),disableScrollPropagation(this._contentNode),on(e,"contextmenu",stopPropagation),this._tipContainer=create$1("div",t+"-tip-container",e),this._tip=create$1("div",t+"-tip",this._tipContainer),this.options.closeButton&&((i=this._closeButton=create$1("a",t+"-close-button",e)).setAttribute("role","button"),i.setAttribute("aria-label",this.options.closeButtonLabel),i.href="#close",i.innerHTML='<span aria-hidden="true">&#215;</span>',on(i,"click",t=>{preventDefault(t),this.close()})),this.options.trackResize&&(this._resizeObserver=new ResizeObserver(t=>{this._map&&(this._containerWidth=t[0]?.contentRect?.width,this._containerHeight=t[0]?.contentRect?.height,this._updateLayout(),this._updatePosition(),this._adjustPan())}),this._resizeObserver.observe(this._contentNode))},_updateLayout(){var t=this._contentNode,e=t.style,i=(e.width="",e.whiteSpace="nowrap",e.maxWidth=this.options.maxWidth+"px",e.minWidth=this.options.minWidth+"px",e.whiteSpace="",e.height="",this._containerHeight??t.offsetHeight),o=this.options.maxHeight,n="leaflet-popup-scrolled";o&&o<i?(e.height=o+"px",t.classList.add(n)):t.classList.remove(n),this._containerWidth=this._container.offsetWidth,this._containerHeight=this._container.offsetHeight},_animateZoom(t){var t=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center),e=this._getAnchor();setPosition(this._container,t.add(e))},_adjustPan(){if(this.options.autoPan)if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning)this._autopanning=!1;else{var i=this._map,o=parseInt(getComputedStyle(this._container).marginBottom,10)||0,o=this._containerHeight+o,n=this._containerWidth,s=new Point(this._containerLeft,-o-this._containerBottom),s=(s._add(getPosition(this._container)),i.layerPointToContainerPoint(s)),a=toPoint(this.options.autoPanPadding),r=toPoint(this.options.autoPanPaddingTopLeft??a),a=toPoint(this.options.autoPanPaddingBottomRight??a),h=i.getSize();let t=0,e=0;s.x+n+a.x>h.x&&(t=s.x+n-h.x+a.x),s.x-t-r.x<0&&(t=s.x-r.x),s.y+o+a.y>h.y&&(e=s.y+o-h.y+a.y),s.y-e-r.y<0&&(e=s.y-r.y),(t||e)&&(this.options.keepInView&&(this._autopanning=!0),i.fire("autopanstart").panBy([t,e]))}},_getAnchor(){return toPoint(this._source?._getPopupAnchor?this._source._getPopupAnchor():[0,0])}})),popup=function(t,e){return new Popup(t,e)},Tooltip=(Map$1.mergeOptions({closePopupOnClick:!0}),Map$1.include({openPopup(t,e,i){return this._initOverlay(Popup,t,e,i).openOn(this),this},closePopup(t){return(t=arguments.length?t:this._popup)&&t.close(),this}}),Layer.include({bindPopup(t,e){return this._popup=this._initOverlay(Popup,this._popup,t,e),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup(t){return this._popup&&(this instanceof FeatureGroup||(this._popup._source=this),this._popup._prepareOpen(t||this._latlng))&&this._popup.openOn(this._map),this},closePopup(){return this._popup&&this._popup.close(),this},togglePopup(){return this._popup&&this._popup.toggle(this),this},isPopupOpen(){return!!this._popup&&this._popup.isOpen()},setPopupContent(t){return this._popup&&this._popup.setContent(t),this},getPopup(){return this._popup},_openPopup(t){var e;this._popup&&this._map&&(stop(t),e=t.propagatedFrom??t.target,this._popup._source!==e||e instanceof Path?(this._popup._source=e,this.openPopup(t.latlng)):this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(t.latlng))},_movePopup(t){this._popup.setLatLng(t.latlng)},_onKeyPress(t){"Enter"===t.originalEvent.code&&this._openPopup(t)}}),DivOverlay.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd(t){DivOverlay.prototype.onAdd.call(this,t),this.setOpacity(this.options.opacity),t.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove(t){DivOverlay.prototype.onRemove.call(this,t),t.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents(){var t=DivOverlay.prototype.getEvents.call(this);return this.options.permanent||(t.preclick=this.close),t},_initLayout(){var t=`leaflet-tooltip ${this.options.className||""} leaflet-zoom-`+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=create$1("div",t),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+stamp(this))},_updateLayout(){},_adjustPan(){},_setPosition(t){let e,i,o=this.options.direction;var n=this._map,s=this._container,a=n.latLngToContainerPoint(n.getCenter()),n=n.layerPointToContainerPoint(t),r=s.offsetWidth,h=s.offsetHeight,l=toPoint(this.options.offset),d=this._getAnchor();i="top"===o?(e=r/2,h):"bottom"===o?(e=r/2,0):(e="center"===o?r/2:"right"===o?0:"left"===o?r:n.x<a.x?(o="right",0):(o="left",r+2*(l.x+d.x)),h/2),t=t.subtract(toPoint(e,i,!0)).add(l).add(d),s.classList.remove("leaflet-tooltip-right","leaflet-tooltip-left","leaflet-tooltip-top","leaflet-tooltip-bottom"),s.classList.add("leaflet-tooltip-"+o),setPosition(s,t)},_updatePosition(){var t=this._map.latLngToLayerPoint(this._latlng);this._setPosition(t)},setOpacity(t){this.options.opacity=t,this._container&&(this._container.style.opacity=t)},_animateZoom(t){t=this._map._latLngToNewLayerPoint(this._latlng,t.zoom,t.center);this._setPosition(t)},_getAnchor(){return toPoint(this._source?._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}})),tooltip=function(t,e){return new Tooltip(t,e)},DivIcon=(Map$1.include({openTooltip(t,e,i){return this._initOverlay(Tooltip,t,e,i).openOn(this),this},closeTooltip(t){return t.close(),this}}),Layer.include({bindTooltip(t,e){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(Tooltip,this._tooltip,t,e),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions(t){var e,i;!t&&this._tooltipHandlersAdded||(e=t?"off":"on",i={remove:this.closeTooltip,move:this._moveTooltip},this._tooltip.options.permanent?i.add=this._openTooltip:(i.pointerover=this._openTooltip,i.pointerout=this.closeTooltip,i.click=this._openTooltip,this._map?this._addFocusListeners(t):i.add=()=>this._addFocusListeners(t)),this._tooltip.options.sticky&&(i.pointermove=this._moveTooltip),this[e](i),this._tooltipHandlersAdded=!t)},openTooltip(t){return this._tooltip&&(this instanceof FeatureGroup||(this._tooltip._source=this),this._tooltip._prepareOpen(t))&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this)),this},closeTooltip(){if(this._tooltip)return this._tooltip.close()},toggleTooltip(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen(){return this._tooltip.isOpen()},setTooltipContent(t){return this._tooltip&&this._tooltip.setContent(t),this},getTooltip(){return this._tooltip},_addFocusListeners(e){this.getElement?this._addFocusListenersOnLayer(this,e):this.eachLayer&&this.eachLayer(t=>this._addFocusListenersOnLayer(t,e),this)},_addFocusListenersOnLayer(t,e){var i,o="function"==typeof t.getElement&&t.getElement();o&&(i=e?"off":"on",e||(o._leaflet_focus_handler&&off(o,"focus",o._leaflet_focus_handler,this),o._leaflet_focus_handler=()=>{this._tooltip&&(this._tooltip._source=t,this.openTooltip())}),o._leaflet_focus_handler&&DomEvent[i](o,"focus",o._leaflet_focus_handler,this),DomEvent[i](o,"blur",this.closeTooltip,this),e)&&delete o._leaflet_focus_handler},_setAriaDescribedByOnLayer(t){t="function"==typeof t.getElement&&t.getElement();t&&t.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip(t){this._tooltip&&this._map&&(this._map.dragging&&this._map.dragging.moving()?"add"!==t.type||this._moveEndOpensTooltip||(this._moveEndOpensTooltip=!0,this._map.once("moveend",()=>{this._moveEndOpensTooltip=!1,this._openTooltip(t)})):(this._tooltip._source=t.propagatedFrom??t.target,this.openTooltip(this._tooltip.options.sticky?t.latlng:void 0)))},_moveTooltip(t){let e=t.latlng,i,o;this._tooltip.options.sticky&&t.originalEvent&&(i=this._map.pointerEventToContainerPoint(t.originalEvent),o=this._map.containerPointToLayerPoint(i),e=this._map.layerPointToLatLng(o)),this._tooltip.setLatLng(e)}}),Icon.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon(t){var t=t&&"DIV"===t.tagName?t:document.createElement("div"),e=this.options;return e.html instanceof Element?(t.replaceChildren(),t.appendChild(e.html)):t.innerHTML=!1!==e.html?e.html:"",e.bgPos&&(e=toPoint(e.bgPos),t.style.backgroundPosition=-e.x+`px ${-e.y}px`),this._setIconStyles(t,"icon"),t},createShadow(){return null}}));function divIcon(t){return new DivIcon(t)}Icon.Default=IconDefault;let GridLayer=Layer.extend({options:{tileSize:256,opacity:1,updateWhenIdle:Browser.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize(t){setOptions(this,t)},onAdd(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd(t){t._addZoomLimit(this)},onRemove(t){this._removeAllTiles(),this._container.remove(),t._removeZoomLimit(this),this._container=null,this._tileZoom=void 0,clearTimeout(this._pruneTimeout)},bringToFront(){return this._map&&(toFront(this._container),this._setAutoZIndex(Math.max)),this},bringToBack(){return this._map&&(toBack(this._container),this._setAutoZIndex(Math.min)),this},getContainer(){return this._container},setOpacity(t){return this.options.opacity=t,this._updateOpacity(),this},setZIndex(t){return this.options.zIndex=t,this._updateZIndex(),this},isLoading(){return this._loading},redraw(){var t;return this._map&&(this._removeAllTiles(),(t=this._clampZoom(this._map.getZoom()))!==this._tileZoom&&(this._tileZoom=t,this._updateLevels()),this._update()),this},getEvents(){var t={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=throttle(this._onMoveEnd,this.options.updateInterval,this)),t.move=this._onMove),this._zoomAnimated&&(t.zoomanim=this._animateZoom),t},createTile(){return document.createElement("div")},getTileSize(){var t=this.options.tileSize;return t instanceof Point?t:new Point(t,t)},_updateZIndex(){this._container&&null!=this.options.zIndex&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex(t){var e,i=this.getPane().children;let o=-t(-1/0,1/0);for(e of i){var n=e.style.zIndex;e!==this._container&&n&&(o=t(o,+n))}isFinite(o)&&(this.options.zIndex=o+t(-1,1),this._updateZIndex())},_updateOpacity(){if(this._map){this._container.style.opacity=this.options.opacity;var i,o,n=+new Date;let t=!1,e=!1;for(i of Object.values(this._tiles??{}))i.current&&i.loaded&&(o=Math.min(1,(n-i.loaded)/200),(i.el.style.opacity=o)<1?t=!0:(i.active?e=!0:this._onOpaqueTile(i),i.active=!0));e&&!this._noPrune&&this._pruneTiles(),t&&(cancelAnimationFrame(this._fadeFrame),this._fadeFrame=requestAnimationFrame(this._updateOpacity.bind(this)))}},_onOpaqueTile:falseFn,_initContainer(){this._container||(this._container=create$1("div","leaflet-layer "+(this.options.className??"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels(){var e=this._tileZoom,i=this.options.maxZoom;if(void 0!==e){for(var o of Object.keys(this._levels))o=Number(o),this._levels[o].el.children.length||o===e?(this._levels[o].el.style.zIndex=i-Math.abs(e-o),this._onUpdateLevel(o)):(this._levels[o].el.remove(),this._removeTilesAtZoom(o),this._onRemoveLevel(o),delete this._levels[o]);let t=this._levels[e];var n=this._map;return t||((t=this._levels[e]={}).el=create$1("div","leaflet-tile-container leaflet-zoom-animated",this._container),t.el.style.zIndex=i,t.origin=n.project(n.unproject(n.getPixelOrigin()),e).round(),t.zoom=e,this._setZoomTransform(t,n.getCenter(),n.getZoom()),falseFn(t.el.offsetWidth),this._onCreateLevel(t)),this._level=t}},_onUpdateLevel:falseFn,_onRemoveLevel:falseFn,_onCreateLevel:falseFn,_pruneTiles(){if(this._map){var t=this._map.getZoom();if(t>this.options.maxZoom||t<this.options.minZoom)this._removeAllTiles();else{for(var e of Object.values(this._tiles))e.retain=e.current;for(var i of Object.values(this._tiles))i.current&&!i.active&&(i=i.coords,this._retainParent(i.x,i.y,i.z,i.z-5)||this._retainChildren(i.x,i.y,i.z,i.z+2));for(var[o,n]of Object.entries(this._tiles))n.retain||this._removeTile(o)}}},_removeTilesAtZoom(t){for(var[e,i]of Object.entries(this._tiles))i.coords.z===t&&this._removeTile(e)},_removeAllTiles(){for(var t of Object.keys(this._tiles))this._removeTile(t)},_invalidateAll(){for(var t of Object.keys(this._levels))this._levels[t].el.remove(),this._onRemoveLevel(Number(t)),delete this._levels[t];this._removeAllTiles(),this._tileZoom=void 0},_retainParent(t,e,i,o){var t=Math.floor(t/2),e=Math.floor(e/2),i=i-1,n=new Point(+t,+e),n=(n.z=i,this._tileCoordsToKey(n)),n=this._tiles[n];return n?.active?n.retain=!0:(n?.loaded&&(n.retain=!0),o<i&&this._retainParent(t,e,i,o))},_retainChildren(t,i,o,n){for(let e=2*t;e<2*t+2;e++)for(let t=2*i;t<2*i+2;t++){var s=new Point(e,t),s=(s.z=o+1,this._tileCoordsToKey(s)),s=this._tiles[s];s?.active?s.retain=!0:(s?.loaded&&(s.retain=!0),o+1<n&&this._retainChildren(e,t,o+1,n))}},_resetView(t){t=t&&(t.pinch||t.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),t,t)},_animateZoom(t){this._setView(t.center,t.zoom,!0,t.noUpdate)},_clampZoom(t){var e=this.options;return void 0!==e.minNativeZoom&&t<e.minNativeZoom?e.minNativeZoom:void 0!==e.maxNativeZoom&&e.maxNativeZoom<t?e.maxNativeZoom:t},_setView(t,e,i,o){let n=Math.round(e);n=void 0!==this.options.maxZoom&&n>this.options.maxZoom||void 0!==this.options.minZoom&&n<this.options.minZoom?void 0:this._clampZoom(n);var s=this.options.updateWhenZooming&&n!==this._tileZoom;o&&!s||(this._tileZoom=n,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),void 0!==n&&this._update(t),i||this._pruneTiles(),this._noPrune=!!i),this._setZoomTransforms(t,e)},_setZoomTransforms(t,e){for(var i of Object.values(this._levels))this._setZoomTransform(i,t,e)},_setZoomTransform(t,e,i){var o=this._map.getZoomScale(i,t.zoom),e=t.origin.multiplyBy(o).subtract(this._map._getNewPixelOrigin(e,i)).round();setTransform(t.el,e,o)},_resetGrid(){var t=this._map,e=t.options.crs,i=this._tileSize=this.getTileSize(),o=this._tileZoom,n=this._map.getPixelWorldBounds(this._tileZoom);n&&(this._globalTileRange=this._pxBoundsToTileRange(n)),this._wrapX=e.wrapLng&&!this.options.noWrap&&[Math.floor(t.project([0,e.wrapLng[0]],o).x/i.x),Math.ceil(t.project([0,e.wrapLng[1]],o).x/i.y)],this._wrapY=e.wrapLat&&!this.options.noWrap&&[Math.floor(t.project([e.wrapLat[0],0],o).y/i.x),Math.ceil(t.project([e.wrapLat[1],0],o).y/i.y)]},_onMoveEnd(){this._map&&!this._map._animatingZoom&&this._update()},_getTiledPixelBounds(t){var e=this._map,i=e._animatingZoom?Math.max(e._animateToZoom,e.getZoom()):e.getZoom(),i=e.getZoomScale(i,this._tileZoom),t=e.project(t,this._tileZoom).floor(),e=e.getSize().divideBy(2*i);return new Bounds(t.subtract(e),t.add(e))},_update(a){var t=this._map;if(t){var r=this._clampZoom(t.getZoom());if(void 0===a&&(a=t.getCenter()),void 0!==this._tileZoom){let t=this._getTiledPixelBounds(a),i=this._pxBoundsToTileRange(t),o=i.getCenter(),n=[],e=this.options.keepBuffer,s=new Bounds(i.getBottomLeft().subtract([e,-e]),i.getTopRight().add([e,-e]));if(!(isFinite(i.min.x)&&isFinite(i.min.y)&&isFinite(i.max.x)&&isFinite(i.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var h of Object.values(this._tiles)){var l=h.coords;l.z===this._tileZoom&&s.contains(new Point(l.x,l.y))||(h.current=!1)}if(1<Math.abs(r-this._tileZoom))this._setView(a,r);else{for(let e=i.min.y;e<=i.max.y;e++)for(let t=i.min.x;t<=i.max.x;t++){var d,p=new Point(t,e);p.z=this._tileZoom,this._isValidTile(p)&&((d=this._tiles[this._tileCoordsToKey(p)])?d.current=!0:n.push(p))}if(n.sort((t,e)=>t.distanceTo(o)-e.distanceTo(o)),0!==n.length){this._loading||(this._loading=!0,this.fire("loading"));var _,u=document.createDocumentFragment();for(_ of n)this._addTile(_,u);this._level.el.appendChild(u)}}}}},_isValidTile(t){var e=this._map.options.crs;if(!e.infinite){var i=this._globalTileRange;if(!e.wrapLng&&(t.x<i.min.x||t.x>i.max.x)||!e.wrapLat&&(t.y<i.min.y||t.y>i.max.y))return!1}return!this.options.bounds||(e=this._tileCoordsToBounds(t),toLatLngBounds(this.options.bounds).overlaps(e))},_keyToBounds(t){return this._tileCoordsToBounds(this._keyToTileCoords(t))},_tileCoordsToNwSe(t){var e=this._map,i=this.getTileSize(),o=t.scaleBy(i),i=o.add(i);return[e.unproject(o,t.z),e.unproject(i,t.z)]},_tileCoordsToBounds(t){t=this._tileCoordsToNwSe(t);let e=new LatLngBounds(t[0],t[1]);return e=this.options.noWrap?e:this._map.wrapLatLngBounds(e)},_tileCoordsToKey(t){return t.x+`:${t.y}:`+t.z},_keyToTileCoords(t){var t=t.split(":"),e=new Point(+t[0],+t[1]);return e.z=+t[2],e},_removeTile(t){var e=this._tiles[t];e&&(e.el.remove(),delete this._tiles[t],this.fire("tileunload",{tile:e.el,coords:this._keyToTileCoords(t)}))},_initTile(t){t.classList.add("leaflet-tile");var e=this.getTileSize();t.style.width=e.x+"px",t.style.height=e.y+"px",t.onselectstart=falseFn,t.onpointermove=falseFn},_addTile(t,e){var i=this._getTilePos(t),o=this._tileCoordsToKey(t),n=this.createTile(this._wrapCoords(t),this._tileReady.bind(this,t));this._initTile(n),this.createTile.length<2&&requestAnimationFrame(this._tileReady.bind(this,t,null,n)),setPosition(n,i),this._tiles[o]={el:n,coords:t,current:!0},e.appendChild(n),this.fire("tileloadstart",{tile:n,coords:t})},_tileReady(t,e,i){e&&this.fire("tileerror",{error:e,tile:i,coords:t});var o=this._tileCoordsToKey(t);(i=this._tiles[o])&&(i.loaded=+new Date,this._map._fadeAnimated?(i.el.style.opacity=0,cancelAnimationFrame(this._fadeFrame),this._fadeFrame=requestAnimationFrame(this._updateOpacity.bind(this))):(i.active=!0,this._pruneTiles()),e||(i.el.classList.add("leaflet-tile-loaded"),this.fire("tileload",{tile:i.el,coords:t})),this._noTilesToLoad())&&(this._loading=!1,this.fire("load"),this._map._fadeAnimated?this._pruneTimeout=setTimeout(this._pruneTiles.bind(this),250):requestAnimationFrame(this._pruneTiles.bind(this)))},_getTilePos(t){return t.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords(t){var e=new Point(this._wrapX?wrapNum(t.x,this._wrapX):t.x,this._wrapY?wrapNum(t.y,this._wrapY):t.y);return e.z=t.z,e},_pxBoundsToTileRange(t){var e=this.getTileSize();return new Bounds(t.min.unscaleBy(e).floor(),t.max.unscaleBy(e).ceil().subtract([1,1]))},_noTilesToLoad(){return Object.values(this._tiles).every(t=>t.loaded)}});function gridLayer(t){return new GridLayer(t)}let TileLayer=GridLayer.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize(t,i){if(this._url=t,null===(i=setOptions(this,i)).attribution&&URL.canParse(t)){let e=new URL(t).hostname;["tile.openstreetmap.org","tile.osm.org"].some(t=>e.endsWith(t))&&(i.attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors')}i.detectRetina&&Browser.retina&&0<i.maxZoom?(i.tileSize=Math.floor(i.tileSize/2),i.zoomReverse?(i.zoomOffset--,i.minZoom=Math.min(i.maxZoom,i.minZoom+1)):(i.zoomOffset++,i.maxZoom=Math.max(i.minZoom,i.maxZoom-1)),i.minZoom=Math.max(0,i.minZoom)):i.zoomReverse?i.minZoom=Math.min(i.maxZoom,i.minZoom):i.maxZoom=Math.max(i.minZoom,i.maxZoom),"string"==typeof i.subdomains&&(i.subdomains=i.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl(t,e){return this._url===t&&void 0===e&&(e=!0),this._url=t,e||this.redraw(),this},createTile(t,e){var i=document.createElement("img");return on(i,"load",this._tileOnLoad.bind(this,e,i)),on(i,"error",this._tileOnError.bind(this,e,i)),!this.options.crossOrigin&&""!==this.options.crossOrigin||(i.crossOrigin=!0===this.options.crossOrigin?"":this.options.crossOrigin),"string"==typeof this.options.referrerPolicy&&(i.referrerPolicy=this.options.referrerPolicy),i.alt="",i.src=this.getTileUrl(t),i},getTileUrl(t){var e={...this.options,r:Browser.retina?"@2x":"",s:this._getSubdomain(t),x:t.x,y:t.y,z:this._getZoomForUrl()};return this._map&&!this._map.options.crs.infinite&&(t=this._globalTileRange.max.y-t.y,this.options.tms&&(e.y=t),e["-y"]=t),template(this._url,e)},_tileOnLoad(t,e){t(null,e)},_tileOnError(t,e,i){var o=this.options.errorTileUrl;o&&e.getAttribute("src")!==o&&(e.src=o),t(i,e)},_onTileRemove(t){t.tile.onload=null},_getZoomForUrl(){let t=this._tileZoom;var e=this.options.maxZoom,i=this.options.zoomReverse,o=this.options.zoomOffset;return(t=i?e-t:t)+o},_getSubdomain(t){t=Math.abs(t.x+t.y)%this.options.subdomains.length;return this.options.subdomains[t]},_abortLoading(){let t,e;for(t of Object.keys(this._tiles)){var i;this._tiles[t].coords.z!==this._tileZoom&&((e=this._tiles[t].el).onload=falseFn,e.onerror=falseFn,e.complete||(e.src=emptyImageUrl,i=this._tiles[t].coords,e.remove(),delete this._tiles[t],this.fire("tileabort",{tile:e,coords:i})))}},_removeTile(t){var e=this._tiles[t];if(e)return e.el.setAttribute("src",emptyImageUrl),GridLayer.prototype._removeTile.call(this,t)},_tileReady(t,e,i){if(this._map&&(!i||i.getAttribute("src")!==emptyImageUrl))return GridLayer.prototype._tileReady.call(this,t,e,i)},_clampZoom(t){return Math.round(GridLayer.prototype._clampZoom.call(this,t))}});function tileLayer(t,e){return new TileLayer(t,e)}let TileLayerWMS=TileLayer.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize(t,e){this._url=t;var i,o={...this.defaultWmsParams};for(i of Object.keys(e))i in this.options||(o[i]=e[i]);var t=(e=setOptions(this,e)).detectRetina&&Browser.retina?2:1,n=this.getTileSize();o.width=n.x*t,o.height=n.y*t,this.wmsParams=o},onAdd(t){this._crs=this.options.crs??t.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var e=1.3<=this._wmsVersion?"crs":"srs";this.wmsParams[e]=this._crs.code,TileLayer.prototype.onAdd.call(this,t)},getTileUrl(t){var e,i,o=this._tileCoordsToNwSe(t),n=this._crs,n=toBounds(n.project(o[0]),n.project(o[1])),o=n.min,n=n.max,o=(1.3<=this._wmsVersion&&this._crs===EPSG4326?[o.y,o.x,n.y,n.x]:[o.x,o.y,n.x,n.y]).join(","),s=new URL(TileLayer.prototype.getTileUrl.call(this,t));for([e,i]of Object.entries({...this.wmsParams,bbox:o}))s.searchParams.append(this.options.uppercase?e.toUpperCase():e,i);return s.toString()},setParams(t,e){return Object.assign(this.wmsParams,t),e||this.redraw(),this}});function tileLayerWMS(t,e){return new TileLayerWMS(t,e)}TileLayer.WMS=TileLayerWMS,tileLayer.wms=tileLayerWMS;let Renderer=BlanketOverlay.extend({initialize(t){setOptions(this,{...t,continuous:!1}),stamp(this),this._layers??={}},onAdd(t){BlanketOverlay.prototype.onAdd.call(this,t),this.on("update",this._updatePaths,this)},onRemove(){BlanketOverlay.prototype.onRemove.call(this),this.off("update",this._updatePaths,this)},_onZoomEnd(){for(var t of Object.values(this._layers))t._project()},_updatePaths(){for(var t of Object.values(this._layers))t._update()},_onViewReset(){for(var t of Object.values(this._layers))t._reset()},_onSettled(){this._update()},_update:falseFn}),Canvas=Renderer.extend({options:{tolerance:0},getEvents(){var t=Renderer.prototype.getEvents.call(this);return t.viewprereset=this._onViewPreReset,t},_onViewPreReset(){this._postponeUpdatePaths=!0},onAdd(t){Renderer.prototype.onAdd.call(this,t),this._draw()},onRemove(){Renderer.prototype.onRemove.call(this),clearTimeout(this._pointerHoverThrottleTimeout)},_initContainer(){var t=this._container=document.createElement("canvas");on(t,"pointermove",this._onPointerMove,this),on(t,"click dblclick pointerdown pointerup contextmenu",this._onClick,this),on(t,"pointerout",this._handlePointerOut,this),t._leaflet_disable_events=!0,this._ctx=t.getContext("2d")},_destroyContainer(){cancelAnimationFrame(this._redrawRequest),this._redrawRequest=null,delete this._ctx,Renderer.prototype._destroyContainer.call(this)},_resizeContainer(){var t=Renderer.prototype._resizeContainer.call(this),e=this._ctxScale=window.devicePixelRatio;this._container.width=e*t.x,this._container.height=e*t.y},_updatePaths(){if(!this._postponeUpdatePaths){this._redrawBounds=null;for(var t of Object.values(this._layers))t._update();this._redraw()}},_update(){var t,e;this._map._animatingZoom&&this._bounds||(t=this._bounds,e=this._ctxScale,this._ctx.setTransform(e,0,0,e,-t.min.x*e,-t.min.y*e),this.fire("update"))},_reset(){Renderer.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath(t){this._updateDashArray(t);t=(this._layers[stamp(t)]=t)._order={layer:t,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=t),this._drawLast=t,this._drawFirst??=this._drawLast},_addPath(t){this._requestRedraw(t)},_removePath(t){var e=t._order,i=e.next,e=e.prev;i?i.prev=e:this._drawLast=e,e?e.next=i:this._drawFirst=i,delete t._order,delete this._layers[stamp(t)],this._requestRedraw(t)},_updatePath(t){this._extendRedrawBounds(t),t._project(),t._update(),this._requestRedraw(t)},_updateStyle(t){this._updateDashArray(t),this._requestRedraw(t)},_updateDashArray(t){var e;"string"==typeof t.options.dashArray?(e=t.options.dashArray.split(/[, ]+/),t.options._dashArray=e.map(t=>Number(t)).filter(t=>!isNaN(t))):t.options._dashArray=t.options.dashArray},_requestRedraw(t){this._map&&(this._extendRedrawBounds(t),this._redrawRequest??=requestAnimationFrame(this._redraw.bind(this)))},_extendRedrawBounds(t){var e;t._pxBounds&&(e=(t.options.weight??0)+1,this._redrawBounds??=new Bounds,this._redrawBounds.extend(t._pxBounds.min.subtract([e,e])),this._redrawBounds.extend(t._pxBounds.max.add([e,e])))},_redraw(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear(){var t,e=this._redrawBounds;e?(t=e.getSize(),this._ctx.clearRect(e.min.x,e.min.y,t.x,t.y)):(this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore())},_draw(){var e,t,i=this._redrawBounds;this._ctx.save(),i&&(t=i.getSize(),this._ctx.beginPath(),this._ctx.rect(i.min.x,i.min.y,t.x,t.y),this._ctx.clip()),this._drawing=!0;for(let t=this._drawFirst;t;t=t.next)e=t.layer,(!i||e._pxBounds&&e._pxBounds.intersects(i))&&e._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly(e,o){if(this._drawing){let t=e._parts,i=this._ctx;t.length&&(i.beginPath(),t.forEach(t=>{t.forEach((t,e)=>{i[e?"lineTo":"moveTo"](t.x,t.y)}),o&&i.closePath()}),this._fillStroke(i,e))}},_updateCircle(t){var e,i,o,n;this._drawing&&!t._empty()&&(e=t._point,i=this._ctx,o=Math.max(Math.round(t._radius),1),1!=(n=(Math.max(Math.round(t._radiusY),1)||o)/o)&&(i.save(),i.scale(1,n)),i.beginPath(),i.arc(e.x,e.y/n,o,0,2*Math.PI,!1),1!=n&&i.restore(),this._fillStroke(i,t))},_fillStroke(t,e){e=e.options;e.fill&&(t.globalAlpha=e.fillOpacity,t.fillStyle=e.fillColor??e.color,t.fill(e.fillRule||"evenodd")),e.stroke&&0!==e.weight&&(t.setLineDash&&(t.lineDashOffset=Number(e.dashOffset??0),t.setLineDash(e._dashArray??[])),t.globalAlpha=e.opacity,t.lineWidth=e.weight,t.strokeStyle=e.color,t.lineCap=e.lineCap,t.lineJoin=e.lineJoin,t.stroke())},_onClick(e){var i=this._map.pointerEventToLayerPoint(e);let o,n;for(let t=this._drawFirst;t;t=t.next)(o=t.layer).options.interactive&&o._containsPoint(i)&&(("click"===e.type||"preclick"===e.type)&&this._map._draggableMoved(o)||(n=o));this._fireEvent(!!n&&[n],e)},_onPointerMove(t){var e;!this._map||this._map.dragging.moving()||this._map._animatingZoom||(e=this._map.pointerEventToLayerPoint(t),this._handlePointerHover(t,e))},_handlePointerOut(t){var e=this._hoveredLayer;e&&(this._container.classList.remove("leaflet-interactive"),this._fireEvent([e],t,"pointerout"),this._hoveredLayer=null,this._pointerHoverThrottled=!1)},_handlePointerHover(t,o){if(!this._pointerHoverThrottled){let e,i;for(let t=this._drawFirst;t;t=t.next)(e=t.layer).options.interactive&&e._containsPoint(o)&&(i=e);i!==this._hoveredLayer&&(this._handlePointerOut(t),i)&&(this._container.classList.add("leaflet-interactive"),this._fireEvent([i],t,"pointerover"),this._hoveredLayer=i),this._fireEvent(!!this._hoveredLayer&&[this._hoveredLayer],t),this._pointerHoverThrottled=!0,this._pointerHoverThrottleTimeout=setTimeout(()=>{this._pointerHoverThrottled=!1},32)}},_fireEvent(t,e,i){this._map._fireDOMEvent(e,i||e.type,t)},_bringToFront(t){var e,i,o=t._order;o&&(e=o.next,i=o.prev,e)&&((e.prev=i)?i.next=e:e&&(this._drawFirst=e),o.prev=this._drawLast,(this._drawLast.next=o).next=null,this._drawLast=o,this._requestRedraw(t))},_bringToBack(t){var e,i,o=t._order;o&&(e=o.next,i=o.prev)&&((i.next=e)?e.prev=i:i&&(this._drawLast=i),o.prev=null,o.next=this._drawFirst,this._drawFirst.prev=o,this._drawFirst=o,this._requestRedraw(t))}});function canvas(t){return new Canvas(t)}function svgCreate(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function pointsToPath(t,e){return t.flatMap(t=>[...t.map((t,e)=>(e?"L":"M")+t.x+" "+t.y),e?"z":""]).join("")||"M0 0"}let create=svgCreate,SVG=Renderer.extend({_initContainer(){this._container=create("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=create("g"),this._container.appendChild(this._rootGroup)},_destroyContainer(){Renderer.prototype._destroyContainer.call(this),delete this._rootGroup,delete this._svgSize},_resizeContainer(){var t=Renderer.prototype._resizeContainer.call(this);this._svgSize&&this._svgSize.equals(t)||(this._svgSize=t,this._container.setAttribute("width",t.x),this._container.setAttribute("height",t.y))},_update(){var t,e;this._map._animatingZoom&&this._bounds||(e=(t=this._bounds).getSize(),this._container.setAttribute("viewBox",[t.min.x,t.min.y,e.x,e.y].join(" ")),this.fire("update"))},_initPath(t){var e=t._path=create("path");t.options.className&&e.classList.add(...splitWords(t.options.className)),t.options.interactive&&e.classList.add("leaflet-interactive"),this._updateStyle(t),this._layers[stamp(t)]=t},_addPath(t){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(t._path),t.addInteractiveTarget(t._path)},_removePath(t){t._path.remove(),t.removeInteractiveTarget(t._path),delete this._layers[stamp(t)]},_updatePath(t){t._project(),t._update()},_updateStyle(t){var e=t._path,t=t.options;e&&(t.stroke?(e.setAttribute("stroke",t.color),e.setAttribute("stroke-opacity",t.opacity),e.setAttribute("stroke-width",t.weight),e.setAttribute("stroke-linecap",t.lineCap),e.setAttribute("stroke-linejoin",t.lineJoin),t.dashArray?e.setAttribute("stroke-dasharray",t.dashArray):e.removeAttribute("stroke-dasharray"),t.dashOffset?e.setAttribute("stroke-dashoffset",t.dashOffset):e.removeAttribute("stroke-dashoffset")):e.setAttribute("stroke","none"),t.fill?(e.setAttribute("fill",t.fillColor||t.color),e.setAttribute("fill-opacity",t.fillOpacity),e.setAttribute("fill-rule",t.fillRule||"evenodd")):e.setAttribute("fill","none"))},_updatePoly(t,e){this._setPath(t,pointsToPath(t._parts,e))},_updateCircle(t){var e=t._point,i=Math.max(Math.round(t._radius),1),o=`a${i},${Math.max(Math.round(t._radiusY),1)||i} 0 1,0 `,e=t._empty()?"M0 0":`M${e.x-i},${e.y}${o}${2*i},0 ${o}${2*-i},0 `;this._setPath(t,e)},_setPath(t,e){t._path.setAttribute("d",e)},_bringToFront(t){toFront(t._path)},_bringToBack(t){toBack(t._path)}});function svg(t){return new SVG(t)}Map$1.include({getRenderer(t){let e=t.options.renderer??this._getPaneRenderer(t.options.pane)??this.options.renderer??this._renderer;return e=e||(this._renderer=this._createRenderer()),this.hasLayer(e)||this.addLayer(e),e},_getPaneRenderer(e){if("overlayPane"!==e&&void 0!==e){let t=this._paneRenderers[e];return void 0===t&&(t=this._createRenderer({pane:e}),this._paneRenderers[e]=t),t}},_createRenderer(t){return this.options.preferCanvas&&canvas(t)||svg(t)}});let Rectangle=Polygon.extend({initialize(t,e){Polygon.prototype.initialize.call(this,this._boundsToLatLngs(t),e)},setBounds(t){return this.setLatLngs(this._boundsToLatLngs(t))},_boundsToLatLngs(t){return[(t=toLatLngBounds(t)).getSouthWest(),t.getNorthWest(),t.getNorthEast(),t.getSouthEast()]}});function rectangle(t,e){return new Rectangle(t,e)}SVG.create=create,SVG.pointsToPath=pointsToPath,GeoJSON.geometryToLayer=geometryToLayer,GeoJSON.coordsToLatLng=coordsToLatLng,GeoJSON.coordsToLatLngs=coordsToLatLngs,GeoJSON.latLngToCoords=latLngToCoords,GeoJSON.latLngsToCoords=latLngsToCoords,GeoJSON.getFeature=getFeature,GeoJSON.asFeature=asFeature,Map$1.mergeOptions({boxZoom:!0});let BoxZoom=Handler.extend({initialize(t){this._map=t,this._container=t._container,this._pane=t._panes.overlayPane,this._resetStateTimeout=0,t.on("unload",this._destroy,this)},addHooks(){on(this._container,"pointerdown",this._onPointerDown,this)},removeHooks(){off(this._container,"pointerdown",this._onPointerDown,this)},moved(){return this._moved},_destroy(){this._pane.remove(),delete this._pane},_resetState(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState(){0!==this._resetStateTimeout&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onPointerDown(t){if(!t.shiftKey||0!==t.button)return!1;this._clearDeferredResetState(),this._resetState(),disableTextSelection(),disableImageDrag(),this._startPoint=this._map.pointerEventToContainerPoint(t),on(document,{contextmenu:stop,pointermove:this._onPointerMove,pointerup:this._onPointerUp,keydown:this._onKeyDown},this)},_onPointerMove(t){this._moved||(this._moved=!0,this._box=create$1("div","leaflet-zoom-box",this._container),this._container.classList.add("leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.pointerEventToContainerPoint(t);var t=new Bounds(this._point,this._startPoint),e=t.getSize();setPosition(this._box,t.min),this._box.style.width=e.x+"px",this._box.style.height=e.y+"px"},_finish(){this._moved&&(this._box.remove(),this._container.classList.remove("leaflet-crosshair")),enableTextSelection(),enableImageDrag(),off(document,{contextmenu:stop,pointermove:this._onPointerMove,pointerup:this._onPointerUp,keydown:this._onKeyDown},this)},_onPointerUp(t){0===t.button&&(this._finish(),this._moved)&&(this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(this._resetState.bind(this),0),t=new LatLngBounds(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point)),this._map.fitBounds(t).fire("boxzoomend",{boxZoomBounds:t}))},_onKeyDown(t){"Escape"===t.code&&(this._finish(),this._clearDeferredResetState(),this._resetState())}}),DoubleClickZoom=(Map$1.addInitHook("addHandler","boxZoom",BoxZoom),Map$1.mergeOptions({doubleClickZoom:!0}),Handler.extend({addHooks(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick(t){var e=this._map,i=e.getZoom(),o=e.options.zoomDelta,i=t.originalEvent.shiftKey?i-o:i+o;"center"===e.options.doubleClickZoom?e.setZoom(i):e.setZoomAround(t.containerPoint,i)}})),Drag=(Map$1.addInitHook("addHandler","doubleClickZoom",DoubleClickZoom),Map$1.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0}),Handler.extend({addHooks(){var t;this._draggable||(t=this._map,this._draggable=new Draggable(t._mapPane,t._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),t.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),t.on("zoomend",this._onZoomEnd,this),t.whenReady(this._onZoomEnd,this))),this._map._container.classList.add("leaflet-grab","leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks(){this._map._container.classList.remove("leaflet-grab","leaflet-touch-drag"),this._draggable.disable()},moved(){return this._draggable?._moved},moving(){return this._draggable?._moving},_onDragStart(){var t,e=this._map;e._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity?(t=toLatLngBounds(this._map.options.maxBounds),this._offsetLimit=toBounds(this._map.latLngToContainerPoint(t.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(t.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))):this._offsetLimit=null,e.fire("movestart").fire("dragstart"),e.options.inertia&&(this._positions=[],this._times=[])},_onDrag(t){var e,i;this._map.options.inertia&&(e=this._lastTime=+new Date,i=this._lastPos=this._draggable._absPos||this._draggable._newPos,this._positions.push(i),this._times.push(e),this._prunePositions(e)),this._map.fire("move",t).fire("drag",t)},_prunePositions(t){for(;1<this._positions.length&&50<t-this._times[0];)this._positions.shift(),this._times.shift()},_onZoomEnd(){var t=this._map.getSize().divideBy(2),e=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=e.subtract(t).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit(t,e){return t-(t-e)*this._viscosity},_onPreDragLimit(){var t,e;this._viscosity&&this._offsetLimit&&(t=this._draggable._newPos.subtract(this._draggable._startPos),e=this._offsetLimit,t.x<e.min.x&&(t.x=this._viscousLimit(t.x,e.min.x)),t.y<e.min.y&&(t.y=this._viscousLimit(t.y,e.min.y)),t.x>e.max.x&&(t.x=this._viscousLimit(t.x,e.max.x)),t.y>e.max.y&&(t.y=this._viscousLimit(t.y,e.max.y)),this._draggable._newPos=this._draggable._startPos.add(t))},_onPreDragWrap(){var t=this._worldWidth,e=Math.round(t/2),i=this._initialWorldOffset,o=this._draggable._newPos.x,n=(o-e+i)%t+e-i,o=(o+e+i)%t-e-i,t=Math.abs(n+i)<Math.abs(o+i)?n:o;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=t},_onDragEnd(t){let l=this._map,d=l.options,e=!d.inertia||t.noInertia||this._times.length<2;if(l.fire("dragend",t),e)l.fire("moveend");else{this._prunePositions(+new Date);let t=this._lastPos.subtract(this._positions[0]),e=(this._lastTime-this._times[0])/1e3,i=d.easeLinearity,o=t.multiplyBy(i/e),n=o.distanceTo([0,0]),s=Math.min(d.inertiaMaxSpeed,n),a=o.multiplyBy(s/n),r=s/(d.inertiaDeceleration*i),h=a.multiplyBy(-r/2).round();h.x||h.y?(h=l._limitOffset(h,l.options.maxBounds),requestAnimationFrame(()=>{l.panBy(h,{duration:r,easeLinearity:i,noMoveStart:!0,animate:!0})})):l.fire("moveend")}}})),Keyboard=(Map$1.addInitHook("addHandler","dragging",Drag),Map$1.mergeOptions({keyboard:!0,keyboardPanDelta:80}),Handler.extend({keyCodes:{left:["ArrowLeft"],right:["ArrowRight"],down:["ArrowDown"],up:["ArrowUp"],zoomIn:["Equal","NumpadAdd","BracketRight"],zoomOut:["Minus","NumpadSubtract","Digit6","Slash"]},initialize(t){this._map=t,this._setPanDelta(t.options.keyboardPanDelta),this._setZoomDelta(t.options.zoomDelta)},addHooks(){var t=this._map._container;t.tabIndex<=0&&(t.tabIndex="0"),t.ariaKeyShortcuts=Object.values(this.keyCodes).flat().join(" "),on(t,{focus:this._onFocus,blur:this._onBlur,pointerdown:this._onPointerDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks(){this._removeHooks(),off(this._map._container,{focus:this._onFocus,blur:this._onBlur,pointerdown:this._onPointerDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onPointerDown(){var t,e,i;this._focused||(i=document.body,t=document.documentElement,e=i.scrollTop||t.scrollTop,i=i.scrollLeft||t.scrollLeft,this._map._container.focus(),window.scrollTo(i,e))},_onFocus(){this._focused=!0,this._map.fire("focus")},_onBlur(){this._focused=!1,this._map.fire("blur")},_setPanDelta(t){var e,i,o,n,s=this._panKeys={},a=this.keyCodes;for(e of a.left)s[e]=[-1*t,0];for(i of a.right)s[i]=[t,0];for(o of a.down)s[o]=[0,t];for(n of a.up)s[n]=[0,-1*t]},_setZoomDelta(t){var e,i,o=this._zoomKeys={},n=this.keyCodes;for(e of n.zoomIn)o[e]=t;for(i of n.zoomOut)o[i]=-t},_addHooks(){on(document,"keydown",this._onKeyDown,this)},_removeHooks(){off(document,"keydown",this._onKeyDown,this)},_onKeyDown(e){if(!(e.altKey||e.ctrlKey||e.metaKey)){var i,o=e.code,n=this._map;let t;if(o in this._panKeys)n._panAnim&&n._panAnim._inProgress||(t=this._panKeys[o],e.shiftKey&&(t=toPoint(t).multiplyBy(3)),n.options.maxBounds&&(t=n._limitOffset(toPoint(t),n.options.maxBounds)),n.options.worldCopyJump?(i=n.wrapLatLng(n.unproject(n.project(n.getCenter()).add(t))),n.panTo(i)):n.panBy(t));else if(o in this._zoomKeys)n.setZoom(n.getZoom()+(e.shiftKey?3:1)*this._zoomKeys[o]);else{if("Escape"!==o||!n._popup||!n._popup.options.closeOnEscapeKey)return;n.closePopup()}stop(e)}}})),ScrollWheelZoom=(Map$1.addInitHook("addHandler","keyboard",Keyboard),Map$1.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60}),Handler.extend({addHooks(){on(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks(){off(this._map._container,"wheel",this._onWheelScroll,this),clearTimeout(this._timer)},_onWheelScroll(t){var e=getWheelDelta(t),i=this._map.options.wheelDebounceTime,e=(this._delta+=e,this._lastMousePos=this._map.pointerEventToContainerPoint(t),this._startTime||(this._startTime=+new Date),Math.max(i-(+new Date-this._startTime),0));clearTimeout(this._timer),this._timer=setTimeout(this._performZoom.bind(this),e),stop(t)},_performZoom(){var t=this._map,e=t.getZoom(),i=this._map.options.zoomSnap??0,o=(t._stop(),this._delta/(4*this._map.options.wheelPxPerZoomLevel)),o=4*Math.log(2/(1+Math.exp(-Math.abs(o))))/Math.LN2,i=i?Math.ceil(o/i)*i:o,o=t._limitZoom(e+(0<this._delta?i:-i))-e;this._delta=0,this._startTime=null,o&&("center"===t.options.scrollWheelZoom?t.setZoom(e+o):t.setZoomAround(this._lastMousePos,e+o))}})),tapHoldDelay=(Map$1.addInitHook("addHandler","scrollWheelZoom",ScrollWheelZoom),600),TapHold=(Map$1.mergeOptions({tapHold:Browser.touchNative&&Browser.safari&&Browser.mobile,tapTolerance:15}),Handler.extend({addHooks(){on(this._map._container,"pointerdown",this._onDown,this)},removeHooks(){off(this._map._container,"pointerdown",this._onDown,this),clearTimeout(this._holdTimeout)},_onDown(t){clearTimeout(this._holdTimeout),1===getPointers().length&&"mouse"!==t.pointerType&&(this._startPos=this._newPos=new Point(t.clientX,t.clientY),this._holdTimeout=setTimeout(()=>{this._cancel(),this._isTapValid()&&(on(document,"pointerup",preventDefault),on(document,"pointerup pointercancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",t))},tapHoldDelay),on(document,"pointerup pointercancel contextmenu",this._cancel,this),on(document,"pointermove",this._onMove,this))},_cancelClickPrevent:function t(){off(document,"pointerup",preventDefault),off(document,"pointerup pointercancel",t)},_cancel(){clearTimeout(this._holdTimeout),off(document,"pointerup pointercancel contextmenu",this._cancel,this),off(document,"pointermove",this._onMove,this)},_onMove(t){this._newPos=new Point(t.clientX,t.clientY)},_isTapValid(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent(t,e){t=new MouseEvent(t,{bubbles:!0,cancelable:!0,view:window,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY});t._simulated=!0,e.target.dispatchEvent(t)}})),PinchZoom=(Map$1.addInitHook("addHandler","tapHold",TapHold),Map$1.mergeOptions({pinchZoom:Browser.touch,bounceAtZoomLimits:!0}),Handler.extend({addHooks(){this._map._container.classList.add("leaflet-touch-zoom"),on(this._map._container,"pointerdown",this._onPointerStart,this)},removeHooks(){this._map._container.classList.remove("leaflet-touch-zoom"),off(this._map._container,"pointerdown",this._onPointerStart,this)},_onPointerStart(t){var e,i=this._map,o=getPointers();2!==o.length||i._animatingZoom||this._zooming||(e=i.pointerEventToContainerPoint(o[0]),o=i.pointerEventToContainerPoint(o[1]),this._centerPoint=i.getSize()._divideBy(2),this._startLatLng=i.containerPointToLatLng(this._centerPoint),"center"!==i.options.pinchZoom&&(this._pinchStartLatLng=i.containerPointToLatLng(e.add(o)._divideBy(2))),this._startDist=e.distanceTo(o),this._startZoom=i.getZoom(),this._moved=!1,this._zooming=!0,i._stop(),on(document,"pointermove",this._onPointerMove,this),on(document,"pointerup pointercancel",this._onPointerEnd,this),preventDefault(t))},_onPointerMove(t){var e=getPointers();if(2===e.length&&this._zooming){var i=this._map,o=i.pointerEventToContainerPoint(e[0]),e=i.pointerEventToContainerPoint(e[1]),n=o.distanceTo(e)/this._startDist;if(this._zoom=i.getScaleZoom(n,this._startZoom),!i.options.bounceAtZoomLimits&&(this._zoom<i.getMinZoom()&&n<1||this._zoom>i.getMaxZoom()&&1<n)&&(this._zoom=i._limitZoom(this._zoom)),"center"===i.options.pinchZoom){if(this._center=this._startLatLng,1==n)return}else{o=o._add(e)._divideBy(2)._subtract(this._centerPoint);if(1==n&&0===o.x&&0===o.y)return;this._center=i.unproject(i.project(this._pinchStartLatLng,this._zoom).subtract(o),this._zoom)}this._moved||(i._moveStart(!0,!1),this._moved=!0),cancelAnimationFrame(this._animRequest);e=i._move.bind(i,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=requestAnimationFrame(e.bind(this)),preventDefault(t)}},_onPointerEnd(){this._moved&&this._zooming?(this._zooming=!1,cancelAnimationFrame(this._animRequest),off(document,"pointermove",this._onPointerMove,this),off(document,"pointerup pointercancel",this._onPointerEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))):this._zooming=!1}})),version=(Map$1.addInitHook("addHandler","pinchZoom",PinchZoom),Map$1.addInitHook(function(){this.touchZoom=this.pinchZoom,void 0!==this.options.touchZoom&&(console.warn("Map: touchZoom option is deprecated and will be removed in future versions. Use pinchZoom instead."),this.options.pinchZoom=this.options.touchZoom,delete this.options.touchZoom),this.options.pinchZoom?this.pinchZoom.enable():this.pinchZoom.disable()}),Map$1.BoxZoom=BoxZoom,Map$1.DoubleClickZoom=DoubleClickZoom,Map$1.Drag=Drag,Map$1.Keyboard=Keyboard,Map$1.ScrollWheelZoom=ScrollWheelZoom,Map$1.TapHold=TapHold,Map$1.PinchZoom=PinchZoom,Map$1.TouchZoom=PinchZoom,pkg.version);var L={__proto__:null,BlanketOverlay:BlanketOverlay,Bounds:Bounds,Browser:Browser,CRS:CRS,Canvas:Canvas,Circle:Circle,CircleMarker:CircleMarker,Class:Class,Control:Control,DivIcon:DivIcon,DivOverlay:DivOverlay,DomEvent:DomEvent,DomUtil:DomUtil,Draggable:Draggable,Evented:Evented,FeatureGroup:FeatureGroup,GeoJSON:GeoJSON,GridLayer:GridLayer,Handler:Handler,Icon:Icon,ImageOverlay:ImageOverlay,LatLng:LatLng,LatLngBounds:LatLngBounds,Layer:Layer,LayerGroup:LayerGroup,LineUtil:LineUtil,Map:Map$1,Marker:Marker,Path:Path,Point:Point,PolyUtil:PolyUtil,Polygon:Polygon,Polyline:Polyline,Popup:Popup,PosAnimation:PosAnimation,Projection:index,Rectangle:Rectangle,Renderer:Renderer,SVG:SVG,SVGOverlay:SVGOverlay,TileLayer:TileLayer,Tooltip:Tooltip,Transformation:Transformation,Util:Util,VideoOverlay:VideoOverlay,bounds:toBounds,canvas:canvas,circle:circle,circleMarker:circleMarker,control:control,divIcon:divIcon,featureGroup:featureGroup,geoJSON:geoJSON,geoJson:geoJson,gridLayer:gridLayer,icon:icon,imageOverlay:imageOverlay,latLng:toLatLng,latLngBounds:toLatLngBounds,layerGroup:layerGroup,map:createMap,marker:marker,point:toPoint,polygon:polygon,polyline:polyline,popup:popup,rectangle:rectangle,setOptions:setOptions,stamp:stamp,svg:svg,svgOverlay:svgOverlay,tileLayer:tileLayer,tooltip:tooltip,transformation:toTransformation,version:version,videoOverlay:videoOverlay};let oldL=getGlobalObject().L;function getGlobalObject(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("Unable to locate global object.")}getGlobalObject().L=L,getGlobalObject().L.noConflict=function(){return getGlobalObject().L=oldL,this};export{BlanketOverlay,Bounds,Browser,CRS,Canvas,Circle,CircleMarker,Class,Control,DivIcon,DivOverlay,DomEvent,DomUtil,Draggable,Evented,FeatureGroup,GeoJSON,GridLayer,Handler,Icon,ImageOverlay,LatLng,LatLngBounds,Layer,LayerGroup,LineUtil,Map$1 as Map,Marker,Path,Point,PolyUtil,Polygon,Polyline,Popup,PosAnimation,index as Projection,Rectangle,Renderer,SVG,SVGOverlay,TileLayer,Tooltip,Transformation,Util,VideoOverlay,toBounds as bounds,canvas,circle,circleMarker,control,L as default,divIcon,featureGroup,geoJSON,geoJson,gridLayer,icon,imageOverlay,toLatLng as latLng,toLatLngBounds as latLngBounds,layerGroup,createMap as map,marker,toPoint as point,polygon,polyline,popup,rectangle,setOptions,stamp,svg,svgOverlay,tileLayer,tooltip,toTransformation as transformation,version,videoOverlay};
//# sourceMappingURL=leaflet.js.map